openapi: 3.0.2
info:
  title: Pigeon API
  description: Pigeon API provides an access to Campaigns and Alerts for Digital Applications
  contact:
    email: <EMAIL>
    url: https://confluence.agile.bns/display/PIGEON/Pigeon+Home
    name: Pigeon Support
  version: 1.x.x
servers:
  - url: https://pigeon-ist.apps.cloud.bns
    description: IST (SmartDNS)
  - url: https://pigeon-ist.apps.stg.azr-cc-pcf.cloud.bns
    description: IST (CC)
  - url: https://pigeon-ist.apps.stg.azr-use2-pcf.cloud.bns
    description: IST (USE2)
  - url: https://pigeon-uat.apps.cloud.bns
    description: UAT (SmartDNS)
  - url: https://pigeon-uat.apps.stg.azr-cc-pcf.cloud.bns
    description: UAT (CC)
  - url: https://pigeon-uat.apps.stg.azr-use2-pcf.cloud.bns
    description: UAT (USE2)
  - url: https://pigeon-nft.apps.cloud.bns
    description: NFT (SmartDNS)
  - url: https://pigeon-nft.apps.stg.azr-cc-pcf.cloud.bns
    description: NFT (CC)
  - url: https://pigeon-nft.apps.stg.azr-use2-pcf.cloud.bns
    description: NFT (USE2)
  - url: https://pigeon.apps.cloud.bns
    description: Production (SmartDNS)
  - url: https://pigeon-prd.apps.prd.azr-cc-pcf.cloud.bns
    description: Production (CC)
  - url: https://pigeon-prd.apps.prd.azr-use2-pcf.cloud.bns
    description: Production (USE2)
tags:
  - name: Alerts
    description: Pre-login alerts
  - name: Campaigns
    description: Campaigns and offers
  - name: Vignette
    description: Legacy contents catalog, web fragments and rules for SOL
paths:
  # alerts
  /v1/alerts:
    get:
      description: >-
        Returns a list of alerts
      tags:
        - Alerts
      parameters:
        - $ref: '#/components/parameters/traceIdHeader'
        - $ref: '#/components/parameters/spanIdHeader'
        - $ref: '#/components/parameters/channelIdHeader'
        - $ref: '#/components/parameters/applicationHeader'
        - $ref: '#/components/parameters/preferredEnvironmentHeader'
        - $ref: '#/components/parameters/languageHeader'
        - $ref: '#/components/parameters/applicationQuery'
        - $ref: '#/components/parameters/platformQuery'
        - $ref: '#/components/parameters/pageQuery'
        - $ref: '#/components/parameters/containerQuery'
        - $ref: '#/components/parameters/applicationVersionQuery'
        - $ref: '#/components/parameters/osVersionQuery'
        - $ref: '#/components/parameters/deviceModelQuery'
        - $ref: '#/components/parameters/alertLimitQuery'
      responses:
        200:
          $ref: '#/components/responses/v1.AlertsResponse'
        400:
          $ref: '#/components/responses/v1.BadRequestResponse'
        500:
          $ref: '#/components/responses/v1.InternalServerErrorResponse'
  # campaigns
  /v1/campaigns-inbox:
    get:
      description: >-
        Return the list of Orion Inbox campaigns
      tags:
        - Campaigns Inbox
      security:
        - ServiceToServiceAuth:
            - cdb.pigeon.campaigns.read
      parameters:
          # Headers
        - $ref: '#/components/parameters/customerCardRequiredHeader'
        - $ref: '#/components/parameters/traceIdRequiredHeader'
        - $ref: '#/components/parameters/spanIdRequiredHeader'
        - $ref: '#/components/parameters/channelIdRequiredHeader'
        - $ref: '#/components/parameters/OriginatingApplicationCodeRequiredHeader'
        - $ref: '#/components/parameters/countryCodeRequiredHeader'
        - $ref: '#/components/parameters/languageRequiredHeader'
        - $ref: '#/components/parameters/mockInsightHeader'
        - $ref: '#/components/parameters/preferredEnvironmentHeader'
        - $ref: '#/components/parameters/sessionIdHeader'
        - $ref: '#/components/parameters/webApplicationHeader'
          # Query
        - $ref: '#/components/parameters/campaignsLimitQuery'
        - $ref: '#/components/parameters/campaignsOffsetQuery'
        - $ref: '#/components/parameters/modeQuery'
        - $ref: '#/components/parameters/insightFlagQuery'
      responses:
        200:
          $ref: '#/components/responses/v1.CampaignsInboxResponse'
        400:
          $ref: '#/components/responses/v1.BadRequestResponse'
        401:
          $ref: '#/components/responses/v1.UnauthorizedResponse'
        403:
          $ref: '#/components/responses/v1.ForbiddenResponse'
        404:
          $ref: '#/components/responses/v1.NotFoundResponse'
        500:
          $ref: '#/components/responses/v1.InternalServerErrorResponse'
  /v1/campaigns:
    get:
      description: >-
        Returns a list of campaigns available for a customer
      tags:
        - Campaigns
      security:
        - ServiceToServiceAuth:
          - cdb.pigeon.campaigns.read
          - ca:baas:campaigns:read
        - OpaqueAuth:
          - standard
      parameters:
        - $ref: '#/components/parameters/traceIdHeader'
        - $ref: '#/components/parameters/spanIdHeader'
        - $ref: '#/components/parameters/channelIdHeader'
        - $ref: '#/components/parameters/applicationHeader'
        - $ref: '#/components/parameters/preferredEnvironmentHeader'
        - $ref: '#/components/parameters/languageHeader'
        - $ref: '#/components/parameters/mockInsightHeader'
        - $ref: '#/components/parameters/customerAuthHeader'
        - $ref: '#/components/parameters/customerCardHeader'
        - $ref: '#/components/parameters/userContext'
        - $ref: '#/components/parameters/countryCode'
        - $ref: '#/components/parameters/applicationQuery'
        - $ref: '#/components/parameters/platformQuery'
        - $ref: '#/components/parameters/pageMultipleQuery'
        - $ref: '#/components/parameters/pageExcludeQuery'
        - $ref: '#/components/parameters/containerQuery'
        - $ref: '#/components/parameters/applicationVersionQuery'
        - $ref: '#/components/parameters/osVersionQuery'
        - $ref: '#/components/parameters/deviceModelQuery'
        - $ref: '#/components/parameters/campaignsLimitQuery'
        - $ref: '#/components/parameters/selectContentsQuery'
        - $ref: '#/components/parameters/OriginatingApplicationCodeParam'
        - $ref: '#/components/parameters/campaignsOffsetQuery'
      responses:
        200:
          $ref: '#/components/responses/v1.CampaignsResponse'
        400:
          $ref: '#/components/responses/v1.BadRequestResponse'
        401:
          $ref: '#/components/responses/v1.UnauthorizedResponse'
        403:
          $ref: '#/components/responses/v1.ForbiddenResponse'
        500:
          $ref: '#/components/responses/v1.InternalServerErrorResponse'
    delete:
      description: >-
        Flushes the redis cache for customer's products data. Available only in NON-PROD environments.
      tags:
        - Campaigns
      security:
        - ServiceToServiceAuth:
          - cdb.pigeon.campaigns.cache.delete
        - OpaqueAuth:
          - standard
      parameters:
        - $ref: '#/components/parameters/traceIdHeader'
        - $ref: '#/components/parameters/spanIdHeader'
        - $ref: '#/components/parameters/channelIdHeader'
        - $ref: '#/components/parameters/applicationHeader'
        - $ref: '#/components/parameters/preferredEnvironmentHeader'
      responses:
        200:
          description: Success
  /v1/campaigns/{ruleId}:
    get:
      description: >-
        Returns a campaign by its id
      tags:
        - Campaigns
      security:
        - ServiceToServiceAuth:
          - cdb.pigeon.campaigns.read
          - ca:baas:campaigns:read
        - OpaqueAuth:
          - standard
      parameters:
        - $ref: '#/components/parameters/ruleId'
        - $ref: '#/components/parameters/traceIdHeader'
        - $ref: '#/components/parameters/spanIdHeader'
        - $ref: '#/components/parameters/channelIdHeader'
        - $ref: '#/components/parameters/applicationHeader'
        - $ref: '#/components/parameters/preferredEnvironmentHeader'
        - $ref: '#/components/parameters/languageHeader'
        - $ref: '#/components/parameters/mockInsightHeader'
        - $ref: '#/components/parameters/customerAuthHeader'
        - $ref: '#/components/parameters/customerCardHeader'
        - $ref: '#/components/parameters/userContext'
        - $ref: '#/components/parameters/countryCode'
        - $ref: '#/components/parameters/messageIdQuery'
        - $ref: '#/components/parameters/insightFlagQuery'
        - $ref: '#/components/parameters/applicationQuery'
        - $ref: '#/components/parameters/selectContentsQuery'
        - $ref: '#/components/parameters/OriginatingApplicationCodeParam'
      responses:
        200:
          $ref: '#/components/responses/v1.CampaignResponse'
        400:
          $ref: '#/components/responses/v1.BadRequestResponse'
        401:
          $ref: '#/components/responses/v1.UnauthorizedResponse'
        403:
          $ref: '#/components/responses/v1.ForbiddenResponse'
        404:
          $ref: '#/components/responses/v1.NotFoundResponse'
        500:
          $ref: '#/components/responses/v1.InternalServerErrorResponse'
  /v1/campaigns/details/{campaignId}:
    get:
      description: >-
        Returns a campaign by campaign ID 
      tags:
        - Campaigns
      security:
        - ServiceToServiceAuth:
          - cdb.pigeon.campaigns.read
          - ca:baas:campaigns:read
        - OpaqueAuth:
          - standard
      parameters:
        - $ref: '#/components/parameters/campaignId'
        - $ref: '#/components/parameters/applicationRequiredQuery'
        - $ref: '#/components/parameters/pageRequiredQuery'
        - $ref: '#/components/parameters/containerRequiredQuery'
        - $ref: '#/components/parameters/platformOptionalQuery'
        - $ref: '#/components/parameters/traceIdHeader'
        - $ref: '#/components/parameters/spanIdHeader'
        - $ref: '#/components/parameters/customerAuthHeader'
        - $ref: '#/components/parameters/customerCardHeader'
        - $ref: '#/components/parameters/userContext'
        - $ref: '#/components/parameters/countryCode'
      responses:
        200:
          $ref: '#/components/responses/v1.CampaignResponse'
        400:
          $ref: '#/components/responses/v1.BadRequestResponse'
        401:
          $ref: '#/components/responses/v1.UnauthorizedResponse'
        403:
          $ref: '#/components/responses/v1.ForbiddenResponse'
        404:
          $ref: '#/components/responses/v1.NotFoundResponse'
        500:
          $ref: '#/components/responses/v1.InternalServerErrorResponse'

  /v1/campaigns/{ruleId}/dispositions:
    post:
      deprecated: true
      description: >-
        Sets a disposition for a campaign by its id
      tags:
        - Campaigns
      security:
        - ServiceToServiceAuth:
          - cdb.pigeon.campaigns.dispositions.write
        - OpaqueAuth:
          - standard
      parameters:
        - $ref: '#/components/parameters/ruleId'
        - $ref: '#/components/parameters/traceIdHeader'
        - $ref: '#/components/parameters/spanIdHeader'
        - $ref: '#/components/parameters/channelIdHeader'
        - $ref: '#/components/parameters/applicationHeader'
        - $ref: '#/components/parameters/preferredEnvironmentHeader'
        - $ref: '#/components/parameters/languageHeader'
        - $ref: '#/components/parameters/mockInsightHeader'
        - $ref: '#/components/parameters/customerAuthHeader'
        - $ref: '#/components/parameters/customerCardHeader'
        - $ref: '#/components/parameters/OriginatingApplicationCodeParam'
      requestBody:
        $ref: '#/components/requestBodies/v1.DispositionRequestBody'
      responses:
        200:
          $ref: '#/components/responses/v1.EmptyDataResponse'
        400:
          $ref: '#/components/responses/v1.BadRequestResponse'
        401:
          $ref: '#/components/responses/v1.UnauthorizedResponse'
        403:
          $ref: '#/components/responses/v1.ForbiddenResponse'
        404:
          $ref: '#/components/responses/v1.NotFoundResponse'
        500:
          $ref: '#/components/responses/v1.InternalServerErrorResponse'
  /v1/campaigns/{ruleId}/token:
    get:
      description: >-
        Returns a SAML token required for a Scotia Home or Small Business campaign's CTA
      tags:
        - Campaigns
      security:
        - ServiceToServiceAuth:
          - cdb.pigeon.campaigns.read
        - OpaqueAuth:
          - standard
      parameters:
        - $ref: '#/components/parameters/ruleId'
        - $ref: '#/components/parameters/traceIdHeader'
        - $ref: '#/components/parameters/spanIdHeader'
        - $ref: '#/components/parameters/channelIdHeader'
        - $ref: '#/components/parameters/applicationHeader'
        - $ref: '#/components/parameters/preferredEnvironmentHeader'
        - $ref: '#/components/parameters/languageHeader'
        - $ref: '#/components/parameters/mockInsightHeader'
        - $ref: '#/components/parameters/customerAuthHeader'
        - $ref: '#/components/parameters/customerCardHeader'
        - $ref: '#/components/parameters/messageIdRequiredQuery'
      responses:
        200:
          $ref: '#/components/responses/v1.SamlTokenResponse'
        400:
          $ref: '#/components/responses/v1.BadRequestResponse'
        401:
          $ref: '#/components/responses/v1.UnauthorizedResponse'
        403:
          $ref: '#/components/responses/v1.ForbiddenResponse'
        404:
          $ref: '#/components/responses/v1.NotFoundResponse'
        500:
          $ref: '#/components/responses/v1.InternalServerErrorResponse'
  /v1/dispositions:
    post:
      description: >-
        Sets a disposition for a campaign.
      tags:
        - Campaigns
      security:
        - ServiceToServiceAuth:
          - cdb.pigeon.campaigns.dispositions.write
      parameters:
        - $ref: '#/components/parameters/traceIdHeader'
        - $ref: '#/components/parameters/spanIdHeader'
        - $ref: '#/components/parameters/channelIdHeader'
        - $ref: '#/components/parameters/applicationHeader'
        - $ref: '#/components/parameters/preferredEnvironmentHeader'
        - $ref: '#/components/parameters/languageHeader'
        - $ref: '#/components/parameters/mockInsightHeader'
        - $ref: '#/components/parameters/customerAuthHeader'
        - $ref: '#/components/parameters/customerCardHeader'
        - $ref: '#/components/parameters/OriginatingApplicationCodeParam'
        - $ref: '#/components/parameters/customerId'
        - $ref: '#/components/parameters/userContext'
        - $ref: '#/components/parameters/countryCode'
      requestBody:
        $ref: '#/components/requestBodies/v1.DispositionRequestBody'
      responses:
        200:
          $ref: '#/components/responses/v1.EmptyDataResponse'
        400:
          $ref: '#/components/responses/v1.BadRequestResponse'
        401:
          $ref: '#/components/responses/v1.UnauthorizedResponse'
        403:
          $ref: '#/components/responses/v1.ForbiddenResponse'
        404:
          $ref: '#/components/responses/v1.NotFoundResponse'
        500:
          $ref: '#/components/responses/v1.InternalServerErrorResponse'
  # vignette
  /v1/contents-catalog:
    get:
      description: >-
        Returns a contents catalog of newly created and updated rules and web fragments for SOL. Replicates a Vignette's endpoint.
      tags:
        - Vignette
      security:
        - ServiceToServiceSolAuth: []
      parameters:
        - $ref: '#/components/parameters/traceIdHeader'
        - $ref: '#/components/parameters/spanIdHeader'
        - $ref: '#/components/parameters/preferredEnvironmentHeader'
        - $ref: '#/components/parameters/vignetteFromDateQuery'
        - $ref: '#/components/parameters/vignetteToDateQuery'
      responses:
        200:
          $ref: '#/components/responses/v1.ContentsCatalogResponse'
        401:
          $ref: '#/components/responses/v1.ContentsCatalogUnauthorizedResponse'
        403:
          $ref: '#/components/responses/v1.ContentsCatalogForbiddenResponse'
        500:
          $ref: '#/components/responses/v1.ContentsCatalogInternalServerErrorResponse'
  /v1/contents-catalog/rules/{ruleId}:
    get:
      description: >-
        Returns a single rule details from a contents catalog for SOL. Replicates a Vignette's endpoint.
      tags:
        - Vignette
      security:
        - ServiceToServiceSolAuth: []
      parameters:
        - $ref: '#/components/parameters/traceIdHeader'
        - $ref: '#/components/parameters/spanIdHeader'
        - $ref: '#/components/parameters/preferredEnvironmentHeader'
        - $ref: '#/components/parameters/vignetteRuleIdParam'
      responses:
        200:
          $ref: '#/components/responses/v1.ContentsCatalogRuleResponse'
        401:
          $ref: '#/components/responses/v1.ContentsCatalogUnauthorizedResponse'
        403:
          $ref: '#/components/responses/v1.ContentsCatalogForbiddenResponse'
        404:
          $ref: '#/components/responses/v1.ContentsCatalogNotFoundResponse'
        500:
          $ref: '#/components/responses/v1.ContentsCatalogInternalServerErrorResponse'
  /v1/contents-catalog/webfragments/{webfragmentId}:
    get:
      description: >-
        Returns a single webfragment from a contents catalog for SOL. Replicates a Vignette's endpoint.
      tags:
        - Vignette
      security:
        - ServiceToServiceSolAuth: []
      parameters:
        - $ref: '#/components/parameters/traceIdHeader'
        - $ref: '#/components/parameters/spanIdHeader'
        - $ref: '#/components/parameters/preferredEnvironmentHeader'
        - $ref: '#/components/parameters/vignetteWebfragmentIdParam'
      responses:
        200:
          $ref: '#/components/responses/v1.ContentsCatalogRuleResponse'
        401:
          $ref: '#/components/responses/v1.ContentsCatalogUnauthorizedResponse'
        403:
          $ref: '#/components/responses/v1.ContentsCatalogForbiddenResponse'
        404:
          $ref: '#/components/responses/v1.ContentsCatalogNotFoundResponse'
        500:
          $ref: '#/components/responses/v1.ContentsCatalogInternalServerErrorResponse'
components:
  securitySchemes:
    ServiceToServiceAuth:
      type: oauth2
      flows:
        authorizationCode:
          authorizationUrl: https://passport-oauth-prd.apps.prd.azr-cc-pcf.cloud.bns/oauth2/v1/authorize
          tokenUrl: https://passport-oauth-prd.apps.prd.azr-cc-pcf.cloud.bns/oauth2/v1/token
          scopes:
            cdb.pigeon.campaigns.read: Get Campaigns
            cdb.pigeon.campaigns.dispositions.write: Set Dispositions
            cdb.pigeon.campaigns.cache.delete: Clear product data cache
            ca:baas:campaigns:read: Get campaigns (deprecated)
            ca:baas:dispositions:write: Set dispositions (deprecated)
    ServiceToServiceSolAuth:
      type: oauth2
      flows:
        authorizationCode:
          authorizationUrl: https://passport-oauth-prd.apps.prd.azr-cc-pcf.cloud.bns/oauth2/v1/authorize
          tokenUrl: https://passport-oauth-prd.apps.prd.azr-cc-pcf.cloud.bns/oauth2/v1/token
    OpaqueAuth:
      type: http
      scheme: bearer
      bearerFormat: Opaque
  parameters:
    # common headers
    traceIdHeader:
      description: >-
        Optional trace ID to correlate multiple requests together.
      name: x-b3-traceid
      in: header
      required: false
      example: bd7a977555f6b982
      schema:
        type: string
    sessionIdHeader:
      description: >-
        Contentful URL would have {{header:x-session-id}}, pigeon api would sub the value and then if you clicked the cta from Orion/PW to some other client app, you could pass the session id to the client app
      name: x-session-id
      in: header
      required: false
      example: bd7a977555f6b982
      schema:
        type: string    
    traceIdRequiredHeader:
      description: >-
        Required trace ID to correlate multiple requests together.
      name: x-b3-traceid
      in: header
      required: true
      example: bd7a977555f6b982
      schema:
        type: string    
    spanIdHeader:
      description: >-
        Optional span ID to correlate multiple requests together.
      name: x-b3-spanid
      in: header
      required: false
      example: bd7a977555f6b982
      schema:
        type: string
    spanIdRequiredHeader:
      description: >-
        Required span ID to correlate multiple requests together.
      name: x-b3-spanid
      in: header
      required: true
      example: bd7a977555f6b982
      schema:
        type: string
    preferredEnvironmentHeader:
      description: >-
        Optional preferred SOL environment. SOL environment mapping can be determined
        from https://sol-status.apps.stg.azr-cc-pcf.cloud.bns/
      name: preferred-environment
      in: header
      required: false
      example: uatblack
      schema:
        type: string
    channelIdHeader:
      description: >-
        Optional header to specify a channel id and to pass to all downstream services.
         * `Mobile` - Mobile channel
         * `Online` - Web channel
         * `ABM` - ABM channel
      name: x-channel-id
      in: header
      required: false
      schema:
        type: string
        minLength: 1
        maxLength: 20
        enum: ['Online','Mobile','ABM']
        default: Mobile
        example: Mobile
    channelIdRequiredHeader:
      description: >-
        Required header to specify a channel id and to pass to all downstream services.
         * `Online` - Web channel
      name: x-channel-id
      in: header
      required: true
      schema:
        type: string
        minLength: 1
        maxLength: 20
        enum: ['Online']
        default: Online
        example: Online
    applicationHeader:
      description: >-
        Optional header to specify an application id and to pass to all downstream services. Required only when `x-channel-id` is `Mobile`.
         * `N1` - Nova iOS
         * `N2` - Nova Android
      name: x-application
      in: header
      required: false
      schema:
        type: string
        minLength: 1
        maxLength: 10
        enum: ['N1','N2']
        example: 'N1'
    webApplicationHeader:
      description: >-
        Optional header to specify an application id and to pass to all downstream services.
         * `M1` - For Web
      name: x-application
      in: header
      required: false
      schema:
        type: string
        minLength: 2
        maxLength: 10
        enum: ['M1']
        example: 'M1'
    customerAuthHeader:
      description: >-
        Optional customer's JSON web token. Should be used with a service to service authentication. If either `x-customer-authorization` or `x-customer-scotiacard` is not specified only mass messages will be returned.
      name: x-customer-authorization
      in: header
      required: false
      schema:
        type: string
    customerCardHeader:
      description: >-
        Optional customer's card number. Should be used with a service to service authentication. If either `x-customer-authorization` or `x-customer-scotiacard` is not specified only mass messages will be returned.
      name: x-customer-scotiacard
      in: header
      required: false
      schema:
        type: string
        minLength: 10
        maxLength: 16
        pattern: '^[0-9]+$'
    customerCardRequiredHeader:
      description: >-
        Required customer's card number. Should be used with a service to service authentication.
      name: x-customer-scotiacard
      in: header
      required: true
      schema:
        type: string
        minLength: 10
        maxLength: 16
        pattern: '^[0-9]+$'
    userContext:
      name: x-user-context
      in: header
      description: |
        - x-user-context Header. CCAU customer identifier. Used to send the customer specific information in base 64 encoded format for CCAU customers.
        - Optional. If provided, and country code provided belongs to CCAU, this value overrides cardNumber when interacting with downstream services
        - Format for Mobile: { "countryCode": "AOH","cid": "ARVELO*TAMAR*03", "locale": "en_AO","login":"username1212", "appId": "com.scotiabank.wave", "customerId": "0ebd8400-31a3-4cb9-b8f4-7066e75be27e", transitId :"789456" } 
        - Format for Web: { "countryCode": "AOH","cid": "ARVELO*TAMAR*03", "locale": "en_AO","login":"username1212", "remoteAddress": "*********", "customerId": "0ebd8400-31a3-4cb9-b8f4-7066e75be27e", transitId :"789456" }
      schema:
        type: string
        example: eyJjb3VudHJ5Q29kZSI6ICJBT0giLCJjaWQiOiAiQVJWRUxPKlRBTUFSKjAzIiwgImxvY2FsZSI6ICJlbl9BTyIsImxvZ2luIjoidXNlcm5hbWUxMjEyIiwgImFwcElkIjogImNvbS5zY290aWFiYW5rLndhdmUifQ==
      required: false
    countryCode:
      name: x-country-code
      in: header
      description: 'Identifies the two letter country code of the customer'
      example: 'DO'
      schema:
        type: string
        example: DO
        default: 'CA'
      required: false
    countryCodeRequiredHeader:
      name: x-country-code
      in: header
      description: 'Identifies the two letter country code of the customer'
      example: 'CA'
      schema:
        type: string
        example: 'CA'
        default: 'CA'
      required: true
    languageHeader:
      description: >-
        Optional language code to specify which language should be used for campaign content.
      name: x-language
      in: header
      required: false
      schema:
        type: string
        minLength: 1
        maxLength: 5
        pattern: '^[a-zA-Z-]+$'
        default: 'en'
    languageRequiredHeader:
      description: >-
        Optional language code to specify which language should be used for campaign content.
      name: x-language
      in: header
      required: true
      schema:
        type: string
        enum: ['en', 'fr']
        default: 'en'
    mockInsightHeader:
      description: >-
        Optional flag that can be used in IST and UAT environments to get mocked
        targeted campaigns.
      name: x-mock-insight
      in: header
      required: false
      schema:
        type: boolean
        default: false
    OriginatingApplicationCodeParam:
      name: x-originating-appl-code
      in: header
      description: 'This is to identify the source Application EPM code which is making
        this request. Originating application is responsible for sending x-originating-appl-code.
        For example, if  application 1 makes call to application 2 and then application
        2 makes a call to application 3, application 1 will populate and send the
        x-originating-appl-code to application 2 and application 2 will propagate
        the same x-originating-appl-code to application 3. example: "BFB6"'
      required: false
      schema:
        type: string
    OriginatingApplicationCodeRequiredHeader:
      name: x-originating-appl-code
      in: header
      description: 'This is to identify the source Application EPM code which is making
        this request. Originating application is responsible for sending x-originating-appl-code.
        For example, if  application 1 makes call to application 2 and then application
        2 makes a call to application 3, application 1 will populate and send the
        x-originating-appl-code to application 2 and application 2 will propagate
        the same x-originating-appl-code to application 3. example: "BFB6"'
      required: true
      schema:
        type: string
        example: 'BFB6'
    customerId:
      name: x-customer-id
      in: header
      description: 'cid or bid'
      required: false
      schema:
        type: string
    # path parameters
    ruleId:
      description: >-
        Campaign's rule id
      name: ruleId
      in: path
      required: true
      schema:
        type: string
        minLength: 1
        maxLength: 20
        pattern: '^[a-zA-Z0-9]+$'
    campaignId:
      description: >-
        Campaign ID (KT/PEGA)
      name: campaignId
      in: path
      example: YGY03
      required: true
      schema:
        type: string
        pattern: '^[a-zA-Z0-9]+$'
    # query parameters
    applicationQuery:
      description: >-
        Optional targeted application id. If not specified `nova` value will be used. Rule search for application is case-insensitive.
      name: application
      in: query
      required: false
      schema:
        type: string
        minLength: 1
        maxLength: 20
        pattern: '^[a-zA-Z0-9_-]+$'
        default: 'nova'
    applicationRequiredQuery:
      description: >-
        Targeted application id.
      name: app
      in: query
      required: true
      schema:
        type: string
        minLength: 1
        maxLength: 20
        pattern: '^[a-zA-Z0-9_-]+$'
      example: nova
    pageRequiredQuery:
      description: >-
        Targeted page id.
      name: page
      in: query
      required: true
      schema:
        type: string
        minLength: 1
        maxLength: 20
        pattern: '^[a-zA-Z0-9_-]+$'
    containerRequiredQuery:
      description: >-
        Targeted container id.
      name: container
      in: query
      required: true
      schema:
        type: string
        minLength: 1
        maxLength: 20
        pattern: '^[a-zA-Z0-9_-]+$'
    platformOptionalQuery:
      description: >-
        Targeted container id.
      name: platform
      in: query
      required: false
      schema:
        type: string
        minLength: 1
        maxLength: 20
        pattern: '^[a-zA-Z0-9_-]+$'
    platformQuery:
      description: >-
        Targeted platform id of an application
      name: platform
      in: query
      required: true
      schema:
        type: string
        minLength: 1
        maxLength: 20
        pattern: '^[a-zA-Z]+$'
    containerQuery:
      description: >-
        Optional targeted container id
      name: container
      in: query
      required: false
      schema:
        type: string
        minLength: 1
        maxLength: 40
        pattern: '^[a-zA-Z0-9_-]+$'
    pageQuery:
      description: >-
        Optional targeted page id
      name: page
      in: query
      required: false
      schema:
        type: string
        minLength: 1
        maxLength: 40
        pattern: '^[a-zA-Z0-9_-]+$'
    pageMultipleQuery:
      description: >-
        Optional comma-separated list of page ids to retrieve campaigns for. Cannot be used together with `page_ex` query parameter. Rule search with page id is case-insensitive.
      name: page
      in: query
      required: false
      style: form
      explode: false
      schema:
        type: array
        minItems: 1
        items:
          type: string
          minLength: 1
          maxLength: 40
          pattern: '^[a-zA-Z0-9_-]+$'
        example: ['accounts','activities']
    pageExcludeQuery:
      description: >-
        Optional comma-separated list of page ids which campaigns need to be excluded from the response. Cannot be used together with `page` query parameter. This field is case-insensitive.
      name: page_ex
      in: query
      required: false
      style: form
      explode: false
      schema:
        type: array
        minItems: 1
        items:
          type: string
          minLength: 1
          maxLength: 40
          pattern: '^[a-zA-Z0-9_-]+$'
        example: ['rewards','chq']
    applicationVersionQuery:
      description: >-
        Optional targeted application version. When specified only rules that are targeting that particular application version or not targeting any application version will be returned. When not specified only rules that are not targeting any application version will be returned.
      name: app_version
      in: query
      required: false
      schema:
        type: string
        minLength: 1
        maxLength: 50
    osVersionQuery:
      description: >-
        Optional targeted OS version. When specified only rules that are targeting that particular OS version or not targeting any OS version will be returned. When not specified only rules that are not targeting any OS version will be returned.
      name: os_version
      in: query
      required: false
      schema:
        type: string
        minLength: 1
        maxLength: 100
    deviceModelQuery:
      description: >-
        Optional targeted device model. When specified only rules that are targeting that particular device model or not targeting any device model will be returned.  When not specified only rules that are not targeting any device model will be returned.
      name: device_model
      in: query
      required: false
      schema:
        type: string
        minLength: 1
        maxLength: 100
    messageIdQuery:
      description: >-
        Unique message id for a targeted campaign
      name: message_id
      in: query
      required: false
      schema:
        type: string
        minLength: 1
        maxLength: 50
        pattern: '^[a-zA-Z0-9-]+$'
    messageIdRequiredQuery:
      description: >-
        Unique message id for a targeted campaign
      name: message_id
      in: query
      required: true
      schema:
        type: string
        minLength: 1
        maxLength: 50
        pattern: '^[a-zA-Z0-9-]+$'
    insightFlagQuery:
      description: >-
        Flag to determine if Insight's campaign data should be added to a response. Used for `ABM` x-channel-id.
      name: insight
      in: query
      required: false
      schema:
        type: boolean
        enum: [true, false]
        default: false
    alertLimitQuery:
      description: >-
        Number of alerts to get. If not specified, one alert with the most recent updated date will be returned.
      name: limit
      in: query
      required: false
      schema:
        type: number
        format: int32
        minimum: 1
        maximum: 50
        default: 1
    campaignsLimitQuery:
      description: >-
        Number of campaigns to get
      name: limit
      in: query
      required: false
      schema:
        type: number
        format: int32
        minimum: 1
        maximum: 50
        default: 50
    vignetteFromDateQuery:
      description: >-
        Filter contents catalog by the last updated time that is grater than a specified value (ISO8601 UTC, for example '2019-01-01T00:00:00.000Z')
      name: from_date
      in: query
      required: true
      schema:
        type: string
        format: date-time
    vignetteToDateQuery:
      description: >-
        Filter contents catalog by the last updated time that is less than a specified value (ISO8601 UTC, for example '2019-12-31T23:59:59.999Z')
      name: to_date
      in: query
      required: true
      schema:
        type: string
        format: date-time
    selectContentsQuery:
      description: >-
        Optional value to be selected on the content fetch done via the call to content-api get content by type and ID.
      name: select_contents
      in: query
      required: false
      schema:
        type: string
        minLength: 1
        maxLength: 20
        pattern: '^[,a-zA-Z0-9_-]+$'
        default: 'default value is `preview` for get campaigns list, and `details` for getting a single campaign'
        example: interceptDetails
    vignetteRuleIdParam:
      description: >-
        Vignette's rule id
      name: ruleId
      in: path
      required: true
      schema:
        type: number
        format: int32
        minimum: 1
    vignetteWebfragmentIdParam:
      description: >-
        Vignette's webfragment id
      name: webfragmentId
      in: path
      required: true
      schema:
        type: number
        format: int32
        minimum: 1
    campaignsOffsetQuery:
      description: >-
        Optional offset for pagination
      name: offset
      in: query
      required: false
      schema:
        type: number
        format: int32
        minimum: 1
        maximum: 50
        default: 0
    modeQuery:
      description: >-
        Optional to return the number of unseen campaigns
      name: mode
      in: query
      required: false
      schema:
        type: string
        enum: ['unseen']
  requestBodies:
    v1.DispositionRequestBody:
      description: Disposition request
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/v1.DispositionRequest'
  schemas:
    # common
    v1.Notification:
      type: object
      properties:
        code:
          description: 'Notification code.'
          type: string
        message:
          description: 'Description of a notification'
          type: string
          example: Something is invalid
        uuid:
          description: 'Unique identifier for this notification.'
          type: string
          example: aadde-ddddee-eeeedd-eeeedd
        timestamp:
          description: 'Timestamp of the notification.'
          type: string
          format: date-time
          example: '2020-01-01T00:00:00.000Z'
        metadata:
          description: 'Optional context specific to this notification.'
          type: array
          items:
            type: object
    # alerts
    v1.AlertContent:
      description: >-
        Content for alert
      type: object
      properties:
        name:
          description: >-
            Alert name that is used by content managers to identify content in
            Contentful. Should not be displayed to customers.
          type: string
        title:
          description: Title
          type: string
        message:
          description: Message
          type: string
    v1.Alert:
      description: >-
        Alert
      type: object
      properties:
        id:
          description: Rule id of an alert
          type: string
        application:
          description: Target application
          type: string
        container:
          description: Target container name
          type: string
        type:
          description: Content type from Contentful
          type: string
        content:
          $ref: '#/components/schemas/v1.AlertContent'
    v1.Alerts:
      description: >-
        List of alerts
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/v1.Alert'
    # campaigns
    v1.CampaignInsightDataItem:
      description: Campaign data key/value pairs
      type: object
      properties:
        name:
          description: Key defined in the list
          type: string
          example: ACCT
        value:
          description: Value defined in the list
          type: string
    v1.CampaignInsight:
      description: Campaign message data from Marvel Data (Insights)
      type: object
      properties:
        message_id:
          description: Downstream system unique message ID
          type: string
        message_source:
          description: message source
          type: string
          example: KT
        message_status:
          description: Message status to give the current state of dispostion value, and it is a cross channel value
          type: string
          example: V
        subject_line:
          description: Message subject line returning from host system
          type: string
          example: you can increase your line of credit
        language:
          description: Language
          type: string
          example: en
        campaign_id:
          description: Campaign id defined in marketing operation used for linking the presentation designs
          type: string
          example: CLI01
        message_category:
          description: Message category id defined in marketing operation used for linking the presentation designs
          type: string
          example: CLI
        pointer_text:
          type: string
        start_date:
          description: message start date
          type: string
          example: 2017-01-09
        expiry_date:
          description: due date of the message
          type: string
          example: 2017-03-29
        conf_number:
          description: unique number returning as a reference number
          type: string
          example: "H12345689"
        additional_data:
          description: Additonal key/values variables are required in campaign contextul
            displays
          type: array
          items:
            $ref: "#/components/schemas/v1.CampaignInsightDataItem"
    v1.CampaignExternalRef:
      description: External campaign information
      type: object
      properties:
        campaign_id:
          description: >-
            Campaign id, may have the following values
             * `MASS` - mass campaign
             * `MESSAGE` - mass message
             * any other value - targeted campaign originated from KT or PEGA
          type: string
        message_id:
          description: >-
            Unique message id for targeted campaigns and a copy of a campaign's
            rule id fo mass campaigns or messages.
          type: string
        message_source:
          description: >-
            Source of a campaign data, may have the following values
             * `KT` - targeted campaign that originates in KT
             * `PEGA` - targeted campaign that originates in PEGA
             * `DMS` - mass campaign or message that originates in Pigeon
          type: string
        data:
          $ref: '#/components/schemas/v1.CampaignInsight'
    v1.Campaign:
      description: >-
        Campaign
      type: object
      properties:
        id:
          description: Rule id of a campaign
          type: string
        name:
          description: Campaign name
          type: string
        application:
          description: Target application
          type: string
        container:
          description: Target container
          type: string
        pages:
          description: Target pages
          type: array
          items:
            type: string
        type:
          description: Content type from Contentful
          type: string
        urgent:
          description: Urgent flag
          type: boolean
        viewed:
          description: Viewed disposition value
          type: boolean
        dismissable:
          description: Campaign dismissable flag
          type: boolean
        start_date:
          description: Campaign's start date (UTC)
          type: string
          format: date-time
        external_ref:
          $ref: '#/components/schemas/v1.CampaignExternalRef'
        content:
          description: >-
            Content for campaign. A dynamic object which structure is defined by
            the `type` property.
          type: object
          additionalProperties: true
    v1.Campaigns:
      description: >-
        List of campaigns
      type: object
      properties:
        total:
          description: Total available number of items
          type: number
          format: int32
        items:
          type: array
          items:
            $ref: '#/components/schemas/v1.Campaign'
    v1.CampaignsInbox:
      description: >-
        List of campaigns for orion inbox
      type: object
      properties:
        total_new:
          description: Total available number of unread campaigns
          type: number
          format: int32
        total:
          description: Total available number of items
          type: number
          format: int32
        limit:
          description: Limit per page
          type: number
          format: int32
        offset:
          description: Offset for pagination
          type: number
          format: int32
        items:
          type: array
          items:
            $ref: '#/components/schemas/v1.Campaign'
    v1.UnseenCampaignsNumberInbox:
      description: >-
        Number of unseen campaigns for orion inbox
      type: object
      properties:
        unseen:
          description: Total number of unseen campaigns for orion inbox
          type: number
          format: int32
    v1.SamlToken:
      description: >-
        SAML token response
      type: object
      properties:
        saml_token:
          description: SAML token
          type: string
    v1.DispositionRequest:
      description: Set disposition request
      required:
        - disposition
        - application
        - platform
      properties:
        disposition:
          description: >-
            Disposition value:
             * `V` - viewed (KT/PEGA/Mas)
             * `D` - dismissed (KT/PEGA/Mass)
             * `P` - in progress (KT/PEGA)
             * `A` - accepted (KT/PEGA)
             * `Y` - yes (KT - ABM) 
             * `C` - no (KT - ABM)
             * `S` - snooze (KT, AKYCE soft stop campaigns only)
          type: string
          enum: ['V','D','P','A','C','Y','S']
          example: 'V'
        application:
          description: Application id
          type: string
          minLength: 1
          maxLength: 20
          pattern: '^[a-zA-Z0-9_-]+$'
          example: 'nova'
        platform:
          description: Platform id of an application
          type: string
          minLength: 1
          maxLength: 20
          pattern: '^[a-zA-Z]'
          example: 'ios'
        page:
          oneOf:
            - description: Optional page id. Required when `V` or `D` dispositions are used.
              type: array
              example: ['accounts', 'activities']
              items:
                pattern: '^[a-zA-Z0-9_-]+$'
            - description: Optional page id. Required when `V` or `D` dispositions are used.
              type: string
              minLength: 1
              maxLength: 40
              pattern: '^[a-zA-Z0-9_-]+$'
              example: 'accounts'
        container:
          description: Optional container id. Required when `V` or `D` dispositions are used.
          type: string
          minLength: 1
          maxLength: 40
          pattern: '^[a-zA-Z0-9_-]+$'
          example: 'offers-and-programs'
        message_id:
          description: Optional unique message id of a targeted/standing campaign. Can be used only with KT/PEGA campaigns.
          type: string
          minLength: 1
          maxLength: 50
          pattern: '^[a-zA-Z0-9-]+$'
        message_category:
          description: Optional campaign code defined in marketing operation used for determining campaign type. Can be used only with KT campaigns. If used, `message_id` property will be ignored.
          type: string
          minLength: 1
          maxLength: 50
          pattern: '^[a-zA-Z0-9]+$'
          example: 'KYC'
        rule_id:
          description: Optional campaign's rule id. Required when `V` or `D` dispositions are used with `POST /v1/dispositions` endpoint.
          type: string
          minLength: 1
          maxLength: 20
          pattern: '^[a-zA-Z0-9]+$'
          example: 'xLFyhpuFdcfg'
    v1.ContentsCatalogWebfragmentUpdate:
      description: >-
        Contents catalog's web fragment update details
      properties:
        webfragment_id:
          description: Eeb fragment id
          type: string
          format: int32
        last_updated_time:
          description: Last updated time of a web fragment (ISO8601 UTC)
          type: string
          format: date-time
    v1.ContentsCatalogRuleUpdate:
      description: >-
        Contents catalog's rule update details
      properties:
        rule_id:
          description: Rule id
          type: string
          format: int32
        last_updated_time:
          description: Last updated time of a web fragment (ISO8601 UTC)
          type: string
          format: date-time
        publishing_state:
          description: >-
            A publishing state of a rule:
             * `C` - created/updated
             * `E` - expired
          type: string
          enum: ['C','E']
    v1.ContentsCatalog:
      description: >-
        Contents catalog updates
      properties:
        webfragments:
          type: array
          minimum: 0
          maximum: 5000
          items:
            $ref: '#/components/schemas/v1.ContentsCatalogWebfragmentUpdate'
        rules:
          type: array
          minimum: 0
          maximum: 5000
          items:
            $ref: '#/components/schemas/v1.ContentsCatalogRuleUpdate'
    v1.ContentsCatalogRule:
      description: >-
        Contents catalog rule details
      type: object
      properties:
        rule_id:
          description: Rule id
          type: string
          format: int32
          example: '31337'
        rule_name:
          description: Rule name
          type: string
          example: 'eHome-LoginL-EN-Jun19'
        content_type:
          description: >-
            A message type:
             * `P` - Priority message
             * `N` - Normal message
             * `U` - Urgent message
             * `M` - Marketing message
          type: string
          enum: ['P','N','U','M']
        start_date:
          description: Start date of the rule (ISO8601 UTC)
          type: string
          format: date-time
        expiry_date:
          description: Expiration date of the rule (ISO8601 UTC)
          type: string
          format: date-time
        language:
          description: >-
            Target language:
             * `en` - English
             * `fr` - French
          type: string
          enum: ['en','fr']
        country:
          description: >-
            Target country:
             * `CA` - Canada
          type: string
          enum: ['CA']
        webfragment_id:
          description: ID of a web fragment attached to the rule
          type: string
          format: int32
          example: '21223'
        container_id:
          description: ID of a container targeted by the rule
          type: string
          example: 'LoginMktgLeft'
        page_ids:
          description: IDs of pages targeted by the rule
          type: array
          items:
            type: string
            minimum: 1
            example: ['Login', 'Logout']
        subject:
          description: An optional subject line (null if not specified)
          type: string
          x-nullable: true
        campaign_id:
          description: An optional campaign id for KT targeted rules (null if not specified)
          type: string
          x-nullable: true
        content_rules:
          description: An optional parameters for Mass targeted rules (an empty array if not specified)
          type: array
          items:
            minimum: 0
            type: object
            properties:
              target_mode:
                description: >-
                  Targeting mode:
                   * `STRICT` - all of (AND)
                   * `LENIENT` - any of (OR)
                   * `EXCLUSIVE` - excluding all of (NOT)
                type: string
                enum: ['STRICT','LENIENT','EXCLUSIVE']
                example: 'LENIENT'
              rule_type:
                description: >-
                  Targeting type:
                   * `BU` - business line
                   * `DEBI` - debit card
                   * `DEVC` - device type
                   * `PR` - product
                   * `PROV` - province
                   * `REGS` - registration status
                type: string
                enum: ['BU','DEBI','DEVC','PR','PROV','REGS']
                example: 'DEVC'
              rule_attributes:
                description: Targeting attributes
                type: array
                minimum: 1
                example: ['Android','iPad','iPhone']
                items:
                  type: string
  responses:
    # errors
    v1.BadRequestResponse:
      description: Bad request error response
      content:
        application/json:
          schema:
            properties:
              data:
                type: object
                example: {}
              notifications:
                type: array
                items:
                  $ref: '#/components/schemas/v1.Notification'
    v1.UnauthorizedResponse:
      description: Unauthorized error response
      content:
        application/json:
          schema:
            properties:
              data:
                type: object
                example: {}
              notifications:
                type: array
                items:
                  $ref: '#/components/schemas/v1.Notification'
    v1.ForbiddenResponse:
      description: Forbidden error response
      content:
        application/json:
          schema:
            properties:
              data:
                type: object
                example: {}
              notifications:
                type: array
                items:
                  $ref: '#/components/schemas/v1.Notification'
    v1.NotFoundResponse:
      description: Not found error response
      content:
        application/json:
          schema:
            properties:
              data:
                type: object
                example: {}
              notifications:
                type: array
                items:
                  $ref: '#/components/schemas/v1.Notification'
    v1.InternalServerErrorResponse:
      description: Internal server error response
      content:
        application/json:
          schema:
            properties:
              data:
                type: object
                example: {}
              notifications:
                type: array
                items:
                  $ref: '#/components/schemas/v1.Notification'
    # empty response
    v1.EmptyDataResponse:
      description: Empty data response
      content:
        application/json:
          schema:
            properties:
              data:
                type: object
                example: {}
              notifications:
                type: array
                items:
                  $ref: '#/components/schemas/v1.Notification'
                example: []
    # alerts
    v1.AlertsResponse:
      description: List of alerts response
      content:
        application/json:
          schema:
            properties:
              data:
                $ref: '#/components/schemas/v1.Alerts'
              notifications:
                type: array
                items:
                  $ref: '#/components/schemas/v1.Notification'
                example: []
    # campaigns
    v1.CampaignsInboxResponse:
      description: List of campaigns or number of unseen campaigns depending on query mode=unseen for Orion Inbox 
      content:
        application/json:
          schema:
            properties:
              data:
                oneOf:
                  - $ref: '#/components/schemas/v1.CampaignsInbox'
                  - $ref: '#/components/schemas/v1.UnseenCampaignsNumberInbox'
              notifications:
                type: array
                items:
                  $ref: '#/components/schemas/v1.Notification'
                example: []
    v1.CampaignsResponse:
      description: List of campaigns response
      content:
        application/json:
          schema:
            properties:
              data:
                $ref: '#/components/schemas/v1.Campaigns'
              notifications:
                type: array
                items:
                  $ref: '#/components/schemas/v1.Notification'
                example: []
    v1.CampaignResponse:
      description: Campaign details response
      content:
        application/json:
          schema:
            properties:
              data:
                $ref: '#/components/schemas/v1.Campaign'
              notifications:
                type: array
                items:
                  $ref: '#/components/schemas/v1.Notification'
                example: []
    v1.SamlTokenResponse:
      description: SAML token response
      content:
        application/json:
          schema:
            properties:
              data:
                $ref: '#/components/schemas/v1.SamlToken'
              notifications:
                type: array
                items:
                  $ref: '#/components/schemas/v1.Notification'
                example: []
    # contents catalog
    v1.ContentsCatalogBadRequestResponse:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/v1.Notification'
    v1.ContentsCatalogUnauthorizedResponse:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/v1.Notification'
    v1.ContentsCatalogForbiddenResponse:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/v1.Notification'
    v1.ContentsCatalogNotFoundResponse:
      description: Not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/v1.Notification'
    v1.ContentsCatalogInternalServerErrorResponse:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/v1.Notification'
    v1.ContentsCatalogResponse:
      description: Contents catalog response
      content:
        application/json:
          schema:
            properties:
              data:
                $ref: '#/components/schemas/v1.ContentsCatalog'
              notifications:
                type: array
                items:
                  $ref: '#/components/schemas/v1.Notification'
                example: []
    v1.ContentsCatalogRuleResponse:
      description: Contents catalog's rule response
      content:
        application/json:
          schema:
            properties:
              data:
                $ref: '#/components/schemas/v1.ContentsCatalogRule'
              notifications:
                type: array
                items:
                  $ref: '#/components/schemas/v1.Notification'
                example: []
    v1.ContentsCatalogWebfragmentResponse:
      description: Contents catalog's webfragment response
      content:
        text/html:
          schema:
            type: string