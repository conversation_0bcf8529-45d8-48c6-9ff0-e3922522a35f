---
applications:
  - name: pigeon-ist
    buildpacks:
      - nodejs_buildpack-v1_8_27
    stack: cflinuxfs4
    command: npm start
    memory: 256M
    instances: 1
    services:
      - pigeon-cache
      - pigeon-pigeon-failover-ist
    routes:
      - route: pigeon-ist.apps.stg.azr-cc-pcf.cloud.bns
      - route: pigeon-ist.apps.cloud.bns
    health-check-type: http
    health-check-http-endpoint: /health
    env:
      EPM: BFB6
      NODE_ENV: production
      NODE_TLS_REJECT_UNAUTHORIZED: 0
      SERVER_HOST: 0.0.0.0
      SERVER_PORT: 8080
      SERVER_GRACEFUL_TIMEOUT: 10000
      CORS_ORIGIN: https://pigeon-web-ist.apps.cloud.bns,https://pigeon-web-ist.apps.stg.azr-cc-pcf.cloud.bns,https://pigeon-web-ist.apps.stg.azr-use2-pcf.cloud.bns
      HOSTING_ENV: PCF
      PIGEON_BFF_ATLAS_URI: https://pigeon-ist.nonp.atlas.bns
      # Alerts
      ALERT_API_URI: https://rule-api-ist.apps.stg.azr-cc-pcf.cloud.bns/v1
      ALERT_API_TIMEOUT: 3000
      ALERT_CACHE_TTL: 5000
      # Campaigns
      CAMPAIGN_API_URI: https://rule-api-ist.apps.stg.azr-cc-pcf.cloud.bns
      CAMPAIGN_API_TIMEOUT: 3000
      # Content
      CONTENT_API_URI: https://content-api-ist.apps.stg.azr-cc-pcf.cloud.bns/v1
      CONTENT_API_TIMEOUT: 11000
      # Insights
      INSIGHTS_API_URI: https://cdb-int-insight-ist.apps.stg.azr-cc-pcf.cloud.bns
      INSIGHTS_ATLAS_API_URI: https://cdb-int-insight-ist.nonp.atlas.bns
      INSIGHTS_API_TIMEOUT: 3000
      INSIGHTS_API_URI_MOCK: https://pigeon-mock-ist.apps.stg.azr-cc-pcf.cloud.bns/api
      #CCAU DC-CAMPAIGNS API
      DCCAMPAIGNS_API_URI: https://dc-campaigns-ist.apps.stg.azr-cc-pcf.cloud.bns/api
      DCCAMPAIGNS_API_URI_MOCK: https://dc-campaigns-nft.apps.stg.azr-cc-pcf.cloud.bns
      # Marvel - Investments
      INVESTMENT_API_URI: https://cdb-int-investments-ist.apps.stg.azr-cc-pcf.cloud.bns
      INVESTMENT_API_TIMEOUT: 3000
      # Marvel - Accounts & Rewards
      MARVEL_ACCOUNTS_API: https://cdb-int-account-ist.apps.stg.azr-cc-pcf.cloud.bns/v1/campaigns
      MARVEL_ACCOUNTS_ATLAS_API: https://cdb-int-account-ue4-ist.nonp.atlas.bns/v1/campaigns
      MARVEL_ACCOUNT_API_REDIS_TTL: 86400
      MARVEL_REWARDS_API: https://cdb-int-rewards-ist.apps.stg.azr-cc-pcf.cloud.bns/v2/rewards
      MARVEL_REWARDS_ATLAS_API: https://cdb-int-rewards-ue4-ist.nonp.atlas.bns/v2/rewards
      MARVEL_CARD_PROFILE: https://cdb-int-credential-cardprofile-ist.apps.stg.azr-cc-pcf.cloud.bns/v1/profile
      REDIS_USE_KEY_HASH: 1
      CIRCUIT_BREAKER_TIMEOUT: 2000
      CIRCUIT_BREAKER_ERROR_THRESHOLD: 50
      CIRCUIT_BREAKER_RESET_TIMEOUT: 3000
      MARVEL_API_TIMEOUT: 5000
      # Marvel - Credentials (SAML token)
      CREDENTIALS_API_URI: https://cdb-int-credentials-ist.apps.stg.azr-cc-pcf.cloud.bns
      CREDENTIALS_ATLAS_API_URI: https://cdb-int-credentials-ist.apps.stg.azr-cc-pcf.cloud.bns
      CREDENTIALS_API_TIMEOUT: 3000
      SAML_EXIT_URL: https://does.not.exists
      # Marvel - Product
      MARVEL_PRODUCT_API_URL: https://cdb-int-product-ist.apps.stg.azr-cc-pcf.cloud.bns/v2/product
      # Authentication
      AUTH_OPAQUE_EXCHANGE_URI: https://passport-oauth-ist.apps.cloud.bns/oauth2/v1/tokeninfo
      AUTH_ACCESS_TOKEN_URI: https://passport-oauth-ist.apps.cloud.bns/oauth2/v1/token
      AUTH_S2S_JWKS_URI: https://passport-oauth-ist.apps.cloud.bns/oauth2/v1/certs
      AUTH_S2S_PRIVATEKEY_ALGO: RS256
      AUTH_S2S_CLAIM_SCOPE: ca:baas:campaign-rules:read ca:baas:alert-rules:read ca:baas:contents:read cdb.mrvl.accounts.read cdb.mrvl.insight.read cdb.mrvl.insight.update cdb.mrvl.credentials.saml.create reward.rewards.bdms.rewards.read cdb.mrvl.cardprofile.read campaign.insight.bdms.campaigns.write campaign.insight.bdms.campaigns.read investment.investmentplans.bdms.gic.rates.write
      AUTH_S2S_CLIENTID: 107eb6a6-da19-41bd-9e7f-01598f83689d
      AUTH_S2S_CLAIM_EXPIRESIN: 1h
      AUTH_S2S_CLAIM_NOTBEFORE: -10s
      AUTH_S2S_ALLOW_CUSTOMER: 1
      AUTH_S2S_ALLOW_OPAQUE: 1
      AUTH_S2S_TOKEN_PATH: auth.token
      AUTH_S2S_CLAIMS_PATH: auth.claims
      # Client IDS for which all requests will be considered unauthenticated (Phoenix)
      ANONYMOUS_CLIENT_IDS: 09e3052b-3534-449c-9416-b06a7191e967
      # Proxy
      PROXY_HOST: pp-webproxy.bns
      PROXY_PORT: 8080
      # Launch Darkly
      LAUNCH_DARKLY_USER_ID: '963ead6c-35d7-40a0-b804-23dd67dfed1b'
      # dev features
      FEATURES_APPLICATION: 1
      FEATURES_DISPOSITION: 1
      # mssql / Dispositions db
      MSSQL_POOL_MIN: 1
      MSSQL_POOL_MAX: 5
      MSSQL_TIMEOUT: 30000
      MSSQL_SCHEMA: disposition
      MSSQL_DBNAME: pigeon-pigeon-failover-ist
      # logging options
      LOG_NAME: pigeon-ist
      LOG_OBFUSCATE: 1
      LOG_COLORIZE: 0
      LOG_PRETTY_PRINT: 0
      LOG_SIEM: 1
      IGNORED_ROUTES_FOR_LOGGING: '["/health"]'
      # rate limiting fallbacks
      RATE_LIMIT_CLIENT_ALERTS_MAX: 30
      RATE_LIMIT_CLIENT_CAMPAIGNS_MAX: 30
      RATE_LIMIT_OVERALL_ALERTS_MAX: 1100
      RATE_LIMIT_OVERALL_CAMPAIGNS_MAX: 700
      RATE_LIMIT_OVERALL_CONTENTS_CATALOG_MAX: 30
      RATE_LIMIT_CDP_TRUSTED_IP: '***********/23'
      # Disposition Purge
      DISPOSITION_PURGE_DELETE_CHECK: 2000
      DISPOSITION_PURGE_DELETE_COUNT: 4000
      DISPOSITION_PURGE_TTL: 60 # seconds
      DISPOSITION_PURGE_INTERVAL: 30 # minutes
      DISPOSITION_PURGE_CHECK_INTERVALE: 15 # minutes
