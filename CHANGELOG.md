# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [1.32.1](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.32.0...1.32.1) (2025-06-24)


### Bug Fixes

* **PIGEON-5649 & PIGEON-5700:** new blackduck issues for packages: brace-expansion & @azure/identity ([1fccf47](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/1fccf4796b792d2150008b5f242837a73582556d))
* **PIGEON-5688:** add tracing headers ([d86e4b2](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/d86e4b24d75f6ad4488e65bf146fcf77bb201671))

## [1.32.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.31.1...1.32.0) (2025-05-28)


### Features

* **PIGEON-5664:** add new campaigns to CC balance transfer variable mapping logic ([df8c9bc](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/df8c9bc04558d59a13f562fd9d74e3c19a6890d4))

### [1.31.1](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.31.0...1.31.1) (2025-05-22)

## [1.31.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.30.0...1.31.0) (2025-05-22)


### Features

* **PIGEON-5536:** add insights url and ld flag ([0e0ee37](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/0e0ee3713d7e43e67d33f3c364e7db6a34799937))
* **PIGEON-5536:** add tests ([6759a24](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/6759a24c355a7606a9966705dcf6546583380bab))
* **PIGEON-5541:** add support for ECM campaign ([3ba3403](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/3ba340370d43df318ea031ef8240cd72832205f6))
* **PIGEON-5541:** process date variables as is for ecm ([ad1123a](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/ad1123a582b9af93590d2e238df70301afd299e5))
* **PIGEON-5634:** add support for high propensity campaign - YKW ([e5d59d1](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/e5d59d18ab321b65846893180f86f17be0c31ec2))


### Bug Fixes

* read dispositions ld flag on orion inbox endpoint ([5241731](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/5241731ded64836abac3d01a2e136d3f0ac0c04b))

## [1.30.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.29.0...1.30.0) (2025-03-13)


### Features

* **PIGEON-5429:** update product json ([81d86b6](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/81d86b6700c44ef0d4c70ea44107ffbf50799f43))

## [1.29.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.28.0...1.29.0) (2025-02-28)


### Features

* **PIGEON-5460:** client advice centre campaign ([b84320b](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/b84320b21133b0160ee3f8ad655a02ca3b7213e7))

## [1.28.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.27.1...1.28.0) (2025-02-07)


### Features

* **PIGEON-5391:** add variable mapping support for SMB term loan renewal campaigns (CTA) ([f507244](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/f5072444e8e783cd5e03e4e399033aa2ea9a4386))


### Bug Fixes

* **PIGEON-5383:** update account number mask PEGA ([03d8404](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/03d84049a9a4cce417019cf9108c36f98f9079ed))

### [1.27.1](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.27.0...1.27.1) (2025-01-17)


### Features

* **PIGEON-5412:** add 'x-feature-flag-uid' support for campaigns inbox ([0319eb4](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/0319eb4872ac10d29959e22db360d68016abb8fb))
* **PIGEON-5412:** pass 'x-feature-flag-uid' header to Insights downstream calls ([1ba6656](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/1ba6656bdafc92369b2afa9a85d15d4f9a41abc5))

## [1.27.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.26.1...1.27.0) (2024-11-27)


### Features

* **PIGEON-4941:** upgrade node to v20.15.1 ([6ac898b](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/6ac898b6b589763e8e116bc9c3333cc551178ff7))

### [1.26.1](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.26.0...1.26.1) (2024-11-13)

## [1.26.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/v1.6.0...v1.26.0) (2024-11-13)


### Features

* **PIGEON-5368:** Variable Mapping SSI Remediation Message ([658cdc3](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/658cdc325e44a2ee3a0212b751006c91eeb2a8cd))
* **PIGEON-5360:** update variable mappings for ULOC RSP campaigns ([026717b](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/026717be98529b8cf316c0fb3af42d6d3aa57992))
* **PIGEON-5289:** switch pigeon API to use marvel product service v2 endpoint ([3e45894](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/3e45894808295962477adfb60776b353cd22297e))

### [1.25.1](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.25.0...1.25.1) (2024-10-21)


### Bug Fixes

* **PIGEON-5349:** handle response type html ([45ad47d](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/45ad47dd01ced608048a497aae68f8e31af4ba01))
* **PIGEON-5352:** update variable matching regex to accept digits at end of variable name ([40e2d9d](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/40e2d9d3efc1e39708953d03256ac69085a69455))

## [1.25.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.24.0...1.25.0) (2024-10-21)


### Features

* **PIGEON-5349:** add pigeon api atlas client ([95759a1](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/95759a1ac6af2b4eb5e46064c11c7a83113a8178))

## [1.24.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.23.3...1.24.0) (2024-10-04)


### Features

* **PIGEON-5310:** allow maintenance mode to be controlled seperately in pcf & gcp atlas ([6ec209f](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/6ec209fea316b89614e3af45791923d1b10b63d7))


### Bug Fixes

* remove support for emob d2d nova campaigns (XXO01, XXO02) ([87cd9e1](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/87cd9e1edd9d7f0b250c4d4ef14cd25c0f4f244e))

### [1.23.3](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.23.1...1.23.3) (2024-09-27)


### Bug Fixes

* remove support for emob d2d nova campaigns (XXO01, XXO02) ([f7f0c52](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/f7f0c52d8b7f9c416d96e6f1481fb03ab5a010f1))

### [1.23.2](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.23.1...1.23.2) (2024-09-27)


### Bug Fixes

* remove support for emob d2d nova campaigns (XXO01, XXO02) ([f7f0c52](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/f7f0c52d8b7f9c416d96e6f1481fb03ab5a010f1))

### [1.23.1](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.22.5...1.23.1) (2024-09-06)


### Features

* **PIGEON-4974:** percentage rollout for insights endpoint ([052771e](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/052771e0cd8ef2b6447bb3832392ef8e9f5819e2))
* **PIGEON-5090:** add support for mortgage renewal campaign ids ([3b69d67](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/3b69d6701210c06ee7aaaa453a8398115b2e3774))
* **PIGEON-5158:** add rm7 campaign and variable mappings ([912aefc](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/912aefc750e31cfe75fcc0513c22f44b296cf4c2))
* **PIGEON-5158:** add rm7 variable mapping to index file ([48fac98](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/48fac98e0057adae9631c2e0f041be3047d5e5f6))
* **PIGEON-5158:** fix date processing and testing ([031648c](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/031648c812db969ed63e541c8693b6816bb9d2fe))
* **PIGEON-5159:** add more robust test inputs ([178a484](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/178a4841d9fc8dff5e1058402efc034dfdb5b353))
* **PIGEON-5159:** add unsecured line of credits variables to index file ([a2ed9c0](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/a2ed9c0bba367d0dd9e91c4ba3a70f5ebb5c7c7c))
* **PIGEON-5159:** add variable mapping for unsecured line of credit ([53d1b3e](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/53d1b3efcbc2bc574a3ed656b0fcc676a5ae7da9))
* **PIGEON-5159:** address comment to make data context more generic ([5efb6f2](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/5efb6f24d0f8821ea612e646e978c3b814a0565f))


### Bug Fixes

* **PIGEON-5302:** add missing dynamic variables for tlp uloc campaigns ([b8d88d3](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/b8d88d38b47825fdabc3a2cfef39deea02c5c566))
* removed trailing slash from insights get campaigns call ([a22c84e](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/a22c84e4d51b903626a6b67852043739ff5b7b67))
* update container check for non dismissible campaigns - orion inbox ([381fdc4](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/381fdc49b8930736ada58cb69c0d8e7820438561))
* **PIGEON-5078:** map dismiss to viewed while checking is insight dispostion update is required ([37dd020](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/37dd0205750e5789aed16c7088c8cc1174513501))

## [1.23.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.22.5...1.23.0) (2024-09-06)


### Features

* **PIGEON-4974:** percentage rollout for insights endpoint ([052771e](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/052771e0cd8ef2b6447bb3832392ef8e9f5819e2))
* **PIGEON-5090:** add support for mortgage renewal campaign ids ([3b69d67](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/3b69d6701210c06ee7aaaa453a8398115b2e3774))
* **PIGEON-5158:** add rm7 campaign and variable mappings ([912aefc](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/912aefc750e31cfe75fcc0513c22f44b296cf4c2))
* **PIGEON-5158:** add rm7 variable mapping to index file ([48fac98](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/48fac98e0057adae9631c2e0f041be3047d5e5f6))
* **PIGEON-5158:** fix date processing and testing ([031648c](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/031648c812db969ed63e541c8693b6816bb9d2fe))
* **PIGEON-5159:** add more robust test inputs ([178a484](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/178a4841d9fc8dff5e1058402efc034dfdb5b353))
* **PIGEON-5159:** add unsecured line of credits variables to index file ([a2ed9c0](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/a2ed9c0bba367d0dd9e91c4ba3a70f5ebb5c7c7c))
* **PIGEON-5159:** add variable mapping for unsecured line of credit ([53d1b3e](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/53d1b3efcbc2bc574a3ed656b0fcc676a5ae7da9))
* **PIGEON-5159:** address comment to make data context more generic ([5efb6f2](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/5efb6f24d0f8821ea612e646e978c3b814a0565f))


### Bug Fixes

* **PIGEON-5302:** add missing dynamic variables for tlp uloc campaigns ([b8d88d3](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/b8d88d38b47825fdabc3a2cfef39deea02c5c566))
* removed trailing slash from insights get campaigns call ([a22c84e](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/a22c84e4d51b903626a6b67852043739ff5b7b67))
* update container check for non dismissible campaigns - orion inbox ([381fdc4](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/381fdc49b8930736ada58cb69c0d8e7820438561))
* **PIGEON-5078:** map dismiss to viewed while checking is insight dispostion update is required ([37dd020](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/37dd0205750e5789aed16c7088c8cc1174513501))

### [1.22.5](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.22.4...1.22.5) (2024-08-06)


### Features

* **PIGEON-5090:** add support for mortgage renewal campaign ids ([a90473a](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/a90473ac7de55d1c8dcf25c6ecd6488aee85ce17))

### [1.22.4](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.22.2...1.22.4) (2024-08-02)


### Bug Fixes

* removed trailing slash from insights get campaigns call ([d9615ed](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/d9615edf055909b68ba2f8dad014ea3fd20bf57f))

### [1.22.3](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.22.2...1.22.3) (2024-07-29)


### Bug Fixes

* removed trailing slash from insights get campaigns call ([d9615ed](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/d9615edf055909b68ba2f8dad014ea3fd20bf57f))

### [1.22.2](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.22.1...1.22.2) (2024-07-25)

### [1.22.1](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.22.0...1.22.1) (2024-07-19)

## [1.22.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.21.0...1.22.0) (2024-07-19)


### Features

* **PIGEON-4975:** add LD flags for rewards, Accounts and Credentials Marvel ([53bbd7d](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/53bbd7d5fd8f0c8cd0dffb9c463eaa44ab3082d8))
* **PIGEON-5026:** add maintenance mode flag middleware ([3dc147e](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/3dc147ed6602d7a03d1aaabcbe3466e7148af464))
* **PIGEON-5027:** added atlas proxy for sol requests ([2d57707](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/2d577072e9a1254d1b321bcedf94667bb07e3053))
* **PIGEON-5045:** update account number masking logic for ULOC CLI campaigns ([f15bac6](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/f15bac63dfbb3544217b628e8fa79f985b67dde7))
* **PIGEON-5055:** create new variable embedded ([88cbffc](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/88cbffc35dc1228a0fd436ed3f29860cee8c44c2))


### Bug Fixes

* lmt process date variable fix ([41dc607](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/41dc607af4f04eea1d80a72a5a834d5f37e5e29c))
* **PIGEON-4975:** disable checkmax ([95b6647](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/95b6647bb25c7d23e56d0f0353d114fde047c632))

## [1.21.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.20.1...1.21.0) (2024-05-30)


### Bug Fixes

* **PIGEON-4979:** update inbox endpoint to set viewed to true only for campaigns viewed from inbox ([1da2d74](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/1da2d74afe1e8dfab8bca0ee3fe17346ee9b6335))

### [1.20.1](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.20.0...1.20.1) (2024-04-26)

## [1.20.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.19.0...1.20.0) (2024-04-25)


### Features

* **PIGEON-4902:** add service, version to health check ([c8fa9db](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/c8fa9db68b51900ad08b9b25410045f881ccc452))
* **PIGEON-4923:** added language targeting filter in get-campaings ([522f2e4](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/522f2e45a61c4a482d63c10712b98ec3030dd579))
* **PIGEON-4924:** return 404 when requested language is not in targeted languages ([a7af9ae](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/a7af9ae2b3058af7bda790b3e86dad261d175cce))
* **PIGEON-4970:** added support to lmt campaign ([a3f1e2d](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/a3f1e2dbbbdb2a0eddcaf4044dc0ff8ffc61d3eb))
* **PIGEON-4972:** return inbox container campaign for orion inbox endpoint ([3eff7cd](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/3eff7cddf462c3e17a634a9eb12c30aceaea8957))


### Bug Fixes

* **PIGEON-4721:** add gic renewal testing data ([50fab6f](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/50fab6f8bb90a119947e198bf33e2f8b37a77bcc))
* **PIGEON-4721:** add missing headers to get-gic-rates, fix fetch client for investmentService ([a6f95ac](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/a6f95ac573ff72c9bd38fb6a19e5dafb7a6852ac))
* **PIGEON-4721:** migrate to v3 for marvel investments ([ab50b8d](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/ab50b8de94f95fe48b8120f654ea859b63578836))

## [1.19.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.18.0...1.19.0) (2024-03-25)


### Features

* **PIGEON-3531:** add new endpoit tp clear disposition ([f4bb5ed](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/f4bb5ed6bad162a256179f930c6f3bc444d0f9d6))
* **PIGEON-4855:** fix idCountToPurge property ([5d795ae](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/5d795ae1fec68407b13b819ac36cf520ee2b0802))
* **PIGEON-4855:** increase purge volume ([ac93a24](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/ac93a2492ed797ba2c9dd3ed71047dee02e938d5))
* **PIGEON-4875:** added Joi header validation for Orion inbox endpoint ([daf0cd5](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/daf0cd57cd150a330a37438fcc6bf51ee514be2c))


### Bug Fixes

* **PIGEON-3384:** fix validation in setting dispostion endpoint ([bb210aa](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/bb210aad9cd55c1fb0cf123491d97d7d53e10cca))
* **PIGEON-4855:** fix merge ([1368e31](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/1368e319b81a41288b0b81f6f67ad314e3f909f1))

## [1.18.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.17.3...1.18.0) (2024-03-05)


### Bug Fixes

* fix missing passing country header to insights ([41a991f](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/41a991fe3b6768584b4937cd62d91963eba93df8))

### [1.17.3](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.17.2...1.17.3) (2024-02-26)


### Bug Fixes

* **PIGEON-4917:** fix missing sending xOriginatingApplCode to set disposition endpoint ([3d1f4b2](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/3d1f4b283cc0b017e2a636931e886454f09ab283))

### [1.17.2](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.17.1...1.17.2) (2024-02-26)


### Bug Fixes

* **PIGEON-4916:** fix missing sending country to set disposition endpoint ([43a2cd1](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/43a2cd15dc4e8924758cb40f80d83869897c84d0))
* **PIGEON-4917:** fix missing sending xOriginatingApplCode to set disposition endpoint ([496b2f1](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/496b2f11c4507a5a5f062d30082cc8f64fa6f7a1))

### [1.17.1](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.17.0...1.17.1) (2024-02-13)

## [1.17.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.16.4...1.17.0) (2024-02-09)


### Features

* **PIGEON-3492:** transform preferred-environment header in non-prod ([c3e3d37](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/c3e3d3798d156c7338ce12dfbf01f5b6903aa1bc))
* **PIGEON-4674:** hide api-docs from prod ([ac33f58](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/ac33f58101609dc3a2ea4094f6e57c2f265ada67))
* **PIGEON-4823:** auto refresh product book ([a839102](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/a83910239fb7f4c713b0cc02701b9ad3efc96615))
* **PIGEON-4857:** support hard delete disposition ([8c71828](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/8c718282fe7cb788f9a06b0dc0228adb945aaf78))
* **PIGEON-4869:** do not set viewed disposition for orion requests to get-campaign ([026aa22](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/026aa220d9a099bbbcfb59c649de9383c26d8971))


### Bug Fixes

* **PIGEON-4841:** return the total number of unread campaigns for orion inbox endpoint ([c3f4091](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/c3f409183a884b00ecaffa95080989bfe64dde89))
* **PIGEON-4876:** get headers from request for orion inbox endpoint ([c79327f](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/c79327f2c5e58f405d19e9d0b8781a17e1bed709))

### [1.16.4](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.16.3...1.16.4) (2024-01-19)


### Bug Fixes

* add missing pcf buildpack version bump for node 18 ([d1dae40](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/d1dae4015038a5e8baf11c251c9fd77f4cf79805))

### [1.16.3](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.16.2...1.16.3) (2024-01-10)

### [1.16.2](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.16.1...1.16.2) (2024-01-10)


### Bug Fixes

* **PIGEON-4845:** fix disposition check for seen rule ([e6a3ff7](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/e6a3ff771246d0d308f7a406da15a5a91998fcd1))

### [1.16.1](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.16.0...1.16.1) (2024-01-09)

## [1.16.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.15.4...1.16.0) (2024-01-09)


### Features

* node version upgraded to 18.17.1 ([5e2caf2](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/5e2caf2eb09df07c82c982130262cb49d4c7fbf1))
* **PIGEON-4258:** return notifications array from the downstream services ([143ef33](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/143ef3394907d1893c361a9a43e742fb09274069))
* **PIGEON-4594:** transform application to lowercase ([8a15bbd](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/8a15bbd17553db675c238b86e6d1ba0feeede26c))


### Bug Fixes

* **PIGEON-4843:** rename function for orion inbox route ([b2b98f5](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/b2b98f51153d6072f6e4567ff43b8b0ba626e5e1))
* **PIGEON-4843:** update swagger file to add campaign inbox endpoint ([0652a5b](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/0652a5b51266e6a500a7b8b0001b164a17b5da29))
* **PIGEON-4843, 4845:** add an endpoint for orion inbox campaigns ([aaae859](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/aaae85907ce549d6e6a628aa7cd68c0e196467b1))
* **PIGEON-4853:** obfuscate x-customer-id in splunk logs ([9721adf](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/9721adfceba642f77250c8d0dbac1e24376e3977))
* **PIGEON-4866:** return more recent start date in get campaign response ([b21beeb](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/b21beeb614856db73467456218cc2a5d753521ac))
* **PIGEON-4883:** add unit test for get campaigns inbox endpoint ([71a3539](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/71a35396c456871bb54f314f989bebcdaa875f11))

### [1.15.4](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.15.3...1.15.4) (2023-11-30)


### Bug Fixes

* **PIGEON-4848:** reduce purge count per run to below mssql query limit ([a3862b4](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/a3862b4dce2c5f17e949278731bcce55af47b578))

### [1.15.3](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.15.2...1.15.3) (2023-11-21)


### Bug Fixes

* **PIGEON-4790:** udpate total rules returned ([679fc02](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/679fc02dbb1b108f2a5ce6fc20e24d29fb8d3fe1))

### [1.15.2](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.15.0...1.15.2) (2023-11-21)


### Bug Fixes

* **PIGEON-4820:** implement alternate solution to purging dispositions ([8e9d9c1](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/8e9d9c135e6b8755acbc5894740353ad836d4372))

## [1.15.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.14.2...1.15.0) (2023-11-17)


### Bug Fixes

* **PIGEON-4788:** return most recent date for camapaign ([59f56a6](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/59f56a69d584e062f7700b2248e7fe6ba4dafc6c))
* **PIGEON-4789:** add unit test for set diposition with array of pages ([625136f](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/625136f4f45764dbde7fe788f2accd4854c6c712))
* **PIGEON-4789:** update disposition endpoint to accept an array of pages ([030e89b](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/030e89bea3ea2622f8bf831f5c1ae1fc23557ac9))
* **PIGEON-4789:** update openapi.yml to accpect array of pages for disposition endpoint ([4902186](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/4902186176fc2d4fb3a2d2fbc64bdade07ad9509))
* **PIGEON-4789:** update openapi.yml to accpect array of pages for disposition endpoint ([9acf744](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/9acf74499fc0187b920c2a89105e9fe40d4e721e))
* **PIGEON-4790:** add offset functionality to support pagination ([25bd973](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/25bd9737e3de0e764bc393a1e23cfa1cd335c271))
* **PIGEON-4803:** update manifest file with disposition purge variables ([d413513](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/d41351387d7ff4d3f3d611b42a9f9b4979fc8242))
* **PIGEON-4820:** hold lock after successful purge check to purge completion ([f5551b4](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/f5551b4d8d78e96281e96034aeddc8b4e6439566))

### [1.14.2](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.14.1...1.14.2) (2023-10-26)


### Features

* **PIGEON-4812:** use parameterized query to reduce DB overhead ([3c1cd36](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/3c1cd361c99dc1d6a0e8ec17febeca473046a1af))
* support new campainid YCU ([71b8f67](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/71b8f6707ef1c7a4280a42afa5823d9fefff888e))


### Bug Fixes

* **PIGEON-4803:** hold lock during purge, improve logging, fix lint issues ([a96b623](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/a96b6238b52620a34fa67b6fbaede84debcbccf0))

### [1.14.1](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.14.0...1.14.1) (2023-10-12)


### Features

* use marvel smartdns endpoints ([436107e](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/436107e29cfba82bf156f39b8974e34101eb276f))

## [1.14.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.13.1...1.14.0) (2023-10-11)


### Bug Fixes

* **PIGEON-3855:** upgrade mssql driver for chain of black duck medium severity vulnerabilities ([e1f79ee](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/e1f79ee6f86df59d7d687f92096f0f1177f9e716))
* **PIGEON-4633:** honour limit query ([f52b6b8](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/f52b6b82b62640e92eaf5c5cb020788e8bf75f10))
* **PIGEON-4645:** send dash instead of underscore as contentful's locale separator ([ae01a19](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/ae01a1900e8aaff73751d77f1eb6a5cf705933f8))
* **PIGEON-4683:** gitversion support ([a443116](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/a4431167b39228c41fdc5748ff79dd1e396373ca))
* **PIGEON-4778:** fix black duck issues - minimist & unset-value ([2ab529f](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/2ab529f4dc903204a90f296beb1fc24d4ca9cd1e))
* **PIGEON-4782:** missing rule type in set disposition and lint error ([a1a5f27](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/a1a5f277bf4b2e373b284fb162d7967c9ce8f047))
* pass contentful language to content api when fetching alerts ([6a76e88](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/6a76e88c90e6e4114507fb1e44724ee0e5140901))
* update locale sent to content api during caching of alert rules ([ed85aa6](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/ed85aa6f0229e498f569f99ed7ab144f5022f2c7))

### [1.13.1](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.13.0...1.13.1) (2023-08-25)


### Features

* **PIGEON-4773:** add new campaign id YBM ([0fadd74](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/0fadd74e960a6ed08564df2b81a17b0e0be56fcb))

## [1.13.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.12.20...1.13.0) (2023-08-24)

### [1.12.20](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.12.9...1.12.20) (2023-08-18)

### [1.12.9](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.12.8...1.12.9) (2023-08-18)


### Features

* **PIGEON-4326:** passport smartdns url ([6c95268](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/6c95268230fe837f2b90e5a7a1d0c56625d78ad9))
* **PIGEON-4724 PIGEON-4725:** update value visa switch & mortgage renewal campaigns ([88a8d2c](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/88a8d2caa4c6e8c5f7065baffd40dcdaaa643156))
* **PIGEON-4740:** support npa cc invitation to apply kt campaign ([7658825](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/76588251ff5e0fcbdc72cc732d8515d9522846fc))


### Bug Fixes

* **PIGEON-4586:** return null value instead of variable name if insights value is null ([9069eec](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/9069eec12b78c97dcf3f6e30abdbe86bef29421f))
* **PIGEON-4727:** only call insights if targeted campaigns remain after filtering by query params ([49d477a](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/49d477acc7a6ef3ef2786574677215529b6875bf))

### [1.12.8](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.12.5...1.12.6) (2023-07-18)


### Features

* **PIGEON-4712:** add support for ofsi step changes campaign ids ([2d6143c](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/2d6143cc59f452445dbfda9b816007bb0f8b0c78))

### [1.12.5](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.12.3...1.12.5) (2023-07-12)


### Bug Fixes

* **PIGEON-4723:** only validate rule type if rule id is passed & rule is successfully fetched ([d7789a2](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/d7789a220fb3f2b742743b09d8d6951cf5e35590))

### [1.12.3](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.12.2...1.12.3) (2023-07-11)


### Bug Fixes

* **PIGEON-4723:** only use user context as uid for CCAU ([fccc75f](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/fccc75fdac8d1833f5fde4b5ee2f83b71f0d7b2f))

### [1.12.2](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.12.1...1.12.2) (2023-06-26)

### [1.12.1](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.12.0...1.12.1) (2023-06-12)


### Features

* **PIGEON-4700:** add new pacc campaign ids ([2100841](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/21008410c46fe92f9b73491a97cc26d840d72a23))

## [1.12.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.11.3...1.12.0) (2023-06-12)


### Features

* **PIGEON-4684:** add new pacc campaign ids ([7e75fff](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/7e75ffff0e8a1a838922ed630de56d220d7c9555))
* **PIGEON-4698:** add ability to globally identify disposition customer ([48f5c82](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/48f5c82a5782c5bdd02bd5e41544cf2a798ea425))
* remove extra console.log ([b680146](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/b68014697f4522fd46be7520a6aa4b275b109b47))
* rename insightsCampaign to targetedCampaignData ([ed226c1](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/ed226c1ef678d25d03cdde93bb4f732856eafda0))
* update release/1.12 with release/1.11 ([b06c4ff](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/b06c4ffdb4df824af68232daae03e4f6c9cbeb9a))
* **PIGEON-4644:** init the data source factory ([d400fd1](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/d400fd15bdf9d3f299e1131f4bf324cb344900d4))
* **PIGEON-4644:** restructure the params for get accounts funtion ([a96399f](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/a96399fef9f1077ec11393e6bea5a43e955be16f))
* **PIGEON-4675:** add new campaign ids to pacc variable mapping ([e5a282e](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/e5a282ef4dbd4f21dd1a238fd5762398e696855b))


### Bug Fixes

* fix async cal for cache ([7e6d280](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/7e6d2800424799b58726144bb0e9563bac7bb19d))
* fix async cal for cache ([ffd3f91](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/ffd3f91d27203e6d47224491350a3d9a48644fa9))
* **PIGEON-4644:** add commrnt to refactor the factory later ([359e2f2](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/359e2f2e565aa560616031ec101e1eb23ec986ac))
* **PIGEON-4646:** fix code maintenability issues, remove code smells ([be6d5e5](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/be6d5e586be5e0cbd15041ffc8a8c5a0ef511ed7))
* **PIGEON-4646:** fix code maintenability issues, remove code smells ([2789328](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/27893285822bf64018f63fa5d52829d3e64f2d3b))
* **PIGEON-4678:** test leakage due to improper tear down ([763dbf2](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/763dbf2e0e2e640cdba9aaaa0f31ef35f7829111))

### [1.11.3](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.11.2...1.11.3) (2023-05-24)

### [1.11.2](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.11.0...1.11.2) (2023-05-24)


### Features

* **PIGEON-4677:** transform product name for products with three character subcodes ([bdd760d](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/bdd760d847b23e979b82393b3256889e2c3bfc6f))


### Bug Fixes

* **PIGEON-4282:** add unit test for as-local-rate processor ([e4eda0a](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/e4eda0a69ef83a80e044339e2b2a676cb6f92798))

### [1.11.1](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.11.0...1.11.1) (2023-05-15)


### Features

* **PIGEON-4677:** transform product name for products with three character subcodes ([bdd760d](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/bdd760d847b23e979b82393b3256889e2c3bfc6f))


### Bug Fixes

* **PIGEON-4282:** add unit test for as-local-rate processor ([e4eda0a](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/e4eda0a69ef83a80e044339e2b2a676cb6f92798))

## [1.11.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.10.0...1.11.0) (2023-05-10)


### Features

* add support for additional pa cli variable ([c25021e](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/c25021e0d775b77b0dc30eab913d49e0923a2e6e))
* **PIGEON-4667:** support new scene visa pa-acq campaigns ([f252ee9](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/f252ee9683edaf28429e2cc67198fdce2aec39ed))


### Bug Fixes

* **PIGEON-4282:** add function to process variables with language specific rates, for pega ([df44377](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/df443777055e41e3afe306c3297dc6b142182a9c))
* **PIGEON-4282:** add test cases ([701459e](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/701459ed3a698eac6d32da65f3fa24da1fc75643))
* **PIGEON-4282:** log error when incorrect format is sent ([3c783c9](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/3c783c9c0a97e477ded064b3f5363f0389d5200e))
* **PIGEON-4282:** update error object in the logger processAsLocalRate ([fcb4746](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/fcb47460c0df9d9124c9c099a3f7c8d0b1f2039c))
* **PIGEON-4660:** code refactoring and unit test for generating old message ids ([1d6e412](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/1d6e412da8b00d5bac98cc1b329dabc2590fc982))
* **PIGEON-4660:** refactor code ([f063e93](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/f063e9330c2997f9b25111704f4a46ca96e389e3))
* **PIGEON-4660:** refactor code ([dc460d2](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/dc460d236c4f32cb0d8e7746aa26a58c1ed58b14))
* **PIGEON-4660:** support new and old message ids for PEGA campaigns ([a3d504b](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/a3d504baa254bf1a84cb600977b16e178111fad2))
* **PIGEON-4660:** support old and new message ids for insights ([9bd2db2](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/9bd2db2e7a20ff0e49a05b676667f0b8954780f5))

### [1.10.4](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.10.3...1.10.4) (2023-04-24)

### [1.10.3](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.10.2...1.10.3) (2023-04-20)

### [1.10.2](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.10.1...1.10.2) (2023-04-20)

### [1.10.1](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.10.0...1.10.1) (2023-04-20)

## [1.10.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.9.7...1.10.0) (2023-04-12)


### Features

* **PIGEON-4509:** pentest lack of rate limiting ([1a0ea79](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/1a0ea795d3faad131d3c4f780dcb01860d9c31cf))
* **PIGEON-4509:** pentest lack of rate limiting - contents catalog ([55dd305](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/55dd305c1ee538efdf0e6873cc2f8cd3e0ab5018))
* **PIGEON-4635:** add support for mpsa retention campaigns ([6c86778](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/6c86778e2aa211360a28ede3d7b460aba30d209a))


### Bug Fixes

* **PIGEON-4503:** fix eror handler json format + vaidate /v1/contents-catalog endpoint ([9cfbbb7](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/9cfbbb755e20b4749a6b06f0eb82d2ba8bc295c0))

## [1.9.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.8.12...1.9.0) (2023-03-02)


### Features

* **PIGEON-4021:** case insensitive placement ([674d807](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/674d8075c9f5b7af5f806809d72afbe057c5be3b))
* **PIGEON-4570:** add two more variables to xab xae xas xat campaigns ([9595cfa](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/9595cfa964b3a6e493084447c918245d533b70e4))

### [1.8.12](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.8.11...1.8.12) (2022-11-29)


### Bug Fixes

* **PIGEON-3847:** keep cache refresh alive even on network exception ([e7e909d](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/e7e909d6f1c783c509c2f63cfd6a0e69f8a64899))

## [1.8.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.7.36...1.8.0) (2022-10-13)


### Features

* **PIGEON-4011:** support new campaigns ids ([ca54798](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/ca54798a9987dd3582ad203cac89d42be7697466))
* **PIGEON-4014:** support x customer id header ([0593ada](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/0593ada8d918433dfdb28ab72a88ab432704a4ac))
* **PIGEON-4038:** support new insight end point ([c289f4e](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/c289f4eaa8fbd5d0374a9464dd0fb4956678a00c))
* **PIGEON-4066:** add new campaign ids AAG , AAP to pacc ([c077b31](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/c077b310cc39bf71ba6f74e5161368279f8c4adc))
* **PIGEON-4093:** add some logs to set dispostions function ([7c2214b](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/7c2214bce79bb1d6301cbc23b04c4bdaeb798f4f))


### Bug Fixes

* **PIGEON-4096:** fix the disposition values ([406b942](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/406b94213c200819f98b528ab5530f656979e950))
* fix disposition validation ([f018e72](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/f018e72afa515f67e5b8bdf48cc5321a0fab5f69))
* **PIGEON-4063:** fix solui_reminder_period_end variable for AKYC ([6cf0f37](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/6cf0f37ffd22c1c00bb6b67660ceb22f4f50c4da))
* add clear error messages ([5232ff3](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/5232ff328e86cb1a09a5989d2e51cdd7063bc106))
* **PIGEON-4038:** add looger error on getting campaigns from insgits ([d536601](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/d536601891b6b78bb74574c4595326e05bcc0ba8))

## [1.7.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/1.6.15...1.7.0) (2022-01-19)


### Bug Fixes

* **PIGEON-3472:** upgrade knex mssql lodash ([0e5d9b6](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/0e5d9b6ef6fda01c7a4798d530caa23606fdfb6f))
* **PIGEON-3526:** handle incorrect response ([1cf254a](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/1cf254af9884f248274b3897039751264b60ce19))
* **PIGEON-3570:** french locale for new SOL variable to support remind me later enhancement ([bf57f2e](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/bf57f2e089b2c15d1b388c48874b2e5682703c63))
* **PIGEON-3580:** fix the 'D' disposition error while sending to insights ([e8e4264](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/e8e4264bf419a5a7666493aeddd7803a777a9e0a))
* **PIGEON-3582:** Adding unit tests ([bee8c76](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/bee8c76a35eb05f198f875354bc91df6d46f7f2d))
* **PIGEON-3585:** fix redis caching duration  issue ([110f229](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/110f229ac9122fd55298c3dcc27a0868517310a1))
* **PIGEON-3592:** pass xOriginatingApplCode downstream ([c7a335d](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/c7a335d42211eccc58c60ac3dcfaf0180dbe1f14))
* cleanup ([04d2230](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/04d22304a34b92b6ba03823afb76fe3caa819d53))

## [1.6.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/v1.5.0...v1.6.0) (2021-11-11)


### Features

* **PIGEON-3365:** fix bd scan authentication, add seperate release/main config files ([ce9a6c3](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/ce9a6c35bb72e8e87427752018798da1eb1bcfa9))
* add `P` and remove `ATP`/`ATC` dispositions support in openapi specs PIGEON-3432 ([bfe8b2d](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/bfe8b2df0aa1f0bdb603d585596d1424fc8d46d3))
* validation to V & D disposition ([ff6e978](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/ff6e978912ceab5d30553138072cd8a6ff15f14f))
* **PIGEON-3353,PIGEON-3240,PIGEON-3354,PIGEON-3241:**  AMLKYC soft/hard intercept support ([4a7c008](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/4a7c0082a1b1b3a646595d339e3a0c71c39adb3f))
* **PIGEON-3371:** add support for AMLKYC campaigns ([27ddb4c](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/27ddb4c0b848e674098021f9062b9de4fac0edfb))


### Bug Fixes

* add missing argument in api call ([9e2590f](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/9e2590fe1cc00e3e9f8497a8318702039c307e0e))
* ELSM logging fixes PIGEON-3509 ([8861693](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/8861693d422093c0cbc629d6b3a87dfae019d58b))
* **PIGEON-3481:** account for locale with and without country code ([3fded0f](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/3fded0fffc6e7b358e29d8d3d5482eef1b73bf4b))
* removing the console.logs ([515bd72](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/515bd721af5be5aacfdf3be9257f412e36c78528))
* **PIGEON-3481:** display minimum of one digit for day of month ([9c7c4bd](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/9c7c4bd20e5c8a006468b4fb7a8fed96866f9d5c))
* **PIGEON-3481:** special format for french locale ([1954106](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/1954106a24a9b0b7cc239a5bf564106713fe14bc))
* **PIGEON-3481:** support french locale for hard stop date transform ([b3e2117](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/b3e2117b4fb77a1646097e9085695edccacc329a))
* **PIGEON-3494:** cta not functional for soa campaigns and additional logging around marvel ([21d71a6](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/21d71a6ef31f2f5393953e6b82b4000d3d0ffd1e))
* passing spanid and traceid for every downstream request logging ([bc035da](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/bc035daae00b96becd80a66e2c513556caeadd1a))
* PIGEON-3417 PIGEON-3418 kyc fixes and masking headers ([48f1d73](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/48f1d735dc2367c8dca4850eea1646a85d707777))
* point marvel accounts to uat ([edec2f3](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/edec2f3085415733919081e740b5128d0add6632))

## [1.5.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/v1.4.12...v1.5.0) (2021-11-11)


### Features

* **PIGEON-3316:** pass headers required for pref mgt sso cta ([3cf0b72](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/3cf0b72b65cb6c4692cacccb2750f4c400e27ef4))
* **PIGEON-3316:** use new header variable format ([ed75913](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/ed75913566d2298e60ffed52ba4ab5782462f734))


### Bug Fixes

* overwriting only redis with older version ([888bf18](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/888bf18615e13596d4c79e2915a4b6df01454b67))
* reverting the redis version in package-lock alone adding more logging ([c82bb48](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/c82bb48485ab19b39f20ed490b2c053faad67544))
* **PIGEON-3376:** bump redis in package lock ([57c1e73](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/57c1e7310e533b4e61a67a4abe617620b032775d))
* **PIGEON-3376:** migrated to latest ld ([a409300](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/a409300f0dd43b1f7ab556ab67ca12e6063d0761))
* **PIGEON-3376:** pin lodash ([3784213](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/3784213fe99b3b7dc8d7594b3f49c59be693568e))
* move let-> const ([74caa8c](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/74caa8cdf4f498c7ce64418c706332ed01f9ae55))
* point nft to real insights instead of mock ([780a2ee](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/780a2eec26e434e05383265074f3c800d9d2de85))
* remove kyc specific code from sept branch for casl ([8cbe0e0](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/8cbe0e0ff872b720cdf1b71b6dc0bedf6c32dc68))
* removing kyc specific code from september branch for CASL Pref Management ([60a70df](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/60a70dff32245a7f23efee48e39b91b8e067d733))
* **PIGEON-3379:** override vulnerable dependencies ([42eb694](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/42eb694def43b941e402775dc5f37edc3ef1f893))
* resolve merge conflicts ([516e2de](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/516e2de46e0a435e92cf72b5f27eb847b9173553))
* **PIGEON-3316:** default to header vars to blank ([6f0dcc6](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/6f0dcc6dd3d9c0b8d9be3be7814cb971395afcad))

## [1.2.0](http://bitbucket.agile.bns:7999/pigeon/pigeon/compare/v1.1.148...v1.2.0) (2021-04-27)


### Features

* added more coverage for unit test PIGEON-3046 ([12fe70d](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/12fe70d6f309288676b65a27db2be0b461ffaf42))
* adding support for  EMOB D2D NOVA (MPSA) campaign PIGEON-3046 ([b027e36](http://bitbucket.agile.bns:7999/pigeon/pigeon/commit/b027e36b67efda40f6e793d7f3b6b8749a364b14))
