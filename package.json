{"name": "pigeon-pigeon", "version": "1.32.1", "description": "<PERSON>eon", "author": {"name": "Pigeon Team", "email": "<EMAIL>"}, "repository": "ssh://***********************:7999/pigeon/pigeon.git", "main": "./src/index.js", "private": false, "engines": {"node": "22.13.1", "npm": "^10.9.2"}, "scripts": {"start": "NODE_ICU_DATA=./data node ./src/index.js", "start:win": "set $NODE_ICU_DATA=./data & node ./src/index.js", "start:dev": "NODE_ICU_DATA=./data nodemon ./src/index.js --watch src", "lint": "eslint ./src/", "lint:fix": "eslint --fix ./src/", "test:unit": "NODE_ICU_DATA=./data jest --env node ./src/", "test:unit:win": "set $NODE_ICU_DATA=./data & jest --env node ./src", "test:acceptance": "NODE_ICU_DATA=./data jest --env node ./acceptance-tests", "test:acceptance:win": "set $NODE_ICU_DATA=./data & jest --env node ./acceptance-tests", "test": "npm run test:unit && npm run test:acceptance", "test:win": "npm run test:unit:win && npm run test:acceptance:win", "coverage": "NODE_ICU_DATA=./data jest --env node --coverage ./src", "coverage:win": "set $NODE_ICU_DATA=./data & jest --env node --coverage ./src", "check": "npm install && npm run coverage", "bumpVersion": "AF_VERSION=$(npm view $npm_package_name version); LATEST_VERSION=$(semver $AF_VERSION $npm_package_version | tail -1); if [ $LATEST_VERSION != $npm_package_version ] || [ $AF_VERSION = $npm_package_version ]; then npm version $AF_VERSION --force --no-git-tag-version --allow-same-version && npm version patch --force --no-git-tag-version; fi", "release": "standard-version"}, "dependencies": {"@hapi/joi": "^15.1.1", "agentkeepalive": "^4.1.0", "async-lock": "^1.2.2", "atob": "^2.1.2", "cors": "^2.8.5", "dot-prop": "^5.2.0", "dotenv": "^8.2.0", "escape-html": "^1.0.3", "escape-markdown": "^1.0.4", "express": "^4.20.0", "express-http-context": "^1.2.4", "express-rate-limit": "^6.7.0", "fetch-mock-jest": "1.3.0", "helmet": "^3.21.2", "jsonwebtoken": "^9.0.0", "jwks-rsa": "^3.1.0", "knex": "^2.4.2", "launchdarkly-node-server-sdk": "^6.2.0", "lodash": "^4.17.21", "lru-cache": "^5.1.1", "markdown-escape": "^1.0.2", "moment": "^2.24.0", "moment-timezone": "^0.5.44", "node-fetch": "^2.6.1", "nrlw-express-scribe": "^1.10.1", "opossum": "^5.0.0", "pigeon-cerberus": "3.0.0", "pigeon-pigeon-pack": "^1.2.0", "pino": "^5.16.0", "pino-noir": "^2.2.1", "pluralize": "^8.0.0", "promise.allsettled": "^1.0.2", "ramda": "^0.29.1", "range_check": "^2.0.4", "redis": "^3.1.2", "redlock": "4.1.0", "rxjs": "^6.5.4", "semver": "^7.5.4", "swagger-ui-express": "^4.1.3", "tedious": "^14.7.0", "uuid": "^3.4.0", "yamljs": "^0.3.0"}, "devDependencies": {"@commitlint/cli": "^12.1.1", "@commitlint/config-conventional": "^12.1.1", "@scotia/eslint-config-scotiabank": "^1.0.1", "@types/jest": "^24.9.0", "conventional-changelog-eslint": "^3.0.9", "eslint": "^6.8.0", "eslint-plugin-import": "^2.20.0", "eslint-plugin-security": "^1.4.0", "eslint-plugin-sonarjs": "^0.5.0", "husky": "^3.0.1", "jest": "^29.7.0", "lint-staged": "^10.3.0", "mock-knex": "^0.4.12", "node-mocks-http": "^1.8.1", "nodemon": "^2.0.22", "pino-pretty": "^3.2.0", "standard-version": "^9.2.0", "supertest": "^4.0.2"}, "overrides": {"brace-expansion": "2.0.2", "@azure/identity": "4.10.1", "@azure/core-tracing": "^1.0.1"}, "eslintConfig": {"env": {"es6": true, "node": true, "jest": true}, "parserOptions": {"ecmaVersion": 9}, "globals": {"fetch": true}, "plugins": ["sonarjs", "security", "import"], "extends": ["@scotia/eslint-config-scotiabank", "plugin:sonarjs/recommended", "plugin:security/recommended", "plugin:import/errors", "plugin:import/warnings"], "rules": {"comma-dangle": ["error", "always-multiline"], "space-before-function-paren": ["error", {"asyncArrow": "always", "anonymous": "never", "named": "never"}], "no-irregular-whitespace": "off"}}, "lint-staged": {"src/**/*.js": ["eslint"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "husky": {"hooks": {"pre-commit": "lint-staged && npm test", "pre-push": "npm run coverage", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "properties": {"artifactory_contextUrl": "https://af.cds.bns/artifactory", "artifactory_projectRepoKey": "local-npm-bns", "artifactory_user": "", "artifactory_password": "", "artifactory_npm_repo": "virtual-npm-bns", "pcf_app_url": "", "cdp_vault_name": "PIGEON", "cdp_environment_name": "", "cdp_region_name": "", "cdp_vault_clientId": "", "cdp_vault_clientSecret": "", "nodejs_tool_name": "nodejs-16.16.0", "pipeline_plugin_version": "1.2.1", "sonar_host_url": "https://sonar.agile.bns/", "sonar_sources": "src/", "sonar_exclusions": "src/**/*.test.js", "sonar_tests": "", "sonar_test_inclusions": "src/**/*.test.js", "sonar_report_paths": "coverage/lcov.info", "sonar_language": "js"}, "settings": {"rootProject.name": "pigeon"}}