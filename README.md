# Pigeon CMS BFF


**Before you begin:**

* Ensure that you have both npm and node installed and setup on your machine.

  * Node version - 20.15.1
  * Npm version - 10.7.0

* Docker - install the latest version of docker found here: https://docs.docker.com/desktop/
  * **Note**: An account may be required to install public images

**Setup:**

*Project Setup:*
* `npm install`
* Copy .env.example to .env in the root project folder
* Please reach out the the Pigeon Engineering Team for the most up-to-date .env file
* If on Windows
  * In your local package.json, replace ```pre-commit``` and ```pre-push``` hooks with windows equivalent scripts
  * This allows husky git hooks to function correctly on Windows
  * Do not commit these changes


*Redis Setup*
  * Install a redis image: ```docker run --name pigeon-redis -d redis -p 6379:6379/tcp redis```
  * Alternatively, install redis with your preferred method. Here's sample steps with Homebrew:
    ```
    // assuming homebrew is already installed
    brew install redis // install redis
    brew services start redis // start redis
    redis-cli ping // should respond with PONG from 127.0.0.1:6379
    brew services stop redis // when you're done with redis
    ```
  * Redis GUI:
    * For a Redis GUI use the setup instructions for this project: https://github.com/luin/medis
      ```
      // Disable global .npmrc if you have one, and it uses artifactory as registry
      git clone https://github.com/luin/medis.git
      npm install
      // Restore global .npmrc if you had to disable it
      npm run pack
      npm start
      ```

*Testing and Debugging*
* The easiest way to test this project will be with postman.
* Pigeon integration tests will have everything you need for most scenarios.
* **Repo link**: https://bitbucket.agile.bns/projects/PIGEON/repos/pigeon-integration-tests/browse
  <br/>
  **Setup**
  * After cloning the repo, import the following collections  and environments:
    <br/>
    ```
    Collections:

    passport.postman_collection.json
    pigeon.postman_collection.son
    ```
    ```
    Environments:

    ist-cc.pigeon.postman_environment.json
    ist-use2.pigeon.postman_environment.json

    ```
  **Required Request Info**

  * To create an opaque token (representing a scotiabank user) you will require a test card
  * Please note the IST environment for the test card to be created (i.e. istred, istgreen, etc)
  * [See here](https://confluence.agile.bns/display/PIGEON/Test+Data) for test data actively managed by QA team
  * Once you have test card credentials, inspect and update the following environment variables in postman::

  ```
      {scotia_card}
      {scotia_card_password}
      {passport_environment}
  ```

  **Request Sequence**

  1. #passport / opaque / [POST] acquire-an-opaque-token
  2. #pigeon-webinspect / pigeon-bff / [GET] get-campaigns-for-activities/ios