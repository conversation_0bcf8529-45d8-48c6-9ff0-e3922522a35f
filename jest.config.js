module.exports = {
  collectCoverageFrom: [ 'src/**/*.js', '!src/index.js', '!src/init.js' ],
  coverageDirectory: 'coverage',
  coverageReporters: [ 'json', 'lcov', 'text' ],
  coverageThreshold: {
    global: {
      branches: 85,
      functions: 95,
      lines: 95,
      statements: 95,
    },
  },
  errorOnDeprecated: true,
  testMatch: [ '**/?(*.)+(spec|test).js' ],
  setupFiles: ["<rootDir>/.jest/setEnvVars.js"]
};
