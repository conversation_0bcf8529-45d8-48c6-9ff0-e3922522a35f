const productBookData = require('../../data/productbook.json');
const ProductBookService = require('../../src/components/productbook');
const { arrToObj } = require('../../src/components/common');

const mockCampaignCacheResponse = (rules) => ({
  campaigns: arrToObj(Array.isArray(rules) ? rules : [ rules ]),
});

const logger = {
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  trace: jest.fn(),
};

const authenticationMiddleware = (req, res, next) => {
  res.locals.auth = {
    token: 'token',
    claims: { sub: '4000111122223333', scope: 'standard' },
  };
  next();
};

const authorizationMiddleware = (scope) => (req, res, next) => {
  next();
};

const alertService = {
  getAll: jest.fn().mockResolvedValue({
    total: 0,
    offset: 0,
    limit: 50,
    items: [],
  }),
};

const ruleAPIClient = {
  get: jest.fn(),
  getAll: jest.fn().mockResolvedValue({
    total: 0,
    offset: 0,
    limit: 50,
    items: [],
  }),
};

const campaignCacheService = {
  get: jest.fn(),
};

const containerCacheService = {
  get: jest.fn(),
};

const targetedCampaignService = {
  getInstance: jest.fn(),
};
targetedCampaignService.getInstance.mockReturnValue({ getCampaigns: jest.fn() , getCampaign: jest.fn() });

const marvelService = {
  getAccounts: jest.fn().mockResolvedValue([]),
  getCardProfile: jest.fn().mockResolvedValue(null),
  getProducts: jest.fn().mockResolvedValue({ product: [] }),
};

const dispositionsService = {
  getDispositions: jest.fn(),
  setDisposition: jest.fn(),
};

const launchDarklyService = jest.fn().mockResolvedValue({
  init: jest.fn(),
  isFeatureEnabled: jest.fn().mockResolvedValue(true),
  close: jest.fn(),
});

const contentService = {
  getContentByTypeAndId: jest.fn(),
  getContentsByIds: jest.fn(),
};

const investmentService = {
  getGicRates: jest.fn(),
};

const productBookConfig = { productsUri: 'localhost', refreshIntervalMillis: 24 * 60 * 60 * 1000 };
const productBookService = ProductBookService({
  config: productBookConfig,
  logger,
  productBook: productBookData,
  marvelService,
});
clearInterval(productBookService.refreshJob);


const rateLimitingMiddleware = async (req, res, next) => {
  req.ldRateLimitConfig = {
    client: { alerts: 5, campaigns: 4 },
    overall: { alerts: 50, campaigns: 40, contentsCatalog: 10 },
  }
  next();
};

const ignoredLogRoutes = [ '/health' ];

module.exports = {
  logger,
  authenticationMiddleware,
  authorizationMiddleware,
  alertService,
  ruleAPIClient,
  marvelService,
  campaignCacheService,
  containerCacheService,
  targetedCampaignService,
  dispositionsService,
  contentService,
  investmentService,
  productBookService,
  launchDarklyService,
  mockCampaignCacheResponse,
  ignoredLogRoutes,
  rateLimitingMiddleware,
};
