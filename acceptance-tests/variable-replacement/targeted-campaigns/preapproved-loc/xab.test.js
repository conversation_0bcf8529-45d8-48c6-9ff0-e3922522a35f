const supertest = require('supertest');
const { createServer } = require('../../../../src/server');
const { createVariableReplacer } = require('../../../../src/components/variables');
const {
  logger,
  authenticationMiddleware,
  authorizationMiddleware,
  alertService,
  campaignCacheService,
  targetedCampaignService,
  contentService,
  productBookService,
  launchDarklyService,
  mockCampaignCacheResponse,
  ignoredLogRoutes,
  rateLimitingMiddleware,
} = require('../../../mock');

const config = {
  space: 'IST',
  clsNamespace: 'test',
  http: { origin: '*' },
  serviceAuth: {
    tokenPath: 'auth.token',
    tokenClaimsPath: 'auth.claims',
    anonymousClientIds: [ 'testId' ],
  },
  rateLimiting: {
    window: 60000,
    client: { alerts: 5, campaigns: 4 },
    overall: { alerts: 50, campaigns: 40, contentsCatalog: 10 },
  }
};

const mockRuleId = 'HshFUej8x4xx';
const mockMessageId = 'D0123456789ABCDE';

const mockCampaignRuleResponse = {
  id: mockRuleId,
  name: 'Pre-approved Line of Credit',
  type: 'campaign',
  start_at: '2019-02-14T00:00:00.000Z',
  end_at: '2030-12-31T23:59:59.999Z',
  content_space: '4szkx38resvm',
  content_type: 'targetedCampaign',
  content_id: '1vsOnVrNvWUr251kZbyi6B',
  anonymous: true,
  authenticated: true,
  disabled: false,
  status: 'published',
  created_by: 's4267303',
  updated_by: 's4267303',
  created_at: '2019-04-23T14:59:27.363Z',
  updated_at: '2019-04-24T16:34:51.653Z',
  external_ref: 'XAB99',
  container: 'offers-and-programs',
  pages: [ 'accounts' ],
  platforms: [ 'ios', 'android' ],
  app_version: null,
  urgent: false,
};

const mockContentResponse = {
  data: {
    id: '6nemNDXeepdWzwe6wYMRND',
    type: 'targetedCampaignTemplateDetails',
    language: 'en-US',
    created_at: '2019-04-16T14:59:40.277Z',
    updated_at: '2019-04-16T14:59:40.277Z',
    content: {
      name: 'XAB99 Test Campaign',
      title: 'title',
      details: `
NAME: SOLUI_NAME_END,
CREDIT: SOLUI_CREDIT_END,
PROD: SOLUI_PROD_END,
OTHER1: SOLUI_OTHER1_END,
OTHER2: SOLUI_OTHER2_END,
OTHER3: SOLUI_OTHER3_END,
DATE3: SOLUI_DATE3_END,
OTHER4: SOLUI_OTHER4_END,
DATE4: SOLUI_DATE4_END,
OTHER5: SOLUI_OTHER5_END,
OTHER6: SOLUI_OTHER6_END,
OTHER7: SOLUI_OTHER7_END,
OTHER8: SOLUI_OTHER8_END,
DATE8: SOLUI_DATE8_END,
OTHER9: SOLUI_OTHER9_END,
OTHER10: SOLUI_OTHER10_END,
OTHER11: SOLUI_OTHER11_END,
DATE11: SOLUI_DATE11_END,
OTHER12: SOLUI_OTHER12_END,
OTHER13: SOLUI_OTHER13_END,
OTHER14: SOLUI_OTHER14_END,
OTHER15: SOLUI_OTHER15_END,
OTHER16: SOLUI_OTHER16_END,
DATE16: SOLUI_DATE16_END,
OTHER17: SOLUI_OTHER17_END,
OTHER18: SOLUI_OTHER18_END,
OTHER19: SOLUI_OTHER19_END,
OTHER20: SOLUI_OTHER20_END,
OTHER21: SOLUI_OTHER21_END,
OTHER22: SOLUI_OTHER22_END,
OTHER23: SOLUI_OTHER23_END,
OTHER24: SOLUI_OTHER24_END,
OTHER25: SOLUI_OTHER25_END.`,
    },
  },
  notifications: [],
};

describe('Variable Replacement', () => {
  describe('Targeted Campaigns - Pre-approved Line of Credit', () => {
    describe('XAB99', () => {
      const mockInsightResponse = {
        data: {
          message_id: mockMessageId,
          message_source: 'KT',
          message_status: '',
          subject_line: 'Pre-approved line of credit',
          language: 'E',
          campaign_type: 'SOL',
          campaign_id: 'XAB99',
          start_date: '2019-02-13',
          expiry_date: '2029-12-31',
          conf_number: null,
          additional_data: [
            { name: 'CREDIT', value: '30000' },
            { name: 'NAME', value: 'MR DEC SPLASHFREL' },
            { name: 'PROD', value: 'VICRG' },
            { name: 'OTHER1', value: '' },
            { name: 'OTHER2', value: '+1.95' },
            { name: 'OTHER3', value: '20190714' },
            { name: 'OTHER4', value: '20190715' },
            { name: 'OTHER5', value: '106' },
            { name: 'OTHER6', value: '' },
            { name: 'OTHER7', value: '' },
            { name: 'OTHER8', value: '20110401' },
            { name: 'OTHER9', value: 'AO013466' },
            { name: 'OTHER10', value: '' },
            { name: 'OTHER11', value: '20291231' },
            { name: 'OTHER12', value: '' },
            { name: 'OTHER13', value: '' },
            { name: 'OTHER14', value: 'USV000004908' },
            { name: 'OTHER15', value: '' },
            { name: 'OTHER16', value: '20190714' },
            { name: 'OTHER17', value: 'P0195000P0195' },
            { name: 'OTHER18', value: '' },
            { name: 'OTHER19', value: 'UNGP195' },
            { name: 'OTHER20', value: '' },
            { name: 'OTHER21', value: '' },
            { name: 'OTHER22', value: '' },
            { name: 'OTHER23', value: '6.15' },
            { name: 'OTHER24', value: '615' },
            { name: 'OTHER25', value: '1384' },
          ],
        },
        notifications: [],
      };
      const expectedResponseEn = {
        content: {
          name: 'XAB99 Test Campaign',
          title: 'title',
          details: `
NAME: MR DEC SPLASHFREL,
CREDIT: $30,000,
PROD: ScotiaLine Line of Credit,
OTHER1: ,
OTHER2: +1.95,
OTHER3: July 14, 2019,
DATE3: July 14, 2019,
OTHER4: July 15, 2019,
DATE4: July 15, 2019,
OTHER5: 106,
OTHER6: ,
OTHER7: ,
OTHER8: April 1, 2011,
DATE8: April 1, 2011,
OTHER9: AO013466,
OTHER10: ,
OTHER11: December 31, 2029,
DATE11: December 31, 2029,
OTHER12: ,
OTHER13: ,
OTHER14: USV000004908,
OTHER15: ,
OTHER16: July 14, 2019,
DATE16: July 14, 2019,
OTHER17: P0195000P0195,
OTHER18: ,
OTHER19: UNGP195,
OTHER20: ,
OTHER21: ,
OTHER22: ,
OTHER23: 6.15,
OTHER24: 615,
OTHER25: 1384.`,
        },
      };
      const expectedResponseFr = {
        content: {
          name: 'XAB99 Test Campaign',
          title: 'title',
          details: `
NAME: MR DEC SPLASHFREL,
CREDIT: 30 000 $,
PROD: Ligne de crédit Scotia,
OTHER1: ,
OTHER2: +1.95,
OTHER3: 14 juillet 2019,
DATE3: 14 juillet 2019,
OTHER4: 15 juillet 2019,
DATE4: 15 juillet 2019,
OTHER5: 106,
OTHER6: ,
OTHER7: ,
OTHER8: 1 avril 2011,
DATE8: 1 avril 2011,
OTHER9: AO013466,
OTHER10: ,
OTHER11: 31 décembre 2029,
DATE11: 31 décembre 2029,
OTHER12: ,
OTHER13: ,
OTHER14: USV000004908,
OTHER15: ,
OTHER16: 14 juillet 2019,
DATE16: 14 juillet 2019,
OTHER17: P0195000P0195,
OTHER18: ,
OTHER19: UNGP195,
OTHER20: ,
OTHER21: ,
OTHER22: ,
OTHER23: 6.15,
OTHER24: 615,
OTHER25: 1384.`,
        },
      };
      test('english', () => {
        targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightResponse);
        campaignCacheService.get.mockReturnValueOnce(mockCampaignCacheResponse(mockCampaignRuleResponse));
        contentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
        const variablesService = createVariableReplacer({ logger, productBookService });
        const server = createServer({
          logger,
          authenticationMiddleware,
          authorizationMiddleware,
          alertService,
          campaignCacheService,
          targetedCampaignService,
          contentService,
          variablesService,
          launchDarklyService,
          ignoredLogRoutes,
          rateLimitingMiddleware,
        }, config);
        return supertest(server)
          .get(`/v1/campaigns/${mockRuleId}?message_id=${mockMessageId}`)
          .set('Content-Type', 'application/json')
          .set('Accept', 'application/json')
          .expect('Content-Type', /json/)
          .expect(200)
          .then((res) => {
            expect(res.body.data.content).toEqual(expectedResponseEn.content);
          });
      });
      test('french', () => {
        targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightResponse);
        campaignCacheService.get.mockReturnValueOnce(mockCampaignCacheResponse(mockCampaignRuleResponse));
        contentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
        const variablesService = createVariableReplacer({ logger, productBookService });
        const server = createServer({
          logger,
          authenticationMiddleware,
          authorizationMiddleware,
          alertService,
          campaignCacheService,
          targetedCampaignService,
          contentService,
          variablesService,
          launchDarklyService,
          ignoredLogRoutes,
          rateLimitingMiddleware,
        }, config);
        return supertest(server)
          .get(`/v1/campaigns/${mockRuleId}?message_id=${mockMessageId}`)
          .set('Content-Type', 'application/json')
          .set('Accept', 'application/json')
          .set('X-Language', 'fr')
          .expect('Content-Type', /json/)
          .expect(200)
          .then((res) => {
            expect(res.body.data.content).toEqual(expectedResponseFr.content);
          });
      });
    });
  });
});
