/* eslint-disable sonarjs/no-duplicate-string */
const supertest = require('supertest');
const { createServer } = require('../../../../src/server');
const { createVariableReplacer } = require('../../../../src/components/variables');
const {
  logger,
  authenticationMiddleware,
  authorizationMiddleware,
  alertService,
  campaignCacheService,
  targetedCampaignService,
  contentService,
  investmentService,
  productBookService,
  launchDarklyService,
  mockCampaignCacheResponse,
  ignoredLogRoutes,
  rateLimitingMiddleware,
} = require('../../../mock');
const mockInvestmentResponse = require('./investment.mock.json');

const config = {
  space: 'IST',
  clsNamespace: 'test',
  http: { origin: '*' },
  serviceAuth: {
    tokenPath: 'auth.token',
    tokenClaimsPath: 'auth.claims',
    anonymousClientIds: [ 'testId' ],
  },
  rateLimiting: {
    window: 60000,
    client: { alerts: 5, campaigns: 4 },
    overall: { alerts: 50, campaigns: 40, contentsCatalog: 10 },
  }
};

const mockRuleId = 'HshFUej8x4xx';
const mockMessageId = 'D0123456789ABCDE';

const mockCampaignRuleResponse = {
  id: mockRuleId,
  name: 'GDD Test',
  type: 'campaign',
  start_at: '2019-02-14T00:00:00.000Z',
  end_at: '2030-12-31T23:59:59.999Z',
  content_space: '4szkx38resvm',
  content_type: 'targetedCampaign',
  content_id: '1vsOnVrNvWUr251kZbyi6B',
  anonymous: true,
  authenticated: true,
  disabled: false,
  status: 'published',
  created_by: 's4267303',
  updated_by: 's4267303',
  created_at: '2019-04-23T14:59:27.363Z',
  updated_at: '2019-04-24T16:34:51.653Z',
  external_ref: 'GDD99',
  container: 'offers-and-programs',
  pages: [ 'accounts' ],
  platforms: [ 'ios', 'android' ],
  app_version: null,
  urgent: false,
};

const mockContentResponse = {
  data: {
    id: '6nemNDXeepdWzwe6wYMRND',
    type: 'targetedCampaignTemplateDetails',
    language: 'en-US',
    created_at: '2019-04-16T14:59:40.277Z',
    updated_at: '2019-04-16T14:59:40.277Z',
    content: {
      name: 'GDD99 Test Campaign',
      title: 'title',
      details: `
NAME: SOLUI_NAME_END,
SPECIAL_RATE: SOLUI_SPECIAL_RATE_END,
SPECIAL_TERM: SOLUI_SPECIAL_TERM_END,
SPECIAL_PRODUCT: SOLUI_SPECIAL_PRODUCT_END,
OTHER1: SOLUI_OTHER1_END,
OTHER2: SOLUI_OTHER2_END,
OTHER3: SOLUI_OTHER3_END,
OTHER4: SOLUI_OTHER4_END,
OTHER5: SOLUI_OTHER5_END,
DATE5: SOLUI_DATE5_END,
OTHER6: SOLUI_OTHER6_END,
OTHER7: SOLUI_OTHER7_END,
OTHER8: SOLUI_OTHER8_END,
OTHER9: SOLUI_OTHER9_END,
OTHER10: SOLUI_OTHER10_END.`,
    },
  },
  notifications: [],
};

describe('Variable Replacement', () => {
  describe('Targeted Campaigns - GIC Renewals', () => {
    describe('GDD99', () => {
      const mockInsightResponse = {
        data: {
          message_id: mockMessageId,
          message_source: 'KT',
          priority: 'N',
          message_response: '',
          subject_line: 'GIC renewals',
          language: 'E',
          campaign_type: 'SOL',
          campaign_id: 'GDD99',
          start_date: '2019-02-13',
          expiry_date: '2029-12-31',
          conf_number: null,
          additional_data: [
            { name: 'NAME', value: 'MR PBANKING TEST1' },
            { name: 'OTHER1', value: 'MCCLTNMP' },
            { name: 'OTHER2', value: '00100000000LWD0' },
            { name: 'OTHER3', value: '***************' },
            { name: 'OTHER4', value: '12' },
            { name: 'OTHER5', value: '********' },
            { name: 'OTHER6', value: '50791' },
            { name: 'OTHER7', value: 'SO' },
            { name: 'OTHER8', value: 'GIC' },
            { name: 'OTHER9', value: 'MATURITY RET' },
            { name: 'OTHER10', value: 'GIC' },
          ],
        },
        notifications: [],
      };
      const expectedResponseEn = {
        content: {
          name: 'GDD99 Test Campaign',
          title: 'title',
          details: `
NAME: MR PBANKING TEST1,
SPECIAL_RATE: 2.55%,
SPECIAL_TERM: 1 year,
SPECIAL_PRODUCT: ,
OTHER1: ,
OTHER2: ,
OTHER3: ,
OTHER4: 12,
OTHER5: April 20, 2019,
DATE5: April 20, 2019,
OTHER6: ,
OTHER7: ,
OTHER8: ,
OTHER9: ,
OTHER10: .`,
        },
      };
      const expectedResponseFr = {
        content: {
          name: 'GDD99 Test Campaign',
          title: 'title',
          details: `
NAME: MR PBANKING TEST1,
SPECIAL_RATE: 2.55%,
SPECIAL_TERM: 1 an,
SPECIAL_PRODUCT: ,
OTHER1: ,
OTHER2: ,
OTHER3: ,
OTHER4: 12,
OTHER5: 20 avril 2019,
DATE5: 20 avril 2019,
OTHER6: ,
OTHER7: ,
OTHER8: ,
OTHER9: ,
OTHER10: .`,
        },
      };
      test('english', () => {
        investmentService.getGicRates.mockResolvedValueOnce(mockInvestmentResponse).mockResolvedValueOnce(mockInvestmentResponse);
        targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightResponse);
        campaignCacheService.get.mockReturnValueOnce(mockCampaignCacheResponse(mockCampaignRuleResponse));
        contentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
        const variablesService = createVariableReplacer({ logger, productBookService, investmentService });
        const server = createServer({
          logger,
          authenticationMiddleware,
          authorizationMiddleware,
          alertService,
          campaignCacheService,
          targetedCampaignService,
          contentService,
          variablesService,
          launchDarklyService,
          ignoredLogRoutes,
          rateLimitingMiddleware,
        }, config);
        return supertest(server)
          .get(`/v1/campaigns/${mockRuleId}?message_id=${mockMessageId}`)
          .set('Content-Type', 'application/json')
          .set('Accept', 'application/json')
          .expect('Content-Type', /json/)
          .expect(200)
          .then((res) => {
            expect(res.body.data.content).toEqual(expectedResponseEn.content);
          });
      });
      test('french', () => {
        investmentService.getGicRates.mockResolvedValueOnce(mockInvestmentResponse).mockResolvedValueOnce(mockInvestmentResponse);
        targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightResponse);
        campaignCacheService.get.mockReturnValueOnce(mockCampaignCacheResponse(mockCampaignRuleResponse));
        contentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
        const variablesService = createVariableReplacer({ logger, productBookService, investmentService });
        const server = createServer({
          logger,
          authenticationMiddleware,
          authorizationMiddleware,
          alertService,
          campaignCacheService,
          targetedCampaignService,
          contentService,
          variablesService,
          launchDarklyService,
          ignoredLogRoutes,
          rateLimitingMiddleware,
        }, config);
        return supertest(server)
          .get(`/v1/campaigns/${mockRuleId}?message_id=${mockMessageId}`)
          .set('Content-Type', 'application/json')
          .set('Accept', 'application/json')
          .set('X-Language', 'fr')
          .expect('Content-Type', /json/)
          .expect(200)
          .then((res) => {
            expect(res.body.data.content).toEqual(expectedResponseFr.content);
          });
      });
      test('failed to get GIC term', () => {
        investmentService.getGicRates.mockResolvedValueOnce(mockInvestmentResponse).mockRejectedValueOnce(new Error('Timeout'));
        targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightResponse);
        campaignCacheService.get.mockReturnValueOnce(mockCampaignCacheResponse(mockCampaignRuleResponse));
        contentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
        const variablesService = createVariableReplacer({ logger, productBookService, investmentService });
        const server = createServer({
          logger,
          authenticationMiddleware,
          authorizationMiddleware,
          alertService,
          campaignCacheService,
          targetedCampaignService,
          contentService,
          variablesService,
          launchDarklyService,
          ignoredLogRoutes,
          rateLimitingMiddleware,
        }, config);
        return supertest(server)
          .get(`/v1/campaigns/${mockRuleId}?message_id=${mockMessageId}`)
          .set('Content-Type', 'application/json')
          .set('Accept', 'application/json')
          .expect('Content-Type', /json/)
          .expect(200)
          .then((res) => {
            expect(res.body.data.content).toEqual({
              ...expectedResponseEn.content, details: `
NAME: MR PBANKING TEST1,
SPECIAL_RATE: 2.55%,
SPECIAL_TERM: ,
SPECIAL_PRODUCT: ,
OTHER1: ,
OTHER2: ,
OTHER3: ,
OTHER4: 12,
OTHER5: April 20, 2019,
DATE5: April 20, 2019,
OTHER6: ,
OTHER7: ,
OTHER8: ,
OTHER9: ,
OTHER10: .`,
            });
          });
      });
      test('failed to get GIC rate', () => {
        investmentService.getGicRates.mockRejectedValueOnce(new Error('Timeout')).mockResolvedValueOnce(mockInvestmentResponse);
        targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightResponse);
        campaignCacheService.get.mockReturnValueOnce(mockCampaignCacheResponse(mockCampaignRuleResponse));
        contentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
        const variablesService = createVariableReplacer({ logger, productBookService, investmentService });
        const server = createServer({
          logger,
          authenticationMiddleware,
          authorizationMiddleware,
          alertService,
          campaignCacheService,
          targetedCampaignService,
          contentService,
          variablesService,
          launchDarklyService,
          ignoredLogRoutes,
          rateLimitingMiddleware,
        }, config);
        return supertest(server)
          .get(`/v1/campaigns/${mockRuleId}?message_id=${mockMessageId}`)
          .set('Content-Type', 'application/json')
          .set('Accept', 'application/json')
          .expect('Content-Type', /json/)
          .expect(200)
          .then((res) => {
            expect(res.body.data.content).toEqual({
              ...expectedResponseEn.content, details: `
NAME: MR PBANKING TEST1,
SPECIAL_RATE: ,
SPECIAL_TERM: 1 year,
SPECIAL_PRODUCT: ,
OTHER1: ,
OTHER2: ,
OTHER3: ,
OTHER4: 12,
OTHER5: April 20, 2019,
DATE5: April 20, 2019,
OTHER6: ,
OTHER7: ,
OTHER8: ,
OTHER9: ,
OTHER10: .`,
            });
          });
      });
    });
  });
});
