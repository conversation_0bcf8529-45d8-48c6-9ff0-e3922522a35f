/* eslint-disable sonarjs/no-duplicate-string */
const supertest = require('supertest');
const { createServer } = require('../../../../src/server');
const { createVariableReplacer } = require('../../../../src/components/variables');
const {
  logger,
  authenticationMiddleware,
  authorizationMiddleware,
  alertService,
  campaignCacheService,
  targetedCampaignService,
  contentService,
  productBookService,
  launchDarklyService,
  mockCampaignCacheResponse,
  ignoredLogRoutes,
  rateLimitingMiddleware,
} = require('../../../mock');

const config = {
  space: 'IST',
  clsNamespace: 'test',
  http: { origin: '*' },
  serviceAuth: {
    tokenPath: 'auth.token',
    tokenClaimsPath: 'auth.claims',
    anonymousClientIds: [ 'testId' ],
  },
  rateLimiting: {
    window: 60000,
    client: { alerts: 5, campaigns: 4 },
    overall: { alerts: 50, campaigns: 40, contentsCatalog: 10 },
  }
};

const mockRuleId = 'HshFUej8x4xx';
const mockMessageId = 'D0123456789ABCDE';

const mockCampaignRuleResponse = {
  id: mockRuleId,
  name: 'Student Migration Test',
  type: 'campaign',
  start_at: '2019-02-14T00:00:00.000Z',
  end_at: '2030-12-31T23:59:59.999Z',
  content_space: '4szkx38resvm',
  content_type: 'targetedCampaign',
  content_id: '1vsOnVrNvWUr251kZbyi6B',
  anonymous: true,
  authenticated: true,
  disabled: false,
  status: 'published',
  created_by: 's4267303',
  updated_by: 's4267303',
  created_at: '2019-04-23T14:59:27.363Z',
  updated_at: '2019-04-24T16:34:51.653Z',
  external_ref: 'SE099',
  container: 'offers-and-programs',
  pages: [ 'accounts' ],
  platforms: [ 'ios', 'android' ],
  app_version: null,
  urgent: false,
};

const mockContentResponse = {
  data: {
    id: '6nemNDXeepdWzwe6wYMRND',
    type: 'targetedCampaignTemplateDetails',
    language: 'en-US',
    created_at: '2019-04-16T14:59:40.277Z',
    updated_at: '2019-04-16T14:59:40.277Z',
    content: {
      name: 'SE099 Test Campaign',
      title: 'title',
      details: `
NAME: SOLUI_NAME_END,
ACCT: SOLUI_ACCT_END,
DATE1: SOLUI_DATE1_END,
OTHER1: SOLUI_OTHER1_END.`,
    },
  },
  notifications: [],
};

describe('Variable Replacement', () => {
  describe('Targeted Campaigns - Student Migration', () => {
    describe('SE099', () => {
      const mockInsightResponse = {
        data: {
          message_id: mockMessageId,
          message_source: 'KT',
          priority: 'N',
          message_response: '',
          subject_line: 'Student Migration',
          language: 'E',
          campaign_type: 'SOL',
          campaign_id: 'SE099',
          start_date: '2019-02-13',
          expiry_date: '2029-12-31',
          conf_number: null,
          additional_data: [
            { name: 'ACCT', value: '600120658227' },
            { name: 'NAME', value: 'MR DEC SPLASHFREL' },
            { name: 'OTHER1', value: 'August 2019' },
          ],
        },
        notifications: [],
      };
      const expectedResponseEn = {
        content: {
          name: 'SE099 Test Campaign',
          title: 'title',
          details: `
NAME: MR DEC SPLASHFREL,
ACCT: \\*\\*\\*\\*\\*\\*\\*\\*8227,
DATE1: August 2019,
OTHER1: August 2019.`,
        },
      };
      const expectedResponseFr = {
        content: {
          name: 'SE099 Test Campaign',
          title: 'title',
          details: `
NAME: MR DEC SPLASHFREL,
ACCT: \\*\\*\\*\\*\\*\\*\\*\\*8227,
DATE1: August 2019,
OTHER1: August 2019.`,
        },
      };
      test('english', () => {
        targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightResponse);
        campaignCacheService.get.mockReturnValueOnce(mockCampaignCacheResponse(mockCampaignRuleResponse));
        contentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
        const variablesService = createVariableReplacer({ logger, productBookService });
        const server = createServer({
          logger,
          authenticationMiddleware,
          authorizationMiddleware,
          alertService,
          campaignCacheService,
          targetedCampaignService,
          contentService,
          variablesService,
          launchDarklyService,
          ignoredLogRoutes,
          rateLimitingMiddleware,
        }, config);
        return supertest(server)
          .get(`/v1/campaigns/${mockRuleId}?message_id=${mockMessageId}`)
          .set('Content-Type', 'application/json')
          .set('Accept', 'application/json')
          .expect('Content-Type', /json/)
          .expect(200)
          .then((res) => {
            expect(res.body.data.content).toEqual(expectedResponseEn.content);
          });
      });
      test('french', () => {
        targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightResponse);
        campaignCacheService.get.mockReturnValueOnce(mockCampaignCacheResponse(mockCampaignRuleResponse));
        contentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
        const variablesService = createVariableReplacer({ logger, productBookService });
        const server = createServer({
          logger,
          authenticationMiddleware,
          authorizationMiddleware,
          alertService,
          campaignCacheService,
          targetedCampaignService,
          contentService,
          variablesService,
          launchDarklyService,
          ignoredLogRoutes,
          rateLimitingMiddleware,
        }, config);
        return supertest(server)
          .get(`/v1/campaigns/${mockRuleId}?message_id=${mockMessageId}`)
          .set('Content-Type', 'application/json')
          .set('Accept', 'application/json')
          .set('X-Language', 'fr')
          .expect('Content-Type', /json/)
          .expect(200)
          .then((res) => {
            expect(res.body.data.content).toEqual(expectedResponseFr.content);
          });
      });
    });
  });
});
