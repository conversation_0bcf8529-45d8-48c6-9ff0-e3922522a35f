/* eslint-disable sonarjs/no-duplicate-string */
const supertest = require('supertest');
const { createServer } = require('../../../../src/server');
const { createVariableReplacer } = require('../../../../src/components/variables');
const {
  logger,
  authenticationMiddleware,
  authorizationMiddleware,
  alertService,
  campaignCacheService,
  targetedCampaignService,
  contentService,
  productBookService,
  launchDarklyService,
  mockCampaignCacheResponse,
  ignoredLogRoutes,
  rateLimitingMiddleware,
} = require('../../../mock');

const config = {
  space: 'IST',
  clsNamespace: 'test',
  http: { origin: '*' },
  serviceAuth: {
    tokenPath: 'auth.token',
    tokenClaimsPath: 'auth.claims',
    anonymousClientIds: [ 'testId' ],
  },
  rateLimiting: {
    window: 60000,
    client: { alerts: 5, campaigns: 4 },
    overall: { alerts: 50, campaigns: 40, contentsCatalog: 10 },
  }
};

const mockRuleId = 'HshFUej8x4xx';
const mockMessageId = 'D0123456789ABCDE';

const mockCampaignRuleResponse = {
  id: mockRuleId,
  name: 'MRV Test',
  type: 'campaign',
  start_at: '2019-02-14T00:00:00.000Z',
  end_at: '2030-12-31T23:59:59.999Z',
  content_space: '4szkx38resvm',
  content_type: 'targetedCampaign',
  content_id: '1vsOnVrNvWUr251kZbyi6B',
  anonymous: true,
  authenticated: true,
  disabled: false,
  status: 'published',
  created_by: 's4267303',
  updated_by: 's4267303',
  created_at: '2019-04-23T14:59:27.363Z',
  updated_at: '2019-04-24T16:34:51.653Z',
  external_ref: 'MRV99',
  container: 'offers-and-programs',
  pages: [ 'accounts' ],
  platforms: [ 'ios', 'android' ],
  app_version: null,
  urgent: false,
};

const mockContentResponse = {
  data: {
    id: '6nemNDXeepdWzwe6wYMRND',
    type: 'targetedCampaignTemplateDetails',
    language: 'en-US',
    created_at: '2019-04-16T14:59:40.277Z',
    updated_at: '2019-04-16T14:59:40.277Z',
    content: {
      name: 'MRV99 Test Campaign',
      title: 'title',
      details: `
NAME: SOLUI_NAME_END,
ACCT: SOLUI_ACCT_END,
OTHER1: SOLUI_OTHER1_END,
OTHER2: SOLUI_OTHER2_END,
OTHER3: SOLUI_OTHER3_END,
OTHER4: SOLUI_OTHER4_END,
OTHER5: SOLUI_OTHER5_END,
OTHER6: SOLUI_OTHER6_END,
OTHER7: SOLUI_OTHER7_END,
OTHER8: SOLUI_OTHER8_END,
OTHER9: SOLUI_OTHER9_END,
OTHER10: SOLUI_OTHER10_END,
OTHER11: SOLUI_OTHER11_END,
OTHER12: SOLUI_OTHER12_END,
OTHER13: SOLUI_OTHER13_END,
DATE13: SOLUI_DATE13_END.`,
    },
  },
  notifications: [],
};

describe('Variable Replacement', () => {
  describe('Targeted Campaigns - Mortgage Renewal', () => {
    describe('MRV99', () => {
      const mockInsightResponse = {
        data: {
          message_id: mockMessageId,
          message_source: 'KT',
          priority: 'N',
          message_response: '',
          subject_line: 'Overdraft Protection',
          language: 'E',
          campaign_type: 'SOL',
          campaign_id: 'MRV99',
          start_date: '2019-02-13',
          expiry_date: '2029-12-31',
          conf_number: null,
          additional_data: [
            { name: 'ACCT', value: '1418356' },
            { name: 'NAME', value: 'MR Pigeon TESTER' },
            { name: 'OTHER1', value: 'MR Pigeon Mapping' },
            { name: 'OTHER2', value: '2019/07/28' },
            { name: 'OTHER3', value: '43' },
            { name: 'OTHER4', value: '5.70%' },
            { name: 'OTHER5', value: '4' },
            { name: 'OTHER6', value: '3.24%' },
            { name: 'OTHER7', value: '2' },
            { name: 'OTHER8', value: '2.49%' },
            { name: 'OTHER9', value: '2020/07/28' },
            { name: 'OTHER10', value: '-0.30' },
            { name: 'OTHER11', value: '-0.10' },
            { name: 'OTHER12', value: '12345678901234' },
            { name: 'OTHER13', value: '2019/05/01' },
          ],
        },
        notifications: [],
      };
      const expectedResponseEn = {
        content: {
          name: 'MRV99 Test Campaign',
          title: 'title',
          details: `
NAME: MR Pigeon TESTER,
ACCT: \\*\\*\\*8356,
OTHER1: MR Pigeon Mapping,
OTHER2: July 28, 2019,
OTHER3: 43,
OTHER4: 5.70%,
OTHER5: ,
OTHER6: ,
OTHER7: ,
OTHER8: ,
OTHER9: July 28, 2020,
OTHER10: ,
OTHER11: ,
OTHER12: ,
OTHER13: May 1, 2019,
DATE13: May 1, 2019.`,
        },
      };
      const expectedResponseFr = {
        content: {
          name: 'MRV99 Test Campaign',
          title: 'title',
          details: `
NAME: MR Pigeon TESTER,
ACCT: \\*\\*\\*8356,
OTHER1: MR Pigeon Mapping,
OTHER2: 28 juillet 2019,
OTHER3: 43,
OTHER4: 5.70%,
OTHER5: ,
OTHER6: ,
OTHER7: ,
OTHER8: ,
OTHER9: 28 juillet 2020,
OTHER10: ,
OTHER11: ,
OTHER12: ,
OTHER13: 1 mai 2019,
DATE13: 1 mai 2019.`,
        },
      };
      test('english', () => {
        targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightResponse);
        campaignCacheService.get.mockReturnValueOnce(mockCampaignCacheResponse(mockCampaignRuleResponse));
        contentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
        const variablesService = createVariableReplacer({ logger, productBookService });
        const server = createServer({
          logger,
          authenticationMiddleware,
          authorizationMiddleware,
          alertService,
          campaignCacheService,
          targetedCampaignService,
          contentService,
          variablesService,
          launchDarklyService,
          ignoredLogRoutes,
          rateLimitingMiddleware,
        }, config);
        return supertest(server)
          .get(`/v1/campaigns/${mockRuleId}?message_id=${mockMessageId}`)
          .set('Content-Type', 'application/json')
          .set('Accept', 'application/json')
          .expect('Content-Type', /json/)
          .expect(200)
          .then((res) => {
            expect(res.body.data.content).toEqual(expectedResponseEn.content);
          });
      });
      test('french', () => {
        targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightResponse);
        campaignCacheService.get.mockReturnValueOnce(mockCampaignCacheResponse(mockCampaignRuleResponse));
        contentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
        const variablesService = createVariableReplacer({ logger, productBookService });
        const server = createServer({
          logger,
          authenticationMiddleware,
          authorizationMiddleware,
          alertService,
          campaignCacheService,
          targetedCampaignService,
          contentService,
          variablesService,
          launchDarklyService,
          ignoredLogRoutes,
          rateLimitingMiddleware,
        }, config);
        return supertest(server)
          .get(`/v1/campaigns/${mockRuleId}?message_id=${mockMessageId}`)
          .set('Content-Type', 'application/json')
          .set('Accept', 'application/json')
          .set('X-Language', 'fr')
          .expect('Content-Type', /json/)
          .expect(200)
          .then((res) => {
            expect(res.body.data.content).toEqual(expectedResponseFr.content);
          });
      });
    });
  });
});
