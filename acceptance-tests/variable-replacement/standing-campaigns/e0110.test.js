/* eslint-disable sonarjs/no-duplicate-string */
const supertest = require('supertest');
const { createServer } = require('../../../src/server');
const { createVariableReplacer } = require('../../../src/components/variables');

const {
  logger,
  authenticationMiddleware,
  authorizationMiddleware,
  alertService,
  campaignCacheService,
  targetedCampaignService,
  contentService,
  investmentService,
  productBookService,
  launchDarklyService,
  mockCampaignCacheResponse,
  ignoredLogRoutes,
  rateLimitingMiddleware,
} = require('../../mock');

const config = {
  space: 'IST',
  clsNamespace: 'test',
  http: { origin: '*' },
  serviceAuth: {
    tokenPath: 'auth.token',
    tokenClaimsPath: 'auth.claims',
    anonymousClientIds: [ 'testId' ],
  },
  rateLimiting: {
    window: 60000,
    client: { alerts: 5, campaigns: 4 },
    overall: { alerts: 50, campaigns: 40, contentsCatalog: 10 },
  }
};

const mockRuleId = 'HshFUej8x4xx';
const mockMessageId = 'D0123456789ABCDE';

const mockCampaignRule = {
  id: mockRuleId,
  name: 'E0110 Test',
  type: 'campaign',
  start_at: '2019-02-14T00:00:00.000Z',
  end_at: '2030-12-31T23:59:59.999Z',
  content_space: '4szkx38resvm',
  content_type: 'standingCampaign',
  content_id: '1vsOnVrNvWUr251kZbyi6B',
  anonymous: true,
  authenticated: true,
  disabled: false,
  status: 'published',
  created_by: 's4267303',
  updated_by: 's4267303',
  created_at: '2019-04-23T14:59:27.363Z',
  updated_at: '2019-04-24T16:34:51.653Z',
  external_ref: 'E0110',
  container: 'offers-and-programs',
  pages: [ 'accounts' ],
  platforms: [ 'ios', 'android' ],
  app_version: null,
  urgent: false,
};

const mockContentResponse = {
  data: {
    id: '6nemNDXeepdWzwe6wYMRND',
    type: 'standingCampaignTemplate2Details',
    language: 'en-US',
    created_at: '2019-04-16T14:59:40.277Z',
    updated_at: '2019-04-16T14:59:40.277Z',
    content: {
      name: 'E0110 Test Campaign',
      details: `
Name: SOLUI_NAME_END,
Reason for rejection: SOLUI_RRC_END,
Payment date: SOLUI_DATE_END,
To: SOLUI_ACCT_END,
From Account: SOLUI_PAYCO_END,
Amount: SOLUI_AMT_END.`,
    },
  },
  notifications: [],
};

describe('Variable Replacement', () => {
  describe('Standing Campaigns', () => {
    describe('E0110', () => {
      const mockInsight = {
        data: {
          message_id: mockMessageId,
          message_source: 'KT',
          priority: 'N',
          message_response: '',
          subject_line: 'Important information regarding the status of your online application.',
          language: 'E',
          campaign_type: 'SOL',
          campaign_id: 'E0110',
          start_date: '2019-02-13',
          expiry_date: '2019-07-31',
          conf_number: null,
          additional_data: [
            { name: 'NAME', value: 'MR TEST UAT18' },
            { name: 'date', value: '2019-03-04' },
            { name: 'acct', value: '************' },
            { name: 'amt', value: '11.11' },
            { name: 'payco', value: 'SCOTIAVALUEVISA' },
            { name: 'adc', value: 'Scotia OnLine' },
            { name: 'rrc', value: 'Available Balance Exceeded' },
          ],
        },
        notifications: [],
      };
      const expectedResponseEn = {
        content: {
          name: 'E0110 Test Campaign',
          details: `
Name: MR TEST UAT18,
Reason for rejection: Available Balance Exceeded,
Payment date: March 4, 2019,
To: \\*\\*\\*\\*\\*\\*\\*\\*7525,
From Account: SCOTIAVALUEVISA,
Amount: $11.11.`,
        },
      };
      const expectedResponseFr = {
        content: {
          name: 'E0110 Test Campaign',
          details: `
Name: MR TEST UAT18,
Reason for rejection: Available Balance Exceeded,
Payment date: 4 mars 2019,
To: \\*\\*\\*\\*\\*\\*\\*\\*7525,
From Account: SCOTIAVALUEVISA,
Amount: 11,11 $.`,
        },
      };
      test('english', () => {
        targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsight);
        campaignCacheService.get.mockReturnValueOnce(mockCampaignCacheResponse(mockCampaignRule));
        contentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
        const variablesService = createVariableReplacer({ logger, productBookService, investmentService });
        const server = createServer({
          logger,
          authenticationMiddleware,
          authorizationMiddleware,
          alertService,
          campaignCacheService,
          targetedCampaignService,
          contentService,
          variablesService,
          launchDarklyService,
          ignoredLogRoutes,
          rateLimitingMiddleware,
        }, config);
        return supertest(server)
          .get(`/v1/campaigns/${mockRuleId}?message_id=${mockMessageId}`)
          .set('Content-Type', 'application/json')
          .set('Accept', 'application/json')
          .set('X-Language', 'en')
          .expect('Content-Type', /json/)
          .expect(200)
          .then((res) => {
            expect(res.body.data.content).toEqual(expectedResponseEn.content);
          });
      });
      test('french', () => {
        targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsight);
        campaignCacheService.get.mockReturnValueOnce(mockCampaignCacheResponse(mockCampaignRule));
        contentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
        const variablesService = createVariableReplacer({ logger, productBookService, investmentService });
        const server = createServer({
          logger,
          authenticationMiddleware,
          authorizationMiddleware,
          alertService,
          campaignCacheService,
          targetedCampaignService,
          contentService,
          variablesService,
          launchDarklyService,
          ignoredLogRoutes,
          rateLimitingMiddleware,
        }, config);
        return supertest(server)
          .get(`/v1/campaigns/${mockRuleId}?message_id=${mockMessageId}`)
          .set('Content-Type', 'application/json')
          .set('Accept', 'application/json')
          .set('X-Language', 'fr')
          .expect('Content-Type', /json/)
          .expect(200)
          .then((res) => {
            expect(res.body.data.content).toEqual(expectedResponseFr.content);
          });
      });
    });
  });
});
