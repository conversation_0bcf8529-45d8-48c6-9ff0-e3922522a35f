/* eslint-disable sonarjs/no-identical-functions */
/* eslint-disable sonarjs/no-duplicate-string */
const supertest = require('supertest');
const { createServer } = require('../../../src/server');
const { createVariableReplacer } = require('../../../src/components/variables');

const {
  logger,
  authenticationMiddleware,
  authorizationMiddleware,
  alertService,
  campaignCacheService,
  targetedCampaignService,
  contentService,
  investmentService,
  productBookService,
  launchDarklyService,
  mockCampaignCacheResponse,
  ignoredLogRoutes,
  rateLimitingMiddleware,
} = require('../../mock');

const config = {
  space: 'IST',
  clsNamespace: 'test',
  http: { origin: '*' },
  serviceAuth: {
    tokenPath: 'auth.token',
    tokenClaimsPath: 'auth.claims',
    anonymousClientIds: [ 'testId' ],
  },
  rateLimiting: {
    window: 60000,
    client: { alerts: 5, campaigns: 4 },
    overall: { alerts: 50, campaigns: 40, contentsCatalog: 10 },
  }
};

const mockRuleId = 'HshFUej8x4xx';
const mockMessageId = 'D0123456789ABCDE';

const mockCampaignRule = {
  id: mockRuleId,
  name: 'CCV06 Test',
  type: 'campaign',
  start_at: '2019-02-14T00:00:00.000Z',
  end_at: '2030-12-31T23:59:59.999Z',
  content_space: '4szkx38resvm',
  content_type: 'standingCampaign',
  content_id: '1vsOnVrNvWUr251kZbyi6B',
  anonymous: true,
  authenticated: true,
  disabled: false,
  status: 'published',
  created_by: 's4267303',
  updated_by: 's4267303',
  created_at: '2019-04-23T14:59:27.363Z',
  updated_at: '2019-04-24T16:34:51.653Z',
  external_ref: 'CCV06',
  container: 'offers-and-programs',
  pages: [ 'accounts' ],
  platforms: [ 'ios', 'android' ],
  app_version: null,
  urgent: false,
};

const mockContentResponse = {
  data: {
    id: '6nemNDXeepdWzwe6wYMRND',
    type: 'standingCampaignTemplate2Details',
    language: 'en-US',
    created_at: '2019-04-16T14:59:40.277Z',
    updated_at: '2019-04-16T14:59:40.277Z',
    content: {
      name: 'CCV06 Test Campaign',
      details: `
CREDIT: SOLUI_CREDIT_END,
PROD: SOLUI_PROD_END,
OTHER1: SOLUI_OTHER1_END,
REASON: SOLUI_REASON_END,
OTHER2: SOLUI_OTHER2_END,
OTHER3: SOLUI_OTHER3_END,
OTHER4: SOLUI_OTHER4_END.`,
    },
  },
  notifications: [],
};

describe('Variable Replacement', () => {
  describe('Standing Campaigns', () => {
    describe('CCV6', () => {
      describe('with VIL4 reason code', () => {
        const mockInsight = {
          data: {
            message_id: mockMessageId,
            message_source: 'KT',
            priority: 'N',
            message_response: '',
            subject_line: 'Important information regarding the status of your online application.',
            language: 'E',
            campaign_type: 'SOL',
            campaign_id: 'CCV06',
            start_date: '2019-02-13',
            expiry_date: '2019-07-31',
            conf_number: null,
            additional_data: [
              { name: 'PROD', value: 'VILG' },
              { name: 'CREDIT', value: '001000' },
              { name: 'OTHER1', value: 'CCV030000000' },
              { name: 'OTHER2', value: 'VIL4' },
              { name: 'OTHER3', value: '' },
              { name: 'OTHER4', value: '**********' },
            ],
          },
          notifications: [],
        };
        const expectedResponseEn = {
          content: {
            name: 'CCV06 Test Campaign',
            details: `
CREDIT: $1,000,
PROD: VILG,
OTHER1: CCV030000000,
REASON: Please fax your verification toll free to [**************](tel:+***********) or mail it to Scotiabank directly at P.O. Box 4100, Toronto, Ontario, M5W 1T1. Please include the reference number noted above.,
OTHER2: Please fax your verification toll free to [**************](tel:+***********) or mail it to Scotiabank directly at P.O. Box 4100, Toronto, Ontario, M5W 1T1. Please include the reference number noted above.,
OTHER3: ,
OTHER4: **********.`,
          },
        };
        const expectedResponseFr = {
          content: {
            name: 'CCV06 Test Campaign',
            details: `
CREDIT: 1 000 $,
PROD: VILG,
OTHER1: CCV030000000,
REASON: Veuillez envoyer vos documents de vérification directement à la Banque Scotia par télécopieur au numéro sans frais [**************](tel:+***********) ou par la poste à l'adresse suivante : C.P. 4100, Toronto (Ontario) M5W 1T1. N'oubliez pas d'inclure le numéro de référence tel qu'il figure ci-dessus.,
OTHER2: Veuillez envoyer vos documents de vérification directement à la Banque Scotia par télécopieur au numéro sans frais [**************](tel:+***********) ou par la poste à l'adresse suivante : C.P. 4100, Toronto (Ontario) M5W 1T1. N'oubliez pas d'inclure le numéro de référence tel qu'il figure ci-dessus.,
OTHER3: ,
OTHER4: **********.`,
          },
        };
        test('english', () => {
          targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsight);
          launchDarklyService.mockReset();
          campaignCacheService.get.mockReturnValueOnce(mockCampaignCacheResponse(mockCampaignRule));
          contentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
          const variablesService = createVariableReplacer({ logger, productBookService, investmentService });
          const server = createServer({
            logger,
            authenticationMiddleware,
            authorizationMiddleware,
            alertService,
            campaignCacheService,
            targetedCampaignService,
            contentService,
            variablesService,
            launchDarklyService,
            ignoredLogRoutes,
            rateLimitingMiddleware,
          }, config);
          return supertest(server)
            .get(`/v1/campaigns/${mockRuleId}?message_id=${mockMessageId}`)
            .set('Content-Type', 'application/json')
            .set('Accept', 'application/json')
            .set('X-Language', 'en')
            .expect('Content-Type', /json/)
            .expect(200)
            .then((res) => {
              expect(res.body.data.content).toEqual(expectedResponseEn.content);
            });
        });
        test('french', () => {
          targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsight);
          campaignCacheService.get.mockReturnValueOnce(mockCampaignCacheResponse(mockCampaignRule));
          contentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
          const variablesService = createVariableReplacer({ logger, productBookService, investmentService });
          const server = createServer({
            logger,
            authenticationMiddleware,
            authorizationMiddleware,
            alertService,
            campaignCacheService,
            targetedCampaignService,
            contentService,
            variablesService,
            launchDarklyService,
            ignoredLogRoutes,
            rateLimitingMiddleware,
          }, config);
          return supertest(server)
            .get(`/v1/campaigns/${mockRuleId}?message_id=${mockMessageId}`)
            .set('Content-Type', 'application/json')
            .set('Accept', 'application/json')
            .set('X-Language', 'fr')
            .expect('Content-Type', /json/)
            .expect(200)
            .then((res) => {
              expect(res.body.data.content).toEqual(expectedResponseFr.content);
            });
        });
      });
      describe('with ODP6 reason code', () => {
        const mockInsight = {
          data: {
            message_id: mockMessageId,
            message_source: 'KT',
            priority: 'N',
            message_response: '',
            subject_line: 'Important information regarding the status of your online application.',
            language: 'E',
            campaign_type: 'SOL',
            campaign_id: 'CCV06',
            start_date: '2019-02-13',
            expiry_date: '2019-07-31',
            conf_number: null,
            additional_data: [
              { name: 'PROD', value: 'VILG' },
              { name: 'CREDIT', value: '001000' },
              { name: 'OTHER1', value: 'CCV030000000' },
              { name: 'OTHER2', value: 'ODP6' },
              { name: 'OTHER3', value: '' },
              { name: 'OTHER4', value: '**********' },
            ],
          },
          notifications: [],
        };
        const expectedResponseEn = {
          content: {
            name: 'CCV06 Test Campaign',
            details: `
CREDIT: $1,000,
PROD: VILG,
OTHER1: CCV030000000,
REASON: Please fax your verification toll free to [**************](tel:+***********) or mail it to Scotiabank directly at P.O. Box 4100, Toronto, Ontario, M5W 1T1. Please include the reference number noted above.,
OTHER2: Please fax your verification toll free to [**************](tel:+***********) or mail it to Scotiabank directly at P.O. Box 4100, Toronto, Ontario, M5W 1T1. Please include the reference number noted above.,
OTHER3: ,
OTHER4: **********.`,
          },
        };
        const expectedResponseFr = {
          content: {
            name: 'CCV06 Test Campaign',
            details: `
CREDIT: 1 000 $,
PROD: VILG,
OTHER1: CCV030000000,
REASON: Veuillez envoyer vos documents de vérification directement à la Banque Scotia par télécopieur au numéro sans frais [**************](tel:+***********) ou par la poste à l'adresse suivante : C.P. 4100, Toronto (Ontario) M5W 1T1. N'oubliez pas d'inclure le numéro de référence tel qu'il figure ci-dessus.,
OTHER2: Veuillez envoyer vos documents de vérification directement à la Banque Scotia par télécopieur au numéro sans frais [**************](tel:+***********) ou par la poste à l'adresse suivante : C.P. 4100, Toronto (Ontario) M5W 1T1. N'oubliez pas d'inclure le numéro de référence tel qu'il figure ci-dessus.,
OTHER3: ,
OTHER4: **********.`,
          },
        };
        test('english', () => {
          targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsight);
          campaignCacheService.get.mockReturnValueOnce(mockCampaignCacheResponse(mockCampaignRule));
          contentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
          const variablesService = createVariableReplacer({ logger, productBookService, investmentService });
          const server = createServer({
            logger,
            authenticationMiddleware,
            authorizationMiddleware,
            alertService,
            campaignCacheService,
            targetedCampaignService,
            contentService,
            variablesService,
            launchDarklyService,
            ignoredLogRoutes,
            rateLimitingMiddleware,
          }, config);
          return supertest(server)
            .get(`/v1/campaigns/${mockRuleId}?message_id=${mockMessageId}`)
            .set('Content-Type', 'application/json')
            .set('Accept', 'application/json')
            .expect('Content-Type', /json/)
            .expect(200)
            .then((res) => {
              expect(res.body.data.content).toEqual(expectedResponseEn.content);
            });
        });
        test('french', () => {
          targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsight);
          campaignCacheService.get.mockReturnValueOnce(mockCampaignCacheResponse(mockCampaignRule));
          contentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
          const variablesService = createVariableReplacer({ logger, productBookService, investmentService });
          const server = createServer({
            logger,
            authenticationMiddleware,
            authorizationMiddleware,
            alertService,
            campaignCacheService,
            targetedCampaignService,
            contentService,
            variablesService,
            launchDarklyService,
            ignoredLogRoutes,
            rateLimitingMiddleware,
          }, config);
          return supertest(server)
            .get(`/v1/campaigns/${mockRuleId}?message_id=${mockMessageId}`)
            .set('Content-Type', 'application/json')
            .set('Accept', 'application/json')
            .set('X-Language', 'fr')
            .expect('Content-Type', /json/)
            .expect(200)
            .then((res) => {
              expect(res.body.data.content).toEqual(expectedResponseFr.content);
            });
        });
      });
      describe('with INS3 reason code', () => {
        const mockInsight = {
          data: {
            message_id: mockMessageId,
            message_source: 'KT',
            priority: 'N',
            message_response: '',
            subject_line: 'Important information regarding the status of your online application.',
            language: 'E',
            campaign_type: 'SOL',
            campaign_id: 'CCV06',
            start_date: '2019-02-13',
            expiry_date: '2019-07-31',
            conf_number: null,
            additional_data: [
              { name: 'PROD', value: 'VILG' },
              { name: 'CREDIT', value: '001000' },
              { name: 'OTHER1', value: 'CCV030000000' },
              { name: 'OTHER2', value: 'INS3' },
              { name: 'OTHER3', value: '' },
              { name: 'OTHER4', value: '**********' },
            ],
          },
          notifications: [],
        };
        const expectedResponseEn = {
          content: {
            name: 'CCV06 Test Campaign',
            details: `
CREDIT: $1,000,
PROD: VILG,
OTHER1: CCV030000000,
REASON: Please fax your verification toll free to [**************](tel:+***********) or mail it to Scotiabank directly at P.O. Box 4100, Toronto, Ontario, M5W 1T1. Please include the reference number noted above.\n\nYour VISA Balance Protection has also been approved. You've taken a very important step in your financial planning by adding VISA Balance Protection to your borrowing strategy.,
OTHER2: Please fax your verification toll free to [**************](tel:+***********) or mail it to Scotiabank directly at P.O. Box 4100, Toronto, Ontario, M5W 1T1. Please include the reference number noted above.\n\nYour VISA Balance Protection has also been approved. You've taken a very important step in your financial planning by adding VISA Balance Protection to your borrowing strategy.,
OTHER3: ,
OTHER4: **********.`,
          },
        };
        const expectedResponseFr = {
          content: {
            name: 'CCV06 Test Campaign',
            details: `
CREDIT: 1 000 $,
PROD: VILG,
OTHER1: CCV030000000,
REASON: Veuillez faire parvenir les documents de vérification à la Banque Scotia par télécopieur au numéro sans frais [**************](tel:+***********) ou par la poste à l'adresse suivante : C.P. 4100, Toronto (Ontario) M5W 1T1. N'oubliez pas d'indiquer le numéro de référence cité en rubrique.\n\nVotre demande d'assurance solde VISA a également été approuvée. Vous avez franchi une étape importante dans le cadre de votre planification financière en complétant votre stratégie d'emprunt avec une assurance solde VISA.,
OTHER2: Veuillez faire parvenir les documents de vérification à la Banque Scotia par télécopieur au numéro sans frais [**************](tel:+***********) ou par la poste à l'adresse suivante : C.P. 4100, Toronto (Ontario) M5W 1T1. N'oubliez pas d'indiquer le numéro de référence cité en rubrique.\n\nVotre demande d'assurance solde VISA a également été approuvée. Vous avez franchi une étape importante dans le cadre de votre planification financière en complétant votre stratégie d'emprunt avec une assurance solde VISA.,
OTHER3: ,
OTHER4: **********.`,
          },
        };
        test('english', () => {
          targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsight);
          campaignCacheService.get.mockReturnValueOnce(mockCampaignCacheResponse(mockCampaignRule));
          contentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
          const variablesService = createVariableReplacer({ logger, productBookService, investmentService });
          const server = createServer({
            logger,
            authenticationMiddleware,
            authorizationMiddleware,
            alertService,
            campaignCacheService,
            targetedCampaignService,
            contentService,
            variablesService,
            launchDarklyService,
            ignoredLogRoutes,
            rateLimitingMiddleware,
          }, config);
          return supertest(server)
            .get(`/v1/campaigns/${mockRuleId}?message_id=${mockMessageId}`)
            .set('Content-Type', 'application/json')
            .set('Accept', 'application/json')
            .expect('Content-Type', /json/)
            .expect(200)
            .then((res) => {
              expect(res.body.data.content).toEqual(expectedResponseEn.content);
            });
        });
        test('french', () => {
          targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsight);
          campaignCacheService.get.mockReturnValueOnce(mockCampaignCacheResponse(mockCampaignRule));
          contentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
          const variablesService = createVariableReplacer({ logger, productBookService, investmentService });
          const server = createServer({
            logger,
            authenticationMiddleware,
            authorizationMiddleware,
            alertService,
            campaignCacheService,
            targetedCampaignService,
            contentService,
            variablesService,
            ignoredLogRoutes,
            rateLimitingMiddleware,
          }, config);
          return supertest(server)
            .get(`/v1/campaigns/${mockRuleId}?message_id=${mockMessageId}`)
            .set('Content-Type', 'application/json')
            .set('Accept', 'application/json')
            .set('X-Language', 'fr')
            .expect('Content-Type', /json/)
            .expect(200)
            .then((res) => {
              expect(res.body.data.content).toEqual(expectedResponseFr.content);
            });
        });
      });
      describe('with INS4 reason code', () => {
        const mockInsight = {
          data: {
            message_id: mockMessageId,
            message_source: 'KT',
            priority: 'N',
            message_response: '',
            subject_line: 'Important information regarding the status of your online application.',
            language: 'E',
            campaign_type: 'SOL',
            campaign_id: 'CCV06',
            start_date: '2019-02-13',
            expiry_date: '2019-07-31',
            conf_number: null,
            additional_data: [
              { name: 'PROD', value: 'VILG' },
              { name: 'CREDIT', value: '001000' },
              { name: 'OTHER1', value: 'CCV030000000' },
              { name: 'OTHER2', value: 'INS4' },
              { name: 'OTHER3', value: '' },
              { name: 'OTHER4', value: '**********' },
            ],
          },
          notifications: [],
        };
        const expectedResponseEn = {
          content: {
            name: 'CCV06 Test Campaign',
            details: `
CREDIT: $1,000,
PROD: VILG,
OTHER1: CCV030000000,
REASON: Please fax your verification toll free to [**************](tel:+***********) or mail it to Scotiabank directly at P.O. Box 4100, Toronto, Ontario, M5W 1T1. Please include the reference number noted above.\n\nYour Line of Credit Protection has also been approved. You've taken a very important step in your financial planning by adding Line of Credit Protection to your borrowing strategy.,
OTHER2: Please fax your verification toll free to [**************](tel:+***********) or mail it to Scotiabank directly at P.O. Box 4100, Toronto, Ontario, M5W 1T1. Please include the reference number noted above.\n\nYour Line of Credit Protection has also been approved. You've taken a very important step in your financial planning by adding Line of Credit Protection to your borrowing strategy.,
OTHER3: ,
OTHER4: **********.`,
          },
        };
        const expectedResponseFr = {
          content: {
            name: 'CCV06 Test Campaign',
            details: `
CREDIT: 1 000 $,
PROD: VILG,
OTHER1: CCV030000000,
REASON: Veuillez faire parvenir les documents de vérification à la Banque Scotia par télécopieur au numéro sans frais [**************](tel:+***********) ou par la poste à l'adresse suivante : C.P. 4100, Toronto (Ontario) M5W 1T1. N'oubliez pas d'indiquer le numéro de référence cité en rubrique.\n\nVotre demande d'assurance-créances pour votre ligne de crédit a également été approuvée. Vous avez franchi une étape importante dans le cadre de votre planification financière en complétant votre stratégie d'emprunt avec une assurance-créances pour votre ligne de crédit.,
OTHER2: Veuillez faire parvenir les documents de vérification à la Banque Scotia par télécopieur au numéro sans frais [**************](tel:+***********) ou par la poste à l'adresse suivante : C.P. 4100, Toronto (Ontario) M5W 1T1. N'oubliez pas d'indiquer le numéro de référence cité en rubrique.\n\nVotre demande d'assurance-créances pour votre ligne de crédit a également été approuvée. Vous avez franchi une étape importante dans le cadre de votre planification financière en complétant votre stratégie d'emprunt avec une assurance-créances pour votre ligne de crédit.,
OTHER3: ,
OTHER4: **********.`,
          },
        };
        test('english', () => {
          targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsight);
          campaignCacheService.get.mockReturnValueOnce(mockCampaignCacheResponse(mockCampaignRule));
          contentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
          const variablesService = createVariableReplacer({ logger, productBookService, investmentService });
          const server = createServer({
            logger,
            authenticationMiddleware,
            authorizationMiddleware,
            alertService,
            campaignCacheService,
            targetedCampaignService,
            contentService,
            variablesService,
            ignoredLogRoutes,
            rateLimitingMiddleware,
          }, config);
          return supertest(server)
            .get(`/v1/campaigns/${mockRuleId}?message_id=${mockMessageId}`)
            .set('Content-Type', 'application/json')
            .set('Accept', 'application/json')
            .expect('Content-Type', /json/)
            .expect(200)
            .then((res) => {
              expect(res.body.data.content).toEqual(expectedResponseEn.content);
            });
        });
        test('french', () => {
          targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsight);
          campaignCacheService.get.mockReturnValueOnce(mockCampaignCacheResponse(mockCampaignRule));
          contentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
          const variablesService = createVariableReplacer({ logger, productBookService, investmentService });
          const server = createServer({
            logger,
            authenticationMiddleware,
            authorizationMiddleware,
            alertService,
            campaignCacheService,
            targetedCampaignService,
            contentService,
            variablesService,
            launchDarklyService,
            ignoredLogRoutes,
            rateLimitingMiddleware,
          }, config);
          return supertest(server)
            .get(`/v1/campaigns/${mockRuleId}?message_id=${mockMessageId}`)
            .set('Content-Type', 'application/json')
            .set('Accept', 'application/json')
            .set('X-Language', 'fr')
            .expect('Content-Type', /json/)
            .expect(200)
            .then((res) => {
              expect(res.body.data.content).toEqual(expectedResponseFr.content);
            });
        });
      });
    });
  });
});
