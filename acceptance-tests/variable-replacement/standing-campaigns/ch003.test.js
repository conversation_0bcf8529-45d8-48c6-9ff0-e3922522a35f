/* eslint-disable sonarjs/no-duplicate-string */
const supertest = require('supertest');
const { createServer } = require('../../../src/server');
const { createVariableReplacer } = require('../../../src/components/variables');

const {
  logger,
  authenticationMiddleware,
  authorizationMiddleware,
  alertService,
  campaignCacheService,
  targetedCampaignService,
  contentService,
  investmentService,
  productBookService,
  launchDarklyService,
  mockCampaignCacheResponse,
  ignoredLogRoutes,
  rateLimitingMiddleware
} = require('../../mock');

const config = {
  space: 'IST',
  clsNamespace: 'test',
  http: { origin: '*' },
  serviceAuth: {
    tokenPath: 'auth.token',
    tokenClaimsPath: 'auth.claims',
    anonymousClientIds: [ 'testId' ],
  },
  rateLimiting: {
    window: 60000,
    client: { alerts: 5, campaigns: 4 },
    overall: { alerts: 50, campaigns: 40, contentsCatalog: 10 },
  }
};

const mockRuleId = 'GEu5cBsM9hgH';
const mockMessageId = 'D0O00O0OOOOCH003';

const mockCampaignRule = {
  id: mockRuleId,
  name: 'CH003_AutoSwitch3',
  type: 'standingCampaignTemplate1Details',
  start_at: '2020-11-10T05:00:00.000Z',
  end_at: '2030-12-31T05:00:00.000Z',
  content_space: '4szkx38resvm',
  content_type: 'standingCampaign',
  content_id: 'sktwic33jtq65kpd',
  disabled: false,
  status: 'published',
  created_by: 's3894179',
  updated_by: 's3894179',
  created_at: '2020-12-11T15:48:43.347Z',
  updated_at: '2020-12-11T15:48:50.780Z',
  external_ref: 'CH003',
  container: 'offers-and-programs-it',
  pages: [ 'accounts-it' ],
  platforms: [ 'ios', 'android' ],
  app_version: null,
  type: 'campaign',
  urgent: false,
};

const mockContentResponse = {
  data: {
    id: 'sktwic33jtq65kpd',
    type: 'standingCampaignTemplate1Details',
    language: 'en-US',
    created_at: '2019-03-14T21:55:39.879Z',
    updated_at: '2019-05-28T20:28:32.632Z',
    content: {
      name: 'CH003 Test Campaign',
      details: `
Name: SOLUI_NAME_END`,
    },
  },
  notifications: [],
};

describe('Variable Replacement', () => {
  describe('Standing Campaigns', () => {
    describe('CH003', () => {
      const mockInsight = {
        data: { // Campaign
          message_id: mockMessageId,
          message_source: 'KT',
          priority: 'N',
          message_response: 'V',
          subject_line: 'AutoSwitch - Activate your new ScotiaCard',
          language: 'en',
          campaign_id: 'CH003',
          start_date: '2020-11-09',
          expiry_date: '2022-12-27',
          conf_number: null,
          additional_data: [
            { name: 'NAME', value: 'MR TESTER IST' },
            { name: 'date', value: '2019-03-04' }, // Not rendered in expected response
          ],
        },
        notifications: [],
      };
      const expectedResponseEn = {
        content: {
          name: 'CH003 Test Campaign',
          details: `
Name: MR TESTER IST`,
        },
      };
      const expectedResponseFr = {
        content: {
          name: 'CH003 Test Campaign',
          details: `
Name: MR TESTER IST`,
        },
      };
      test('english', () => {
        targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsight);
        campaignCacheService.get.mockReturnValueOnce(mockCampaignCacheResponse(mockCampaignRule));
        contentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
        const variablesService = createVariableReplacer({ logger, productBookService, investmentService });
        const server = createServer({
          logger,
          authenticationMiddleware,
          authorizationMiddleware,
          alertService,
          campaignCacheService,
          targetedCampaignService,
          contentService,
          variablesService,
          launchDarklyService,
          ignoredLogRoutes,
          rateLimitingMiddleware
        }, config);
        return supertest(server)
          .get(`/v1/campaigns/${mockRuleId}?message_id=${mockMessageId}`)
          .set('Content-Type', 'application/json')
          .set('Accept', 'application/json')
          .set('X-Language', 'en')
          .expect('Content-Type', /json/)
          .expect(200)
          .then((res) => {
            expect(res.body.data.content).toEqual(expectedResponseEn.content);
          });
      });
      test('french', () => {
        targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsight);
        campaignCacheService.get.mockReturnValueOnce(mockCampaignCacheResponse(mockCampaignRule));
        contentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
        const variablesService = createVariableReplacer({ logger, productBookService, investmentService });
        const server = createServer({
          logger,
          authenticationMiddleware,
          authorizationMiddleware,
          alertService,
          campaignCacheService,
          targetedCampaignService,
          contentService,
          variablesService,
          launchDarklyService,
          ignoredLogRoutes,
          rateLimitingMiddleware
        }, config);
        return supertest(server)
          .get(`/v1/campaigns/${mockRuleId}?message_id=${mockMessageId}`)
          .set('Content-Type', 'application/json')
          .set('Accept', 'application/json')
          .set('X-Language', 'fr')
          .expect('Content-Type', /json/)
          .expect(200)
          .then((res) => {
            expect(res.body.data.content).toEqual(expectedResponseFr.content);
          });
      });
    });
  });
});
