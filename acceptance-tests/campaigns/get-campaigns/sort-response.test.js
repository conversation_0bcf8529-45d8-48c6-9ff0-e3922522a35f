/* eslint-disable sonarjs/no-duplicate-string */
const R = require('ramda');
const supertest = require('supertest');
const { createServer } = require('../../../src/server');
const initLaunchDarkly = require('../../../src/components/launch-darkly');
const { createVariableReplacer } = require('../../../src/components/variables');
const {
  logger,
  authenticationMiddleware,
  authorizationMiddleware,
  alertService,
  campaignCacheService,
  containerCacheService,
  targetedCampaignService,
  dispositionsService,
  contentService,
  investmentService,
  productBookService,
  marvelService,
  mockCampaignCacheResponse,
  ignoredLogRoutes,
  rateLimitingMiddleware,
} = require('../../mock');

const config = {
  space: 'IST',
  hostingEnv: 'PCF',
  clsNamespace: 'test',
  http: { origin: '*' },
  serviceAuth: {
    tokenPath: 'auth.token',
    tokenClaimsPath: 'auth.claims',
    anonymousClientIds: [ 'testId' ],
  },
  rateLimiting: {
    window: 60000,
    client: { alerts: 5, campaigns: 4 },
    overall: { alerts: 50, campaigns: 40 },
  },
};

// urgent priority message
const mockRule = ({ index, ...props }) => ({
  id: `mockid${index}`,
  name: `mock rule #${index}`,
  type: 'campaign',
  start_at: '2019-02-14T00:00:00.000Z',
  end_at: '2030-12-31T23:59:59.999Z',
  content_space: '4szkx38resvm',
  content_type: 'priorityMessage',
  content_id: `fakecontent${index}`,
  status: 'published',
  created_by: 's4267303',
  updated_by: 's4267303',
  created_at: '2019-04-23T14:59:27.363Z',
  updated_at: '2019-04-24T16:34:51.653Z',
  container: 'offers-and-programs',
  anonymous: false,
  disabled: false,
  authenticated: true,
  external_ref: 'MESSAGE',
  pages: [ 'accounts' ],
  platforms: [ 'ios', 'android' ],
  mass_targeting: {},
  app_version: null,
  urgent: false,
  ...props,
});

const rules = [
  mockRule({ // mass campaign, new, non-urgent
    index: 0,
    external_ref: 'MASS',
  }),
  mockRule({ // mass campaign, new, urgent, recent
    index: 1,
    external_ref: 'MASS',
    urgent: true,
    start_at: '2021-05-15T00:00:00.000Z',
  }),
  mockRule({ // mass campaign, new, urgent, old
    index: 2,
    external_ref: 'MASS',
    urgent: true,
  }),
  mockRule({ // mass campaign, viewed, non-urgent
    index: 3,
    external_ref: 'MASS',
  }),
  mockRule({ // mass campaign, viewed, urgent, old
    index: 4,
    external_ref: 'MASS',
    urgent: true,
  }),
  mockRule({ // mass campaign, viewed, urgent, recent
    index: 5,
    external_ref: 'MASS',
    urgent: true,
    start_at: '2021-05-15T00:00:00.000Z',
  }),
  mockRule({ // priority message, new, non-urgent
    index: 6,
  }),
  mockRule({ // priority message, new, urgent, recent
    index: 7,
    urgent: true,
    start_at: '2021-05-15T00:00:00.000Z',
  }),
  mockRule({ // priority message, new, urgent, old
    index: 8,
    urgent: true,
  }),
  mockRule({ // priority message, viewed, non-urgent
    index: 9,
  }),
  mockRule({ // priority message, viewed, urgent, old
    index: 10,
    urgent: true,
  }),
  mockRule({ // priority message, viewed, urgent, recent
    index: 11,
    urgent: true,
    start_at: '2021-05-15T00:00:00.000Z',
  }),
  mockRule({ // targeted, viewed, non-urgent
    index: 12,
    external_ref: 'CCV01',
    mass_targeting: {
      'v': 1,
      'by_product': {
        'any_of': [ { 'ownership': 'R', 'code': 'ABC', 'sub_code': 'EF' } ],
      },
    },
  }),
  mockRule({ // targeted, new, non-urgent
    index: 13,
    external_ref: 'CCV01',
    mass_targeting: {
      'v': 1,
      'by_product': {
        'any_of': [ { 'ownership': 'R', 'code': 'ABC', 'sub_code': 'EF' } ],
      },
    },
  }),
  mockRule({ // targeted, viewed, urgent
    index: 14,
    external_ref: 'CCV02',
    mass_targeting: {
      'v': 1,
      'by_product': {
        'any_of': [ { 'ownership': 'R', 'code': 'ABC', 'sub_code': 'EF' } ],
      },
    },
    urgent: true,
  }),
  mockRule({ // targeted, new, urgent
    index: 15,
    external_ref: 'CCV02',
    mass_targeting: {
      'v': 1,
      'by_product': {
        'any_of': [ { 'ownership': 'R', 'code': 'ABC', 'sub_code': 'EF' } ],
      },
    },
    urgent: true,
  }),
];

const mockInsightResponse = {
  data: [
    {
      message_id: '********',
      message_source: 'KT',
      message_status: 'N',
      priority: 'N',
      message_response: '',
      subject_line: 'Important information regarding the status of your online application.',
      language: 'E',
      campaign_type: 'SOL',
      campaign_id: 'CCV01',
      start_date: '2019-02-10',
      expiry_date: '2019-07-31',
      conf_number: null,
      additional_data: [
        { name: 'PROD', value: 'VILG' },
        { name: 'OTHER1', value: 'CCV030000000' },
        { name: 'OTHER2', value: 'SAM1' },
      ],
    },
    {
      message_id: '********',
      message_source: 'KT',
      message_status: 'N',
      priority: 'N',
      message_response: '',
      subject_line: 'Important information regarding the status of your online application.',
      language: 'E',
      campaign_type: 'SOL',
      campaign_id: 'CCV02',
      start_date: '2019-02-16',
      expiry_date: '2019-07-31',
      conf_number: null,
      additional_data: [
        { name: 'PROD', value: 'VILG' },
        { name: 'OTHER1', value: 'CCV030000000' },
        { name: 'OTHER2', value: 'SAM1' },
      ],
    },
  ],
  notifications: null,
};

const mockMarvelResponse = {
  accountList: [ { 'ownership': 'R', 'ciProductCode': 'ABC', 'ciProductSubCode': 'EF', 'accountUniqueId': 'GHI' } ],
};
const mockMarvelCardProfile = null;
const fakeContentResponse = (props) => ({
  type: 'priorityMessageDetails',
  language: 'en-US',
  created_at: '2019-04-16T14:59:40.277Z',
  updated_at: '2019-04-16T14:59:40.277Z',
  content: { name: 'Priority Message', title: 'hello' },
  ...props,
});

const mockLaunchDarkly = initLaunchDarkly();

const contentResponses = rules.map(rule => fakeContentResponse({ id: rule.content_id }));

contentService.getContentsByIds.mockImplementation((space, ids) => {
  const found = ids.map((id) => contentResponses.find((content) => content.id === id));
  return found ? Promise.resolve({ data: { items: found } }) : Promise.reject(new Error('invalid id'));
});

describe('Campaigns', () => {
  describe('Get campaigns', () => {
    describe('Sort response', () => {
      beforeEach(() => {
        targetedCampaignService.getInstance().getCampaign.mockReset();
        marvelService.getAccounts.mockReset();
        dispositionsService.getDispositions.mockReset();
        campaignCacheService.get.mockReset();
        containerCacheService.get.mockReset();
        jest.spyOn(mockLaunchDarkly, 'isFeatureEnabled')
          .mockResolvedValueOnce(false) // Call the actual implementation on the first call
          .mockResolvedValue(true); // Mock the rest of the calls
      });

      test('should return rules with correct sort order - viewed set via dispositions db', () => {
        targetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce(mockInsightResponse);
        marvelService.getAccounts.mockResolvedValueOnce(mockMarvelResponse);
        marvelService.getCardProfile.mockResolvedValueOnce(mockMarvelCardProfile);
        // set new/viewed according to rule comment
        dispositionsService.getDispositions.mockResolvedValueOnce([
          { rule_id: 'mockid3', message_id: null, disposition: 'V' }, // mass campaign, viewed, non-urgent
          { rule_id: 'mockid4', message_id: null, disposition: 'V' }, // mass campaign, viewed, urgent, old
          { rule_id: 'mockid5', message_id: null, disposition: 'V' }, // mass campaign, viewed, urgent, recent
          { rule_id: 'mockid9', message_id: null, disposition: 'V' }, // priority message, viewed, non-urgent
          { rule_id: 'mockid10', message_id: null, disposition: 'V' }, // priority message, viewed, urgent, old
          { rule_id: 'mockid11', message_id: null, disposition: 'V' }, // priority message, viewed, urgent, recent
          { rule_id: 'mockid12', message_id: '********', disposition: 'V' }, // targeted, viewed, non-urgent
          { rule_id: 'mockid14', message_id: '********', disposition: 'V' }, // targeted, viewed, urgent
        ]);
        campaignCacheService.get.mockReturnValue(mockCampaignCacheResponse(rules));
        const variablesService = createVariableReplacer({ logger, productBookService, investmentService });
        const server = createServer({
          logger,
          authenticationMiddleware,
          authorizationMiddleware,
          alertService,
          campaignCacheService,
          containerCacheService,
          targetedCampaignService,
          dispositionsService,
          contentService,
          variablesService,
          launchDarklyService: mockLaunchDarkly,
          marvelService,
          ignoredLogRoutes,
          rateLimitingMiddleware,
        }, config);
        return supertest(server)
          .get(`/v1/campaigns?platform=ios&page=accounts&limit=50`)
          .set('Content-Type', 'application/json')
          .set('Accept', 'application/json')
          .expect('Content-Type', /json/)
          .expect(200)
          .then((res) => R.path([ 'body', 'data' ], res))
          .then((res) => {
            expect(res.total).toEqual(16);
            // New - urgent
            expect(res.items[0].id).toEqual('mockid7'); // priority message, new, urgent, recent
            expect(res.items[1].id).toEqual('mockid8'); // priority message, new, urgent, old
            expect(res.items[2].id).toEqual('mockid15'); // targeted, new, urgent
            expect(res.items[3].id).toEqual('mockid1'); // mass campaign, new, urgent, recent
            expect(res.items[4].id).toEqual('mockid2'); // mass campaign, new, urgent, old
            // New - non-urgent
            expect(res.items[5].id).toEqual('mockid13'); // targeted, new, non-urgent
            expect(res.items[6].id).toEqual('mockid6'); // priority message, new, non-urgent
            expect(res.items[7].id).toEqual('mockid0'); // mass campaign, new, non-urgent
            // Viewed - urgent
            expect(res.items[8].id).toEqual('mockid11'); // priority message, viewed, urgent, recent
            expect(res.items[9].id).toEqual('mockid10'); // priority message, viewed, urgent, old
            expect(res.items[10].id).toEqual('mockid14'); // targeted, viewed, urgent
            expect(res.items[11].id).toEqual('mockid5'); // mass campaign, viewed, urgent, recent
            expect(res.items[12].id).toEqual('mockid4'); // mass campaign, viewed, urgent, old
            // Viewed - non-urgent
            expect(res.items[13].id).toEqual('mockid12'); // targeted, viewed, non-urgent
            expect(res.items[14].id).toEqual('mockid9'); // priority message, viewed, non-urgent
            expect(res.items[15].id).toEqual('mockid3'); // mass campaign, viewed, non-urgent
          });
      });
      test('should return rules with correct sort order - viewed set via insights', () => {
        // chnages mock rule 14 & 15 to viewed (12 & 13 are now new)
        const mockViewedInsightResponse = {
          data: mockInsightResponse.data.map(o => o.campaign_id === 'CCV02' ? { ...o, message_status: 'V' } : o),
        };
        targetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce(mockViewedInsightResponse);
        targetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce(mockInsightResponse);
        marvelService.getAccounts.mockResolvedValueOnce(mockMarvelResponse);
        marvelService.getCardProfile.mockResolvedValueOnce(mockMarvelResponse);
        // set new/viewed according to rule comment
        dispositionsService.getDispositions.mockResolvedValueOnce([
          { rule_id: 'mockid3', message_id: null, disposition: 'V' }, // mass campaign, viewed, non-urgent
          { rule_id: 'mockid4', message_id: null, disposition: 'V' }, // mass campaign, viewed, urgent, old
          { rule_id: 'mockid5', message_id: null, disposition: 'V' }, // mass campaign, viewed, urgent, recent
          { rule_id: 'mockid9', message_id: null, disposition: 'V' }, // priority message, viewed, non-urgent
          { rule_id: 'mockid10', message_id: null, disposition: 'V' }, // priority message, viewed, urgent, old
          { rule_id: 'mockid11', message_id: null, disposition: 'V' }, // priority message, viewed, urgent, recent
        ]);
        campaignCacheService.get.mockReturnValue(mockCampaignCacheResponse(rules));
        const variablesService = createVariableReplacer({ logger, productBookService, investmentService });
        const server = createServer({
          logger,
          authenticationMiddleware,
          authorizationMiddleware,
          alertService,
          campaignCacheService,
          containerCacheService,
          targetedCampaignService,
          dispositionsService,
          contentService,
          variablesService,
          launchDarklyService: mockLaunchDarkly,
          marvelService,
          ignoredLogRoutes,
          rateLimitingMiddleware,

        }, config);
        return supertest(server)
          .get(`/v1/campaigns?platform=ios&page=accounts&limit=50`)
          .set('Content-Type', 'application/json')
          .set('Accept', 'application/json')
          .expect('Content-Type', /json/)
          .expect(200)
          .then((res) => R.path([ 'body', 'data' ], res))
          .then((res) => {
            expect(res.total).toEqual(16);
            // New - urgent
            expect(res.items[0].id).toEqual('mockid7'); // priority message, new, urgent, recent
            expect(res.items[1].id).toEqual('mockid8'); // priority message, new, urgent, old
            expect(res.items[2].id).toEqual('mockid1'); // mass campaign, new, urgent, recent
            expect(res.items[3].id).toEqual('mockid2'); // mass campaign, new, urgent, old
            // New - non-urgent
            expect(res.items[4].id).toEqual('mockid12'); // targeted, new, non-urgent
            expect(res.items[5].id).toEqual('mockid13'); // targeted, new, non-urgent
            expect(res.items[6].id).toEqual('mockid6'); // priority message, new, non-urgent
            expect(res.items[7].id).toEqual('mockid0'); // mass campaign, new, non-urgent
            // Viewed - urgent
            expect(res.items[8].id).toEqual('mockid11'); // priority message, viewed, urgent, recent
            expect(res.items[9].id).toEqual('mockid10'); // priority message, viewed, urgent, old
            expect(res.items[10].id).toEqual('mockid14'); // targeted, viewed, urgent
            expect(res.items[11].id).toEqual('mockid15'); // targeted, viewed, urgent
            expect(res.items[12].id).toEqual('mockid5'); // mass campaign, viewed, urgent, recent
            expect(res.items[13].id).toEqual('mockid4'); // mass campaign, viewed, urgent, old
            // Viewed - non-urgent
            expect(res.items[14].id).toEqual('mockid9'); // priority message, viewed, non-urgent
            expect(res.items[15].id).toEqual('mockid3'); // mass campaign, viewed, non-urgent
          });
      });
    });
  });
});
