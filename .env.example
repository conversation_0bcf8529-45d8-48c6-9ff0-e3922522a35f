NODE_ENV=development
NODE_TLS_REJECT_UNAUTHORIZED=0
SERVER_PORT=8000
HOSTING_ENV=PCF
PIGEON_BFF_ATLAS_URI=https://pigeon-ist.nonp.atlas.bns
PIGEON_BFF_PCF_URI=https://pigeon-ist.apps.cloud.bns

EPM=BFB6

ALERT_API_URI=https://rule-api-ist.nonp.atlas.bns/v1
ALERT_API_TIMEOUT=3000

CAMPAIGN_API_URI=https://rule-api-ist.nonp.atlas.bns

CONTENT_API_URI=https://content-api-ist.nonp.atlas.bns/v1
CONTENT_API_TIMEOUT=3000

INSIGHTS_API_URI=https://cdb-int-insight-ist.apps.cloud.bns/v0
INSIGHTS_API_URI_MOCK=https://pigeon-mock-ist.apps.stg.azr-cc-pcf.cloud.bns/api
INVESTMENT_API_URI=https://cdb-int-investments-uat.apps.stg.azr-cc-pcf.cloud.bns
INVESTMENT_API_TIMEOUT=3000

# CCAU DC-CAMPAIGNS API
DCCAMPAIGNS_API_URI=https://dc-campaigns-ist.apps.stg.azr-cc-pcf.cloud.bns/api
DCCAMPAIGNS_API_URI_MOCK=https://dc-campaigns-nft.apps.stg.azr-cc-pcf.cloud.bns

# Marvel Credentials API
CREDENTIALS_API_URI=https://cdb-int-credentials-ist.apps.stg.azr-cc-pcf.cloud.bns

MARVEL_ACCOUNT_API_REDIS_TTL=600

# Passport
PASSPORT_API_URI=https://passport-oauth-ist.apps.cloud.bns/oauth2/v1
PASSPORT_API_TIMEOUT=3000
PASSPORT_API_S2S_TTL=60
PASSPORT_API_S2S_SCOPE=ca:baas:campaign-rules:read,ca:baas:alert-rules:read,ca:baas:contents:read,ca:baas:rules:read,cdb.mrvl.accounts.read,cdb.mrvl.insight.read,cdb.mrvl.insight.update,gwm.customer.bfbh.read
PASSPORT_API_S2S_ALGORITHM=RS256

# SAML
SAML_EXIT_URL=https://pigeon-web-ist.apps.cloud.bns

# Pigeon BFF client
VCAP_APPLICATION='{"space_name"="IST"}'
PASSPORT_API_S2S_CLIENTID=d25053e8-3a5e-42cc-bc57-8389df4401f1
CDP_SECRET_S2S_PRIVATE_KEY=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
MARVEL_ACCOUNTS_API=https://cdb-int-account-ist.apps.stg.azr-cc-pcf.cloud.bns/v1/campaigns
MARVEL_REWARDS_API=https://cdb-int-rewards-ist.apps.stg.azr-cc-pcf.cloud.bns/v2/rewards
MARVEL_CARD_PROFILE=https://cdb-int-credential-cardprofile-uat.apps.stg.azr-cc-pcf.cloud.bns/v1/profile
MARVEL_PRODUCT_API_URL=https://cdb-int-product-ist.apps.stg.azr-cc-pcf.cloud.bns/v2/product
MARVEL_API_TIMEOUT=5000
CIRCUIT_BREAKER_TIMEOUT=2000
CIRCUIT_BREAKER_ERROR_THRESHOLD=50
CIRCUIT_BREAKER_RESET_TIMEOUT=3000
PROXY_HOST=webproxy.bns
PROXY_PORT=8080
CDP_SECRET_LAUNCH_DARKLY_SDK_KEY=********************************************************
ANONYMOUS_CLIENT_IDS='09e3052b-3534-449c-9416-b06a7191e967'

# Mssql dispositions db
MSSQL_POOL_MIN=1
MSSQL_POOL_MAX=5
MSSQL_TIMEOUT=30000
MSSQL_SCHEMA=disposition
MSSQL_HOST=localhost
MSSQL_PORT=1433
MSSQL_USER=sa
MSSQL_PASSWORD=Password1!
MSSQL_DATABASE=master

#logging
CDP_SECRET_CARD_PEPPER='NWM0N2Q5ZWRmNjg0ZThmNTVmYjAxNzliNDI2ZWJlY2Q5YTY4NzY2NjIyYTA1ZGQ0MGNiMWZmMDFlYTBiOTIyYQ=='
LOG_NAME=pigeon-ist
LOG_OBFUSCATE=1
LOG_COLORIZE=0
LOG_PRETTY_PRINT=0
LOG_SIEM=0
IGNORED_ROUTES_FOR_LOGGING='["/health"]'
CDP_SECRET_CUSTOMER_PEPPER=

# Rate limiting
RATE_LIMIT_CLIENT_ALERTS_MAX=30
RATE_LIMIT_CLIENT_CAMPAIGNS_MAX=30
RATE_LIMIT_OVERALL_ALERTS_MAX=1100
RATE_LIMIT_OVERALL_CAMPAIGNS_MAX=700
RATE_LIMIT_OVERALL_CONTENTS_CATALOG_MAX=30
RATE_LIMIT_CDP_TRUSTED_IP='***********/23'

# Disposition Purge
DISPOSITION_PURGE_DELETE_COUNT=10000
DISPOSITION_PURGE_TTL=60 # seconds
DISPOSITION_PURGE_INTERVAL=90 # minutes
DISPOSITION_PURGE_CHECK_INTERVALE=30 # minutes

# Prodigy Wealth API
PRODIGY_WEALTH_API_URI=https://swm-svc-customers-uat.apps.stg.azr-cc-pcf.cloud.bns
PRODIGY_WEALTH_API_TIMEOUT=3000
