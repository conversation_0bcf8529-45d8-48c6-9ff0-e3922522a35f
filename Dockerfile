# Pipeline: FROM us-docker.pkg.dev/pbyqs-8001-gkemgmt-5af67598/common-images/common/node/node:22.13.1-alpine3.20 AS builder
# Local: FROM us-docker.pkg.dev/nbyqs-8001-gkemgmt-aa8503ee/unscanned-images/pigeon/node22 AS builder
FROM us-docker.pkg.dev/pbyqs-8001-gkemgmt-5af67598/common-images/common/node/node:22.13.1-alpine3.20 AS builder

# Set the working directory
WORKDIR /build

# Copy app sources and dependencies
COPY openapi.yml CHANGELOG.md jest.config.js .npmrc sonar-project.properties package-lock.json package.json manifest.yml .env.example GitVersion.yml .cfignore ./
COPY src src
COPY data data
COPY acceptance-tests acceptance-tests
COPY .jest .jest
COPY node_modules node_modules/
# Test files cleanup
RUN find ./src -name "*.test.js" -delete

# Build the application
# RUN npm run build

# Using distroless image to exclude all unnecessary packages and avoid vulnerabilities in build
# Pipeline: FROM us-docker.pkg.dev/pbyqs-8001-gkemgmt-5af67598/common-images/node/nodejs22:nonroot
# Local: FROM us-docker.pkg.dev/nbyqs-8001-gkemgmt-aa8503ee/unscanned-images/pigeon/node22
FROM us-docker.pkg.dev/pbyqs-8001-gkemgmt-5af67598/common-images/node/nodejs22:nonroot

LABEL owner=digital
LABEL os=alpine
LABEL description='Runtime image for the application pigeon'

# Add labels for Aqua Exemption
LABEL CDPMigration=W3
LABEL EPM=BFB6

# Copy Scotia CA certs
COPY certs /etc/ssl/certs

# set the working directory
WORKDIR /build

# copy all app related stuff from earlier builder image to this
COPY --from=builder /build .

# Add ENV var for Dynatrace monitoring
ENV DT_CUSTOM_PROP='UNIQUE_ID=AL_PIGEON EPM=BFB6'

# Other important ENV vars
ENV PORT="8080"
ENV NODE_EXTRA_CA_CERTS="/etc/ssl/certs/ca-certificates.crt"
ENV NODE_ENV="production"

# Switch to non-root user
USER node

EXPOSE ${PORT}

# Start the application
CMD ["./src/index.js"]