const supertest = require('supertest');
const { createServer } = require('./server');
const Config = require('./config');
const {
  logger,
  authenticationMiddleware,
  authorizationMiddleware,
  alertService,
  campaignCacheService,
  targetedCampaignService,
  dispositionsService,
  contentService,
  launchDarklyService,
  marvelService,
  ignoredLogRoutes,
  rateLimitingMiddleware,
} = require('../acceptance-tests/mock');

const config = Config();
describe('Create server', () => {
  test('test initializing the server', async () => {
    const app = createServer({ logger, targetedCampaignService, launchDarklyService, dispositionsService, contentService, marvelService, campaignCacheService, ignoredLogRoutes, authenticationMiddleware, authorizationMiddleware, alertService, rateLimitingMiddleware }, config);
    const prodApp = createServer({ logger, targetedCampaignService, launchDarklyService, dispositionsService, contentService, marvelService, campaignCacheService, ignoredLogRoutes, authenticationMiddleware, authorizationMiddleware, alertService, rateLimitingMiddleware }, { ...config, space: 'PRD' });

    expect(app).toBeDefined();
    await supertest(app).get('/').expect(404);
    await supertest(app).get('/health').expect(200);
    await supertest(prodApp).get('/').set('preferred-environment', 'uat').expect(404);
    const res = await supertest(app).get('/').set('x-trace-id', '123').expect(404);
    expect(res.get('x-trace-id')).toEqual('123');
  });
});
