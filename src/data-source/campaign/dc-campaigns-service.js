const R = require('ramda');
const getCampaigns = require('../../components/dc-campaigns-client/get-campaigns');
const getCampaign = require('../../components/dc-campaigns-client/get-campaign');
const setDisposition = require('../../components/dc-campaigns-client/set-disposition');
const CampaignServiceInterface = require('./campaign-service-interface');
const { handleLDError } = require('../../components/campaign/routes/common');

const DC_CAMPAIGNS_FLAG = 'pigeon-api.downstreams.dc-campaigns';

// Define the first data service
class DcCampaignsService extends CampaignServiceInterface {
  constructor({ fetch, logger, basePath, mockUri, launchDarklyService }) {
    super();
    this.fetch = fetch;
    this.logger = logger;
    this.basePath = basePath;
    this.mockUri = mockUri;
    this.launchDarklyService = launchDarklyService;
  }

  async getCampaigns({ useMock, anonymousRequestFlag, ...props }) {
    // Check if we need to call the CCAU downstream service ( dc-campaigns ) to get the targeted campaign for this card ( user )
    let isDcCampaignsEnabled = true;
    try {
      isDcCampaignsEnabled = !anonymousRequestFlag && await this.launchDarklyService.isFeatureEnabled(DC_CAMPAIGNS_FLAG, false);
    } catch (err) {
      handleLDError({ err, flag: DC_CAMPAIGNS_FLAG, caller: 'dc-campaigns-service/get-campaigns', logger: this.logger });
    }
    if (!isDcCampaignsEnabled) return Promise.resolve({ data: {}, notifications: [] });

    return getCampaigns(
      {
        fetch: this.fetch,
        logger: this.logger,
        basePath: this.basePath,
        mockPath: R.curry((path, isMock) => (isMock ? path : undefined))(this.mockUri)(useMock),
      },
      props,
    );
  }

  // get campaign details
  async getCampaign({ useMock, anonymousRequestFlag, ...props }) {
    // Check if we need to call the CCAU downstream service ( dc-campaigns ) to get the targeted campaign for this card ( user )
    let isDcCampaignsEnabled = true;
    try {
      isDcCampaignsEnabled = !anonymousRequestFlag && await this.launchDarklyService.isFeatureEnabled(DC_CAMPAIGNS_FLAG, false);
    } catch (err) {
      handleLDError({ err, flag: DC_CAMPAIGNS_FLAG, caller: 'dc-campaigns-service/get-campaigns', logger: this.logger });
    }
    if (!isDcCampaignsEnabled) return Promise.resolve({ data: {}, notifications: [] });
    // get campaign details
    return getCampaign(
      {
        fetch: this.fetch,
        logger: this.logger,
        basePath: this.basePath,
        mockPath: R.curry((path, isMock) => (isMock ? path : undefined))(this.mockUri)(useMock),
        ...props,
      },
    );
  }

  // set disposition
  async setDisposition({ useMock, anonymousRequestFlag, ...props }) {
    // Check if we need to call the CCAU downstream service ( dc-campaigns ) to get the targeted campaign for this card ( user )
    let isDcCampaignsEnabled = true;
    try {
      isDcCampaignsEnabled = !anonymousRequestFlag && await this.launchDarklyService.isFeatureEnabled(DC_CAMPAIGNS_FLAG, false);
    } catch (err) {
      handleLDError({ err, flag: DC_CAMPAIGNS_FLAG, caller: 'dc-campaigns-service/set-campaign-disposition', logger: this.logger });
    }
    if (!isDcCampaignsEnabled) return Promise.resolve({ data: {}, notifications: [] });

    return setDisposition({ fetch: this.fetch, logger: this.logger, basePath: this.basePath, mockPath: R.curry((path, isMock) => (isMock ? path : undefined))(this.mockUri)(useMock), ...props });
  }
}

module.exports = DcCampaignsService;
