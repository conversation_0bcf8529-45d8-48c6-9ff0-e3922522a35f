const R = require('ramda');
const getCampaigns = require('../../components/insights-client/get-campaigns');
const getCampaign = require('../../components/insights-client/get-campaign');
const setDisposition = require('../../components/insights-client/set-disposition');
const CampaignServiceInterface = require('./campaign-service-interface');
const { handleLDError } = require('../../components/campaign/routes/common');

const ATLAS_INSIGHTS_FLAG = 'pigeon-api.downstreams.insights-atlas';

// Define the first data service
class InsightsService extends CampaignServiceInterface {
  constructor({ fetch, logger, basePath, atlasUri, mockUri, launchDarklyService }) {
    super();
    this.fetch = fetch;
    this.logger = logger;
    this.basePath = basePath;
    this.atlasUri = atlasUri;
    this.mockUri = mockUri;
    this.launchDarklyService = launchDarklyService;
  }

  async getInsightsUrl() {
    try {
      const useAtlas = await this.launchDarklyService.isFeatureEnabled(ATLAS_INSIGHTS_FLAG, false);
      return useAtlas ? this.atlasUri : this.basePath;
    } catch (err) {
      handleLDError({ err, flag: ATLAS_INSIGHTS_FLAG, caller: 'insight-service/get-insights-url', logger: this.logger });
      return this.basePath; // Default to base path on error
    }
  }

  async getCampaigns({ useMock, anonymousRequestFlag, ...props }) {
    let isInsightsEnabled = true;
    try {
      isInsightsEnabled = !anonymousRequestFlag && await this.launchDarklyService.isFeatureEnabled('pigeon-api.downstreams.insights', false);
    } catch (err) {
      handleLDError({ err, flag: 'pigeon-api.downstreams.insights', caller: 'insight-service/get-campaigns', logger: this.logger });
    }

    if (!isInsightsEnabled) return Promise.resolve({ data: [], notifications: null });

    const insightsUrl = await this.getInsightsUrl();

    return getCampaigns(
      {
        fetch: this.fetch,
        logger: this.logger,
        basePath: insightsUrl,
        mockPath: R.curry((path, isMock) => (isMock ? path : undefined))(this.mockUri)(useMock),
      },
      props,
    );
  }

  async getCampaign({ useMock, ...props }) {
    const insightsUrl = await this.getInsightsUrl();
    return getCampaign({
      fetch: this.fetch,
      logger: this.logger,
      basePath: insightsUrl,
      mockPath: R.curry((path, isMock) => (isMock ? path : undefined))(this.mockUri)(useMock),
      ...props,
    });
  }

  async setDisposition({ useMock, ...props }) {
    const insightsUrl = await this.getInsightsUrl();
    return setDisposition({
      fetch: this.fetch,
      logger: this.logger,
      basePath: insightsUrl,
      mockPath: R.curry((path, isMock) => (isMock ? path : undefined))(this.mockUri)(useMock),
      ...props,
    });
  }
}

module.exports = InsightsService;
