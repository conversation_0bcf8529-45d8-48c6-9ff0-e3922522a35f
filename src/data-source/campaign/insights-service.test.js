const TargetedCampaignServiceFactory = require('./campaign-service-factory');

const mockFetch = jest.fn();
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

const mockUri = 'https://insights.apps.cloud.bns';
const mockAtlasUri = 'https://atlas.insights.apps.cloud.bns';
const mockToken = '12345678';
const mockId = '11112222';
const mockLanguage = 'en-US';

const mockConfig = {
  targetedCampaignAPI: {
    insightBasePath: 'https://base.path',
    insightsMockUri: mockUri,
    insightsAtlasUri: mockAtlasUri,
    timeout: 2000,
  },
};

const createMockLaunchDarklyService = (insightsEnabled = true, atlasEnabled = false) => ({
  isFeatureEnabled: jest.fn().mockImplementation((flag) => {
    if (flag === 'pigeon-api.downstreams.insights') return Promise.resolve(insightsEnabled);
    if (flag === 'pigeon-api.downstreams.insights-atlas') return Promise.resolve(atlasEnabled);
    return Promise.resolve(false);
  }),
});

describe('Insights API Client', () => {
  beforeEach(() => {
    mockFetch.mockClear();
  });
  test('should init', () => {
    const targetedCampaignService = new TargetedCampaignServiceFactory({ fetch: mockFetch, logger: mockLogger, config: mockConfig.targetedCampaignAPI });
    const insightsService = targetedCampaignService.getInstance({});
    expect(insightsService).toHaveProperty('getCampaigns');
    expect(typeof insightsService.getCampaigns).toEqual('function');
    expect(insightsService).toHaveProperty('getCampaign');
    expect(typeof insightsService.getCampaign).toEqual('function');
    expect(insightsService).toHaveProperty('setDisposition');
    expect(typeof insightsService.setDisposition).toEqual('function');
  });
  test('should call getCampaigns', async () => {
    const mockPayload = { data: [ { message_id: '123456abcde' } ], notifications: null };
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockPayload)) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const targetedCampaignService = new TargetedCampaignServiceFactory({ fetch: mockFetch, logger: mockLogger, config: mockConfig.targetedCampaignAPI });
    const insightsService = targetedCampaignService.getInstance({});

    const res = await insightsService.getCampaigns({ token: mockToken, language: mockLanguage });
    expect(res).toBeDefined();
    expect(res).toEqual(mockPayload);
  });

  test('should call return empty array if LD flag is false', async () => {
    const mockLaunchDarklyService = {
      isFeatureEnabled: jest.fn(),
    };
    mockLaunchDarklyService.isFeatureEnabled.mockReturnValue(false);
    const mockPayload = { data: [], notifications: null };
    const targetedCampaignService = new TargetedCampaignServiceFactory({ fetch: mockFetch, logger: mockLogger, config: mockConfig.targetedCampaignAPI, launchDarklyService: mockLaunchDarklyService });
    const insightsService = targetedCampaignService.getInstance({});
    const res = await insightsService.getCampaigns({ token: mockToken, language: mockLanguage });
    expect(res).toBeDefined();
    expect(res).toEqual(mockPayload);
  });
  test('should call getCampaigns - mock', async () => {
    const mockPayload = { data: [ { message_id: '123456abcde' } ], notifications: null };
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockPayload)) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const targetedCampaignService = new TargetedCampaignServiceFactory({ fetch: mockFetch, logger: mockLogger, config: mockConfig.targetedCampaignAPI });
    const insightsService = targetedCampaignService.getInstance({});

    const res = await insightsService.getCampaigns({ token: mockToken, language: mockLanguage, useMock: true });
    expect(res).toBeDefined();
    expect(res).toEqual(mockPayload);
  });
  test('should call getCampaign', async () => {
    const mockPayload = { data: { message_id: '123456abcde' }, notifications: null };
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockPayload)) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const targetedCampaignService = new TargetedCampaignServiceFactory({ fetch: mockFetch, logger: mockLogger, config: mockConfig.targetedCampaignAPI });
    const insightsService = targetedCampaignService.getInstance({});
    const res = await insightsService.getCampaign(mockToken, mockId, mockLanguage);
    expect(res).toBeDefined();
    expect(res).toEqual(mockPayload);
  });
  test('should call getCampaign - mock ', async () => {
    const mockPayload = { data: { message_id: '123456abcde' }, notifications: null };
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockPayload)) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const targetedCampaignService = new TargetedCampaignServiceFactory({ fetch: mockFetch, logger: mockLogger, config: mockConfig.targetedCampaignAPI });
    const insightsService = targetedCampaignService.getInstance({});
    const res = await insightsService.getCampaign({ token: mockToken, id: mockId, language: mockLanguage, useMock: true });
    expect(res).toBeDefined();
    expect(res).toEqual(mockPayload);
  });
  test('should call setDisposition', async () => {
    const mockPayload = { data: { message_id: '123456abcde' }, notifications: null };
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockPayload)) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const targetedCampaignService = new TargetedCampaignServiceFactory({ fetch: mockFetch, logger: mockLogger, config: mockConfig.targetedCampaignAPI });
    const insightsService = targetedCampaignService.getInstance({});

    const res = await insightsService.setDisposition(mockToken, mockId, mockLanguage);
    expect(res).toBeDefined();
    expect(res).toEqual(mockPayload);
  });
  test('should call setDisposition - mock', async () => {
    const mockPayload = { data: { message_id: '123456abcde' }, notifications: null };
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockPayload)) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const targetedCampaignService = new TargetedCampaignServiceFactory({ fetch: mockFetch, logger: mockLogger, config: mockConfig.targetedCampaignAPI });
    const insightsService = targetedCampaignService.getInstance({});

    const res = await insightsService.setDisposition({ token: mockToken, id: mockId, language: mockLanguage, useMock: true });
    expect(res).toBeDefined();
    expect(res).toEqual(mockPayload);
  });
  test('should call getCampaigns with base path when atlas flag is false', async () => {
    const mockLaunchDarklyService = createMockLaunchDarklyService(true, false);
    const mockPayload = { data: [ { message_id: '123456abcde' } ], notifications: null };
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockPayload)) };
    mockFetch.mockResolvedValueOnce(mockResponse);

    const targetedCampaignService = new TargetedCampaignServiceFactory({
      fetch: mockFetch,
      logger: mockLogger,
      config: mockConfig.targetedCampaignAPI,
      launchDarklyService: mockLaunchDarklyService,
    });
    const insightsService = targetedCampaignService.getInstance({});

    const res = await insightsService.getCampaigns({ token: mockToken, language: mockLanguage });
    expect(res).toEqual(mockPayload);
    expect(mockFetch).toHaveBeenCalledWith(
      `${mockConfig.targetedCampaignAPI.insightBasePath}/v1/insights/campaigns`,
      expect.any(Object),
    );
  });

  test('should call getCampaigns with atlas path when atlas flag is true', async () => {
    const mockLaunchDarklyService = createMockLaunchDarklyService(true, true);
    const mockPayload = { data: [ { message_id: '123456abcde' } ], notifications: null };
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockPayload)) };
    mockFetch.mockResolvedValueOnce(mockResponse);

    const targetedCampaignService = new TargetedCampaignServiceFactory({
      fetch: mockFetch,
      logger: mockLogger,
      config: mockConfig.targetedCampaignAPI,
      launchDarklyService: mockLaunchDarklyService,
    });
    const insightsService = targetedCampaignService.getInstance({});

    const res = await insightsService.getCampaigns({ token: mockToken, language: mockLanguage });
    expect(res).toEqual(mockPayload);
    expect(mockFetch).toHaveBeenCalledWith(
      `${mockAtlasUri}/v1/insights/campaigns`,
      expect.any(Object),
    );
  });

  test('should use base path when Launch Darkly throws error', async () => {
    const mockLaunchDarklyService = {
      isFeatureEnabled: jest.fn().mockRejectedValue(new Error('LD Error')),
    };
    const mockPayload = { data: [ { message_id: '123456abcde' } ], notifications: null };
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockPayload)) };
    mockFetch.mockResolvedValueOnce(mockResponse);

    const targetedCampaignService = new TargetedCampaignServiceFactory({
      fetch: mockFetch,
      logger: mockLogger,
      config: mockConfig.targetedCampaignAPI,
      launchDarklyService: mockLaunchDarklyService,
    });
    const insightsService = targetedCampaignService.getInstance({});

    const res = await insightsService.getCampaigns({ token: mockToken, language: mockLanguage });
    expect(res).toEqual(mockPayload);
    expect(mockFetch).toHaveBeenCalledWith(
      `${mockConfig.targetedCampaignAPI.insightBasePath}/v1/insights/campaigns`,
      expect.any(Object),
    );
    expect(mockLogger.error).toHaveBeenCalled();
  });
});
