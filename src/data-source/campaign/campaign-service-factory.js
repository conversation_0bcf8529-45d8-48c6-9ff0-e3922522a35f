const InsightsService = require('./insights-service');
const DcCampaignsService = require('./dc-campaigns-service');

// This is not the correct behavior of the factory design pattern , usually the factory design pattern create a new instance form the sub class every time we create an instance from the factory class
// more details :- https://refactoring.guru/design-patterns/factory-method
// need to refactor the naming here when we start apply new strategy for the data source

class TargetedCampaignServiceFactory {
  constructor({ fetch, logger, launchDarklyService, config }) {
    this.insightsService = new InsightsService({
      fetch,
      logger,
      basePath: config.insightBasePath,
      atlasUri: config.insightsAtlasUri,
      mockUri: config.insightsMockUri,
      launchDarklyService,
    });
    this.dcService = new DcCampaignsService({
      fetch,
      logger,
      basePath: config.dcCampaignsBasePath,
      mockUri: config.dcCampaignsMockUri,
      launchDarklyService,
    });
  }

  getInstance({ ruleType = 'campaign' }) {
    switch (ruleType) {
      case 'campaign':
        return this.insightsService;
      case 'ccau_campaign':
        return this.dcService;
      default:
        return this.insightsService;
    }
  }
}

module.exports = TargetedCampaignServiceFactory;
