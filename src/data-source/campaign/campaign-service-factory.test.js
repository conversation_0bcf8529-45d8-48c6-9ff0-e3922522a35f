const TargetedCampaignServiceFactory = require('./campaign-service-factory');

const mockLogger = {
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};
const mockConfig = {
  campaignAPI: {
    insightBasePath: '',
    insightsMockUri: '',
    timeout: 2000,
  },
};
const mockFetch = jest.fn();

describe('TargetedCampaignServiceFactory Class', () => {
  test('create new instance of TargetedCampaignServiceFactory class  ', async () => {
    const targetedCampaignService = new TargetedCampaignServiceFactory({ logger: mockLogger, fetch: mockFetch, config: mockConfig });
    expect(targetedCampaignService).toHaveProperty('insightsService');
    expect(targetedCampaignService).toHaveProperty('dcService');
  });

  test('create new instance of TargetedCampaignServiceFactory Class ', async () => {
    const targetedCampaignService = new TargetedCampaignServiceFactory({ logger: mockLogger, fetch: mockFetch, config: mockConfig });
    expect(targetedCampaignService.getInstance({ ruleType: 'campaign' })).toEqual(targetedCampaignService.insightsService);
    expect(targetedCampaignService.getInstance({ ruleType: 'ccau_campaign' })).toEqual(targetedCampaignService.dcService);
    expect(targetedCampaignService.getInstance({})).toEqual(targetedCampaignService.insightsService);
  });

  test('test insights instance', async () => {
    const targetedCampaignService = new TargetedCampaignServiceFactory({ logger: mockLogger, fetch: mockFetch, config: mockConfig });
    const instanceService = targetedCampaignService.getInstance({ ruleType: 'campaign' });
    expect(instanceService).toHaveProperty('getCampaign');
    expect(instanceService).toHaveProperty('getCampaigns');
    expect(instanceService).toHaveProperty('setDisposition');
  });

  test('test insights instance default value', async () => {
    const targetedCampaignService = new TargetedCampaignServiceFactory({ logger: mockLogger, fetch: mockFetch, config: mockConfig });
    const instanceService = targetedCampaignService.getInstance({ ruleType: 'insights' });
    expect(instanceService).toHaveProperty('getCampaign');
    expect(instanceService).toHaveProperty('getCampaigns');
    expect(instanceService).toHaveProperty('setDisposition');
  });
});
