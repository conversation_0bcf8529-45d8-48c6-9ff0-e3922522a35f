const TargetedCampaignService = require('./campaign-service-factory');

const mockFetch = jest.fn();
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

const mockUri = 'https://dc-campaigns.apps.cloud.bns';
const mockToken = '12345678';
const mockId = '123456abcde';
const mockLanguage = 'en-US';

const mockConfig = {
  targetedCampaignAPI: {
    dcCampaignsBasePath: '',
    dcCampaignsMockUri: mockUri,
    timeout: 2000,
  },
};

const mockEmptyPayload = {
  'data': {},
  'notifications': [],
};

describe('DC Campaigns API Client', () => {
  beforeEach(() => {
    mockFetch.mockClear();
  });
  test('should init', () => {
    const targetedCampaignService = new TargetedCampaignService({ fetch: mockFetch, logger: mockLogger, config: mockConfig.targetedCampaignAPI });
    const dcCampaignsService = targetedCampaignService.getInstance({ ruleType: 'ccau_campaign' });
    expect(dcCampaignsService).toHaveProperty('getCampaigns');
    expect(typeof dcCampaignsService.getCampaigns).toEqual('function');
    expect(dcCampaignsService).toHaveProperty('getCampaign');
    expect(typeof dcCampaignsService.getCampaign).toEqual('function');
    expect(dcCampaignsService).toHaveProperty('setDisposition');
    expect(typeof dcCampaignsService.setDisposition).toEqual('function');
  });
  test('should call getCampaigns', async () => {
    const mockPayload = { data: [ { message_id: '123456abcde' } ], notifications: null };
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockPayload)) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const targetedCampaignService = new TargetedCampaignService({ fetch: mockFetch, logger: mockLogger, config: mockConfig.targetedCampaignAPI });
    const dcCampaignsService = targetedCampaignService.getInstance({ ruleType: 'ccau_campaign' });

    const res = await dcCampaignsService.getCampaigns({ token: mockToken, language: mockLanguage });
    expect(res).toBeDefined();
    expect(res).toEqual(mockPayload);
  });
  test('should call return empty object if LD flag is false - campaigns', async () => {
    const mockLaunchDarklyService = {
      isFeatureEnabled: jest.fn(),
    };
    mockLaunchDarklyService.isFeatureEnabled.mockReturnValue(false);
    const mockPayload = { data: {}, notifications: [] };
    const targetedCampaignService = new TargetedCampaignService({ fetch: mockFetch, logger: mockLogger, config: mockConfig.targetedCampaignAPI, launchDarklyService: mockLaunchDarklyService });
    const dcCampaignsService = targetedCampaignService.getInstance({ ruleType: 'ccau_campaign' });
    const res = await dcCampaignsService.getCampaigns({ token: mockToken, language: mockLanguage });
    expect(res).toBeDefined();
    expect(res).toEqual(mockPayload);
  });
  test('should call getCampaigns - mock', async () => {
    const mockPayload = { data: [ { message_id: '123456abcde' } ], notifications: null };
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockPayload)) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const targetedCampaignService = new TargetedCampaignService({ fetch: mockFetch, logger: mockLogger, config: mockConfig.targetedCampaignAPI });
    const dcCampaignsService = targetedCampaignService.getInstance({ ruleType: 'ccau_campaign' });

    const res = await dcCampaignsService.getCampaigns({ token: mockToken, language: mockLanguage, useMock: true });
    expect(res).toBeDefined();
    expect(res).toEqual(mockPayload);
  });

  test('should call getCampaign', async () => {
    const mockPayload = {
      data: {
        message_id: '123456abcde',
        message_source: 'DC_CAMPAIGNS',
        message_status: 'N',
        language: 'en',
        country_code: 'DO',
        campaign_id: 'ABC01',
        start_date: '2022-08-20',
        expiry_date: '2025-08-20',
        conf_number: null,
        additional_data: [
          {
            value: '3',
            name: 'CCAU_IDV_ID_TYPE',
          },
          {
            value: 'PASSPORT',
            name: 'CCAU_IDV_ID_TYPE_TEXT',
          },
        ],
      },
      notifications: [] };

    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockPayload)) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const targetedCampaignService = new TargetedCampaignService({ fetch: mockFetch, logger: mockLogger, config: mockConfig.targetedCampaignAPI });
    const dcCampaignsService = targetedCampaignService.getInstance({ ruleType: 'ccau_campaign' });
    const res = await dcCampaignsService.getCampaign({ token: mockToken, id: mockId, language: mockLanguage, useMock: false });

    expect(res).toBeDefined();
    expect(res).toEqual(mockPayload);
  });
  test('should call return empty object if LD flag is false - campaign', async () => {
    const mockLaunchDarklyService = {
      isFeatureEnabled: jest.fn(),
    };
    mockLaunchDarklyService.isFeatureEnabled.mockReturnValue(false);
    const mockPayload = { data: {}, notifications: [] };
    const targetedCampaignService = new TargetedCampaignService({ fetch: mockFetch, logger: mockLogger, config: mockConfig.targetedCampaignAPI, launchDarklyService: mockLaunchDarklyService });
    const dcCampaignsService = targetedCampaignService.getInstance({ ruleType: 'ccau_campaign' });
    const res = await dcCampaignsService.getCampaign({ token: mockToken, id: mockId, language: mockLanguage, useMock: true });
    expect(res).toBeDefined();
    expect(res).toEqual(mockPayload);
  });

  test('should call getCampaign - mock', async () => {
    const mockPayload = {
      data: {
        message_id: '123456abcde',
        message_source: 'DC_CAMPAIGNS',
        message_status: 'N',
        language: 'en',
        country_code: 'DO',
        campaign_id: 'ABC01',
        start_date: '2022-08-20',
        expiry_date: '2025-08-20',
        conf_number: null,
        additional_data: [
          {
            value: '3',
            name: 'CCAU_IDV_ID_TYPE',
          },
          {
            value: 'PASSPORT',
            name: 'CCAU_IDV_ID_TYPE_TEXT',
          },
        ],
      },
      notifications: [] };

    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockPayload)) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const targetedCampaignService = new TargetedCampaignService({ fetch: mockFetch, logger: mockLogger, config: mockConfig.targetedCampaignAPI });
    const dcCampaignsService = targetedCampaignService.getInstance({ ruleType: 'ccau_campaign' });
    const res = await dcCampaignsService.getCampaign({ token: mockToken, id: mockId, language: mockLanguage, useMock: true });

    expect(res).toBeDefined();
    expect(res).toEqual(mockPayload);
  });
  test('should call setDisposition', async () => {
    const mockPayload = { data: { }, notifications: [] };
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockPayload)) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const targetedCampaignService = new TargetedCampaignService({ fetch: mockFetch, logger: mockLogger, config: mockConfig.targetedCampaignAPI });
    const dcCampaignsService = targetedCampaignService.getInstance({ ruleType: 'ccau_campaign' });

    const res = await dcCampaignsService.setDisposition({ token: mockToken, language: mockLanguage, useMock: true });
    expect(res).toBeDefined();
    expect(res).toEqual(mockEmptyPayload);
  });
  test('should call return empty object if LD flag is false - campaign', async () => {
    const mockLaunchDarklyService = {
      isFeatureEnabled: jest.fn(),
    };
    mockLaunchDarklyService.isFeatureEnabled.mockReturnValue(false);
    const mockPayload = { data: {}, notifications: [] };
    const targetedCampaignService = new TargetedCampaignService({ fetch: mockFetch, logger: mockLogger, config: mockConfig.targetedCampaignAPI, launchDarklyService: mockLaunchDarklyService });
    const dcCampaignsService = targetedCampaignService.getInstance({ ruleType: 'ccau_campaign' });
    const res = await dcCampaignsService.setDisposition(mockToken, mockId, mockLanguage);
    expect(res).toBeDefined();
    expect(res).toEqual(mockPayload);
  });
  test('should call setDisposition - mock', async () => {
    const mockPayload = { data: { }, notifications: [] };
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockPayload)) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const targetedCampaignService = new TargetedCampaignService({ fetch: mockFetch, logger: mockLogger, config: mockConfig.targetedCampaignAPI });
    const dcCampaignsService = targetedCampaignService.getInstance({ ruleType: 'ccau_campaign' });

    const res = await dcCampaignsService.setDisposition({ token: mockToken, language: mockLanguage, useMock: false });
    expect(res).toBeDefined();
    expect(res).toEqual(mockEmptyPayload);
  });
});
