const CampaignServiceInterface = require('./campaign-service-interface');

describe('CampaignServiceInterface', () => {
  it('test all functions', () => {
    const CampaignServiceInterfaceInstance = new CampaignServiceInterface();
    expect(typeof CampaignServiceInterfaceInstance.getCampaigns).toBe('function');
    expect(typeof CampaignServiceInterfaceInstance.getCampaign).toBe('function');
    expect(typeof CampaignServiceInterfaceInstance.setDisposition).toBe('function');
    expect(CampaignServiceInterfaceInstance.getCampaigns()).toBeUndefined();
    expect(CampaignServiceInterfaceInstance.getCampaign()).toBeUndefined();
    expect(CampaignServiceInterfaceInstance.setDisposition()).toBeUndefined();
  });
});
