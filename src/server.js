/* eslint-disable sonarjs/no-duplicate-string */
const Server = require('./components/server');
const { createRequestLogger } = require('./components/server/middleware/request-logger');
const localeMiddleware = require('./components/server/middleware/locale');
const undefineddRouteMW = require('./components/server/middleware/undefined-route');
const nonProdRoutesMiddleware = require('./components/server/middleware/non-prod-routes');
const { errorMiddleware } = require('./components/errors');
const Health = require('./components/health');
const Alert = require('./components/alert');
const Campaign = require('./components/campaign');
const CampaignInbox = require('./components/campaign-inbox');
const Dispositions = require('./components/dispositions');
const ContentsCatalog = require('./components/contents-catalog');
const { traceHeaders } = require('pigeon-pigeon-pack');
const httpContext = require('express-http-context');
const { setTraceId, setSpanId, setPreferredEnvironment } = require('./components/server/httpContext');
const { loggerMiddleware, reqStartTimeMiddleware } = require('nrlw-express-scribe');
const { campaignsTotalRateLimiter, campaignsClientRateLimiter, alertsTotalRateLimiter, alertsClientRateLimiter, contentsCatalogTotalRateLimiter } = require('./components/rate-limit/rate-limiters');

const createServer = (deps, config) => {
  const {
    logger,
    authenticationMiddleware,
    authorizationMiddleware,
    alertService,
    contentService,
    targetedCampaignService,
    ruleAPIClient,
    variablesService,
    credentialsService,
    launchDarklyService,
    marvelService,
    campaignCacheService,
    containerCacheService,
    dispositionsService,
    redisClient,
    ignoredLogRoutes,
    rateLimitingMiddleware,
    fetch,
    jwksClient,
    jwt,
    pigeonBffAtlasClient,
    pigeonBffPCFClient,
    prodigyWealthService,
  } = deps;
  // initialize HTTP server
  const server = Server({ origin: config.http.origin });
  const level = (req, res) => ((res.statusCode >= 400 && res.statusCode !== 422) ? 'warn' : 'info');
  // whitelist the routes from logging to save some space
  const ignoreLogging = (req, res) => (ignoredLogRoutes.includes(req.originalUrl));

  traceHeaders.createNamespace(config.clsNamespace);
  server.use(traceHeaders.middleware(config.clsNamespace));
  server.use(reqStartTimeMiddleware());
  server.use(loggerMiddleware({
    logger,
    level,
    maskedReqHeaders: [
      'Authorization', 'cookie', 'csrf-token', 'x-customer-scotiacard',
      'x-customer-authorization', 'x-user-context',
    ],
    maskedResHeaders: [ 'set-cookie' ],
    getContext: (req, res) => ({ cardNumber: req.get('x-customer-scotiacard') }),
    ignoreLogging,
  }));

  // setting the context for every request that goes through
  server.use((req, res, next) => {
    httpContext.ns.bindEmitter(req);
    httpContext.ns.bindEmitter(res);

    // Fetching the required headers from request
    const traceId = req.get('x-b3-traceid');
    const spanId = req.get('x-b3-spanid');
    const preferredEnvironment = req.get('preferred-environment');

    // Setting these required properties to the context globally and can be accessed using the getter fn of httpContext function
    traceId && setTraceId(traceId);
    spanId && setSpanId(spanId);
    preferredEnvironment && setPreferredEnvironment(preferredEnvironment);
    next();
  });

  server.use((req, res, next) => {
    // close the connection ambiguous HTTP requests, such as those that contain both Content-Length and Transfer-Encoding headers
    const hasTransferEncoding = req.get('transfer-encoding') !== undefined;
    const hasContentLength = req.get('content-length') !== undefined;

    if (hasTransferEncoding && hasContentLength) {
      res.set('Connection', 'close');
      res.status(400).end();
    } else {
      next();
    }
  });

  // add health check routes
  server.use('/health', Health.routes());
  // add api docs middleware
  if (config.space !== 'PRD') {
    server.use(nonProdRoutesMiddleware({
      authenticationMiddleware,
      config,
      campaignCacheService,
      dispositionsService,
    }));
  }
  // add locale middleware
  server.use(localeMiddleware());

  // add request logger
  server.use(createRequestLogger({ logger, clsNamespace: config.clsNamespace }));
  // add authentication middleware
  server.use(authenticationMiddleware);

  const getHeaderKey = (key, obj) => Object.keys(obj).find((k) => k.toLowerCase() === key);
  server.use((req, _, next) => {
    const pcfEnvrionment = config.space.toLowerCase();
    if (pcfEnvrionment && pcfEnvrionment === 'prd' && req.get('preferred-environment')) {
      delete req.headers[getHeaderKey('preferred-environment', req.headers)];
    }
    next();
  });

  // add alerts routes
  server.use('/v1/alerts', Alert.routes({
    logger,
    alertService,
    rateLimitingMiddleware,
    alertsTotalRateLimiter: alertsTotalRateLimiter(config.rateLimiting, logger),
    alertsClientRateLimiter: alertsClientRateLimiter(config.rateLimiting, logger),
    launchDarklyService,
  }, {
    features: config.features,
    hostingEnv: config.hostingEnv,
  }));

  // add campaigns routes
  server.use('/v1/campaigns', Campaign.routes({
    logger,
    targetedCampaignService,
    contentService,
    variablesService,
    credentialsService,
    launchDarklyService,
    authorize: authorizationMiddleware,
    marvelService,
    campaignCacheService,
    containerCacheService,
    dispositionsService,
    redisClient,
    rateLimitingMiddleware,
    campaignsTotalRateLimiter: campaignsTotalRateLimiter(config.rateLimiting, logger),
    campaignsClientRateLimiter: campaignsClientRateLimiter(config.rateLimiting, logger),
    fetch,
    jwksClient,
    jwt,
    prodigyWealthService,
  }, {
    saml: config.saml,
    space: config.space,
    tokenPath: config.serviceAuth.tokenPath,
    tokenClaimsPath: config.serviceAuth.tokenClaimsPath,
    features: config.features,
    anonymousClientIds: config.serviceAuth.anonymousClientIds,
    publicKeyJWKS: config.serviceAuth.publicKeyJWKS,
    hostingEnv: config.hostingEnv,
  }));
  // campaigns inbox routes
  server.use('/v1/campaigns-inbox', CampaignInbox.routes({
    logger,
    targetedCampaignService,
    contentService,
    variablesService,
    launchDarklyService,
    authorize: authorizationMiddleware,
    marvelService,
    campaignCacheService,
    containerCacheService,
    dispositionsService,
  }, {
    features: config.features,
    hostingEnv: config.hostingEnv,
  }));
  // new dispositions routes
  server.use('/v1/dispositions', Dispositions.routes({
    logger,
    targetedCampaignService,
    authorize: authorizationMiddleware,
    campaignCacheService,
    dispositionsService,
  }, {
    saml: config.saml,
    space: config.space,
    tokenPath: config.serviceAuth.tokenPath,
    tokenClaimsPath: config.serviceAuth.tokenClaimsPath,
    features: config.features,
    anonymousClientIds: config.serviceAuth.anonymousClientIds,
  }));

  // contents catalog routes
  server.use('/v1/contents-catalog', ContentsCatalog.routes({
    logger,
    ruleAPIClient,
    contentService,
    rateLimitingMiddleware,
    contentsCatalogTotalRateLimiter: contentsCatalogTotalRateLimiter(config.rateLimiting, logger),
    launchDarklyService,
    pigeonBffAtlasClient,
    pigeonBffPCFClient,
  }, { space: config.space, hostingEnv: config.hostingEnv }));

  // add unhandled route middleware
  server.use(undefineddRouteMW());

  // add x-trace-id to error response
  server.use((err, req, res, next) => {
    const traceId = req.get('x-trace-id');
    traceId && res.set('x-trace-id', traceId);
    next(err);
  });

  // add global error handling middleware
  server.use(errorMiddleware({ logger }));

  return server;
};

module.exports = {
  createServer,
};
