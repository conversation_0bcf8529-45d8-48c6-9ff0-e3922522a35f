const rateLimitMiddleware = require('./rate-limit-middleware');

describe('Rate Limit Middleware', () => {
  const next = jest.fn();
  const json = jest.fn();
  const status = jest.fn().mockReturnValue({ json });
  const res = { status, json };
  const logger = {
    error: jest.fn(),
  };
  const launchDarklyService = {
    isFeatureEnabled: () => {
      return {
        client: { alerts: 10, campaigns: 8 },
        overall: { alerts: 100, campaigns: 80 },
      };
    },
  };
  const rateLimitConfigs = {
    window: 60000,
    client: { alerts: 5, campaigns: 4 },
    overall: { alerts: 50, campaigns: 40, contentsCatalog: 10 },
  };

  beforeEach(() => {
    status.mockClear();
    json.mockClear();
    next.mockClear();
  });

  test('should add LD rate limiting configs and the x-true-client-ip to the request', async () => {
    const req = {
      header: () => '**************',
    };
    await rateLimitMiddleware(launchDarklyService, logger, rateLimitConfigs)(req, res, next);

    expect(req.trueClientIp).toEqual('**************');
    expect(req.ldRateLimitConfig.client).toEqual({ alerts: 10, campaigns: 8 });
    expect(req.ldRateLimitConfig.overall).toEqual({ alerts: 100, campaigns: 80 });
    expect(next).toHaveBeenCalled();
  });

  test('should add LD rate limiting configs and the x-true-client-ip to the request', async () => {
    const req = {
      header: () => 'some invalid ip',
    };
    await rateLimitMiddleware(launchDarklyService, logger, rateLimitConfigs)(req, res, next);

    expect(req.trueClientIp).toEqual('');
    expect(req.ldRateLimitConfig.client).toEqual({ alerts: 10, campaigns: 8 });
    expect(req.ldRateLimitConfig.overall).toEqual({ alerts: 100, campaigns: 80 });
    expect(next).toHaveBeenCalled();
  });
});
