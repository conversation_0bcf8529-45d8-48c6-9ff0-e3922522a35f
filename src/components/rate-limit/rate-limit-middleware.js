const { isIP, inRange } = require('range_check');
const { omit } = require('lodash');

/**
 * Middleware to add LD rate limiting config & Akamai's true client IP to the req object
 * If the IP is invalid or is an CDP load balancer/instance, client rate limiting will be disabled
 * @param {*} launchDarklyService
 * @param {*} rateLimitConfigs - Manifest file rate limit configs
 * @returns
 */
const rateLimitMiddleware = (launchDarklyService, logger, rateLimitConfigs) => async (req, res, next) => {
  try {
    req.ldRateLimitConfig = await launchDarklyService.isFeatureEnabled('pigeon-api.config.rateLimit', omit(rateLimitConfigs, 'cdpTrustedIp'));
  } catch (err) {
    req.ldRateLimitConfig = omit(rateLimitConfigs, 'cdpTrustedIp');
    logger.error({ message: 'Unable to call Launch Darkly for rate limiting middleware', err });
  }

  const akamaiXTrueClientIP = req.header('x-true-client-ip');
  req.trueClientIp = akamaiXTrueClientIP && isIP(akamaiXTrueClientIP) && !inRange(akamaiXTrueClientIP, rateLimitConfigs.cdpTrustedIp)
    ? akamaiXTrueClientIP
    : '';

  next();
};

module.exports = rateLimitMiddleware;
