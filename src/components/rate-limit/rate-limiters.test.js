const rateLimit = require('express-rate-limit');

const {
  campaignsClientRateLimiter,
  campaignsTotalRateLimiter,
  alertsTotalRateLimiter,
  alertsClientRateLimiter,
  contentsCatalogTotalRateLimiter,
} = require('./rate-limiters');

// Mock express-rate-limit
jest.mock('express-rate-limit');

const tooManyRequestError = 'Too Many Requests';

describe('campaignsClientRateLimiter', () => {
  let mockLogger;
  let rateLimitConfigs;
  let mockRateLimitInstance;
  let capturedConfig;

  beforeEach(() => {
    mockLogger = {
      warn: jest.fn(),
    };

    rateLimitConfigs = {
      window: 60000,
    };

    mockRateLimitInstance = jest.fn();

    // Capture the config passed to rateLimit
    rateLimit.mockImplementation((config) => {
      capturedConfig = config;
      return mockRateLimitInstance;
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should create rate limiter with correct configuration - campaign client', () => {
    campaignsClientRateLimiter(rateLimitConfigs, mockLogger);

    expect(rateLimit).toHaveBeenCalledWith(expect.objectContaining({
      windowMs: 60000,
      standardHeaders: true,
      legacyHeaders: false,
      requestPropertyName: 'campaignsClientRateLimit',
    }));
  });

  test('should return correct max value when trueClientIp exists', () => {
    campaignsClientRateLimiter(rateLimitConfigs, mockLogger);

    const req = {
      trueClientIp: '***********',
      ldRateLimitConfig: {
        client: {
          campaigns: 10,
        },
      },
    };
    const res = {};

    const maxValue = capturedConfig.max(req, res);
    expect(maxValue).toBe(10);
  });

  test('should return 0 when trueClientIp is missing', () => {
    campaignsClientRateLimiter(rateLimitConfigs, mockLogger);

    const req = {
      trueClientIp: null,
      ldRateLimitConfig: {
        client: {
          campaigns: 10,
        },
      },
    };
    const res = {};

    const maxValue = capturedConfig.max(req, res);
    expect(maxValue).toBe(0);
  });

  test('should return 0 when trueClientIp is empty string', () => {
    campaignsClientRateLimiter(rateLimitConfigs, mockLogger);

    const req = {
      trueClientIp: '',
      ldRateLimitConfig: {
        client: {
          campaigns: 5,
        },
      },
    };
    const res = {};

    const maxValue = capturedConfig.max(req, res);
    expect(maxValue).toBe(0);
  });

  test('should use trueClientIp as key generator', () => {
    campaignsClientRateLimiter(rateLimitConfigs, mockLogger);

    const req = {
      trueClientIp: '********',
    };
    const res = {};

    const key = capturedConfig.keyGenerator(req, res);
    expect(key).toBe('********');
  });

  test('should log warning and send response when rate limit is hit - campaigns client', () => {
    campaignsClientRateLimiter(rateLimitConfigs, mockLogger);

    const req = {
      trueClientIp: '*************',
    };
    const res = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn(),
    };
    const next = jest.fn();
    const options = {
      statusCode: 429,
      message: tooManyRequestError,
    };

    capturedConfig.handler(req, res, next, options);

    expect(mockLogger.warn).toHaveBeenCalledWith({
      message: 'Client /campaigns rate limit hit for ip *************',
    });
    expect(res.status).toHaveBeenCalledWith(429);
    expect(res.send).toHaveBeenCalledWith(tooManyRequestError);
  });

  test('should handle undefined trueClientIp in handler', () => {
    campaignsClientRateLimiter(rateLimitConfigs, mockLogger);

    const req = {
      trueClientIp: undefined,
    };
    const res = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn(),
    };
    const next = jest.fn();
    const options = {
      statusCode: 429,
      message: tooManyRequestError,
    };

    capturedConfig.handler(req, res, next, options);

    expect(mockLogger.warn).toHaveBeenCalledWith({
      message: 'Client /campaigns rate limit hit for ip undefined',
    });
    expect(res.status).toHaveBeenCalledWith(429);
    expect(res.send).toHaveBeenCalledWith('Too Many Requests');
  });

  test('should return rate limiter instance', () => {
    const result = campaignsClientRateLimiter(rateLimitConfigs, mockLogger);
    expect(result).toBe(mockRateLimitInstance);
  });
});

describe('campaignsTotalRateLimiter', () => {
  let mockLogger;
  let rateLimitConfigs;
  let mockRateLimitInstance;
  let capturedConfig;

  beforeEach(() => {
    mockLogger = {
      warn: jest.fn(),
    };

    rateLimitConfigs = {
      window: 30000,
    };

    mockRateLimitInstance = jest.fn();

    rateLimit.mockImplementation((config) => {
      capturedConfig = config;
      return mockRateLimitInstance;
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should create rate limiter with correct configuration - campaigns client', () => {
    campaignsTotalRateLimiter(rateLimitConfigs, mockLogger);

    expect(rateLimit).toHaveBeenCalledWith(expect.objectContaining({
      windowMs: 30000,
      standardHeaders: true,
      legacyHeaders: false,
      requestPropertyName: 'campaignsTotalRateLimit',
    }));
  });

  test('should return correct max value from LD config - campaigns client', () => {
    campaignsTotalRateLimiter(rateLimitConfigs, mockLogger);

    const req = {
      ldRateLimitConfig: {
        overall: {
          campaigns: 100,
        },
      },
    };
    const res = {};

    const maxValue = capturedConfig.max(req, res);
    expect(maxValue).toBe(100);
  });

  test('should log warning and send response when rate limit is hit - campaigns client', () => {
    campaignsTotalRateLimiter(rateLimitConfigs, mockLogger);

    const req = {
      ip: '***********',
    };
    const res = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn(),
    };
    const next = jest.fn();
    const options = {
      statusCode: 429,
      message: tooManyRequestError,
    };

    capturedConfig.handler(req, res, next, options);

    expect(mockLogger.warn).toHaveBeenCalledWith({
      message: 'Total /campaigns rate limit hit for instance ***********',
    });
    expect(res.status).toHaveBeenCalledWith(429);
    expect(res.send).toHaveBeenCalledWith(tooManyRequestError);
  });
});

describe('alertsTotalRateLimiter', () => {
  let mockLogger;
  let rateLimitConfigs;
  let mockRateLimitInstance;
  let capturedConfig;

  beforeEach(() => {
    mockLogger = {
      warn: jest.fn(),
    };

    rateLimitConfigs = {
      window: 45000,
    };

    mockRateLimitInstance = jest.fn();

    rateLimit.mockImplementation((config) => {
      capturedConfig = config;
      return mockRateLimitInstance;
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should create rate limiter with correct configuration - alerts', () => {
    alertsTotalRateLimiter(rateLimitConfigs, mockLogger);

    expect(rateLimit).toHaveBeenCalledWith(expect.objectContaining({
      windowMs: 45000,
      standardHeaders: true,
      legacyHeaders: false,
      requestPropertyName: 'alertsTotalRateLimit',
    }));
  });

  test('should return correct max value from LD config', () => {
    alertsTotalRateLimiter(rateLimitConfigs, mockLogger);

    const req = {
      ldRateLimitConfig: {
        overall: {
          alerts: 50,
        },
      },
    };
    const res = {};

    const maxValue = capturedConfig.max(req, res);
    expect(maxValue).toBe(50);
  });

  test('should log warning and send response when rate limit is hit - alerts', () => {
    alertsTotalRateLimiter(rateLimitConfigs, mockLogger);

    const req = {
      ip: '************',
    };
    const res = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn(),
    };
    const next = jest.fn();
    const options = {
      statusCode: 429,
      message: tooManyRequestError,
    };

    capturedConfig.handler(req, res, next, options);

    expect(mockLogger.warn).toHaveBeenCalledWith({
      message: 'Total /alerts rate limit hit for instance ************',
    });
    expect(res.status).toHaveBeenCalledWith(429);
    expect(res.send).toHaveBeenCalledWith(tooManyRequestError);
  });
});

describe('alertsClientRateLimiter', () => {
  let mockLogger;
  let rateLimitConfigs;
  let mockRateLimitInstance;
  let capturedConfig;

  beforeEach(() => {
    mockLogger = {
      warn: jest.fn(),
    };

    rateLimitConfigs = {
      window: 90000,
    };

    mockRateLimitInstance = jest.fn();

    rateLimit.mockImplementation((config) => {
      capturedConfig = config;
      return mockRateLimitInstance;
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should create rate limiter with correct configuration', () => {
    alertsClientRateLimiter(rateLimitConfigs, mockLogger);

    expect(rateLimit).toHaveBeenCalledWith(expect.objectContaining({
      windowMs: 90000,
      standardHeaders: true,
      legacyHeaders: false,
      requestPropertyName: 'alertsClientRateLimit',
    }));
  });

  test('should return correct max value when trueClientIp exists', () => {
    alertsClientRateLimiter(rateLimitConfigs, mockLogger);

    const req = {
      trueClientIp: '**********',
      ldRateLimitConfig: {
        client: {
          alerts: 20,
        },
      },
    };
    const res = {};

    const maxValue = capturedConfig.max(req, res);
    expect(maxValue).toBe(20);
  });

  test('should return 0 when trueClientIp is missing', () => {
    alertsClientRateLimiter(rateLimitConfigs, mockLogger);

    const req = {
      trueClientIp: null,
      ldRateLimitConfig: {
        client: {
          alerts: 15,
        },
      },
    };
    const res = {};

    const maxValue = capturedConfig.max(req, res);
    expect(maxValue).toBe(0);
  });

  test('should use trueClientIp as key generator', () => {
    alertsClientRateLimiter(rateLimitConfigs, mockLogger);

    const req = {
      trueClientIp: '********',
    };
    const res = {};

    const key = capturedConfig.keyGenerator(req, res);
    expect(key).toBe('********');
  });

  test('should log warning and send response when rate limit is hit', () => {
    alertsClientRateLimiter(rateLimitConfigs, mockLogger);

    const req = {
      trueClientIp: '*********',
    };
    const res = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn(),
    };
    const next = jest.fn();
    const options = {
      statusCode: 429,
      message: tooManyRequestError,
    };

    capturedConfig.handler(req, res, next, options);

    expect(mockLogger.warn).toHaveBeenCalledWith({
      message: 'Client /alerts rate limit hit for ip *********',
    });
    expect(res.status).toHaveBeenCalledWith(429);
    expect(res.send).toHaveBeenCalledWith(tooManyRequestError);
  });
});

describe('contentsCatalogTotalRateLimiter', () => {
  let mockLogger;
  let rateLimitConfigs;
  let mockRateLimitInstance;
  let capturedConfig;

  beforeEach(() => {
    mockLogger = {
      warn: jest.fn(),
    };

    rateLimitConfigs = {
      window: 120000,
    };

    mockRateLimitInstance = jest.fn();

    rateLimit.mockImplementation((config) => {
      capturedConfig = config;
      return mockRateLimitInstance;
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should create rate limiter with correct configuration', () => {
    contentsCatalogTotalRateLimiter(rateLimitConfigs, mockLogger);

    expect(rateLimit).toHaveBeenCalledWith(expect.objectContaining({
      windowMs: 120000,
      standardHeaders: true,
      legacyHeaders: false,
      requestPropertyName: 'contentsCatalogTotalRateLimit',
    }));
  });

  test('should return correct max value from LD config', () => {
    contentsCatalogTotalRateLimiter(rateLimitConfigs, mockLogger);

    const req = {
      ldRateLimitConfig: {
        overall: {
          contentsCatalog: 25,
        },
      },
    };
    const res = {};

    const maxValue = capturedConfig.max(req, res);
    expect(maxValue).toBe(25);
  });

  test('should log warning and send response when rate limit is hit', () => {
    contentsCatalogTotalRateLimiter(rateLimitConfigs, mockLogger);

    const req = {
      ip: '***********00',
    };
    const res = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn(),
    };
    const next = jest.fn();
    const options = {
      statusCode: 429,
      message: tooManyRequestError,
    };

    capturedConfig.handler(req, res, next, options);

    expect(mockLogger.warn).toHaveBeenCalledWith({
      message: 'Total /contents-catalog rate limit hit for instance ***********00',
    });
    expect(res.status).toHaveBeenCalledWith(429);
    expect(res.send).toHaveBeenCalledWith(tooManyRequestError);
  });
});
