const rateLimit = require('express-rate-limit');

/**
 * Rate limiter middlewares:
    * Limit the number of requests allowed per instance ip & the number of requests allowed by the client ip
 * Rate limit params:
    * windowMs - Time frame for which requests are checked/remembered. Set in manifest files
    * max - The maximum number of connections to allow during the window before rate limiting the client. Pulled from LD on every request, falls back to value in manifest if LD cannot be reached
 * @param {*} rateLimitConfigs - rate limit configs defined in manifest files
 * @param {*} logger
 * @returns rate limiter middleware
 */

const campaignsTotalRateLimiter = (rateLimitConfigs, logger) => rateLimit({
  windowMs: rateLimitConfigs.window,
  max: (req, res) => req.ldRateLimitConfig.overall.campaigns,
  standardHeaders: true,
  legacyHeaders: false,
  requestPropertyName: 'campaignsTotalRateLimit',
  handler: (req, res, next, options) => {
    logger.warn({ message: `Total /campaigns rate limit hit for instance ${req.ip}` });
    return res.status(options.statusCode).send(options.message);
  },
});

const campaignsClientRateLimiter = (rateLimitConfigs, logger) => rateLimit({
  windowMs: rateLimitConfigs.window,
  max: (req, res) => {
    if (req.trueClientIp) {
      return req.ldRateLimitConfig.client.campaigns;
    }
    return 0; // if unable to determine client ip, client rate limiting is disabled
  },
  standardHeaders: true,
  legacyHeaders: false,
  requestPropertyName: 'campaignsClientRateLimit',
  keyGenerator: (req, res) => req.trueClientIp,
  handler: (req, res, next, options) => {
    logger.warn({ message: `Client /campaigns rate limit hit for ip ${req.trueClientIp}` });
    return res.status(options.statusCode).send(options.message);
  },
});

const alertsTotalRateLimiter = (rateLimitConfigs, logger) => rateLimit({
  windowMs: rateLimitConfigs.window,
  max: (req, res) => req.ldRateLimitConfig.overall.alerts,
  standardHeaders: true,
  legacyHeaders: false,
  requestPropertyName: 'alertsTotalRateLimit',
  handler: (req, res, next, options) => {
    logger.warn({ message: `Total /alerts rate limit hit for instance ${req.ip}` });
    return res.status(options.statusCode).send(options.message);
  },
});

const alertsClientRateLimiter = (rateLimitConfigs, logger) => rateLimit({
  windowMs: rateLimitConfigs.window,
  max: (req, res) => {
    if (req.trueClientIp) {
      return req.ldRateLimitConfig.client.alerts;
    }
    return 0; // if unable to determine client ip, client rate limiting is disabled
  },
  standardHeaders: true,
  legacyHeaders: false,
  requestPropertyName: 'alertsClientRateLimit',
  keyGenerator: (req, res) => req.trueClientIp,
  handler: (req, res, next, options) => {
    logger.warn({ message: `Client /alerts rate limit hit for ip ${req.trueClientIp}` });
    return res.status(options.statusCode).send(options.message);
  },
});

const contentsCatalogTotalRateLimiter = (rateLimitConfigs, logger) => rateLimit({
  windowMs: rateLimitConfigs.window,
  max: (req, res) => req.ldRateLimitConfig.overall.contentsCatalog,
  standardHeaders: true,
  legacyHeaders: false,
  requestPropertyName: 'contentsCatalogTotalRateLimit',
  handler: (req, res, next, options) => {
    logger.warn({ message: `Total /contents-catalog rate limit hit for instance ${req.ip}` });
    return res.status(options.statusCode).send(options.message);
  },
});

module.exports = {
  campaignsTotalRateLimiter,
  campaignsClientRateLimiter,
  alertsTotalRateLimiter,
  alertsClientRateLimiter,
  contentsCatalogTotalRateLimiter,
};
