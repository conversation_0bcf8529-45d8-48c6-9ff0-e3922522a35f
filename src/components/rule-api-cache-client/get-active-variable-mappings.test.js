const getActiveMappingSet = require('./get-active-variable-mappings');

const mockFetch = jest.fn();
const mockTokenService = {
  get: jest.fn(),
};
const mockUri = 'https://cloud.bns';
const mockVariableMapping = {
  'data': [
    {
      'variable_template': 'SOLUI_CUST_FULL_NAME_END',
      'variable_campaign': 'cust_full_name',
      'variable_type': 'text',
    },
    {
      'variable_template': 'SOLUI_EXPIRY_DATE_END',
      'source_campaign': 'expiry_date',
      'variable_type': 'date',
    },
  ],
};

describe('Campaign API Client', () => {
  beforeEach(() => {
    mockFetch.mockClear();
    mockTokenService.get.mockClear();
  });
  test('should throw an error on reponse status >= 400', async () => {
    const mockResponse = { status: 400, ok: false, statusText: 'bad request', text: jest.fn().mockResolvedValueOnce(JSON.stringify({})) };
    mockTokenService.get.mockResolvedValueOnce('sample-token');
    mockFetch.mockResolvedValueOnce(mockResponse);
    try {
      const res = await getActiveMappingSet({ fetch: mockFetch, tokenService: mockTokenService }, mockUri);
      throw new Error(`successful response: ${JSON.stringify(res)}`);
    } catch (err) {
      expect(err).toBeDefined();
      expect(err.message).toEqual(mockResponse.statusText);
    }
  });
  test('should parse out successful response data', async () => {
    const mockResponse = { status: 200, ok: true, statusText: 'good request', text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockVariableMapping)) };
    mockTokenService.get.mockResolvedValueOnce('sample-token');
    mockFetch.mockResolvedValueOnce(mockResponse);
    const res = await getActiveMappingSet({ fetch: mockFetch, tokenService: mockTokenService }, mockUri);
    expect(res).toEqual(mockVariableMapping.data);
  });
});
