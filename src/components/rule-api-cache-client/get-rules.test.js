const getRules = require('./get-rules');

describe('get rules for rule cache service', () => {
  it('should return null and report error when error fetching rules', async () => {
    const fetchMock = jest.fn().mockResolvedValue({
      ok: false,
    });
    const loggerMock = {
      error: jest.fn(),
    };
    const contentServiceMock = {
      getContentsByIds: jest.fn(),
    };
    const rules = await getRules({
      fetch: fetchMock,
      logger: loggerMock,
      contentService: contentServiceMock,
    }, '/', 'alerts', false, [], {});
    expect(rules).toEqual(null);
    expect(loggerMock.error).toHaveBeenCalledTimes(1);
  });

  it('should return null and report error when fetch throws', async () => {
    const fetchMock = jest.fn().mockRejectedValue({
      error: true,
    });
    const loggerMock = {
      error: jest.fn(),
    };
    const contentServiceMock = {
      getContentsByIds: jest.fn(),
    };
    const rules = await getRules({
      fetch: fetchMock,
      logger: loggerMock,
      contentService: contentServiceMock,
    }, '/', 'alerts', false, [], {});
    expect(rules).toEqual(null);
    expect(loggerMock.error).toHaveBeenCalledTimes(1);
  });

  it('should return empty object when 0 rules', async () => {
    const fetchMock = jest.fn().mockResolvedValue({
      ok: true,
      text: jest.fn().mockResolvedValue(`{
        "items": []
      }`),
    });
    const loggerMock = {
      error: jest.fn(),
    };
    const contentServiceMock = {
      getContentsByIds: jest.fn(),
    };
    const rules = await getRules({
      fetch: fetchMock,
      logger: loggerMock,
      contentService: contentServiceMock,
    }, '/', 'alerts', false, [], {});
    expect(rules).toEqual({});
  });

  it('should return rules without content', async () => {
    const fetchMock = jest.fn().mockResolvedValue({
      ok: true,
      text: jest.fn().mockResolvedValue(`{
        "items": [{
          "id": "rule_id_1",
          "other_rule_prop": "other_rule_value"
        }]
      }`),
    });
    const loggerMock = {
      error: jest.fn(),
    };
    const contentServiceMock = {
      getContentsByIds: jest.fn(),
    };
    const rules = await getRules({
      fetch: fetchMock,
      logger: loggerMock,
      contentService: contentServiceMock,
    }, '/', 'alerts', false, [], {});
    expect(rules).toEqual({
      rule_id_1: {
        id: 'rule_id_1',
        other_rule_prop: 'other_rule_value',
      },
    });
  });

  it('should return rules with content (multi-locale)', async () => {
    const fetchMock = jest.fn().mockResolvedValue({
      ok: true,
      text: jest.fn().mockResolvedValue(`{
        "items": [{
          "id": "rule_id_1",
          "other_rule_prop": "other_rule_value",
          "content_id": "content_id_1",
          "content_space": "content_space"
        }]
      }`),
    });
    const loggerMock = {
      error: jest.fn(),
    };
    const contentServiceMock = {
      getContentsByIds: jest.fn().mockReturnValue({
        data: {
          items: [ {
            id: 'content_id_1',
            content: {
              content_prop: 'content_value',
            },
          } ],
        },
      }),
    };
    const rules = await getRules({
      fetch: fetchMock,
      logger: loggerMock,
      contentService: contentServiceMock,
    }, '/', 'campaigns', true, [ 'en', 'fr' ], {});
    expect(contentServiceMock.getContentsByIds).toHaveBeenCalledWith('content_space', [ 'content_id_1' ], {
      language: 'en',
    });
    expect(rules).toEqual({
      en: {
        rule_id_1: {
          id: 'rule_id_1',
          other_rule_prop: 'other_rule_value',
          content_id: 'content_id_1',
          content_space: 'content_space',
          content: {
            content_prop: 'content_value',
          },
        },
      },
      fr: {
        rule_id_1: {
          id: 'rule_id_1',
          other_rule_prop: 'other_rule_value',
          content_id: 'content_id_1',
          content_space: 'content_space',
          content: {
            content_prop: 'content_value',
          },
        },
      },
    });
  });
});
