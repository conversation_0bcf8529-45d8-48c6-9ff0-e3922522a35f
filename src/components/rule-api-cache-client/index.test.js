const CacheClient = require('./index');

const mockAlertCache = {
  rule_id_1: {
    rule_prop_1: 'rule_value_1',
    rule_prop_2: 'rule_value_2',
  },
};

const mockVariableMapping = {
  variable_mappings: [
    {
      'variable_template': 'SOLUI_EXPIRY_DATE_END',
      'variable_campaign': 'expiry_date',
      'variable_type': 'date',
    },
    {
      'variable_template': 'SOLUI_CUST_FULL_NAME_END',
      'variable_campaign': 'cust_full_name',
      'variable_type': 'text',
    },
    {
      'variable_template': 'SOLUI_APPROVED_CREDIT_LIMIT_END',
      'variable_campaign': 'approved_credit_limit',
      'variable_type': 'currency',
    },
    {
      'variable_template': 'SOLUI_CURRENT_ACCOUNT_NUMBER_END',
      'variable_campaign': 'current_account_number',
      'variable_type': 'account-number-mask',
    },
  ],
};

jest.mock('./get-rules', () => () => mockAlertCache);
jest.mock('./get-active-variable-mappings', () => () => mockVariableMapping);

describe('Rule Cache Client', () => {
  beforeAll(() => {
    jest.useFakeTimers();
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  it('should initialize and update alert cache correctly', async () => {
    const fetchMock = jest.fn();
    const contentServiceMock = jest.fn();
    const loggerMock = { error: jest.fn(), info: jest.fn() };
    const client = await CacheClient({
      fetch: fetchMock,
      contentService: contentServiceMock,
      logger: loggerMock,
    }, '', 'alerts', true, [ 'en', 'ru' ], 1);
    expect(typeof client).toEqual('object');
    expect(client).toHaveProperty('get');
    jest.advanceTimersByTime(2);
    expect(client.get()).toEqual({ alerts: mockAlertCache });
  });
  it('should initialize and update variable-mapping cache correctly', async () => {
    const fetchMock = jest.fn();
    const loggerMock = { error: jest.fn(), info: jest.fn() };
    const client = await CacheClient({
      logger: loggerMock,
      fetch: fetchMock,
    }, '', 'variable_mappings', false, [], 1);
    expect(typeof client).toEqual('object');
    expect(client).toHaveProperty('get');
    jest.advanceTimersByTime(2);
    expect(client.get()).toEqual({ variable_mappings: mockVariableMapping });
  });
});
