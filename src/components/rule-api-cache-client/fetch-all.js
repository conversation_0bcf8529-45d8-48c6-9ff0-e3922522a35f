const qs = require('querystring');
const { getResponse } = require('../common');

const fetchAll = async ({ fetch }, basePath, target = 'campaigns', query = {}) => {
  const endpoint = target === 'campaigns' ? '/campaign-rules' : '/alert-rules';
  const queryParams = target === 'campaigns' ? { ...query, type: 'all' } : query;
  const result = await fetch(`${basePath}${endpoint}${Object.keys(queryParams).length > 0 ? '?' + qs.stringify(queryParams) : ''}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json; charset=utf-8',
      Accept: 'application/json',
    },
  });
  return getResponse(result);
};

module.exports = fetchAll;
