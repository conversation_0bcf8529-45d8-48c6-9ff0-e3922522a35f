/* eslint-disable no-unused-vars */
const fetchAll = require('./fetch-all');
const { mergeContentIntoRules, arrToObj } = require('../common');
const splunkErrorCodes = require('../campaign/routes/common').splunkErrorCodes;
const R = require('ramda');

const getRules = async ({
  fetch,
  logger,
  contentService,
},
  basePath,
  target,
  includeContent,
  locales,
  query = {},
) => {
  let rules;
  try {
    const response = await fetchAll({ fetch }, basePath, target, query);
    rules = response.items || [];
  } catch (err) {
    logger.error({ message: 'unable to fetch a list of rules', err, target, query, code: 1004, description: splunkErrorCodes[1004] });
    return null;
  }
  if (rules.length === 0) {
    return {};
  }
  if (!includeContent) {
    return arrToObj(rules, 'id');
  }
  let spaces;
  if (rules.length > 0) {
    spaces = R.uniq(rules.map(r => r.content_space));
  }

  const contentIds = rules.map(({ content_id }) => content_id); // eslint-disable-line

  try {
    // fetch contents in batch in parallel for each locale
    const contents = await Promise.all(locales.map(language => new Promise(async (resolve, reject) => {
      try {
        let contentOut = {};
        for (const s of spaces) {
          const outputData = await contentService.getContentsByIds(s, contentIds, { language });
          Object.assign(contentOut, arrToObj(outputData.data.items, 'id'));
        }
        return resolve({ [language]: contentOut || [] });
      } catch (err) {
        logger.error({ message: `unable to get batch content`, err, language, contentIds });
        return reject(err);
      }
    })));
    // return rules with contents
    return contents.reduce((o, current) => {
      const [ locale, contentEntries ] = Object.entries(current)[0];
      return Object.assign(o, {
        [locale]: mergeContentIntoRules({ logger }, arrToObj(rules, 'id'), contentEntries),
      });
    }, {});
  } catch (err) {
    logger.error({ message: 'unable to get content for a list of rules', err, query, code: 1010, description: splunkErrorCodes[1010] });
  }
};

module.exports = getRules;
