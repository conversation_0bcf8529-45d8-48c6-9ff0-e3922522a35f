const { getResponse } = require('../common');

const opts = {
  method: 'GET',
  headers: { 'Content-Type': 'application/json; charset=utf-8', Accept: 'application/json' },
};

const getActiveMappingSet = async ({ fetch }, basePath) => {
  const result = await fetch(`${basePath}/variable-mappings`, opts);
  const response = await getResponse(result);
  return response.data || [];
};

module.exports = getActiveMappingSet;
