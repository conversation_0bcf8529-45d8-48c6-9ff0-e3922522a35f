const { getResponse } = require('../common');

const opts = {
  method: 'GET',
  headers: { 'Content-Type': 'application/json; charset=utf-8', Accept: 'application/json' },
};

const getContainers = async ({ fetch }, basePath) => {
  const result = await fetch(`${basePath}/containers`, opts);
  const response = await getResponse(result);
  return response.data || [];
};

module.exports = getContainers;
