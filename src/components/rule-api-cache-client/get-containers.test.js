const getContainers = require('./get-containers');
const { getResponse } = require('../common');

jest.mock('../common', () => ({
  getResponse: jest.fn(),
}));

const mockFetch = jest.fn();
const mockBasePath = 'https://cloud.bns';
const mockContainers = {
  data: [
    { id: 'container1', name: 'Container One' },
    { id: 'container2', name: 'Container Two' },
  ],
};

describe('getContainers', () => {
  beforeEach(() => {
    mockFetch.mockClear();
    getResponse.mockClear();
  });

  test('should throw an error on response status >= 400', async () => {
    const mockResponse = {
      status: 400,
      ok: false,
      statusText: 'Bad Request',
      text: jest.fn().mockResolvedValueOnce(JSON.stringify({})),
    };
    mockFetch.mockResolvedValueOnce(mockResponse);
    getResponse.mockImplementationOnce(() => {
      throw new Error(mockResponse.statusText);
    });

    try {
      await getContainers({ fetch: mockFetch }, mockBasePath);
      throw new Error('Expected function to throw');
    } catch (err) {
      expect(err).toBeDefined();
      expect(err.message).toEqual('Bad Request');
    }
  });

  test('should return parsed container data on success', async () => {
    const mockResponse = {
      status: 200,
      ok: true,
      statusText: 'OK',
      text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockContainers)),
    };
    mockFetch.mockResolvedValueOnce(mockResponse);
    getResponse.mockResolvedValueOnce(mockContainers);

    const result = await getContainers({ fetch: mockFetch }, mockBasePath);
    expect(result).toEqual(mockContainers.data);
  });

  test('should return empty array if response has no data', async () => {
    const mockResponse = {
      status: 200,
      ok: true,
      statusText: 'OK',
      text: jest.fn().mockResolvedValueOnce(JSON.stringify({})),
    };
    mockFetch.mockResolvedValueOnce(mockResponse);
    getResponse.mockResolvedValueOnce({});

    const result = await getContainers({ fetch: mockFetch }, mockBasePath);
    expect(result).toEqual([]);
  });
});
