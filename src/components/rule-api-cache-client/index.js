const getRules = require('./get-rules');
const getActiveMappingSet = require('./get-active-variable-mappings');
const getContainers = require('./get-containers');

const baseQuery = {
  status: 'published',
  disabled: false,
  limit: 5000,
};

const queryAllAlerts = {
  sort: '-updated_at',
  ...baseQuery,
};

const queryAllCampaigns = baseQuery;

const updateCache = async ({ fetch, contentService, logger }, basePath, target, withContent, locales) => {
  if (target === 'variable_mappings') {
    return getActiveMappingSet({ fetch }, basePath);
  }
  if (target === 'containers') {
    return getContainers({ fetch }, basePath);
  }
  const query = target === 'alerts' ? queryAllAlerts : queryAllCampaigns;
  return getRules({
    fetch,
    logger,
    contentService,
  }, basePath, target, withContent, locales, {
    ...query,
    now: (new Date()).toISOString(),
  });
};

const recursiveCacheRefresh = async ({ fetch, contentService, logger }, basePath, target, withContent, locales, cacheTTL, cachePointer) => {
  try {
    const newCacheValue = await updateCache({ fetch, contentService, logger }, basePath, target, withContent, locales);
    cachePointer[`${target}`] = newCacheValue;
    // logger.info(`rules from cache updated for ${target} ${withContent ? 'with contents' : ''}`);
  } catch (err) {
    logger.error({
      message: `could not update cache for target: ${target}`,
      err: { message: err.message, stack: err.stack },
    });
  }
  return setTimeout(
    recursiveCacheRefresh.bind(
      'no context', {
        fetch,
        contentService,
        logger,
      }, basePath, target, withContent, locales, cacheTTL, cachePointer,
    ),
    cacheTTL,
  );
};

const init = async ({ fetch, contentService, logger }, basePath, target, withContent, locales, cacheTTL) => {
  let cache = {};
  // begin polling
  const refreshJob = await recursiveCacheRefresh({
    fetch,
    contentService,
    logger,
  }, basePath, target, withContent, locales, cacheTTL, cache);
  return {
    get: () => cache,
    refreshJob,
  };
};

module.exports = init;
