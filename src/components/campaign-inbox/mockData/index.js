// Mock constants
const defaultCardNumber = '****************';
const config = {
  features: { application: true, disposition: true },
};

const authorizationToken = 'authorization.token';

// Request and Response
const mockRequest = () => ({
  headers: {
    'authorization': `Bearer ${authorizationToken}`,
    'x-customer-scotiacard': defaultCardNumber,
    'x-b3-traceid': 'fd891ff840c5fed4d0b40c118cf674b5',
    'x-b3-spanid': 'd9dfe1006bc577a8',
    'x-channel-id': 'Online',
    'x-originating-appl-code': 'xyz',
    'x-country-code': 'CA',
    'x-language': 'en',
    'x-application': 'M1',
  },
  get: jest.fn().mockImplementation((arg) => {
    if (arg === 'authorization') return `Bearer ${authorizationToken}`;
    if (arg === 'x-customer-scotiacard') return defaultCardNumber;
    if (arg === 'x-b3-traceid') return 'fd891ff840c5fed4d0b40c118cf674b5';
    if (arg === 'x-b3-spanid') return 'd9dfe1006bc577a8';
    if (arg === 'x-channel-id') return 'Online';
    if (arg === 'x-originating-appl-code') return 'xyz';
    if (arg === 'x-country-code') return 'CA';
    if (arg === 'x-language') return 'en';
    if (arg === 'x-application') return 'M1';
  }),
  query: {},
});
const mockResponse = () => {
  const res = {
    locals: {
      language: 'en',
      languageContentful: 'en',
    },
  };
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};

const baseRuleFields = {
  start_at: '2023-01-01T04:00:00.000Z',
  end_at: '2030-01-01T04:00:00.000Z',
  content_space: '4szkx38resvm',
  container: 'orion-campaign',
  pages: [ 'orion-account-summary' ],
  platforms: [ 'web' ],
  platforms_targeting: [ { v: 1, platform: 'web', items: [] } ],
  app_version: null,
  urgent: true,
  type: 'campaign',
  dismissable_flag: true,
  status: 'published',
  disabled: false,
  application: 'orion',
};

// Mass Targeting
const massTargeting = {
  by_product: {
    any_of: [ // target any/all products
      { ownership: 'R' },
      { ownership: 'B' },
    ],
  },
  by_scene_points: {
    targeting_criteria: 'greater',
    points: 1000,
  },
  enrollment_status: [],
};

// Priority Message Rule
const mockPriorityMessageRule = {
  ...baseRuleFields,
  id: 'RSowWu1cvsBk',
  name: 'Mock Mass Message Rule',
  content_type: 'targetedCampaign',
  content_id: '3JNYCPNdDFVnSLRHkBlwgk',
  external_ref: 'MESSAGE',
  mass_targeting: { v: 1 },
};

// Mass Campaign Rule
const mockMassCampaignRule = {
  ...baseRuleFields,
  id: 'MvAEPYaM2e6T',
  name: 'Mock Mass Campaign Rule',
  content_type: 'nova_campaign',
  content_id: '5qIdrK7TBRYrn7cwBfVaWS',
  external_ref: 'MASS',
};

// Target Campaign Rule
const mockTargetedCampaignRule = {
  ...baseRuleFields,
  id: 'U8HHmCLcF2eg',
  name: '5571112101 Mock Pega Targeted Rule',
  content_type: 'targetedCampaign',
  content_id: '1B54DYwMRVIqxtRUYtEf7r',
  external_ref: '5571112101',
  mass_targeting: {
    product_pages: {},
    enrollment_status: [],
  },
};

const mockContentResponse = {
  data: {
    total: 3,
    items: [
      {
        id: mockPriorityMessageRule.content_id,
        type: 'targetedCampaignPreview',
        language: 'en-US',
        content: {
          name: 'Priority Message Name',
          title: 'Priority Message Title',
        },
      },
      {
        id: mockMassCampaignRule.content_id,
        type: 'nova__rewards-campaign-preview',
        language: 'en-US',
        content: {
          name: 'Mass Campaign Name',
          title: 'Mass Campaign Title',
        },
      },
      {
        id: mockTargetedCampaignRule.content_id,
        type: 'targetedCampaignPreview',
        language: 'en-US',
        content: {
          name: 'Pega Targeted Campaign Name',
          title: 'Pega Targeted Campaign Title',
        },
      },
    ],
  },
  notifications: [],
};

const mockAccountsResponse = {
  accountList: [
    {
      accountUniqueId: 'AAAAAAAAAAAAAAAAAAAA=',
      type: 'Saving',
      ciProductCode: 'AAA',
      ciProductSubCode: 'A1',
      productSystem: 'AAA',
      ownership: 'R',
      dateOpened: '2018-01-08T00:00:00-0500',
      dateClosed: '',
    },
    {
      accountUniqueId: 'AAAAAAAAAAAAAAAAAAAB=',
      type: 'Borrowing',
      ciProductCode: 'BBB',
      ciProductSubCode: 'B1',
      productSystem: 'BBB',
      ownership: 'B',
      dateOpened: '2018-01-09T00:00:00-0500',
      dateClosed: '',
    },
  ],
};

// Insights Response
const mockInsightsResponse = {
  data: [
    {
      message_id: '6c9d31ad-24c3-33e4-a68d-dc4a5c6393ee-********',
      message_source: 'PEGAV2',
      message_status: 'N',
      message_response: null,
      subject_line: 'Pre-approved unsecured ScotiaLine Personal line of credit, competitive rate of interest, (refer to offer interest rate field), time limited',
      language: 'en',
      campaign_id: mockTargetedCampaignRule.external_ref,
      campaign_code: 'OP12020',
      message_category: null,
      pointer_text: null,
      start_date: '2020-01-01',
      expiry_date: '2030-01-31',
      conf_number: null,
      additional_data: [
        { name: 'savings', value: '1124' },
        { name: 'uspo_key', value: 'USV000003908' },
        { name: 'prime_as_of_date', value: '2023-01-26' },
        { name: 'primary_source_code', value: '106' },
        { name: 'primary_product_service_code', value: 'VIC' },
      ],
    },
  ],
};

// Rule API container's data response
const mockRuleAPIContainersResponse = {
  data: [
    {
      admin_container_id: 1,
      admin_container_name: 'Orion Priority Container',
      admin_container_description: 'Orion Priority Container',
      admin_rule_type_id: 1,
      content_type: '["alert"]',
      created_by: '<EMAIL>',
      created_ts: '2019-10-16T14:57:34.440Z',
      updated_by: 'u2ba6b476b5a',
      updated_ts: '2023-05-23T19:48:02.103Z',
      subject_line: false,
      admin_container_slug: 'orion-campaign',
      admin_container_status: true,
      admin_application_id: 3,
      admin_container_limit: 1,
    },
    {
      admin_container_id: 2,
      admin_container_name: 'Orion Inbox Container',
      admin_container_description: 'Orion Inbox Container',
      admin_rule_type_id: 2,
      content_type: '["standingCampaign"]',
      created_by: '<EMAIL>',
      created_ts: '2019-10-16T14:57:34.450Z',
      updated_by: 'u2ba6b476b5a',
      updated_ts: '2023-03-31T14:33:32.916Z',
      subject_line: false,
      admin_container_slug: 'inbox-updates---orion',
      admin_container_status: true,
      admin_application_id: 3,
      admin_container_limit: 2,
    },
  ],
};

// Mock services
const mockCampaignCacheService = {
  get: jest.fn(),
};
const mockContainerCacheService = {
  get: jest.fn(),
};
const mockTargetedCampaignService = { getInstance: jest.fn() };
const mockDispositionsService = {
  getDispositions: jest.fn(),
  setDisposition: jest.fn(),
};
const mockContentService = {
  getContentsByIds: jest.fn(),
};
const mockMarvelService = {
  getAccounts: jest.fn(),
  getRewardPoints: jest.fn(),
  getCardProfile: jest.fn(),
};
const mockVariablesService = jest.fn().mockImplementation((env, token, content, campaign, rule, language) =>
  Promise.resolve({ content, transformed: {}, dataContext: 'test' }));
const mockLaunchDarklyService = {
  init: jest.fn().mockResolvedValue(true),
  isFeatureEnabled: jest.fn().mockResolvedValue(true),
  close: jest.fn(),
};
const mockLogger = {
  warn: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
  debug: (...opts) => {
    console.log(JSON.stringify(...opts));
  },
};

module.exports = {
  mockRequest,
  mockResponse,
  mockPriorityMessageRule,
  mockMassCampaignRule,
  mockTargetedCampaignRule,
  mockContentResponse,
  mockAccountsResponse,
  mockInsightsResponse,
  mockRuleAPIContainersResponse,
  config,
  mockCampaignCacheService,
  mockContainerCacheService,
  mockTargetedCampaignService,
  mockDispositionsService,
  mockContentService,
  mockMarvelService,
  mockVariablesService,
  mockLaunchDarklyService,
  mockLogger,
  massTargeting,
};
