const Joi = require('@hapi/joi');

const getCampaignsInboxSchema = Joi.object()
  .keys({
    limit: Joi.number().min(1).default(10),
    insight: Joi.boolean().default(false),
    offset: Joi.number().min(0).default(0),
    mode: Joi.string().min(2).max(10).allow('').optional().valid('unseen'),
  });

const getCampaignsInboxHeaderSchema = Joi.object()
  .keys({
    authorization: Joi.string().regex(/^Bearer\s\w+/),
    'x-customer-scotiacard': Joi.string(),
    'x-b3-traceid': Joi.string().length(32).alphanum(),
    'x-b3-spanid': Joi.string().length(16).alphanum(),
    'x-channel-id': Joi.string().valid('Online'),
    'x-originating-appl-code': Joi.string(),
    'x-country-code': Joi.string().valid('CA'),
    'x-language': Joi.string().min(2).valid('en', 'fr'),
    'x-mock-insight': Joi.string(),
    'preferred-environment': Joi.string(),
    'x-session-id': Joi.string(),
    'x-application': Joi.string().valid('M1'),
  })
  .requiredKeys([
    'authorization',
    'x-customer-scotiacard',
    'x-b3-traceid',
    'x-b3-spanid',
    'x-channel-id',
    'x-originating-appl-code',
    'x-country-code',
    'x-language',
  ]);

module.exports = {
  getCampaignsInboxSchema,
  getCampaignsInboxHeaderSchema,
};
