const { Router } = require('express');
const wrapAsync = require('../../server/middleware/wrap-async');
const getCampaignsInbox = require('./get-campaigns-inbox');
const { scopes } = require('../../campaign/routes/common');
const maintenanceMode = require('../../server/middleware/maintenance-mode');

const init = ({
  logger,
  targetedCampaignService,
  contentService,
  variablesService,
  launchDarklyService,
  authorize,
  marvelService,
  campaignCacheService,
  containerCacheService,
  dispositionsService,
}, config) => {
  const router = Router();
  // get a list of campaigns
  router.get(
    '/',
    [
      maintenanceMode({ launchDarklyService, hostingEnv: config.hostingEnv }, { code: 200, type: 'getCampaignsInbox' }),
      authorize([ scopes.campaignsRead ]),
    ],
    wrapAsync(getCampaignsInbox({ logger, targetedCampaignService, contentService, variablesService, campaignCacheService, containerCacheService, dispositionsService, launchDarklyService, marvelService }, config)));
  return router;
};

module.exports = init;
