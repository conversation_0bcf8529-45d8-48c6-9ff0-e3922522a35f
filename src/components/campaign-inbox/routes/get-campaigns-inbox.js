/* eslint-disable sonarjs/cognitive-complexity */
const R = require('ramda');
const Joi = require('@hapi/joi');
const { cloneDeep } = require('lodash');
const _ = require('lodash');
const escapeHtml = require('escape-html');
const promiseAllSettled = require('promise.allsettled');

const { HttpError } = require('../../errors');
const { getCampaignsInboxSchema, getCampaignsInboxHeaderSchema } = require('./validation');
const {
  accessHeadersAndParams,
  filterRulesByQuery,
  sortCampaignsByStartDate,
  paginateRules,
  mapTargetedCampaigns,
  mapCustomerProducts,
  mapContentAndVariables,
  mapMassCampaigns,
} = require('../../helpers/utils');
const {
  filterByExternalRef,
  filterRulesByCustomerProducts,
  filterRulesByCustomerScenePoints,
  generateMessageIdsPega,
  filterRulesByLanguageTargetting,
  buildRuleLimitPerContainerMap,
  applyContainerLimitToRuleList,
} = require('../../helpers');
const {
  priorityMessageCampaignId,
  isCampaignViewed,
  isTargetedCampaign,
  isMassCampaign,
  isPriorityMessageCampaign,
  massCampaignId,
  handleLDError,
  ldFlagDb,
  ldFlagAcct,
  ldFlagRewards,
} = require('../../campaign/routes/common');
const splunkErrorCodes = require('../../campaign/routes/common').splunkErrorCodes;
const {
  validateCampaignRuleType,
  dispositionValues,
  messageSourceArr,
  modesList,
  ORION_INBOX_CONTAINER_ID,
  orionInboxContainers,
  ORION_INBOX_PAGE_ID,
} = require('../../common');
const logFile = 'get-campaigns-inbox.js';
const ldCaller = 'routes/get-campaigns';
const getUniqueCampaignIds = R.pipe(R.map(R.prop('campaign_id')), R.uniq);

const setDbDisposition = async (dispositionsService, country, cardNumber, rule, insightsCampaign, disposition) => {
  let expiryDate = rule.end_at;
  if (insightsCampaign && insightsCampaign.expiry_date) {
    // Insights date in YYYY-MM-DD format, rule date in YYYY-MM-DDThh:mm:ss.sssZ format
    expiryDate = new Date(insightsCampaign.expiry_date) < new Date(rule.end_at)
      ? new Date(insightsCampaign.expiry_date).toISOString()
      : rule.end_at;
  }
  const body = {
    message_id: insightsCampaign ? insightsCampaign.message_id : null,
    container: rule.container,
    page: rule.pages && rule.pages.length === 1 ? rule.pages[0] : null,
    platform: rule.platforms && rule.platforms.length === 1 ? rule.platforms[0] : null,
    application: rule.application,
  };
  await dispositionsService.setDisposition(cardNumber, country, rule.id, expiryDate, disposition, body);
};

const setCampaignDisposition = async (targetedCampaignService, messageId, headerData, logger) => {
  const { userIdentifier, channelId, xApplication, language, preferredEnv, useMock, spanId, traceId, xOriginatingApplCode, type, country, xFeatureFlagUid } = headerData;
  try {
    await targetedCampaignService.getInstance({ ruleType: type }).setDisposition({
      id: messageId,
      disposition: dispositionValues.viewed,
      userIdentifier,
      channelId,
      xApplication,
      language,
      preferredEnv,
      useMock,
      spanId,
      traceId,
      xOriginatingApplCode,
      country,
      xFeatureFlagUid,
    });
    logger.info({ message: 'Successfully updated campaigns to db disposition value', data: { messageId } });
  } catch (err) {
    logger.error({ message: 'Error in updating campaigns to db disposition value', err, messageId });
  }
};

const filterAndMapDispositions = async (targetedCampaignService, campaigns, rules, dbDispositions, logger, isDispositionsDbEnabled, headerData, query) => {
  return Promise.all(rules.map(async rule => {
    const campaign = campaigns.find((c) => c.message_id === R.path([ 'external_ref', 'message_id' ], rule));
    const campaignViewed = isCampaignViewed(campaign);
    const messageIds = campaign ? [ campaign.message_id ] : [ null ];

    // Support old and new message ids
    let oldMessageId = campaign && messageSourceArr.includes(campaign.message_source) && generateMessageIdsPega(campaign.campaign_id, campaign.language, campaign.message_source);
    if (oldMessageId && campaign.message_id !== oldMessageId) {
      messageIds.push(oldMessageId);
    }

    // List of dispositions is for a given card number & application, can have multiple dispositions (i.e. viewed, deleted) per rule/message
    const dispositions = dbDispositions.filter(disp => (disp.rule_id === rule.id && messageIds.includes(disp.message_id)));
    const dispositionDeleted = dispositions.some(disp => disp.disposition === dispositionValues.deleted);
    const dispositionViewed = dispositions.some(disp => (disp.disposition === dispositionValues.viewed) && (disp.container === ORION_INBOX_CONTAINER_ID));
    if (query && query.mode === modesList.unseenCount) {
      const dispositionSeen = dispositions.some(disp => disp.disposition === dispositionValues.seen);
      const isRuleSeen = dispositionViewed || dispositionSeen;
      if (isRuleSeen) rule.seen = true;
      return rule;
    }
    if (campaign) {
      if (dispositionViewed || dispositionDeleted) {
        // If disposition db entries exist, the rule is either viewed or deleted
        if (!campaignViewed) {
          const messageId = messageIds[0];
          await setCampaignDisposition(targetedCampaignService, messageId, headerData, logger);
        }
        if (dispositionDeleted) return;
        rule.viewed = dispositionViewed;
      } else {
        // If disposition db entries do not exist, the rule is new
        rule.viewed = campaignViewed;
      }
    } else {
      if (dispositionDeleted) return;
      rule.viewed = dispositionViewed;
    }

    // If launch drakly flag is off, set viewed property of mass/message rules prior to sorting by viewed
    if (isDispositionsDbEnabled === false && (R.path([ 'external_ref', 'campaign_id' ], rule) === priorityMessageCampaignId ||
      R.path([ 'external_ref', 'campaign_id' ], rule) === massCampaignId)) {
      rule.viewed = true;
    }
    return rule;
  }));
};

const getCampaigns = ({
  logger,
  contentService,
  targetedCampaignService,
  variablesService,
  campaignCacheService,
  containerCacheService,
  dispositionsService,
  launchDarklyService,
  marvelService,
}, config) => async (req, res, next) => {
  // 1. Joi schema validation - accepted query params: limit, insight, offset, mode
  // ========== Joi validation ========== //
  const { error, value: validatedQuery } = Joi.validate(req.query, getCampaignsInboxSchema, { stripUnknown: true });
  const query = {
    ...validatedQuery,
    application: 'orion',
    platform: 'web',
  };
  if (error) {
    const errorDetails = error.details.map((err) => ({ path: err.path.join('.'), message: escapeHtml(err.message) }));
    return next(HttpError.badRequest('Validation error', errorDetails));
  }
  // 4. Header validation - channel=Online & x-originating-appl-code=Orions EPM code
  const { error: headerError, value: headers } = Joi.validate(req.headers, getCampaignsInboxHeaderSchema, { stripUnknown: true });
  if (headerError) {
    const errorDetails = headerError.details.map((err) => ({ path: err.path.join('.'), message: escapeHtml(err.message) }));
    return next(HttpError.badRequest('Validation error', errorDetails));
  }
  // 2. Declare const variables: application=orion, page=orion-account-summary, container=orion-campaign (will change if rule-linking is implemented)
  // 3. Extract header variables & store as variables
  // ========== Access params/headers step ========== //
  const {
    country,
    cardNumber,
    application,
    channelId,
    xApplication,
    language,
    languageContentful,
    preferredEnv,
    mockedCampaign,
    spanId,
    traceId,
    xOriginatingApplCode,
    xFeatureFlagUid,
  } = await accessHeadersAndParams(res, req, query);

  // 5. Get rules from cache
  // ========== Get all active campaign rules and filter them by available query params ========== //
  let rules = Object.values(cloneDeep(R.pathOr([], [ 'campaigns' ], campaignCacheService.get())));
  // 6. Apply filter functions: filterByContainerAndPage(), filterByApplication() (config feature application check not required)
  // filter by orion inbox containers
  rules = rules.filter(r => orionInboxContainers.includes(r.container));
  rules = filterRulesByQuery(rules, query, config, false);
  rules = filterRulesByLanguageTargetting(rules, language);
  let targetedRules = rules.filter(rule => isTargetedCampaign(rule));
  let massRules = rules.filter(rule => isMassCampaign(rule));
  let messageRules = rules.filter(rule => isPriorityMessageCampaign(rule));
  // ========== Validate rule types ========== //
  // ========== Notifications ========== //
  const notifications = [];
  const uniqueRuleTypes = _.uniq(_.map(rules, 'type'));
  const { error: ruleTypeError, value: ruleType } = validateCampaignRuleType(country, uniqueRuleTypes[0]);

  if (ruleTypeError) {
    return next(HttpError.badRequest(ruleTypeError));
  }
  // fetching from more than one downstream data source is not supported
  if (uniqueRuleTypes.length > 1) {
    logger.error({
      message: 'Get campaigns handler failed to return rules due to misconfigured application with more than one rule type.',
      details: { application, uniqueRuleTypes },
    });
    notifications.push({
      message: 'Application (placement) is misconfigured. More than one campaign rule type is not supported.',
      metadata: [ { application } ],
    });
    return res.status(200).json({ data: { total_new: 0, total: 0, limit: query.limit, offset: query.offset, items: [] }, notifications });
  }

  // Downstream calls
  const downstreamCalls = new Map();
  if (targetedRules.length) {
    // 7) b. Call insights getCampaigns (Check if LD flag is enabled is already implemented as part of service call)
    downstreamCalls.set('campaigns', targetedCampaignService.getInstance({ ruleType }).getCampaigns({
      userIdentifier: cardNumber,
      channelId,
      xApplication,
      language,
      country,
      preferredEnv,
      spanId,
      traceId,
      useMock: mockedCampaign,
      xOriginatingApplCode,
      xFeatureFlagUid,
    }));
  }
  // 8) b. Check LD flag pigeon-api.downstreams.marvel-account-cache is enabled
  let isAccountsEnabled = false;
  try {
    isAccountsEnabled = await launchDarklyService.isFeatureEnabled(ldFlagAcct, true);
  } catch (err) {
    handleLDError({ err, flag: ldFlagAcct, caller: ldCaller, logger });
  }
  if (isAccountsEnabled) {
    // 8) c. Call Marvel getAccounts() and map into customerProducts array (BAU)
    downstreamCalls.set('accounts', marvelService.getAccounts({ cardNumber, language, preferredEnv, xOriginatingApplCode }));
  }

  // 11. Dispositions: Filter by deleted and set viewed property of rule
  let isDispositionsDbEnabled = false;
  try {
    isDispositionsDbEnabled = await launchDarklyService.isFeatureEnabled(ldFlagDb, true);
  } catch (err) {
    handleLDError({ err, flag: ldFlagDb, caller: ldCaller, logger });
  }

  if (isDispositionsDbEnabled) {
    downstreamCalls.set('dispositions', dispositionsService.getDispositions(cardNumber, country, application));
  }

  const pendingPromises = [];
  const servicesCalled = [];
  downstreamCalls.forEach((value, key) => {
    pendingPromises.push(value);
    servicesCalled.push(key);
  });

  // issue 2 requests in parallel (targeted campaigns, accounts/products)
  const downstreamCallsResults = await promiseAllSettled(pendingPromises);

  const campaignsIndex = servicesCalled.indexOf('campaigns');
  const accountIndex = servicesCalled.indexOf('accounts');
  const dispositionsIndex = servicesCalled.indexOf('dispositions');

  const accountResponse = downstreamCallsResults[accountIndex];
  const campaignsResponse = downstreamCallsResults[campaignsIndex];
  const dispositionsResponse = downstreamCallsResults[dispositionsIndex];

  let campaigns = [];
  if (campaignsResponse && campaignsResponse.status === 'fulfilled') {
    campaigns = Array.isArray(campaignsResponse.value.data) ? campaignsResponse.value.data : [];
  } else {
    if (campaignsResponse && campaignsResponse.reason.message.indexOf('KTMGD-ACCT-NOT-FOUND') === -1) {
      logger.error({ message: 'unable to get insights campaigns', err: campaignsResponse.reason, code: 1009, description: splunkErrorCodes[1009] });
    }
  }

  let customerProducts = mapCustomerProducts(accountResponse);

  let dispositions = [];
  if (dispositionsResponse && dispositionsResponse.status === 'fulfilled') {
    dispositions = dispositionsResponse.value;
  }

  // 7. Targeted campaign filtering: filterByTargetedCampaigns()
  // a. Check if any rules are targeted
  if (targetedRules.length) {
    // c. Filter out rules with external_refs that aren't MASS or MESSAGE that aren't in the insights response
    // ========== Filter by external ref ========== //
    const externalRefs = getUniqueCampaignIds(campaigns);
    targetedRules = filterByExternalRef(rules, externalRefs);
    // d. Map rules to build externa_ref object out of campaign_id, message_id, message_source, data
    // Note: customer products filtering/resolveDynamicPages() functionality not required
    targetedRules = mapTargetedCampaigns(campaigns, targetedRules, query.insight, customerProducts);
  }

  // 8. Mass campaign filtering: filterByCustomerProducts()
  // a. Check if any rules have: external_ref=MASS
  if (massRules.length && isAccountsEnabled) {
    // d. Filter rules by customer products: filterRulesByCustomerProducts(rules, customerProducts)
    massRules = mapMassCampaigns(filterRulesByCustomerProducts(massRules, customerProducts));
  }

  if (messageRules.length) {
    messageRules = mapMassCampaigns(messageRules);
  }
  rules = [ ...targetedRules, ...massRules, ...messageRules ];

  // 10. Scene point filtering: filterBySceneRewards()
  // ========== Filter by Scene points ========== //
  // if any rules target scene points, fetch cutomer's portfolio balance & filter by scene points
  // a. Check if any remaining rules are targeting scene points: if (rules.some(rule => rule.mass_targeting && rule.mass_targeting.by_scene_points)) {}
  if (rules.some(rule => rule.mass_targeting && rule.mass_targeting.by_scene_points)) {
    let isRewardsEnabled = false;
    try {
      isRewardsEnabled = await launchDarklyService.isFeatureEnabled(ldFlagRewards, true);
    } catch (err) {
      handleLDError({ err, flag: ldFlagRewards, caller: ldCaller, logger });
    }
    if (isRewardsEnabled) {
      let customerScenePoints;
      try {
        // b. Check LD flag pigeon-api.downstreams.rewards is enabled
        customerScenePoints = await marvelService.getRewardPoints(cardNumber, language, preferredEnv, channelId, traceId, spanId, xOriginatingApplCode);
      } catch (error) {
        logger.error({ message: 'unable to get customers rewards points', err: campaignsResponse.reason, code: 1009, description: splunkErrorCodes[1009] });
      }
      // d. Filter rules by scene points: filterRulesByCustomerScenePoints(rules, customerScenePoints)
      rules = filterRulesByCustomerScenePoints(rules, customerScenePoints);
    }
  }

  // a. Can leverage existing sortAndMapDispositions() function with some simplifications (no sorting in this step)
  rules = await filterAndMapDispositions(targetedCampaignService, campaigns, rules, dispositions, logger, isDispositionsDbEnabled, {
    userIdentifier: cardNumber,
    channelId,
    xApplication,
    language,
    preferredEnv,
    useMock: mockedCampaign,
    spanId,
    traceId,
    xOriginatingApplCode,
    type: ruleType,
    country,
    xFeatureFlagUid,
  }, query);
  // remove false values
  rules = _.remove(rules, (x) => !!x);

  // If query mode is unseen, return number of unseen rules
  if (query.mode === modesList.unseenCount) {
    const unseen = rules.filter(r => !r.seen).length;
    return res.status(200).json({ data: { unseen }, notifications });
  }

  // 12. Set seen disposition for any remaining rules where viewed=false
  // Set seen Disposition for each rule
  await Promise.all(rules.map(async rule => {
    try {
      const campaign = campaigns.find((c) => c.message_id === R.path([ 'external_ref', 'message_id' ], rule));
      await setDbDisposition(dispositionsService, country, cardNumber, { ...rule, container: ORION_INBOX_CONTAINER_ID, pages: [ ORION_INBOX_PAGE_ID ] }, campaign, dispositionValues.seen);
    } catch (error) {
      logger.error({ message: 'Unable to set Seen disposition', rule, error });
    }
  }));

  // 13. Sort by start date
  rules = sortCampaignsByStartDate(rules);

  // 14. Fetch content (BAU)
  const contentOpts = { select: 'preview', language: languageContentful };
  let contents = [];

  try {
    // As long as the feature 'FEATURES_APPLICATION' have been enabled,
    // all the rules remaining have the same content space.
    const ruleWithSpaceId = rules.find((c) => !!c.content_space);
    if (ruleWithSpaceId) {
      const contentIds = R.pipe(R.map((r) => r.content_id), R.filter((id) => !!id), R.uniq)(rules);
      const contentItems = await contentService.getContentsByIds(ruleWithSpaceId.content_space, contentIds, contentOpts);

      if (contentItems && contentItems.data && Array.isArray(contentItems.data.items)) {
        contents = contentItems.data.items;
      }
    }
  } catch (err) {
    logger.error({ message: 'failed to get content', err, code: 1011, description: splunkErrorCodes[1011] });
  }

  // 15. Replace variables
  // ========== Map contents to rules ========== //
  const catchVarSvcErr = (rule, content) => err => {
    notifications.push({
      message: `failed to replace variables`,
      metadata: [ { rule_id: rule.id, message_id: R.path([ 'external_ref', 'message_id' ], rule) } ],
    });
    logger.warn({ message: 'failed to replace variables', err, rule, code: 1013, description: splunkErrorCodes[1013] });
    return { dataContext: 'unknown', transformed: {}, content: content.content };
  };

  const reqHeaders = {
    sessionId: headers['x-session-id'],
    channelId,
    preferredEnv,
    xApplication,
  };
  let result;
  try {
    result = await mapContentAndVariables({
      query,
      rules,
      contents,
      campaigns,
      reqHeaders,
      variablesService,
      preferredEnv,
      customerToken: null,
      language,
      catchVarSvcErr,
      isDispositionsDbEnabled,
      notifications,
    });
  } catch (error) {
    logger.error({ message: 'Failed to map and paginate results', error, code: 1014, description: splunkErrorCodes[1014] });
    return res.status(200).json({ data: { total_new: 0, total: 0, limit: query.limit, offset: query.offset, items: [] }, notifications });
  }

  // 16. Remove rules that exceed the container max from response - https://jira.agile.bns/browse/PIGEON-5626
  const containers = Object.values(cloneDeep(R.pathOr([], [ 'containers' ], containerCacheService.get())));
  const maxRulePerContainerMap = buildRuleLimitPerContainerMap(orionInboxContainers, containers);
  // 16.1. Apply container limit on the list of valid campaigns with contents only
  let filteredRules = applyContainerLimitToRuleList(result, maxRulePerContainerMap);
  // 16.2. Get the total of rules and new rules
  const totalNew = filteredRules.filter(rule => !rule.viewed).length;
  const totalRules = filteredRules.length;

  // 17. Apply limit/pagination params
  filteredRules = paginateRules({ query, rules: filteredRules });

  const response = { data: { total_new: totalNew, total: totalRules, limit: query.limit, offset: query.offset, items: filteredRules }, notifications };
  logger.info({
    message: 'response to client',
    logFile,
    logType: 'response to client',
    response: {
      ...response,
      data: {
        ...response.data,
        items: response.data.items.map(i => ({
          ...i,
          external_ref: {
            ...i.external_ref,
            ...(R.path([ 'external_ref', 'data', 'additional_data' ], i) && {
              data: {
                ...i.external_ref.data,
                additional_data: null,
              },
            }),
            ...(R.path([ 'external_ref', 'data_transformed' ], i) && {
              data_transformed: null,
            }),
          },
          content: null,
        })),
      },
      request: {
        headers: {
          'x-b3-traceid': traceId,
        },
      },
    },
  });

  res.status(200).json(response);
};
module.exports = getCampaigns;
