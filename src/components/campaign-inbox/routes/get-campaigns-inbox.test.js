const { HttpError } = require('../../errors');
const getCampaigns = require('./get-campaigns-inbox');
const {
  mockRequest,
  mockResponse,
  mockPriorityMessageRule,
  mockMassCampaignRule,
  mockTargetedCampaignRule,
  mockContentResponse,
  mockAccountsResponse,
  mockInsightsResponse,
  mockRuleAPIContainersResponse,
  config,
  mockCampaignCacheService,
  mockContainerCacheService,
  mockTargetedCampaignService,
  mockDispositionsService,
  mockContentService,
  mockMarvelService,
  mockVariablesService,
  mockLaunchDarklyService,
  mockLogger,
  massTargeting,
} = require('../mockData');
const { ORION_INBOX_CONTAINER_ID, ORION_INBOX_PAGE_ID, orionInboxContainers } = require('../../common');

const defaultCardNumber = '****************';
const mockStatus = jest.fn();
const mockJson = jest.fn();
const mockNext = jest.fn();
const defaultResponse = { items: [], limit: 10, total: 0, total_new: 0, offset: 0 };
const defaultDownstream = {
  logger: mockLogger,
  campaignCacheService: mockCampaignCacheService,
  containerCacheService: mockContainerCacheService,
};
const otherDownstream = {
  contentService: mockContentService,
  marvelService: mockMarvelService,
  dispositionsService: mockDispositionsService,
  launchDarklyService: mockLaunchDarklyService,
  targetedCampaignService: mockTargetedCampaignService,
};

const allRules = [ mockPriorityMessageRule, mockMassCampaignRule, mockTargetedCampaignRule ];

const generateRulesCacheResponse = (rules) => rules.reduce((acc, curVal, curIndex) => {
  acc[curVal.id] = curVal;
  return acc;
}, {});

mockTargetedCampaignService.getInstance.mockReturnValue({
  getCampaigns: jest.fn(),
  setDisposition: jest.fn(),
});

describe('Get Campaigns for Orion Inbox', () => {
  beforeEach(() => {
    mockTargetedCampaignService.getInstance.mockClear();
    mockTargetedCampaignService.getInstance().getCampaigns.mockClear();
    mockMarvelService.getAccounts.mockClear();
    mockMarvelService.getRewardPoints.mockClear();
    mockMarvelService.getCardProfile.mockClear();
    mockDispositionsService.getDispositions.mockClear();
    mockContentService.getContentsByIds.mockClear();
    mockVariablesService.mockClear();
    mockLaunchDarklyService.init.mockClear();
    mockLaunchDarklyService.isFeatureEnabled.mockClear();
    mockLaunchDarklyService.close.mockClear();
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
    mockLogger.error.mockClear();
    mockCampaignCacheService.get.mockClear();
    mockContainerCacheService.get.mockClear();
  });

  describe('Request validation', () => {
    test('should return bad request on invalid query param', async () => {
      const mockReq = { ...mockRequest(), query: { limit: '#' } };
      const mockRes = mockResponse();

      const endpoint = getCampaigns({}, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockRes.status).not.toBeCalled();
      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(HttpError);
      expect(mockNext.mock.calls[0][0].statusCode).toEqual(400);
      expect(mockNext.mock.calls[0][0].message).toEqual('Validation error');
      expect(mockNext.mock.calls[0][0].metadata[0].message)
        .toEqual('&quot;limit&quot; must be a number');
    });
    test('should return bad request on invalid headers', async () => {
      const mockReq = { ...mockRequest(), headers: {} };
      const mockRes = mockResponse();

      const endpoint = getCampaigns({}, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockRes.status).not.toBeCalled();
      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(HttpError);
      expect(mockNext.mock.calls[0][0].statusCode).toEqual(400);
      expect(mockNext.mock.calls[0][0].message).toEqual('Validation error');
      expect(mockNext.mock.calls[0][0].metadata[0].message)
        .toEqual('&quot;authorization&quot; is required');
    });
  });

  describe('Rule type validations', () => {
    test('Rule type error', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();
      const rules = [ { ...mockPriorityMessageRule, type: 'other_campaign' } ];
      const mockRuleCacheResponse = generateRulesCacheResponse(rules);
      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns: mockRuleCacheResponse,
      });
      const endpoint = getCampaigns(defaultDownstream, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockRes.status).not.toBeCalled();
      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(HttpError);
      expect(mockNext.mock.calls[0][0].statusCode).toEqual(400);
      expect(mockNext.mock.calls[0][0].message)
        .toEqual('Given country has insufficient access to rule types available');
    });

    test('should return bad request if multiple rule types remain after filtering by application', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();
      const rules = [ mockMassCampaignRule, { ...mockPriorityMessageRule, type: 'other_campaign' } ];
      const mockRuleCacheResponse = generateRulesCacheResponse(rules);
      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns: mockRuleCacheResponse,
      });
      const endpoint = getCampaigns(defaultDownstream, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockRes.status).toBeCalledWith(200);
      expect(mockRes.json).toBeCalledWith({
        data: defaultResponse,
        notifications: [ {
          message: 'Application (placement) is misconfigured. More than one campaign rule type is not supported.',
          metadata: [ { application: 'orion' } ],
        } ],
      });
    });
  });

  describe('Targeting by reward points', () => {
    test('Filter by reward points', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();
      const rewardsTargetingRule = [ { ...mockMassCampaignRule, mass_targeting: massTargeting } ];
      const mockRuleCacheResponse = generateRulesCacheResponse(rewardsTargetingRule);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns: mockRuleCacheResponse,
      });
      mockTargetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce(mockInsightsResponse);
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockContentService.getContentsByIds.mockResolvedValue(mockContentResponse);
      mockMarvelService.getRewardPoints.mockResolvedValueOnce(2000);
      const endpoint = getCampaigns({
        ...defaultDownstream,
        ...otherDownstream,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockRes.json).toHaveBeenCalled();
    });
  });

  describe('Orion Inbox', () => {
    test('set seen disposition for call coming from orion inbox', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();
      const mockRuleCacheResponse = generateRulesCacheResponse([ mockTargetedCampaignRule ]);
      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns: mockRuleCacheResponse,
      });
      mockTargetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce(mockInsightsResponse);
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      const endpoint = getCampaigns({
        ...defaultDownstream,
        ...otherDownstream,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockDispositionsService.setDisposition).toHaveBeenCalledWith(
        defaultCardNumber,
        mockReq.headers['x-country-code'],
        mockTargetedCampaignRule.id,
        mockTargetedCampaignRule.end_at,
        'W',
        {
          'application': mockTargetedCampaignRule.application,
          'container': ORION_INBOX_CONTAINER_ID,
          'message_id': mockInsightsResponse.data[0].message_id,
          'page': ORION_INBOX_PAGE_ID,
          'platform': mockTargetedCampaignRule.platforms[0],
        },
      );
    });

    test('Sort and Map dispositions', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();
      const mockRuleCacheResponse = generateRulesCacheResponse([ { ...mockTargetedCampaignRule, container: ORION_INBOX_CONTAINER_ID } ]);
      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns: mockRuleCacheResponse,
      });
      mockTargetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce(mockInsightsResponse);
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([ {
        rule_id: mockTargetedCampaignRule.id,
        message_id: mockInsightsResponse.data[0].message_id,
        disposition: 'V',
        container: ORION_INBOX_CONTAINER_ID,
      } ]);
      const endpoint = getCampaigns({
        ...defaultDownstream,
        ...otherDownstream,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockTargetedCampaignService.getInstance().setDisposition).toBeCalled();
    });

    test('Return number of unseen rules if mode = query is passed in the query param', async () => {
      const mockReq = {
        ...mockRequest(),
        query: {
          mode: 'unseen',
        },
      };
      const mockRes = mockResponse();
      const mockRuleCacheResponse = generateRulesCacheResponse(allRules);
      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns: mockRuleCacheResponse,
      });
      mockTargetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce(mockInsightsResponse);
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      const endpoint = getCampaigns({
        ...defaultDownstream,
        ...otherDownstream,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockRes.json).toBeCalledWith({
        data: {
          unseen: allRules.length,
        },
        notifications: [],
      });
    });

    test('Failed to paginate', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();
      const mockRuleCacheResponse = generateRulesCacheResponse([ mockMassCampaignRule ]);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns: mockRuleCacheResponse,
      });
      mockTargetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce(mockInsightsResponse);
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockContentService.getContentsByIds.mockResolvedValue(mockContentResponse);
      const endpoint = getCampaigns({
        ...defaultDownstream,
        ...otherDownstream,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockLogger.error).toBeCalled();
      expect(mockRes.json).toBeCalledWith({
        data: defaultResponse,
        notifications: [ ],
      });
    });

    test('return only allowed orion inbox containers', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();
      const orionPriorityContainerRule = { ...mockMassCampaignRule, container: 'orion-priority', id: '111' };
      const orionInboxContainerRule = { ...mockMassCampaignRule, container: orionInboxContainers[1], id: '222' };
      const allRules = [ mockMassCampaignRule, orionPriorityContainerRule, orionInboxContainerRule ];
      const mockRuleCacheResponse = generateRulesCacheResponse(allRules);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns: mockRuleCacheResponse,
      });
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockContentService.getContentsByIds.mockResolvedValue(mockContentResponse);
      const endpoint = getCampaigns({
        ...defaultDownstream,
        ...otherDownstream,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      const response = mockRes.json.mock.calls[0][0];
      expect(response.data.items.length).toBe(2);
    });

    test('should limit number of rules per container', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();
      const orionPriorityContainerRule1 = { ...mockMassCampaignRule, container: orionInboxContainers[0], id: '111' };
      const orionPriorityContainerRule2 = { ...mockMassCampaignRule, container: orionInboxContainers[0], id: '112' };
      const orionInboxContainerRule1 = { ...mockMassCampaignRule, container: orionInboxContainers[1], id: '222' };
      const orionInboxContainerRule2 = { ...mockMassCampaignRule, container: orionInboxContainers[1], id: '223' };
      const allRules = [ orionPriorityContainerRule1, orionInboxContainerRule2, orionInboxContainerRule1, orionPriorityContainerRule2 ];
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns: allRules,
      });
      mockContainerCacheService.get.mockReturnValueOnce({
        containers: mockRuleAPIContainersResponse.data,
      });
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockContentService.getContentsByIds.mockResolvedValue(mockContentResponse);

      const endpoint = getCampaigns({
        ...defaultDownstream,
        ...otherDownstream,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      const response = mockRes.json.mock.calls[0][0];
      // 'orion-campaign' container only allows 1 rule in mockData
      expect(response.data.items.length).toBe(3);
    });
  });
});
