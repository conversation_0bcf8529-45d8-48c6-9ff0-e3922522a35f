const customerInfo = require('./customer-info');

const mockFetch = jest.fn();
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

const basePath = 'https://swm-svc-customers-uat.apps.stg.azr-cc-pcf.cloud.bns';
const defaultParams = {
  cardNumber: '****************',
  channelId: 'Mobile',
  country: 'CA',
  xOriginatingApplCode: 'BFB6',
  traceId: 'bd7a977555f6b982bd7a977555f6b982',
  spanId: 'bd7a977555f6b982',
};

describe('customerInfo', () => {
  beforeEach(() => {
    mockFetch.mockClear();
    mockLogger.info.mockClear();
    mockLogger.error.mockClear();
  });

  describe('should successfully fetch customer info', () => {
    it('should return customer data when fetch is successful', async () => {
      const body = {
        'data': {
          'language': 'English',
          'country': 'CA',
          'province': 'AB',
          'gender': 'MISS',
          'customer_id': {
            'brk_id': '1488199',
            'is_virtual_card': false,
          },
          'status': 'ACTIVE',
          'first_name': 'KRUTI',
          'last_name': 'TRADEZ',
          'date_of_birth': '1991-01-27',
          'email_address': '<EMAIL>',
          'phone_number': '9052743616',
          'wealth_attributes': [
            {
              'name': 'RT_OP_FLAG',
              'value': 'Y',
            },
            {
              'name': 'tabCode',
              'value': '99',
            },
            {
              'name': 'RT_EQ_FLAG',
              'value': 'Y',
            },
            {
              'name': 'realtime_quotes_eligible',
              'value': 'false',
            },
            {
              'name': 'ENTITLEMENT_ACTIVE_TRADER_PLATFORM',
              'value': 'false',
            },
            {
              'name': 'ENTITLEMENT_NEO',
              'value': 'true',
            },
            {
              'name': 'ENT_IT_TRADE',
              'value': 'true',
            },
            {
              'name': 'ENT_IT_WATCHLISTS',
              'value': 'true',
            },
            {
              'name': 'ENT_IT_MARKETS',
              'value': 'true',
            },
            {
              'name': 'ENT_IT_RESEARCH',
              'value': 'true',
            },
            {
              'name': 'ENT_IT_EQUITY_ORDERS',
              'value': 'true',
            },
            {
              'name': 'ENT_IT_OPTION_ORDERS',
              'value': 'true',
            },
            {
              'name': 'ENT_IT_MF_ORDERS',
              'value': 'true',
            },
            {
              'name': 'ENT_IT_FI_ORDERS',
              'value': 'true',
            },
            {
              'name': 'ENT_IT_NI_ORDERS',
              'value': 'true',
            },
            {
              'name': 'ENT_IT_GIC_ORDERS',
              'value': 'true',
            },
          ],
          'sys_list': [
            {
              'sys_type': 'N',
            },
          ],
          'business_line': [
            {
              'business_line': 'ITRADE',
              'business_unit': 'SDBI',
              'service_pkgs': [
                'SL',
              ],
            },
          ],
          'quoteResearchProduct': 'SCOTIA_EQUITY_RT|SCOTIA_OPTIONS_RT|SCOTIA_BASE_ITRADE',
          'segment': '4',
          'platforms': [
            'itrade',
          ],
          'mf_knowledge': 'L',
          'fix_income_knowledge': 'L',
          'stock_knowledge': 'L',
          'margin_knowledge': 'L',
          'equity_options_knowledge': 'L',
          'short_sale_knowledge': 'L',
        },
        'paging': null,
        'notifications': null,
      };

      const mockResponse = {
        ok: true,
        text: jest.fn().mockResolvedValue(
          JSON.stringify({
            data: body,
          }),
        ),
      };

      mockFetch.mockResolvedValue(mockResponse);

      const result = await customerInfo(
        { fetch: mockFetch, logger: mockLogger },
        basePath,
        defaultParams,
      );

      expect(result).toEqual(body);
      expect(mockFetch).toHaveBeenCalledWith(`${basePath}/api/v3/customer`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
          Accept: 'application/json',
          'x-country-code': 'CA',
          'x-channel-id': 'Mobile',
          'x-originating-appl-code': 'BFB6',
          'x-b3-traceid': 'bd7a977555f6b982bd7a977555f6b982',
          'x-b3-spanid': 'bd7a977555f6b982',
        },
        body: JSON.stringify({
          scotia_card_number: '****************',
        }),
      });
      expect(mockLogger.info).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'customer request: get customer',
          request: expect.objectContaining({
            url: `${basePath}/api/v3/customer`,
            method: 'POST',
            headers: expect.objectContaining({
              'x-customer-scotiacard': '***',
            }),
          }),
          response_time: expect.any(Number),
        }),
      );
    });
  });

  describe('should handle fetch error', () => {
    it('should handle fetch errors and log error message', async () => {
      const fetchError = new Error('Network error');
      mockFetch.mockRejectedValue(fetchError);

      await expect(
        customerInfo(
          { fetch: mockFetch, logger: mockLogger },
          basePath,
          defaultParams,
        ),
      ).rejects.toThrow('Network error');

      expect(mockLogger.error).toHaveBeenCalledWith({
        message: 'Error: getting customer',
        cause: 'Error: Network error',
      });
    });
  });
});
