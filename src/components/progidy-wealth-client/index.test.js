const init = require('./index');

jest.mock('./customer-info.js', () => {
  return jest.fn().mockResolvedValue({ mockData: 'test' });
});

const customerInfo = require('./customer-info.js');

describe('progidy-wealth-client index', () => {
  const mockLogger = {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  };

  const mockFetch = jest.fn();
  const mockLaunchDarklyService = {
    isFeatureEnabled: jest.fn(),
  };

  const mockDependencies = {
    logger: mockLogger,
    fetch: mockFetch,
    launchDarklyService: mockLaunchDarklyService,
  };

  const mockBasePath = 'https://swm-svc-customers-uat.nonp.atlas.bns/v3';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should initialize and return an object with customerInfo function', () => {
    const client = init(mockDependencies, mockBasePath);

    expect(client).toBeDefined();
    expect(client).toHaveProperty('customerInfo');
    expect(typeof client.customerInfo).toBe('function');
  });

  test('should call customerInfo with correct dependencies, basePath, query, and useMock parameters', async () => {
    const client = init(mockDependencies, mockBasePath);
    const mockQuery = { customerId: '12345' };
    const mockUseMock = false;

    await client.customerInfo(mockQuery, mockUseMock);

    expect(customerInfo).toHaveBeenCalledTimes(1);
    expect(customerInfo).toHaveBeenCalledWith(
      mockDependencies,
      mockBasePath,
      mockQuery,
      mockUseMock,
    );
  });

  test('should call customerInfo with useMock = true', async () => {
    const client = init(mockDependencies, mockBasePath);
    const mockQuery = { customerId: '67890' };
    const useMock = true;

    await client.customerInfo(mockQuery, useMock);

    expect(customerInfo).toHaveBeenCalledWith(
      mockDependencies,
      mockBasePath,
      mockQuery,
      true,
    );
  });

  test('should call customerInfo with undefined query and useMock parameters', async () => {
    const client = init(mockDependencies, mockBasePath);

    await client.customerInfo();

    expect(customerInfo).toHaveBeenCalledWith(
      mockDependencies,
      mockBasePath,
      undefined,
      undefined,
    );
  });

  test('should return the result from customerInfo function', async () => {
    const expectedResult = { data: 'customer data', status: 'success' };
    customerInfo.mockResolvedValueOnce(expectedResult);

    const client = init(mockDependencies, mockBasePath);
    const result = await client.customerInfo({ customerId: 'test' }, false);

    expect(result).toEqual(expectedResult);
  });

  test('should handle customerInfo rejection', async () => {
    const expectedError = new Error('API Error');
    customerInfo.mockRejectedValueOnce(expectedError);

    const client = init(mockDependencies, mockBasePath);

    await expect(client.customerInfo({ customerId: 'test' }, false))
      .rejects
      .toThrow('API Error');
  });

  test('should work with different basePath values', () => {
    const alternativeBasePath = 'https://swm-svc-customers-uat-nane2.nonp.atlas.bns/v3';
    const client = init(mockDependencies, alternativeBasePath);

    client.customerInfo({ customerId: 'test' });

    expect(customerInfo).toHaveBeenCalledWith(
      mockDependencies,
      alternativeBasePath,
      { customerId: 'test' },
      undefined,
    );
  });

  test('should work with minimal dependencies', () => {
    const minimalDeps = {
      logger: mockLogger,
      fetch: mockFetch,
      launchDarklyService: mockLaunchDarklyService,
    };

    const client = init(minimalDeps, mockBasePath);

    expect(client.customerInfo).toBeDefined();
    expect(typeof client.customerInfo).toBe('function');
  });

  test('should handle null/undefined basePath', () => {
    const client = init(mockDependencies, null);
    client.customerInfo({ customerId: 'test' });

    expect(customerInfo).toHaveBeenCalledWith(
      mockDependencies,
      null,
      { customerId: 'test' },
      undefined,
    );
  });

  test('should handle empty string basePath', () => {
    const client = init(mockDependencies, '');
    client.customerInfo({ customerId: 'test' });

    expect(customerInfo).toHaveBeenCalledWith(
      mockDependencies,
      '',
      { customerId: 'test' },
      undefined,
    );
  });
});
