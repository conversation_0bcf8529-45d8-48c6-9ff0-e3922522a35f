const ProductBookService = require('./index');
const productBook = require('./productbook.test.json');

let productBookService;
const config = { productsUri: 'localhost', refreshIntervalMillis: 24 * 60 * 60 * 1000 };
const logger = { info: jest.fn(), error: jest.fn() };
const marvelService = { getProducts: jest.fn().mockImplementation(() => Promise.resolve(
  [
    {
      product_id: '340030',
      product_types: [ { product_domain: 'default', product_code: 'NRS', sub_product_code: 'ST' } ],
      ownership: 'R',
      properties: [ { type: 'CORNERSTONE_ID', value: 'Investing' } ],
      descriptions: [ { locale: 'en_CA', value: 'new description' } ],
    },
  ],
)) };

describe('ProductBook', () => {
  beforeEach(() => {
    jest.useFakeTimers();
    logger.info.mockClear();
    logger.error.mockClear();
    productBookService = ProductBookService({ config, logger, productBook, marvelService });
  });
  afterAll(() => {
    jest.useRealTimers();
  });
  test('should not get anything when `productBook` is undefined', () => {
    const pbs = ProductBookService({ config, logger, productBook: undefined, marvelService });
    const product = pbs.getProduct('AFB', 'B1');
    expect(product).not.toBeDefined();
  });
  test('should not get anything when `product` is not an missing', () => {
    const pbs = ProductBookService({ config, logger, productBook: {}, marvelService });
    const product = pbs.getProduct('AFB', 'B1');
    expect(product).not.toBeDefined();
  });
  test('should not get anything when `product` is not an array', () => {
    const pbs = ProductBookService({ config, logger, productBook: { product: 1 }, marvelService });
    const product = pbs.getProduct('AFB', 'B1');
    expect(product).not.toBeDefined();
  });
  test('should not get anything when `product_types` is not an array', () => {
    const productBook = { products: [ { product_id: '580040', product_types: 1 } ] };
    const pbs = ProductBookService({ config, logger, productBook, marvelService });
    const product = pbs.getProduct('AFB', 'B1');
    expect(product).not.toBeDefined();
  });
  test('should get product by `default` domain and code when there is no `KT` domain', () => {
    const product = productBookService.getProduct('LIR');
    expect(product).toBeDefined();
    expect(product).toHaveProperty('product_id');
    expect(product.product_id).toEqual('340490');
  });
  test('should get product by `default` domain, code and subcode when there is no `KT` domain', () => {
    const product = productBookService.getProduct('CSS', 'AB');
    expect(product).toBeDefined();
    expect(product).toHaveProperty('product_id');
    expect(product.product_id).toEqual('900150');
  });
  test('should get product by `default` domain, code and subcode when there is `KT` domain', () => {
    const product = productBookService.getProduct('VIC', 'MB');
    expect(product).toBeDefined();
    expect(product).toHaveProperty('product_id');
    expect(product.product_id).toEqual('470420');
  });
  test('should get product by `KT` domain, code and subcode', () => {
    // TODO does it work if customer only has default prod code
    const product = productBookService.getProduct('VIS', 'ZZ');
    expect(product).toBeDefined();
    expect(product).toHaveProperty('product_id');
    expect(product.product_id).toEqual('470820');
  });
  test('should not get product by unknown code', () => {
    const product = productBookService.getProduct('ZZZ', 'MB');
    expect(product).not.toBeDefined();
  });
  test('should not get product by unknown subcode', () => {
    const product = productBookService.getProduct('VIC', 'ZZ');
    expect(product).not.toBeDefined();
  });
  test('should auto update', async () => {
    const prodBefore = productBookService.getProduct('NRS', 'ST');
    const descBefore = prodBefore.descriptions.find(d => d.locale === 'en_CA').value;
    expect(descBefore).toBe('Non-Reg Savings - BNS');
    await jest.advanceTimersByTime(config.refreshIntervalMillis);
    expect(marvelService.getProducts).toHaveBeenCalled();
    const prodAfter = productBookService.getProduct('NRS', 'ST');
    const descAfter = prodAfter.descriptions.find(d => d.locale === 'en_CA').value;
    expect(descAfter).toBe('new description');
  });
});
