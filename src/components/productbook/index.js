// Marvel product book url 'https://cdb-int-product-ist.apps.stg.azr-cc-pcf.cloud.bns/v2/product';

const isProperty = (o, p, v) =>
  // true if v is undefined and object's property is missing
  (v === undefined && !Object.prototype.hasOwnProperty.call(o, p)) ||
  // or true if v is defined and equal to object's property, otherwise false
  (Object.prototype.hasOwnProperty.call(o, p) && o[String(p)] === v);

const isCodeSubcode = (pt, domain, code, subcode) =>
  isProperty(pt, 'product_domain', domain) &&
  isProperty(pt, 'product_code', code) &&
  isProperty(pt, 'sub_product_code', subcode);

const getProduct = (ps, code, subcode) => {
  return ps.find((p) => {
    const { product_types: pts } = p;
    if (!Array.isArray(pts)) {
      // `productTypes` is missing or not an array
      return false;
    }
    return pts.some((pt) => isCodeSubcode(pt, 'default', code, subcode)) ||
      pts.some((pt) => isCodeSubcode(pt, 'KT', code, subcode));
  });
};

let products;
const init = ({ config, logger, productBook = {}, marvelService }) => {
  products = typeof productBook !== 'undefined' && typeof productBook.data === 'object' && Array.isArray(productBook.data.products) ? productBook.data.products : [];

  const refreshJob = setInterval(async () => {
    try {
      products = await marvelService.getProducts();
      logger.info('product book - successfully updated product book');
    } catch (err) {
      logger.error({
        message: 'product book - failed to update product book',
        err: { error: err.message, stack: err.stack },
      });
    }
  }, config.refreshIntervalMillis);

  return {
    getProduct: (code, subcode) => getProduct(products, code, subcode),
    refreshJob,
  };
};

module.exports = init;
