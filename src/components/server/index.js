const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const { contextMiddleware } = require('./httpContext');

/**
 * Initializes and returns Express app
 * @returns {Object} Express app
 */
module.exports = (opts = {}) => {
  const app = express();
  app.use(helmet({
    dnsPrefetchControl: true,
    frameguard: true,
    hidePoweredBy: true,
    hsts: true,
    ieNoOpen: true,
    nocache: true,
    noSniff: true,
    xssFilter: true,
  }));
  const { origin } = opts;
  if (origin) {
    app.use(cors({ origin }));
  }
  app.use(contextMiddleware);
  app.use(express.json());
  return app;
};
