const languageEnglish = 'en';
const languageFrench = 'fr';

const languages = {
  'en': languageEnglish,
  'en-US': languageEnglish,
  'en-CA': languageEnglish,
  'fr': languageFrench,
  'fr-FR': languageFrench,
  'fr-CA': languageFrench,
};

/**
 * Returns a middleware that get the language from the header, validates it and sets to `res.locals.language`
 * @returns {(req, res, next) => void} Wrapped language middleware
 */
const init = () => (req, res, next) => {
  const xLangHeader = req.get('X-Language');
  // rewrite en and fr languages
  // legacy spaces in Canada such as CBDE_CMS for pigeon, and itrade's own space
  // are created with en-US and fr locales only, not all locales are enabled,
  // even en-CA and fr-CA
  const language = languages[ xLangHeader ] || xLangHeader || languageEnglish;
  const country = req.get('x-country-code') || 'CA';
  const locale = `${language}_${country}`;
  const languageContentful = language === 'en' && country === 'CA' ? 'en-US'
    : language === 'fr' ? language
      : `${language}-${country}`;

  res.locals.language = language;
  res.locals.country = country;
  res.locals.locale = locale;
  res.locals.languageContentful = languageContentful;

  next();
};

module.exports = init;
