const locale = require('./index');

const mockGet = jest.fn();
const mockNext = jest.fn();

describe('locale middleware', () => {
  beforeEach(() => {
    mockNext.mockClear();
    mockGet.mockClear();
  });
  test('should get `fr` language from `X-Language` and set to `res.locals.language`', () => {
    const mw = locale();
    const mockLanguage = 'fr';
    mockGet.mockReturnValueOnce(mockLanguage);
    const mockReq = { get: mockGet };
    const mockRes = { locals: {} };
    mw(mockReq, mockRes, mockNext);
    expect(mockNext).toBeCalledTimes(1);
    expect(mockRes.locals).toHaveProperty('language');
    expect(mockRes.locals.language).toEqual(mockLanguage);
  });
  test('should set `en` language if no `X-Language` header present', () => {
    const mw = locale();
    mockGet.mockReturnValueOnce(undefined);
    const mockReq = { get: mockGet };
    const mockRes = { locals: {} };
    mw(mockReq, mockRes, mockNext);
    expect(mockNext).toBeCalledTimes(1);
    expect(mockRes.locals).toHaveProperty('language');
    expect(mockRes.locals.language).toEqual('en');
  });
  test('should set `en` language if unknown `X-Language` header value present', () => {
    const mw = locale();
    mockGet.mockReturnValueOnce('be-BE');
    const mockReq = { get: mockGet };
    const mockRes = { locals: {} };
    mw(mockReq, mockRes, mockNext);
    expect(mockNext).toBeCalledTimes(1);
    expect(mockRes.locals).toHaveProperty('language');
    expect(mockRes.locals.language).toEqual('en');
  });
  test.only('validate country, locale, and contentful language', () => {
    const mw = locale();
    const mockReq = { get: jest.fn(header => {
      if (header === 'X-Language') return 'es';
      if (header === 'x-country-code') return 'DO';
    }) };
    const mockRes = { locals: {} };
    mw(mockReq, mockRes, mockNext);
    expect(mockNext).toBeCalledTimes(1);
    expect(mockRes.locals.language).toEqual('es');
    expect(mockRes.locals.country).toEqual('DO');
    expect(mockRes.locals.locale).toEqual('es_DO');
    expect(mockRes.locals.languageContentful).toEqual('es-DO');
  });
});
