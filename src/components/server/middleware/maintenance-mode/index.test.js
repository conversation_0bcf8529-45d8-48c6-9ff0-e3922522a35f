const maintenanceMode = require('./index');

// Mock LaunchDarkly service
const mockLaunchDarklyService = {
  isFeatureEnabled: jest.fn(),
};

// Mock request, response, and next
const mockRequest = {};
const mockResponse = {
  status: jest.fn().mockReturnThis(),
  json: jest.fn(),
};
const mockNext = jest.fn();
const hostingEnv = 'PCF';

describe('Maintenance Middleware', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should proceed to next middleware if not in maintenance mode', async () => {
    // Arrange
    mockLaunchDarklyService.isFeatureEnabled.mockResolvedValue({ pcf: false, gcp: false });

    // Act
    await maintenanceMode({ launchDarklyService: mockLaunchDarklyService, hostingEnv }, { code: 200, type: 'default' })(
      mockRequest,
      mockResponse,
      mockNext,
    );

    // Assert
    expect(mockNext).toHaveBeenCalled();
    expect(mockResponse.status).not.toHaveBeenCalled();
    expect(mockResponse.json).not.toHaveBeenCalled();
  });

  it('should return default response if in maintenance mode', async () => {
    // Arrange
    mockLaunchDarklyService.isFeatureEnabled.mockResolvedValue({ pcf: true, gcp: true });

    // Act
    await maintenanceMode({ launchDarklyService: mockLaunchDarklyService, hostingEnv }, { code: 200, type: 'default' })(
      mockRequest,
      mockResponse,
      mockNext,
    );

    // Assert
    expect(mockNext).not.toHaveBeenCalled();
    expect(mockResponse.status).toHaveBeenCalledWith(200);
    expect(mockResponse.json).toHaveBeenCalledWith(undefined);
  });

  it('should handle errors during feature flag check', async () => {
    // Arrange
    const errorMessage = 'Error checking maintenance mode';
    mockLaunchDarklyService.isFeatureEnabled.mockRejectedValue(new Error(errorMessage));

    // Act
    await maintenanceMode({ launchDarklyService: mockLaunchDarklyService, hostingEnv }, { code: 404, type: 'default' })(
      mockRequest,
      mockResponse,
      mockNext,
    );

    // Assert
    expect(mockNext).not.toHaveBeenCalled();
    expect(mockResponse.status).toHaveBeenCalledWith(500);
    expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Internal server error' });
  });
});
