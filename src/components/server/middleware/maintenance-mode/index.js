// Define default response data
const responses = {
  getAlerts: {
    data: {
      total: 0,
      items: [],
    },
    notifications: [],
  },
  getCampaigns: {
    data: {
      total: 0,
      limit: 100,
      offset: 0,
      items: [], // Empty array for campaign items
    },
    notifications: [], // Empty array for notifications
  },
  getCampaignsInbox: {
    data: {
      total_new: 0,
      total: 0,
      limit: 100,
      offset: 0,
      items: [], // Empty array for campaign inbox items
    },
    notifications: [], // Empty array for notifications
  },
  default: undefined, // Default response (undefined)
};

const defaultMaintanceFlagConfig = {
  'gcp': false,
  'pcf': false,
};

// Define a middleware function for maintenance mode
const maintenanceMode = ({ launchDarklyService, hostingEnv }, { code = 404, type = 'default' }) => async (req, res, next) => {
  try {
    // Check if maintenance mode is enabled using LaunchDarkly service
    const isMaintenanceModeEnabled = await launchDarklyService.isFeatureEnabled(
      'pigeon-api.features.maintenance-mode-v2',
      defaultMaintanceFlagConfig,
    );

    if (isMaintenanceModeEnabled[hostingEnv.toLowerCase()]) {
      // Return the appropriate response based on the specified type
      return res.status(code).json(responses[type]);
    }

    // If not in maintenance mode, proceed to the next middleware
    next();
  } catch (error) {
    // Handle any errors that occur during feature flag check
    console.error('Error checking maintenance mode:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = maintenanceMode;
