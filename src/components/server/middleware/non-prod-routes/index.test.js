const nonProdRoutesMiddleware = require('./index');
const YAML = require('yamljs');
const swaggerUI = require('swagger-ui-express');
const { authenticationMiddleware } = require('../../../../../acceptance-tests/mock');

const openApiDef = YAML.load('openapi.yml');

jest.mock('swagger-ui-express', () => ({
  serve: jest.fn(() => jest.fn()),
  setup: jest.fn(() => jest.fn()),
}));

let mockRes = {};
const mockNext = jest.fn();

describe('API docs middleware', () => {
  beforeEach(() => {
    mockRes = {
      status: jest.fn(),
    };
  });

  test('should serve swagger UI', () => {
    const mw = nonProdRoutesMiddleware({ authenticationMiddleware });
    mw.handle({ url: '/api-docs' }, mockRes, mockNext);
    expect(swaggerUI.serve).toHaveBeenCalled();
    expect(swaggerUI.setup).toHaveBeenCalledWith(openApiDef);
  });

  test('should serve openapi json', () => {
    const mw = nonProdRoutesMiddleware({ authenticationMiddleware });
    mw.handle({ url: '/openapi.json', method: 'GET' }, mockRes, mockNext);
    expect(mockRes.status).toHaveBeenCalledWith(200);
  });

  test('should serve schema-discovery', () => {
    const mw = nonProdRoutesMiddleware({ authenticationMiddleware });
    mw.handle({ url: '/.well-known/schema-discovery', method: 'GET' }, mockRes, mockNext);
    expect(mockRes.status).toHaveBeenCalledWith(200);
  });
});
