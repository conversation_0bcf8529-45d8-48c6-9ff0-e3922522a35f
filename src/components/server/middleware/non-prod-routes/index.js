const { Router } = require('express');
const swaggerUI = require('swagger-ui-express');
const YAML = require('yamljs');
const clearDisposition = require('../../../dispositions/routes/clear-disposition');

const openApiDef = YAML.load('openapi.yml');

const init = ({ authenticationMiddleware, config,
  campaignCacheService,
  dispositionsService }) => {
  const router = Router();

  router.use('/api-docs', swaggerUI.serve, swaggerUI.setup(openApiDef));
  router.get('/openapi.json', (req, res) => res.status(200).json(openApiDef));
  router.get('/.well-known/schema-discovery', (req, res) => res.status(200).json({
    schema_url: '/openapi.json',
    schema_type: 'openapi-3.0',
    ui_url: '/api-docs/',
  }));
  router.delete('/v1/dispositions', authenticationMiddleware, clearDisposition({ dispositionsService, campaignCacheService }, config));

  return router;
};

module.exports = init;
