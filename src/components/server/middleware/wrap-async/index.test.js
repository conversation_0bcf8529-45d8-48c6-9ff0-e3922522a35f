const wrapAsync = require('./index');

const mockReq = {};
const mockRes = {};
const mockNext = jest.fn();

describe('Wrap Async middleware', () => {
  test('initialize', () => {
    const fn = jest.fn().mockResolvedValueOnce(true);
    const mw = wrapAsync(fn);
    expect(mw).toBeDefined();
    expect(typeof mw).toEqual('function');
  });
  test('pass rejection to next', async () => {
    const mockError = new Error('some error');
    const fn = async () => {
      throw mockError;
    };
    const mw = wrapAsync(fn);
    await mw(mockReq, mockRes, mockNext);
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls.length).toEqual(1);
    expect(mockNext.mock.calls[0][0] instanceof Error).toEqual(true);
    expect(mockNext.mock.calls[0][0].message).toEqual(mockError.message);
  });
});
