const undefinedRoute = require('./index');
const { HttpError } = require('../../../errors');

const mockNext = jest.fn();

describe('Undefined Route middleware', () => {
  beforeEach(() => {
    mockNext.mockClear();
  });
  test('should return 404', () => {
    const mw = undefinedRoute();
    mw({}, {}, mockNext);
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls.length).toEqual(1);
    expect(mockNext.mock.calls[0][0] instanceof HttpError).toEqual(true);
    expect(mockNext.mock.calls[0][0].statusCode).toEqual(404);
  });
});
