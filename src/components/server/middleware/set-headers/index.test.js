const httpMocks = require('node-mocks-http');
const setHeadersMiddleware = require('./index');

const next = jest.fn();

const channelIdHeader = 'x-channel-id';
const applicationHeader = 'x-application';
const userAgentHeader = 'x-user-agent';

describe('Undefined Route middleware', () => {
  beforeEach(() => {
    next.mockClear();
  });

  test('should not change existing header values', () => {
    const req = httpMocks.createRequest({
      headers: {
        [channelIdHeader]: 'aaa',
        [applicationHeader]: 'bbb',
      },
      query: {},
    });
    const res = httpMocks.createResponse();
    setHeadersMiddleware(req, res, next);
    expect(next).toBeCalledTimes(1);
    expect(req.get(channelIdHeader)).toEqual('aaa');
    expect(req.get(applicationHeader)).toEqual('bbb');
  });

  test('should set default language to `en`', () => {
    const req = httpMocks.createRequest({
      headers: {},
    });
    const res = httpMocks.createResponse();
    setHeadersMiddleware(req, res, next);
    expect(next).toBeCalledTimes(1);
  });

  test('should set x-channel-id to `Mobile` and x-application to `N1`', () => {
    const req = httpMocks.createRequest({
      headers: {},
      query: { application: 'nova', platform: 'ios' },
    });
    const res = httpMocks.createResponse();
    setHeadersMiddleware(req, res, next);
    expect(next).toBeCalledTimes(1);
    expect(req.get(channelIdHeader)).toEqual('Mobile');
    expect(req.get(applicationHeader)).toEqual('N1');
  });

  test('should set x-application to `N1` by x-user-agent', () => {
    const req = httpMocks.createRequest({
      headers: { [userAgentHeader]: 'Scotiabank Mobile iOS/20.28.1-1 (iPhone11,8; iOS 15.0)' },
      query: { application: 'nova' },
    });
    const res = httpMocks.createResponse();
    setHeadersMiddleware(req, res, next);
    expect(next).toBeCalledTimes(1);
    expect(req.get(channelIdHeader)).toEqual('Mobile');
    expect(req.get(applicationHeader)).toEqual('N1');
  });

  test('should set x-channel-id to `Mobile` and x-application to `N2`', () => {
    const req = httpMocks.createRequest({
      headers: {},
      query: { application: 'nova', platform: 'android' },
    });
    const res = httpMocks.createResponse();
    setHeadersMiddleware(req, res, next);
    expect(next).toBeCalledTimes(1);
    expect(req.get(channelIdHeader)).toEqual('Mobile');
    expect(req.get(applicationHeader)).toEqual('N2');
  });

  test('should set x-application to `N2` by x-user-agent', () => {
    const req = httpMocks.createRequest({
      headers: { [userAgentHeader]: 'Scotiabank Mobile Android/20.31.1 (Android 11; Build/RP1A.200720.012.G970WVLU6GUH5)' },
      query: { application: 'nova' },
    });
    const res = httpMocks.createResponse();
    setHeadersMiddleware(req, res, next);
    expect(next).toBeCalledTimes(1);
    expect(req.get(channelIdHeader)).toEqual('Mobile');
    expect(req.get(applicationHeader)).toEqual('N2');
  });

  test('should set x-channel-id to `ABM`', () => {
    const req = httpMocks.createRequest({
      headers: {},
      query: { application: 'abm', platform: 'web' },
    });
    const res = httpMocks.createResponse();
    setHeadersMiddleware(req, res, next);
    expect(next).toBeCalledTimes(1);
    expect(req.get(channelIdHeader)).toEqual('ABM');
  });

  test('should set x-channel-id to `Online`', () => {
    const req = httpMocks.createRequest({
      headers: {},
      query: { application: 'some', platform: 'web' },
    });
    const res = httpMocks.createResponse();
    setHeadersMiddleware(req, res, next);
    expect(next).toBeCalledTimes(1);
    expect(req.get(channelIdHeader)).toEqual('Online');
  });

  test('should set x-channel-id to `Online`', () => {
    const req = httpMocks.createRequest({
      headers: {},
      query: { application: 'nova', platform: 'web' },
    });
    const res = httpMocks.createResponse();
    setHeadersMiddleware(req, res, next);
    expect(next).toBeCalledTimes(1);
    expect(req.get(channelIdHeader)).toEqual('Mobile');
  });
});
