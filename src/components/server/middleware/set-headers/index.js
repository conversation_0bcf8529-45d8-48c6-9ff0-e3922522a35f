/* eslint-disable security/detect-object-injection */
const channelIdHeader = 'x-channel-id';
const applicationHeader = 'x-application';

const channelMobile = 'Mobile';
const channelOnline = 'Online';
const channelAbm = 'ABM';
const applicationIos = 'N1';
const applicationAndroid = 'N2';

const identifyChannelByApplication = (application) => {
  if (application === 'nova') {
    return channelMobile;
  } else if (application === 'abm') {
    return channelAbm;
  } else {
    return channelOnline;
  }
};

const identifyApplication = (platform, userAgent) => {
  if (platform === 'ios') {
    return applicationIos;
  } else if (platform === 'android') {
    return applicationAndroid;
  } else if (userAgent) {
    return userAgent.toLowerCase().indexOf('ios') !== -1 ? applicationIos : applicationAndroid;
  }
  return undefined;
};

const setHeadersMiddleware = (req, res, next) => {
  // x-channel-id
  if (!req.get(channelIdHeader)) {
    req.headers[channelIdHeader] = identifyChannelByApplication(req.query.application);
  }
  // x-application
  if (req.get(channelIdHeader) === 'Mobile' && !req.get(applicationHeader)) {
    req.headers[applicationHeader] = identifyApplication(req.query.platform, req.get('x-user-agent'));
  }
  next();
};

module.exports = setHeadersMiddleware;
