/* eslint-disable sonarjs/no-identical-functions */
/* eslint-disable sonarjs/no-duplicate-string */
const EventEmitter = require('events');
const requestLogger = require('./index');

const logger = {
  info: jest.fn(),
};

const mockReq = {
  remoteAddress: '::1',
  remotePort: 31337,
  method: 'GET',
  originalUrl: '/v1/test/url',
  headers: {
    'content-type': 'application/json',
    accept: 'application/json',
    Authorization: 'Bearer 12345678',
  },
};
const mockRes = new EventEmitter();
mockRes.json = jest.fn();
mockRes.getHeaders = jest.fn();
mockRes.statusCode = 200;
mockRes.locals = {};
mockRes.headers = {
  'content-type': 'application/json',
};
const mockNext = jest.fn();
const mockError401 = {
  code: 'HTTP_UNAUTHORIZED',
  message: 'Unauthorized',
  uuid: '58af90d1-8605-4a03-92be-fab768284784',
  timestamp: '2019-03-24T02:18:26.072Z',
  metadata: [],
};

describe('Request Logger Middleware', () => {
  beforeEach((done) => {
    logger.info.mockClear();
    mockNext.mockClear();
    done();
  });
  test('should export createRequestLogger', () => {
    expect(requestLogger).toHaveProperty('createRequestLogger');
    expect(requestLogger.createRequestLogger).toBeInstanceOf(Function);
  });
  test('should log 200 response', (done) => {
    const mw = requestLogger.createRequestLogger({ logger });
    mw(mockReq, mockRes, mockNext);
    mockRes.json(mockError401);
    mockRes.emit('finish');
    expect(mockNext).toBeCalledTimes(1);
    setTimeout(() => {
      expect(logger.info).toBeCalledTimes(1);
      const logData = logger.info.mock.calls[0][0];
      expect(logData.card_number).toEqual('not found');
      expect(logData.request.method).toEqual(mockReq.method);
      expect(logData.request.url).toEqual(mockReq.originalUrl);
      expect(logData.request.headers.Authorization).toEqual('***');
      expect(logData.response.bidy).toBeUndefined();
      expect(logData.message).toEqual('request');
      done();
    }, 1);
  });
  test('should log card number from res.locals.auth.claims.profile.customerInfo.cardNumber', (done) => {
    const mw = requestLogger.createRequestLogger({ logger });
    mw(mockReq, mockRes, mockNext);
    mockRes.json(mockError401);
    mockRes.locals = { auth: { claims: { profile: { customerInfo: { cardNumber: '****************' } } } } };
    mockRes.emit('finish');
    expect(mockNext).toBeCalledTimes(1);
    setTimeout(() => {
      expect(logger.info).toBeCalledTimes(1);
      const logData = logger.info.mock.calls[0][0];
      expect(logData.card_number).toEqual('************1111');
      done();
    }, 1);
  });
  test('should log card number from res.locals.auth.claims.sub', (done) => {
    const mw = requestLogger.createRequestLogger({ logger });
    mw(mockReq, mockRes, mockNext);
    mockRes.json(mockError401);
    mockRes.locals = { auth: { claims: { sub: '****************' } } };
    mockRes.emit('finish');
    expect(mockNext).toBeCalledTimes(1);
    setTimeout(() => {
      expect(logger.info).toBeCalledTimes(1);
      const logData = logger.info.mock.calls[0][0];
      expect(logData.card_number).toEqual('************1111');
      done();
    }, 1);
  });
  test('should log 401 response', (done) => {
    const mw = requestLogger.createRequestLogger({ logger });
    mockRes.statusCode = 400;
    mw(mockReq, mockRes, mockNext);
    mockRes.json(mockError401);
    mockRes.emit('finish');
    expect(mockNext).toBeCalledTimes(1);
    setTimeout(() => {
      expect(logger.info).toBeCalledTimes(1);
      const logData = logger.info.mock.calls[0][0];
      expect(logData.request.method).toEqual(mockReq.method);
      expect(logData.request.url).toEqual(mockReq.originalUrl);
      expect(logData.request.headers.Authorization).toEqual('***');
      expect(logData.response).toHaveProperty('body');
      expect(logData.response.body.code).toEqual(mockError401.code);
      expect(logData.message).toEqual('request');
      done();
    }, 1);
  });
  test('should not log /health endpoint', () => {
    const mw = requestLogger.createRequestLogger({ logger });
    mw({ ...mockReq, originalUrl: '/health' }, mockRes, mockNext);
    expect(mockNext).toBeCalledTimes(1);
  });
});
