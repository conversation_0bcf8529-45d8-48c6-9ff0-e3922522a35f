const R = require('ramda');
const { traceHeaders } = require('pigeon-pigeon-pack');

const excludedHeaders = R.map(<PERSON><PERSON>toLower, [
  'authorization',
  'X-customer-authorization',
  'x-customer-scotiacard',
  'x-customer-id',
]);

/**
 * Returns an object with redacted keys (case-insensitive)
 * @param {String[]} keys - Keys to redact
 * @param {Object} obj - Object
 * @returns {Object} - Result
 */
const redactKeys = (items, o) => {
  return R.mapObjIndexed((val, key) => {
    return items.includes(R.toLower(key)) ? '***' : val;
  }, o);
};

/**
 * Creates and returns a request logging middleware
 * @param {Object} deps - Dependencies
 * @returns {(req, res, next) => void} Middleware
 */
const createRequestLogger = ({ logger, clsNamespace }) => (req, res, next) => {
  if (req.originalUrl === '/health') {
    next();
    return;
  }
  const started = Date.now();
  let responseBody = {};
  const jsonOrg = res.json.bind(res);
  res.json = (body) => {
    responseBody = body;
    jsonOrg(body);
  };
  res.once('finish', () => {
    const diff = Date.now() - started;
    const request = {
      remoteAddress: req.remoteAddress,
      remotePort: req.remotePort,
      method: req.method,
      url: req.originalUrl,
      headers: redactKeys(excludedHeaders, req.headers),
    };
    const response = {
      statusCode: res.statusCode,
      headers: { ...res.getHeaders() },
      body: res.statusCode < 400 ? undefined : responseBody,
    };
    const cardNumber = R.path([ 'auth', 'claims', 'profile', 'customerInfo', 'cardNumber' ], res.locals) || R.path([ 'auth', 'claims', 'sub' ], res.locals);
    logger.info({
      message: 'request',
      response_time: diff,
      ...traceHeaders.getTraceIds(clsNamespace),
      card_number: cardNumber ? `************${cardNumber.slice(-4)}` : 'not found',
      url: {
        originalUrl: req.originalUrl,
        params: req.params,
        query: req.query,
        baseUrl: `${req.baseUrl}${R.pathOr('', [ 'route', 'path' ], req)}`,
      },
      request,
      response,
    });
  });
  next();
};

module.exports = {
  createRequestLogger,
};
