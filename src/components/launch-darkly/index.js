const init = (LaunchDarkly, secret, user, ldConfig) => {
  let ldclient;
  const initFunc = async () => {
    ldclient = LaunchDarkly.init(secret, ldConfig);
    return ldclient.waitForInitialization();
  };

  return {
    init: initFunc,
    isFeatureEnabled: (featureName, defaultValue = false) => ldclient.variation(featureName, { key: user }, defaultValue),
    close: () => ldclient.close(),
  };
};

module.exports = init;
