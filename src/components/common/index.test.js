const { getResponse, arrToObj, mergeContentIntoRules, validateCampaignRuleType } = require('./index');

describe('Common > get and parse fetch response', () => {
  test('should handle non JSON response', async () => {
    try {
      const res = await getResponse({
        status: 200,
        ok: true,
        text: jest.fn().mockResolvedValueOnce('sample text'),
      });
      expect(res).toBeUndefined();
    } catch (err) {
      expect(err).toHaveProperty('response');
      expect(err.response).toEqual('sample text');
    }
  });
});

const mockContentStr = 'mock content str';

describe('array to object conversion func arrToObj', () => {
  it('should convert an array to a keyed object', () => {
    const obj = arrToObj([ {
      prop1: 'key1',
      prop2: 'prop2',
    }, {
      prop1: 'key2',
      prop2: 'prop2',
    } ], 'prop1');
    expect(obj).toEqual({
      key1: {
        prop1: 'key1',
        prop2: 'prop2',
      },
      key2: {
        prop1: 'key2',
        prop2: 'prop2',
      },
    });
  });
});

describe('merging content into rules', () => {
  const logger = {
    warn: jest.fn(),
  };

  beforeEach(() => {
    logger.warn.mockReset();
  });

  it('should merge content into rules', () => {
    const rulesObj = {
      rule1: {
        content_id: 'content_id_1',
      },
      rule2: {
        content_id: 'content_id_2',
      },
    };
    const contentsObj = {
      content_id_1: {
        content: {
          preview: {
            title: mockContentStr,
          },
        },
      },
      content_id_2: {
        content: {
          preview: {
            title: mockContentStr,
          },
        },
      },
    };
    const output = mergeContentIntoRules({ logger }, rulesObj, contentsObj);
    expect(output).toEqual({
      rule1: {
        content_id: 'content_id_1',
        content: {
          preview: {
            title: mockContentStr,
          },
        },
      },
      rule2: {
        content_id: 'content_id_2',
        content: {
          preview: {
            title: mockContentStr,
          },
        },
      },
    });
  });
  it('should skip a rule if content is missing when merging', () => {
    const rulesObj = {
      rule1: {
        content_id: 'content_id_1',
      },
    };
    const contentsObj = {
      content_id_2: { // mismatch id here
        content: {
          preview: {
            title: mockContentStr,
          },
        },
      },
    };
    const output = mergeContentIntoRules({ logger }, rulesObj, contentsObj);
    expect(output).toEqual({});
    expect(logger.warn).toHaveBeenCalledTimes(1);
  });
  it('should skip 1 rule if content path is invalid for that rule', () => {
    const rulesObj = {
      rule1: {
        content_id: 'content_id_1',
      },
      rule2: {
        content_id: 'content_id_2',
      },
    };
    const contentsObj = {
      content_id_1: {
        content: {
          preview: {
            title: mockContentStr,
          },
          // details: {} is missing for this one,
        },
      },
      content_id_2: {
        content: {
          details: {
            contentProp: mockContentStr,
          },
        },
      },
    };
    const output = mergeContentIntoRules({ logger }, rulesObj, contentsObj, 'details');
    expect(output).toEqual({
      rule2: {
        content_id: 'content_id_2',
        content: {
          contentProp: mockContentStr,
        },
      },
    });
    expect(logger.warn).toHaveBeenCalledTimes(1);
  });
});

describe('common > validateCampaignRuleType', () => {
  it('CA', () => {
    const { error, value } = validateCampaignRuleType('CA', 'campaign');
    expect(error).toBeFalsy();
    expect(value).toBe('campaign');
  });

  it('CCAU', () => {
    const { error, value } = validateCampaignRuleType('DO', 'ccau_campaign');
    expect(error).toBeFalsy();
    expect(value).toBe('ccau_campaign');
  });

  it('default value to campaign if no rules are found', () => {
    const { value, error } = validateCampaignRuleType('DO', undefined);
    expect(error).toBe(undefined);
    expect(value).toBe('ccau_campaign');
  });

  it('disallowed combo', () => {
    const { error } = validateCampaignRuleType('CA', 'ccau_campaign');
    expect(error).toBe('Given country has insufficient access to rule types available');
  });

  it('unknown country', () => {
    const { error } = validateCampaignRuleType('BAD COUNTRY CODE', 'campaign');
    expect(error).toBe('Given country has insufficient access to rule types available');
  });
});
