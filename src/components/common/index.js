const getResponse = async (result) => {
  const response = await result.text();
  try {
    const json = JSON.parse(response);
    if (result.ok) {
      return Promise.resolve(json);
    } else {
      const err = new Error(result.statusText);
      err.response = json;
      return Promise.reject(err);
    }
  } catch (err) {
    err.response = response;
    return Promise.reject(err);
  }
};

/**
 * Creates a object based on a an array of objects based on a supplied key name (or id as default)
 * @param {Array} arr - Array of objects
 * @param {String} arr - Name of the key
 * @returns {Object} - Resulting object
 */
const arrToObj = (arr = [], key = 'id') => arr.reduce((obj, cur) => ({ ...obj, [cur[key || 'id']]: cur }), {});

/**
 * Merges contents objects into objects of rules.
 * @param {Object} rules - Object of rules, where keys are rule ids
 * @param {Object} contents - Object of contents from content service, where keys are content ids
 * @param {String} contentType - An additional path to pick from content object, e.g. "preview" or "details"
 * @returns {Object} - An object of rules with injected content objects.
 * A rule with a missing content is omitted from the response and an error is reported via logger.
 */
const mergeContentIntoRules = ({ logger }, rules = {}, contents = {}, contentType = undefined) => {
  return Object.entries(rules).reduce((output, [ ruleId, rule ]) => {
    if (!contents[rule.content_id]) {
      logger.warn({
        message: `content (id ${rule.content_id}) missing for rule id ${ruleId}`,
        ruleId,
        contentSpace: rule.content_space,
        contentId: rule.content_id,
      });
      return output;
    } else {
      let content;
      try {
        content = contents[rule.content_id].content;
        if (contentType) {
          content = content[contentType]; // eslint-disable-line
          if(!content) { // eslint-disable-line
            throw new Error(`Error while merging rules: \n
              content.${contentType} is invalid path in the contentful object`);
          }
        }
      } catch (err) {
        logger.warn({ message: `invalid path for content object ${contentType}`, err });
        return output;
      }
      return {
        ...output,
        [ruleId]: {
          ...rule,
          content,
        },
      };
    }
  }, {});
};

// disposition values - https://confluence.agile.bns/display/PIGEON/Disposition+Mapping
const dispositionValues = {
  accepted: 'A',
  dismissed: 'D',
  inprogress: 'P',
  newMobile: 'N',
  newABM: '',
  no: 'C',
  snooze: 'S',
  viewed: 'V',
  yes: 'Y',
  seen: 'W',
  deleted: 'X',
};

// dispositions that needs to be saved to Pigeon database
const dispositionDatabase = [
  dispositionValues.dismissed,
  dispositionValues.viewed,
  dispositionValues.seen,
  dispositionValues.deleted,
];

// dispositions that can be sent to Insights
const dispositionInsights = [
  dispositionValues.accepted,
  dispositionValues.dismissed,
  dispositionValues.viewed,
  dispositionValues.inprogress,
  dispositionValues.no,
  dispositionValues.snooze,
  dispositionValues.yes,
];

// Modes for orion inbox endpoint
const modesList = {
  unseenCount: 'unseen',
};

/**
 * Checks if disposition update is required for Insights
 * @param {string} cd Current disposition value
 * @param {string} nd New disposition value
 * @returns {boolean} Disposition's expiration date
 */
const dispositionUpdateRequired = (cd, nd) => {
  if (nd === dispositionValues.viewed) {
    return typeof cd === 'string' && (cd.trim() === dispositionValues.newMobile || cd.trim() === dispositionValues.newABM);
  }
  return cd !== nd;
};

/**
 * Determine if new disposition need to be stored to pigeon disposition DB.
 * If new disposition is V, always store.
 * If new disposition is D, store if rule is dismissible.
 * Otherwise do not store.
 *
 * @param {boolean} dismissibleFlag - dismissible flag from pigeon rule
 * @param {string} disposition - new disposition from request
 * @returns {boolean} Whether disposition value should be stored to Pigeon's dispositions database
 */
const shouldStoreDispositionToDb = (dismissibleFlag, disposition) => {
  return !((!dismissibleFlag && disposition === 'D') || !dispositionDatabase.includes(disposition));
};
const messageSourceArr = [ 'PEGA', 'PEGAV2' ];

/**
 * Verify if the combination of campaign rule type and country code is whitelisted.
 * Default to whitelisted rule type if none is provided.
 * https://jira.agile.bns/browse/PIGEON-4657
 * - campaign: CA
 * - ccau_campaign: BS, BB, KY, DO, JM, TT, TC, PA, GY
 *
 * @param {string} country - two letter country code.
 *   Expected to be CA if not provided, in order to support legacy clients.
 * @param {string} ruleType - active campaigns rule type.
 *   Expected to be derived from active campaign rule cache.
 * @return {Object} result
 * @return {boolean} result.value - validated rule type.
 *   Returns as is if valid. Defaults to whitelisted value if not provided.
 *   Undefined if invalid.
 * @return {string} result.error - validation error
 */
const validateCampaignRuleType = (country = 'CA', ruleType) => {
  const whitelist = [
    {
      countries: [ 'CA' ],
      ruleType: 'campaign',
    },
    { // ccau region
      countries: [ 'BS', 'BB', 'KY', 'DO', 'JM', 'TT', 'TC', 'PA', 'GY' ],
      ruleType: 'ccau_campaign',
    },
  ];
  const whitelisted = whitelist.find(entry => entry.countries.includes(country.toUpperCase()));
  const validated = whitelisted && (!ruleType || ruleType === whitelisted.ruleType);
  return {
    error: validated ? undefined : 'Given country has insufficient access to rule types available',
    value: validated && whitelisted.ruleType,
  };
};

const ORION_INBOX_CONTAINER_ID = 'inbox-updates---orion';

const ORION_INBOX_PAGE_ID = 'inbox-updates';

const orionInboxContainers = [ 'inbox-updates---orion', 'orion-campaign' ];

module.exports = {
  getResponse,
  arrToObj,
  mergeContentIntoRules,
  dispositionUpdateRequired,
  shouldStoreDispositionToDb,
  dispositionValues,
  dispositionDatabase,
  dispositionInsights,
  messageSourceArr,
  validateCampaignRuleType,
  modesList,
  ORION_INBOX_CONTAINER_ID,
  orionInboxContainers,
  ORION_INBOX_PAGE_ID,
};
