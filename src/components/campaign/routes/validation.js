const Joi = require('@hapi/joi');

const id = Joi.string().min(1).max(20).regex(/^[a-zA-Z0-9]+$/);

const getCampaignPathSchema = Joi.object()
  .keys({ id })
  .requiredKeys([ 'id' ]);

const getCampaignSchema = Joi.object()
  .keys({
    message_id: Joi.string().min(1).max(50).regex(/^[a-zA-Z0-9-]+$/),
    insight: Joi.boolean().default(false),
    application: Joi.string().min(1).max(20).regex(/^[a-zA-Z0-9_-]+$/).lowercase(),
    select_contents: Joi.string().min(1).max(50).regex(/^[,a-zA-Z0-9_-]+$/).optional().default('details'),
  });

const getCampaignsSchema = Joi.object()
  .keys({
    page: Joi.string().min(1).max(64).regex(/^[a-zA-Z0-9_\-.,]+$/),
    page_ex: Joi.string().min(1).max(64).regex(/^[a-zA-Z0-9_\-.,]+$/),
    container: Joi.string().min(1).max(64).regex(/^[a-zA-Z0-9_-]+$/),
    platform: Joi.string().min(1).max(20).regex(/^[a-zA-Z]+$/),
    app_version: Joi.string().min(1).max(50).allow(''),
    os_version: Joi.string().min(1).max(100).allow(''),
    device_model: Joi.string().min(1).max(100).allow(''),
    limit: Joi.number().min(1).default(100),
    insight: Joi.boolean().default(false),
    application: Joi.string().min(1).max(20).regex(/^[a-zA-Z0-9_-]+$/).lowercase(),
    secure_device: Joi.boolean().optional(),
    select_contents: Joi.string().min(1).max(50).regex(/^[,a-zA-Z0-9_-]+$/).optional().default('preview'),
    offset: Joi.number().min(0).default(0),
  })
  .requiredKeys([ 'platform' ]).oxor('page', 'page_ex', 'Request can only incudes one of page/page_ex');

const getSamlTokenSchema = Joi.object()
  .keys({
    message_id: Joi.string().min(1).max(50).regex(/^[a-zA-Z0-9-]+$/),
  })
  .requiredKeys([ 'message_id' ]);

const dispositionPathSchema = Joi.object()
  .keys({ id })
  .requiredKeys([ 'id' ]);

const dispositionBodySchema = Joi.object()
  .keys({
    message_id: Joi.string().min(1).max(50).regex(/^[a-zA-Z0-9-]+$/),
    disposition: Joi.string().valid('V', 'D', 'P', 'A', 'C', 'Y', 'N', 'S', ' '),
    page: Joi.string().min(1).max(64).regex(/^[a-zA-Z0-9_\-.]+$/),
    container: Joi.string().min(1).max(64).regex(/^[a-zA-Z0-9_-]+$/),
    platform: Joi.string().min(1).max(20).regex(/^[a-zA-Z]+$/),
    application: Joi.string().min(1).max(20).regex(/^[a-zA-Z]+$/).lowercase(),
  });

const getCatalogSchema = Joi.object()
  .keys({
    from_date: Joi.date().iso().raw(true).required(),
    to_date: Joi.date().iso().raw(true).required(),
  });

module.exports = {
  getCampaignPathSchema,
  getCampaignSchema,
  getCampaignsSchema,
  getSamlTokenSchema,
  dispositionPathSchema,
  dispositionBodySchema,
  getCatalogSchema,
};
