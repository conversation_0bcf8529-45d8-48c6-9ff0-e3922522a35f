const { Router } = require('express');
const routes = require('./index');

const mockNext = jest.fn();
const mockJson = jest.fn();
const mockStatus = jest.fn();

const mockLogger = {
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};
const mockAuthorize = () => (req, res, next) => next();

const router = routes({
  logger: mockLogger,
  authorize: mockAuthorize,
  rateLimitingMiddleware: jest.fn(),
  campaignsTotalRateLimiter: jest.fn(),
  campaignsClientRateLimiter: jest.fn(),
}, { space: 'IST', features: { application: true, disposition: true } });

describe('Campaign routes', () => {
  beforeEach(() => {
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
  });
  test('should be an express.Router object', () => {
    expect(router.prototype).toEqual(Router.prototype);
    expect(typeof router.get).toEqual('function');
    expect(typeof router.use).toEqual('function');
    expect(typeof router.handle).toEqual('function');
  });
  test('should have GET /campaigns', () => {
    const found = router.stack.some((item) => item.route && item.route.path === '/' && item.route.methods.get);
    expect(found).toEqual(true);
  });
  test('should have GET /campaigns/:id', () => {
    const found = router.stack.some((item) => item.route && item.route.path === '/:id' && item.route.methods.get);
    expect(found).toEqual(true);
  });
  test('should have GET /campaigns/:id/token', () => {
    const found = router.stack.some((item) => item.route && item.route.path === '/:id/token' && item.route.methods.get);
    expect(found).toEqual(true);
  });
  test('should have DELETE /campaigns', () => {
    const found = router.stack.some((item) => item.route && item.route.path === '/' && item.route.methods.delete);
    expect(found).toEqual(true);
  });
  test('should have POST /campaigns/:id/dispositions when enabled', () => {
    const found = router.stack.some((item) => item.route && item.route.path === '/:id/dispositions' && item.route.methods.post);
    expect(found).toEqual(true);
  });
  test('should not have POST /campaigns/:id/dispositions when disabled', () => {
    const r = routes({
      logger: mockLogger,
      authorize: mockAuthorize,
      rateLimitingMiddleware: jest.fn(),
      campaignsTotalRateLimiter: jest.fn(),
      campaignsClientRateLimiter: jest.fn(),
    }, { space: 'IST', features: { disposition: false } });
    const found = r.stack.some((item) => item.route && item.route.path === '/:id/dispositions' && item.route.methods.post);
    expect(found).toEqual(false);
  });
});
