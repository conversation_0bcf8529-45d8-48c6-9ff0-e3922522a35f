const getSamlToken = require('./get-saml-token');

const next = jest.fn();
const json = jest.fn();
const status = jest.fn().mockReturnValue({ json });
const logger = { info: jest.fn(), error: jest.fn() };
const targetedCampaignService = {
  getInstance: jest.fn(),
};
targetedCampaignService.getInstance.mockReturnValue({ getCampaign: jest.fn(), setDisposition: jest.fn() });
const credentialsService = { createSamlToken: jest.fn() };
const campaignCacheService = { get: jest.fn() };
const get = jest.fn();
const jwt = {
  verify: jest.fn().mockReturnValue({}),
};

const res = {
  status,
  locals: {
    language: 'en',
    auth: {
      token: 'token',
      claims: {
        some: 'thing',
      },
    },
  },
};

const samlToken = 'testsamltoken';
const xChannelId = 'Mobile';
const userAgent = 'iPhone';

const mockInsightDataMortgageRenewal = { data: { campaign_id: 'XGC99' } };
const mockInsightDataMortgageRenewalPegaV2 = { data: { campaign_id: 'MTG', campaign_code: 'ET04024' } };
const mockInsightDataStepCli = { data: { campaign_id: 'JAB99' } };
const mockInsightDataStepAcquisition = { data: { campaign_id: 'XEY99' } };
const mockInsightDataCeba = { data: { campaign_id: 'CTN01' } };
const mockInsightDataSBPacc = { data: { campaign_id: 'CZA01' } };

const mockCampaignDataMortgageRenewal = {
  id: 'abc123',
  external_ref: 'XGC99',
  start_at: '2019-01-01T00:00:00.000Z',
  end_at: '2029-12-31T23:59:59.999Z',
  status: 'published',
  deleted: false,
  disabled: false,
  type: 'campaign',
};
const mockCampaignDataMortgageRenewalPegaV2 = {
  id: 'abc123',
  external_ref: 'MTG',
  start_at: '2019-01-01T00:00:00.000Z',
  end_at: '2029-12-31T23:59:59.999Z',
  status: 'published',
  deleted: false,
  disabled: false,
  type: 'campaign',
};
const mockCampaignDataStepCli = {
  id: 'abc1231',
  external_ref: 'JAB99',
  start_at: '2019-01-01T00:00:00.001Z',
  end_at: '2029-12-31T23:59:59.998Z',
  status: 'published',
  deleted: false,
  disabled: false,
  type: 'campaign',
};
const mockCampaignDataStepAcquisition = {
  id: 'abc1232',
  external_ref: 'XEY99',
  start_at: '2019-01-01T00:00:00.002Z',
  end_at: '2029-12-31T23:59:59.997Z',
  status: 'published',
  deleted: false,
  disabled: false,
  type: 'campaign',
};

const mockCampaignDataCeba = {
  id: 'abc1233',
  external_ref: 'CTN01',
  start_at: '2019-01-01T00:00:00.002Z',
  end_at: '2029-12-31T23:59:59.997Z',
  status: 'published',
  deleted: false,
  disabled: false,
  type: 'campaign',
};

const mockCampaignDataSBPacc = {
  id: 'abc1233',
  external_ref: 'CZA01',
  start_at: '2020-01-01T00:00:00.002Z',
  end_at: '2028-12-31T23:59:59.997Z',
  status: 'published',
  deleted: false,
  disabled: false,
  type: 'campaign',
};

const mockCredentialData = samlToken;
const config = {
  saml: { exitUrl: 'https://exit.url' },
  publicKeyJWKS: 'https://passport-oauth-ist.apps.cloud.bns/oauth2/v1/certs',
};

const jwksClient = jest.fn().mockReturnValue({
  getSigningKey: jest.fn().mockResolvedValue({
    getPublicKey: jest.fn().mockReturnValue('mock-signing-key'),
  }),
});

const jwksResponse = {
  ok: true,
  json: jest.fn().mockResolvedValue({
    keys: [ {
      kty: 'RSA',
      e: 'AQAB',
      use: 'sig',
      kid: 'QWo32GiQbjJ-8_ciRzJoGGNQG0cCGpdeNu5afhJAAiA',
      alg: 'RS256',
      n: 'way67Wsm3PwL13FFQv8pB-_rPPOXq0ARxTW4fDPtpn3GmnCiY7f1X4Sk5Iye8YfsRLZYBhUtkdRrZ3bPJKjuIcTEC2Iz_th86Y-6jm05EEYERh7iOKZMCLtVslkkRW5x8MNajoTnbvPYlFpaZ9lU7vylbtR0DVg9NhlnBSnLZx0BdMXM8oApoYqS7UxqvrpSS70pQoQqGXjki2iO0fYSfNlosTt6mIBnD6PWESdLpQDb739Qzy1auXOc8T4JQFWznNVgS921-8xbfR7D3yNSPSaduYwXP8hML3o-vcCcPkYglhzywK7vDUpB1bsZoeoEW76RXFiyB-jEaUfJpuGq3Q',
    } ],
  }),
};

const mockFetch = jest.fn().mockResolvedValue(jwksResponse); ;

describe('Route for getting SAML token', () => {
  beforeEach(() => {
    next.mockClear();
    json.mockClear();
    status.mockClear();
    logger.error.mockClear();
    targetedCampaignService.getInstance().getCampaign.mockClear();
    targetedCampaignService.getInstance().setDisposition.mockClear();
    credentialsService.createSamlToken.mockClear();
    campaignCacheService.get.mockClear();
    get.mockClear();
    jwt.verify.mockClear();
    mockFetch.mockClear();
  });
  test('should be a function', () => {
    expect(getSamlToken).toBeDefined();
    expect(getSamlToken).toBeInstanceOf(Function);
  });
  test('should fail on invalid path parameter', async () => {
    const endpoint = getSamlToken({ logger, campaignCacheService, targetedCampaignService, credentialsService, fetch: mockFetch, jwksClient, jwt }, config);
    await endpoint({ params: { id: 'abc#' } }, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err.message).toEqual('Validation error');
    expect(err.metadata[0].path).toEqual('id');
  });
  test('should fail on invalid message_id parameter', async () => {
    const endpoint = getSamlToken({ logger, campaignCacheService, targetedCampaignService, credentialsService, fetch: mockFetch, jwksClient, jwt }, config);
    await endpoint({ params: { id: 'abc' }, query: { message_id: 'def#' } }, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err.message).toEqual('Validation error');
    expect(err.metadata[0].path).toEqual('message_id');
  });
  test('should create a token for Mortgage Renewal', async () => {
    const endpoint = getSamlToken({ logger, campaignCacheService, targetedCampaignService, credentialsService, fetch: mockFetch, jwksClient, jwt }, config);
    campaignCacheService.get.mockReturnValue({
      campaigns: {
        [mockCampaignDataMortgageRenewal.id]: mockCampaignDataMortgageRenewal,
      },
    });
    targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightDataMortgageRenewal);
    credentialsService.createSamlToken.mockResolvedValueOnce(mockCredentialData);
    get.mockReturnValueOnce('istgreen').mockReturnValueOnce(undefined);
    await endpoint({ get, params: { id: mockCampaignDataMortgageRenewal.id }, query: { message_id: 'def' } }, res, next);
    expect(next).not.toBeCalled();
    expect(campaignCacheService.get).toBeCalledTimes(1);
    expect(targetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
    expect(credentialsService.createSamlToken).toBeCalledTimes(1);
    expect(targetedCampaignService.getInstance().setDisposition).not.toBeCalled();
    expect(status).toBeCalledWith(200);
    expect(json).toBeCalledWith({ data: { saml_token: samlToken }, notifications: [] });
  });
  test('should create a token for PegaV2 Mortgage Renewal', async () => {
    const endpoint = getSamlToken({ logger, campaignCacheService, targetedCampaignService, credentialsService, fetch: mockFetch, jwksClient, jwt }, config);
    campaignCacheService.get.mockReturnValue({
      campaigns: {
        [mockCampaignDataMortgageRenewalPegaV2.id]: mockCampaignDataMortgageRenewalPegaV2,
      },
    });
    targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightDataMortgageRenewalPegaV2);
    credentialsService.createSamlToken.mockResolvedValueOnce(mockCredentialData);
    get.mockReturnValueOnce('istgreen').mockReturnValueOnce(undefined);
    await endpoint({ get, params: { id: mockCampaignDataMortgageRenewalPegaV2.id }, query: { message_id: 'def' } }, res, next);
    expect(next).not.toBeCalled();
    expect(campaignCacheService.get).toBeCalledTimes(1);
    expect(targetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
    expect(credentialsService.createSamlToken).toBeCalledTimes(1);
    expect(targetedCampaignService.getInstance().setDisposition).toBeCalledTimes(1);
    expect(status).toBeCalledWith(200);
    expect(json).toBeCalledWith({ data: { saml_token: samlToken }, notifications: [] });
  });
  test('should create a token for Step CLI', async () => {
    const endpoint = getSamlToken({ logger, campaignCacheService, targetedCampaignService, credentialsService, fetch: mockFetch, jwksClient, jwt }, config);
    campaignCacheService.get.mockReturnValue({
      campaigns: {
        [mockCampaignDataStepCli.id]: mockCampaignDataStepCli,
      },
    });
    targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightDataStepCli);
    credentialsService.createSamlToken.mockResolvedValueOnce(mockCredentialData);
    get.mockReturnValueOnce('istgreen').mockReturnValueOnce(undefined);
    await endpoint({ get, params: { id: mockCampaignDataStepCli.id }, query: { message_id: 'def' } }, res, next);
    expect(next).not.toBeCalled();
    expect(campaignCacheService.get).toBeCalledTimes(1);
    expect(targetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
    expect(credentialsService.createSamlToken).toBeCalledTimes(1);
    expect(targetedCampaignService.getInstance().setDisposition).not.toBeCalled();
    expect(status).toBeCalledWith(200);
    expect(json).toBeCalledWith({ data: { saml_token: samlToken }, notifications: [] });
  });
  test('should create a token for Step Acquisition', async () => {
    const endpoint = getSamlToken({ logger, campaignCacheService, targetedCampaignService, credentialsService, fetch: mockFetch, jwksClient, jwt }, config);
    campaignCacheService.get.mockReturnValue({
      campaigns: {
        [mockCampaignDataStepAcquisition.id]: mockCampaignDataStepAcquisition,
      },
    });
    targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightDataStepAcquisition);
    credentialsService.createSamlToken.mockResolvedValueOnce(mockCredentialData);
    get.mockReturnValueOnce('istgreen').mockReturnValueOnce(undefined);
    await endpoint({ get, params: { id: mockCampaignDataStepAcquisition.id }, query: { message_id: 'def' } }, res, next);
    expect(next).not.toBeCalled();
    expect(campaignCacheService.get).toBeCalledTimes(1);
    expect(targetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
    expect(credentialsService.createSamlToken).toBeCalledTimes(1);
    expect(targetedCampaignService.getInstance().setDisposition).not.toBeCalled();
    expect(status).toBeCalledWith(200);
    expect(json).toBeCalledWith({ data: { saml_token: samlToken }, notifications: [] });
  });
  test('should create a token for CEBA', async () => {
    const opaqueToken = 'eyJraWQiOiJmS0Y0alIxY0JIVmJWVVNrLTBCZGxOZnp6blBwT3k3d1FWNm1nX0VVSUNFIiwidHlwIjoiSldUIiwiYWxnIjoiUlMyNTYifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jqbilDgZ1Ru_3ycCy1F7Q2vxDvlM129D4MnRU0kM7EwF_vOPZCd0dY-0RSs5VFkVSFHk3AoDF2j7rd9BU_qIYwENono2U27vugkrIfsy6Lo849HVtqvLt6cbPOLH9SM4rZXib9-CVm7XqOlpUvdhfd0LcmU0UmSbYO7A_UWyTb1LnVhmUcD5wZrTfOelzTvT4NKWzcXJ0qK94Fye0fYkJYVdV3MEIW5O7YESjvz9mtrd8dOeCKUZ5tVqMzZP6JgN9gl4GkFtectdCGrmsP61GsGozUP8UjbpYvmwHIFaVMq15A5N4HzLT8yzEeSF91QK7n4XnpXhZR2_L3mxWUuPQw';
    const _get = jest.fn((header) => {
      if (header === 'x-customer-authorization') {
        return opaqueToken;
      } else if (header === 'Preferred-Environment') {
        return 'istgreen';
      } else {
        return undefined;
      }
    });
    jwt.verify.mockReturnValueOnce({
      profile: {
        customerInfo: {
          custSysInfo: [ {
            sysId: 'CI',
            infoId: [ {
              type: 'BID',
            } ],
          } ],
        },
      },
    });
    const endpoint = getSamlToken({ logger, campaignCacheService, targetedCampaignService, credentialsService, fetch: mockFetch, jwksClient, jwt }, config);
    campaignCacheService.get.mockReturnValue({
      campaigns: {
        [mockCampaignDataCeba.id]: mockCampaignDataCeba,
      },
    });
    targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightDataCeba);
    credentialsService.createSamlToken.mockResolvedValueOnce(mockCredentialData);
    get.mockReturnValueOnce('istgreen').mockReturnValueOnce(undefined);
    await endpoint({ get: _get, params: { id: mockCampaignDataCeba.id }, query: { message_id: 'def' } }, res, next);
    expect(next).not.toBeCalled();
    expect(campaignCacheService.get).toBeCalledTimes(1);
    expect(targetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
    expect(credentialsService.createSamlToken).toBeCalledTimes(1);
    expect(targetedCampaignService.getInstance().setDisposition).not.toBeCalled();
    expect(status).toBeCalledWith(200);
    expect(json).toBeCalledWith({ data: { saml_token: samlToken }, notifications: [] });
  });
  test('should return 400 if SAML token creation for campaign is not supported', async () => {
    const endpoint = getSamlToken({ logger, campaignCacheService, targetedCampaignService, credentialsService, fetch: mockFetch, jwksClient, jwt }, config);
    campaignCacheService.get.mockReturnValue({
      campaigns: {
        [mockCampaignDataStepCli.id]: mockCampaignDataStepCli,
      },
    });
    targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce({ data: { campaign_id: 'ABC99' } });
    credentialsService.createSamlToken.mockResolvedValueOnce(mockCredentialData);
    get.mockReturnValueOnce('istgreen').mockReturnValueOnce(undefined);
    await endpoint({ get, params: { id: mockCampaignDataStepCli.id }, query: { message_id: 'def' } }, res, next);
    expect(next).toBeCalled();
    expect(next.mock.calls[0][0].message).toEqual('Invalid campaign type');
  });
  test('should return 404 if insight campaign does not match rule', async () => {
    const endpoint = getSamlToken({ logger, campaignCacheService, targetedCampaignService, credentialsService, fetch: mockFetch, jwksClient, jwt }, config);
    campaignCacheService.get.mockReturnValue({
      campaigns: {
        [mockCampaignDataStepCli.id]: mockCampaignDataStepCli,
      },
    });
    targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightDataStepAcquisition);
    credentialsService.createSamlToken.mockResolvedValueOnce(mockCredentialData);
    get.mockReturnValueOnce('istgreen').mockReturnValueOnce(undefined);
    await endpoint({ get, params: { id: mockCampaignDataStepCli.id }, query: { message_id: 'def' } }, res, next);
    expect(next).toBeCalled();
    expect(next.mock.calls[0][0].message).toEqual('Not found');
  });
  test('should set disposition P for ScotiaHome campaign', async () => {
    const endpoint = getSamlToken({ logger, campaignCacheService, targetedCampaignService, credentialsService, fetch: mockFetch, jwksClient, jwt }, config);
    campaignCacheService.get.mockReturnValueOnce({
      campaigns: {
        [mockCampaignDataMortgageRenewalPegaV2.id]: mockCampaignDataMortgageRenewalPegaV2,
      },
    });
    targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightDataMortgageRenewalPegaV2);
    credentialsService.createSamlToken.mockResolvedValueOnce(mockCredentialData);
    get.mockReturnValueOnce('istgreen').mockReturnValueOnce(undefined);
    await endpoint({ get,
      headers: {
        'Preferred-Environment': 'istgreen',
        'x-channel-id': xChannelId,
        'User-Agent': userAgent,
      },
      params: { id: mockCampaignDataMortgageRenewalPegaV2.id },
      query: { message_id: 'ET04024' } }, res, next);
    expect(next).not.toBeCalled();
    expect(campaignCacheService.get).toBeCalledTimes(1);
    expect(targetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
    expect(targetedCampaignService.getInstance().setDisposition).toBeCalledTimes(1);
    expect(credentialsService.createSamlToken).toBeCalledTimes(1);
    expect(status).toBeCalledWith(200);
    expect(json).toBeCalledWith({ data: { saml_token: samlToken }, notifications: [] });
  });
  test('should log error is set disposition fails for ScotiaHome campaign', async () => {
    const endpoint = getSamlToken({ logger, campaignCacheService, targetedCampaignService, credentialsService, fetch: mockFetch, jwksClient, jwt }, config);
    campaignCacheService.get.mockReturnValueOnce({
      campaigns: {
        [mockCampaignDataMortgageRenewalPegaV2.id]: mockCampaignDataMortgageRenewalPegaV2,
      },
    });
    targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightDataMortgageRenewalPegaV2);
    targetedCampaignService.getInstance().setDisposition.mockRejectedValueOnce(new Error('mock error'));
    credentialsService.createSamlToken.mockResolvedValueOnce(mockCredentialData);
    get.mockReturnValueOnce('istgreen').mockReturnValueOnce(undefined);
    await endpoint({ get, params: { id: mockCampaignDataMortgageRenewalPegaV2.id }, query: { message_id: 'ET04024' } }, res, next);
    expect(next).not.toBeCalled();
    expect(campaignCacheService.get).toBeCalledTimes(1);
    expect(targetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
    expect(targetedCampaignService.getInstance().setDisposition).toBeCalledTimes(1);
    expect(credentialsService.createSamlToken).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(1);
    expect(status).toBeCalledWith(200);
    expect(json).toBeCalledWith({ data: { saml_token: samlToken }, notifications: [] });
  });
  test('should create a token for Small Business PACC', async () => {
    const opaqueToken = 'eyJraWQiOiJmS0Y0alIxY0JIVmJWVVNrLTBCZGxOZnp6blBwT3k3d1FWNm1nX0VVSUNFIiwidHlwIjoiSldUIiwiYWxnIjoiUlMyNTYifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jqbilDgZ1Ru_3ycCy1F7Q2vxDvlM129D4MnRU0kM7EwF_vOPZCd0dY-0RSs5VFkVSFHk3AoDF2j7rd9BU_qIYwENono2U27vugkrIfsy6Lo849HVtqvLt6cbPOLH9SM4rZXib9-CVm7XqOlpUvdhfd0LcmU0UmSbYO7A_UWyTb1LnVhmUcD5wZrTfOelzTvT4NKWzcXJ0qK94Fye0fYkJYVdV3MEIW5O7YESjvz9mtrd8dOeCKUZ5tVqMzZP6JgN9gl4GkFtectdCGrmsP61GsGozUP8UjbpYvmwHIFaVMq15A5N4HzLT8yzEeSF91QK7n4XnpXhZR2_L3mxWUuPQw';
    const _get = jest.fn((header) => {
      if (header === 'x-customer-authorization') {
        return opaqueToken;
      } else if (header === 'Preferred-Environment') {
        return 'istred';
      } else {
        return undefined;
      }
    });
    jwt.verify.mockReturnValueOnce({
      profile: {
        customerInfo: {
          custSysInfo: [ {
            sysId: 'CI',
            infoId: [ {
              type: 'BID',
            } ],
          } ],
        },
      },
    });
    const endpoint = getSamlToken({ logger, campaignCacheService, targetedCampaignService, credentialsService, fetch: mockFetch, jwksClient, jwt }, config);
    campaignCacheService.get.mockReturnValue({
      campaigns: {
        [mockCampaignDataSBPacc.id]: mockCampaignDataSBPacc,
      },
    });
    targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightDataSBPacc);
    credentialsService.createSamlToken.mockResolvedValueOnce(mockCredentialData);
    get.mockReturnValueOnce('istred').mockReturnValueOnce(undefined);
    await endpoint({ get: _get, params: { id: mockCampaignDataSBPacc.id }, query: { message_id: 'def' } }, res, next);
    expect(next).not.toBeCalled();
    expect(campaignCacheService.get).toBeCalledTimes(1);
    expect(targetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
    expect(credentialsService.createSamlToken).toBeCalledTimes(1);
    expect(targetedCampaignService.getInstance().setDisposition).not.toBeCalled();
    expect(status).toBeCalledWith(200);
    expect(json).toBeCalledWith({ data: { saml_token: samlToken }, notifications: [] });
  });

  test('should throw error when token could not be fetched', async () => {
    mockFetch.mockResolvedValueOnce({ ok: false });
    const endpoint = getSamlToken({ logger, campaignCacheService, targetedCampaignService, credentialsService, fetch: mockFetch, jwksClient, jwt }, config);
    campaignCacheService.get.mockReturnValue({
      campaigns: {
        [mockCampaignDataSBPacc.id]: mockCampaignDataSBPacc,
      },
    });
    targetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightDataSBPacc);
    credentialsService.createSamlToken.mockResolvedValueOnce(mockCredentialData);
    get.mockReturnValueOnce('istred').mockReturnValueOnce(undefined);
    await endpoint({ get, params: { id: mockCampaignDataSBPacc.id }, query: { message_id: 'def' } }, res, next);
    expect(next).toHaveBeenCalledTimes(1);
    // expect(next).not.toBeCalled();
    expect(campaignCacheService.get).toBeCalledTimes(1);
    expect(targetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
    // expect(credentialsService.createSamlToken).toBeCalledTimes(1);
    // expect(targetedCampaignService.getInstance().setDisposition).not.toBeCalled();
    // expect(status).toBeCalledWith(200);
    // expect(json).toBeCalledWith({ data: { saml_token: samlToken }, notifications: [] });
    expect(next.mock.calls[0][0].message).toEqual('Error - Could not fetch jwks');
  });
});
