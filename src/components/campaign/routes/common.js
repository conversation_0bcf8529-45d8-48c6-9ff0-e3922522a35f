const R = require('ramda');

const scopes = {
  // customer token's scope
  standard: 'standard',
  // current scopes
  campaignsRead: 'cdb.pigeon.campaigns.read',
  campaignsDispositionsWrite: 'cdb.pigeon.campaigns.dispositions.write',
  campaignsCacheDelete: 'cdb.pigeon.campaigns.cache.delete',
  // legacy scopes
  legacy: {
    campaignsRead: 'ca:baas:campaigns:read',
    campaignsDispositionsWrite: 'ca:baas:dispositions:write',
  },
};

const applications = {
  nova: 'nova',
  abm: 'abm',
};

const addExternalRef = (rule, insights) => {
  const insight = insights.find((item) => item.campaign_id === rule.external_ref);
  const externalRef = {};
  if (insight) {
    externalRef.message_id = insight.message_id;
    externalRef.campaign_id = insight.campaign_id;
    externalRef.message_source = insight.message_source;
  }
  return Object.assign({}, rule, { external_ref: externalRef });
};

const addContentPreview = (rule, content) => {
  return {
    id: rule.id,
    container: rule.container,
    external_ref: rule.external_ref,
    type: content.type,
    content: content.content.preview,
  };
};

const addContentDetails = (rule, content) => {
  return {
    id: rule.id,
    container: rule.container,
    external_ref: rule.external_ref,
    type: content.type,
    content: content.content.details,
  };
};

const getDispositionValue = (o) => R.trim(R.or(R.propOr('', 'message_status', o), R.propOr('', 'message_response', o)));

const isCampaignViewed = R.pipe(getDispositionValue, R.cond([
  [ R.equals('N'), R.always(false) ],
  [ R.T, R.always(true) ],
]));

const priorityMessageCampaignId = 'MESSAGE';
const wealthCampaignId = 'WEALTH';
const massCampaignId = 'MASS';

const isPriorityMessageCampaign = R.propEq(priorityMessageCampaignId, 'external_ref');
const isWealthCampaign = R.propEq(wealthCampaignId, 'external_ref');
const isMassCampaign = R.propEq(massCampaignId, 'external_ref');
const isTargetedCampaign = (c) => R.not(R.or(isPriorityMessageCampaign(c), isMassCampaign(c)));

const splunkErrorCodes = {
  1001: 'Exchange of opaque token fails',
  1002: 'Getting customer info fails',
  1003: 'Getting the list of targeted campaigns fails',
  1004: 'Getting the list of active rules fails',
  1005: 'Process and filtering of rules fails',
  1006: 'getting content for assigned rules fails',
  1007: 'Getting GIC renewal rate fails',
  1008: 'Replacing variables in content fails',
  1009: 'Getting campaign details fails',
  1010: 'Getting rule detail fails',
  1011: 'Getting content for campaign detail fails',
  1012: 'Getting GIC renewal fails',
  1013: 'Variable replacement fails',
  1014: 'Getting mapped and paginated results',
};

/**
 * Log the error of calling LD service.
 * @param {Object} opts - options parameter.
 * @param {Array} opts.err - error object.
 * @param {Array} opts.flag - the target flag.
 * @param {string} opts.caller - where the call was made from.
 * @param {Array} opts.logger - the logger object to log the error .
 */
const handleLDError = ({ err, flag, caller, logger }) => {
  logger.error({
    message: 'Error calling Launch Darkly',
    details: { err, flag, caller },
  });
};

const ldFlagDb = 'pigeon-api.features.dispositions.db';
const ldFlagAcct = 'pigeon-api.downstreams.marvel-account-cache';
const ldFlagRewards = 'pigeon-api.downstreams.rewards';

module.exports = {
  scopes,
  applications,
  addExternalRef,
  addContentPreview,
  addContentDetails,
  getDispositionValue,
  isCampaignViewed,
  isPriorityMessageCampaign,
  isMassCampaign,
  isTargetedCampaign,
  isWealthCampaign,
  priorityMessageCampaignId,
  wealthCampaignId,
  massCampaignId,
  splunkErrorCodes,
  handleLDError,
  ldFlagDb,
  ldFlagAcct,
  ldFlagRewards,
};
