/* eslint-disable sonarjs/no-duplicate-string */
const getCampaigns = require('./get-campaigns');
const { HttpError } = require('../../errors');
const momentTz = require('moment-timezone');
const {
  mockPriorityMessageRule,
  mockTargetedCampaignRule,
  mockContentResponse,
  mockInsightsResponse,
  mockAccountsResponse,
  mockMassCampaignRule,
} = require('../mockData');
const { generateMessageIdsPega } = require('../../helpers');

// Mock constants
const defaultToken = 'sample-token';
const defaultTitle = 'some title';
const defaultMessage = 'some message';
const defaultCardNumber = '****************';
const config = {
  tokenPath: 'auth.token',
  tokenClaimsPath: 'auth.claims',
  features: { application: true, disposition: true },
  anonymousClientIds: [ 'testAnonymousClientId' ],
};

const defaultResponse = { items: [], limit: 100, total: 0, offset: 0 };

// Mock services
const mockCampaignCacheService = {
  get: jest.fn(),
};
const mockContainerCacheService = {
  get: jest.fn(),
};
const mockTargetedCampaignService = { getInstance: jest.fn() };
mockTargetedCampaignService.getInstance.mockReturnValue({
  getCampaigns: jest.fn(),
});
const mockDispositionsService = {
  getDispositions: jest.fn(),
  setDisposition: jest.fn(),
};
const mockContentService = {
  getContentsByIds: jest.fn(),
};
const mockMarvelService = {
  getAccounts: jest.fn(),
  getRewardPoints: jest.fn(),
  getCardProfile: jest.fn(),
};
const mockVariablesService = jest.fn().mockImplementation((env, token, content, campaign, rule, language) =>
  Promise.resolve({ content, transformed: {}, dataContext: 'test' }));
const mockLaunchDarklyService = {
  init: jest.fn().mockResolvedValue(true),
  isFeatureEnabled: jest.fn().mockResolvedValue(true),
  close: jest.fn(),
};
const mockStatus = jest.fn();
const mockJson = jest.fn();
const mockNext = jest.fn();
const mockLogger = {
  warn: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
  debug: (...opts) => {
    console.log(JSON.stringify(...opts));
  },
};
const mockRequest = () => ({
  get: jest.fn().mockImplementation((arg) => {
    if (arg === 'x-customer-scotiacard') return defaultCardNumber;
  }),
  query: { application: 'nova', page: 'accounts', container: 'offers-and-programs', platform: 'ios' },
});
const mockResponse = () => {
  const res = {
    locals: {
      language: 'en',
      languageContentful: 'en',
    },
  };
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};

describe('Campaign Routes getCampaigns', () => {
  beforeEach(() => {
    mockTargetedCampaignService.getInstance.mockClear();
    mockTargetedCampaignService.getInstance().getCampaigns.mockClear();
    mockMarvelService.getAccounts.mockClear();
    mockMarvelService.getRewardPoints.mockClear();
    mockMarvelService.getCardProfile.mockClear();
    mockDispositionsService.getDispositions.mockClear();
    mockContentService.getContentsByIds.mockClear();
    mockVariablesService.mockClear();
    mockLaunchDarklyService.init.mockClear();
    mockLaunchDarklyService.isFeatureEnabled.mockClear();
    mockLaunchDarklyService.close.mockClear();
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
    mockLogger.error.mockClear();
    mockCampaignCacheService.get.mockClear();
    mockContainerCacheService.get.mockClear();
  });

  describe('Request validation', () => {
    test('should return bad request on invalid query param', async () => {
      const mockReq = { query: { page: '#' } };
      const mockRes = mockResponse();

      const endpoint = getCampaigns({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        containerCacheService: mockContainerCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockRes.status).not.toBeCalled();
      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(HttpError);
      expect(mockNext.mock.calls[0][0].statusCode).toEqual(400);
      expect(mockNext.mock.calls[0][0].message).toEqual('Validation error');
      expect(mockNext.mock.calls[0][0].metadata[0].message)
        .toEqual('&quot;page&quot; with value &quot;#&quot; fails to match the required pattern: /^[a-zA-Z0-9_\\-.,]+$/');
    });

    test('should fail on validation error for select_contents value', async () => {
      const mockReq = { query: { platform: 'ios', select_contents: '#' } };
      const mockRes = mockResponse();

      const endpoint = getCampaigns({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        containerCacheService: mockContainerCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockStatus).not.toBeCalled();
      expect(mockNext).toBeCalled();
      const err = mockNext.mock.calls[0][0];
      expect(err).toBeInstanceOf(HttpError);
      expect(err.statusCode).toEqual(400);
      expect(err.metadata[0].message)
        .toEqual('&quot;select_contents&quot; with value &quot;#&quot; fails to match the required pattern: /^[,a-zA-Z0-9_-]+$/');
    });

    test('should return bad request if country header is CA but rule type is ccau', async () => {
      const mockReq = {
        ...mockRequest(),
        get: jest.fn().mockImplementation((arg) => {
          if (arg === 'x-country-code') {
            return 'CA';
          }
        }),
      };
      const mockRes = mockResponse();

      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {
          [mockPriorityMessageRule.id]: {
            ...mockPriorityMessageRule,
            type: 'ccau_campaign',
          },
        },
      });

      const endpoint = getCampaigns({
        logger: mockLogger,
        contentService: mockContentService,
        containerCacheService: mockContainerCacheService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        campaignCacheService: mockCampaignCacheService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockRes.status).not.toBeCalled();
      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(HttpError);
      expect(mockNext.mock.calls[0][0].statusCode).toEqual(400);
      expect(mockNext.mock.calls[0][0].message).toEqual('Given country has insufficient access to rule types available');
    });

    test('should return bad request if country header is CCAU but rule type is campaign', async () => {
      const mockReq = {
        ...mockRequest(),
        get: jest.fn().mockImplementation((arg) => {
          if (arg === 'x-country-code') {
            return 'DO';
          }
        }),
      };
      const mockResponseCopy = mockResponse();
      const mockRes = {
        ...mockResponseCopy,
        locals: {
          ...mockResponseCopy.locals,
          country: 'DO',
          languageContentful: 'en-DO',
        },
      };

      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {
          [mockPriorityMessageRule.id]: mockPriorityMessageRule, // campaign rule type (not ccau_campaign)
        },
      });

      const endpoint = getCampaigns({
        logger: mockLogger,
        contentService: mockContentService,
        containerCacheService: mockContainerCacheService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        campaignCacheService: mockCampaignCacheService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockRes.status).not.toBeCalled();
      expect(mockNext.mock.calls[0][0]).toBeInstanceOf(HttpError);
      expect(mockNext.mock.calls[0][0].statusCode).toEqual(400);
      expect(mockNext.mock.calls[0][0].message).toEqual('Given country has insufficient access to rule types available');
    });

    test('should return bad request if multiple rule types remain after filtering by application', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();

      const mockCampaignRules = [ {
        id: 'UB3kxJ1nfHRE',
        external_ref: '5576112101',
        content_space: 'alternateSpace',
        content_type: 'creditCardOffer',
        content_id: 'aaaa1111',
        platforms: [ 'ios', 'android' ],
        platforms_targeting: [
          { v: 1, platform: 'ios', items: [] },
          { v: 1, platform: 'android', items: [] },
        ],
        container: mockReq.query.container,
        pages: [ mockReq.query.page ],
        application: 'nova',
        type: 'campaign',
      },
      {
        id: 'UB3kxJ1nfHRF',
        external_ref: '5576112101',
        content_space: 'alternateSpace',
        content_type: 'creditCardOffer',
        content_id: 'aaaa1111',
        platforms: [ 'ios', 'android' ],
        platforms_targeting: [
          { v: 1, platform: 'ios', items: [] },
          { v: 1, platform: 'android', items: [] },
        ],
        container: mockReq.query.container,
        pages: [ mockReq.query.page ],
        application: 'nova',
        type: 'ccau_campaign',
      } ];
      const campaigns = mockCampaignRules.reduce((acc, curVal, curIndex) => {
        acc[curVal.id] = curVal;
        return acc;
      }, {});
      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns,
      });

      const endpoint = getCampaigns({
        logger: mockLogger,
        contentService: mockContentService,
        containerCacheService: mockContainerCacheService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        campaignCacheService: mockCampaignCacheService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockRes.status).toBeCalledWith(200);
      expect(mockRes.json).toBeCalledWith({
        data: defaultResponse,
        notifications: [ {
          message: 'Application (placement) is misconfigured. More than one campaign rule type is not supported.',
          metadata: [ { application: 'nova' } ],
        } ],
      });
    });
  });

  describe('Content service', () => {
    test('should skip rules without content space set and report an error on missing content', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();

      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns: {
          [mockPriorityMessageRule.id]: {
            ...mockPriorityMessageRule,
            content_space: undefined,
          },
        },
      });
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);

      const endpoint = getCampaigns({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        containerCacheService: mockContainerCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockLogger.error).not.toHaveBeenCalled();
      expect(mockRes.status).toBeCalledWith(200);
      const response = mockRes.json.mock.calls[0][0];
      expect(response.data.total).toEqual(0);
      expect(response.notifications[0].message).toEqual('failed to get content');
    });

    test('should skip rules when failed to get content', async () => {
      const mockReq = {
        get: jest.fn().mockReturnValueOnce('istgreen'),
        query: { container: 'abc123', page: 'test', platform: 'ios', limit: '3' },
      };
      const mockRes = { locals: { auth: { token: defaultToken, claims: { sub: '****************' } }, language: 'fr', languageContentful: 'fr' }, status: mockStatus };

      // mock campaign service
      mockTargetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce({
        data: [
          {
            message_id: 'ABCDEF123456',
            campaign_id: 'PAD01',
            message_source: 'KT',
            start_date: '2018-01-02',
          },
        ],
      });
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);

      // mock rules
      const mockCampaignRule = {
        id: 'aaaa0001',
        external_ref: 'PAD01',
        content_space: 'samplespace',
        content_type: 'creditCardOffer',
        content_id: 'aaaa1111',
        platforms_targeting: [
          { v: 1, platform: 'ios', items: [] },
        ],
        container: mockReq.query.container,
        pages: [ mockReq.query.page ],
      };
      // mock content response
      const content = JSON.parse(JSON.stringify(mockContentResponse));

      const rules = {};
      [ ...Array(content.data.items.length).keys() ].forEach(function(idx) {
        rules[`r${idx}`] = { ...mockCampaignRule, id: `r${idx}`, content_id: `c${idx}` };
      });

      content.data.items = [];
      content.data.total = 4; // decrement by one to account for content fetch failure on one of the items
      Object.keys(rules).forEach(ruleId => {
        const id = rules[ruleId].content_id;
        content.data.items.push({ ...mockContentResponse.data.items[0], id });
      });

      // mock content fetch failure from contentful for content item 1
      content.notifications.push(`Failed to fetch content for ${content.data.items[0].id}`);
      delete content.data.items[0];

      mockCampaignCacheService.get.mockReturnValue({ campaigns: rules });
      mockContentService.getContentsByIds.mockResolvedValue(content);
      mockStatus.mockReturnValueOnce({ json: mockJson });

      const endpoint = getCampaigns({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        containerCacheService: mockContainerCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockContentService.getContentsByIds).toBeCalled();
      const callContent = mockContentService.getContentsByIds.mock.calls[0];
      expect(callContent[0]).toEqual(mockCampaignRule.content_space);
      expect(callContent[1]).toEqual(Object.keys(rules).map(ruleId => rules[ruleId].content_id));
      expect(callContent[2]).toHaveProperty('language');
      expect(callContent[2].language).toEqual('fr');
      expect(mockStatus).toBeCalled();
      expect(mockJson).toBeCalled();
      const response = mockJson.mock.calls[0][0];
      expect(response).toHaveProperty('data');
      expect(response.data).toHaveProperty('items');
      expect(response.data.total).toBe(3);
      expect(response.data.items.length).toEqual(3);
      expect(response).toHaveProperty('notifications');
      expect(response.notifications.length).toEqual(1);
    });

    test('should pass `fr` language as an option to content api', async () => {
      const mockReq = mockRequest();
      const mockRes = { ...mockResponse(), locals: { language: 'fr', languageContentful: 'fr' } };

      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {
          [mockPriorityMessageRule.id]: mockPriorityMessageRule,
        },
      });
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      mockContentService.getContentsByIds.mockResolvedValue(mockContentResponse);

      const endpoint = getCampaigns({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        containerCacheService: mockContainerCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      const contentOpts = mockContentService.getContentsByIds.mock.calls[0][2];
      expect(contentOpts.language).toEqual('fr');
    });

    test('should pass `interceptDetails` as an option to content api', async () => {
      const mockRequestCopy = mockRequest();
      const mockReq = {
        ...mockRequestCopy,
        query: {
          ...mockRequestCopy.query,
          select_contents: 'interceptDetails',
        },
      };
      const mockRes = mockResponse();

      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns: {
          [mockTargetedCampaignRule.id]: mockTargetedCampaignRule,
        },
      });
      mockTargetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce(mockInsightsResponse);
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      mockContentService.getContentsByIds.mockResolvedValueOnce(mockContentResponse);
      mockVariablesService.mockResolvedValueOnce({ content: mockContentResponse.data, dataContext: 'test', transformed: {} });

      const endpoint = getCampaigns({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        containerCacheService: mockContainerCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      const contentOpts = mockContentService.getContentsByIds.mock.calls[0][2];
      expect(contentOpts.select).toEqual('interceptDetails');
    });
  });

  describe('Targeted campaigns', () => {
    test('should not call insights if no targeted rules are returned from cache', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();

      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {
          [mockPriorityMessageRule.id]: mockPriorityMessageRule,
        },
      });
      const setCampaignsMock = jest.fn();
      mockTargetedCampaignService.getInstance.mockReturnValue({
        getCampaigns: setCampaignsMock,
      });
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);

      const endpoint = getCampaigns({
        logger: mockLogger,
        contentService: mockContentService,
        containerCacheService: mockContainerCacheService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        campaignCacheService: mockCampaignCacheService,
        marvelService: mockMarvelService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(setCampaignsMock).not.toBeCalled();
      expect(mockRes.status).toBeCalledWith(200);
      const response = mockRes.json.mock.calls[0][0];
      expect(response.data.total).toEqual(1);
      expect(response.notifications).toEqual([]);
    });

    test('should return Insights data when `insight` query param is true', async () => {
      const mockReq = {
        get: jest.fn().mockImplementation((arg) => {
          if (arg === 'x-customer-scotiacard') return defaultCardNumber;
        }),
        query: { application: 'nova', page: 'accounts', container: 'offers-and-programs', platform: 'ios', insight: true },
      };
      const mockRes = mockResponse();

      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns: {
          [mockTargetedCampaignRule.id]: mockTargetedCampaignRule,
        },
      });
      mockTargetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce(mockInsightsResponse);
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      mockContentService.getContentsByIds.mockResolvedValueOnce(mockContentResponse);
      mockVariablesService.mockResolvedValueOnce({ content: mockContentResponse.data, dataContext: 'test', transformed: {} });

      const endpoint = getCampaigns({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        containerCacheService: mockContainerCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockRes.status).toBeCalledWith(200);
      const response = mockRes.json.mock.calls[0][0];
      expect(response.data.total).toEqual(1);
      expect(response.notifications).toEqual([]);
      const campaign = response.data.items[0];
      expect(campaign).toHaveProperty('external_ref');
      expect(campaign.external_ref.data).toBeDefined();
      expect(campaign.external_ref).toEqual(expect.objectContaining({
        data_context: 'test',
        data_transformed: {},
      }));
    });

    test('should not return Insights data when `insight` query param is false', async () => {
      const mockReq = {
        get: jest.fn().mockImplementation((arg) => {
          if (arg === 'x-customer-scotiacard') return defaultCardNumber;
        }),
        query: { application: 'nova', page: 'accounts', container: 'offers-and-programs', platform: 'ios', insight: false },
      };
      const mockRes = mockResponse();

      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns: {
          [mockTargetedCampaignRule.id]: mockTargetedCampaignRule,
        },
      });
      mockTargetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce(mockInsightsResponse);
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      mockContentService.getContentsByIds.mockResolvedValue(mockContentResponse);
      mockVariablesService.mockResolvedValueOnce({ content: mockContentResponse.data, dataContext: 'test', transformed: {} });

      const endpoint = getCampaigns({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        containerCacheService: mockContainerCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockRes.status).toBeCalledWith(200);
      const response = mockRes.json.mock.calls[0][0];
      expect(response.data.total).toEqual(1);
      expect(response.notifications).toEqual([]);
      const campaign = response.data.items[0];
      expect(campaign).toHaveProperty('external_ref');
      expect(campaign.external_ref.data).toBeUndefined();
      expect(campaign.external_ref.data_context).toBeUndefined();
      expect(campaign.external_ref.data_transformed).toBeUndefined();
    });

    test('should not return Insights data when `insight` query param is undefined', async () => {
      const mockReq = {
        get: jest.fn().mockImplementation((arg) => {
          if (arg === 'x-customer-scotiacard') return defaultCardNumber;
        }),
        query: { application: 'nova', page: 'accounts', container: 'offers-and-programs', platform: 'ios', insight: undefined },
      };
      const mockRes = mockResponse();

      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns: {
          [mockTargetedCampaignRule.id]: mockTargetedCampaignRule,
        },
      });
      mockTargetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce(mockInsightsResponse);
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      mockContentService.getContentsByIds.mockResolvedValue(mockContentResponse);
      mockVariablesService.mockResolvedValueOnce({ content: mockContentResponse.data, dataContext: 'test', transformed: {} });

      const endpoint = getCampaigns({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        containerCacheService: mockContainerCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockRes.status).toBeCalledWith(200);
      const response = mockRes.json.mock.calls[0][0];
      expect(response.data.total).toEqual(1);
      expect(response.notifications).toEqual([]);
      const campaign = response.data.items[0];
      expect(campaign).toHaveProperty('external_ref');
      expect(campaign.external_ref.data).toBeUndefined();
      expect(campaign.external_ref.data_context).toBeUndefined();
      expect(campaign.external_ref.data_transformed).toBeUndefined();
    });

    test('Insights error - should treat `KTMGD-ACCT-NOT-FOUND` as a valid `no campaigns` response from Insights', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();

      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns: {
          [mockTargetedCampaignRule.id]: mockTargetedCampaignRule,
        },
      });
      mockTargetedCampaignService.getInstance().getCampaigns.mockRejectedValueOnce({
        message: 'Datapower service error occurred: The backend system to DataPower responded with an application level error. Backend: IMS_IMU1-2. Transaction error: [RLI-ERR: 12] [KTMGD-ACCT-NOT-FOUND]',
      });
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);

      const endpoint = getCampaigns({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        containerCacheService: mockContainerCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockTargetedCampaignService.getInstance().getCampaigns).toHaveBeenCalledTimes(1); ;
      expect(mockLogger.error).not.toBeCalled();
      expect(mockRes.status).toBeCalledWith(200);
      expect(mockRes.json).toBeCalledWith({
        data: defaultResponse,
        notifications: [], // TODO: should notification be returned?
      });
    });

    test('Insights error - should log other Insights errors', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();

      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns: {
          [mockTargetedCampaignRule.id]: mockTargetedCampaignRule,
        },
      });
      mockTargetedCampaignService.getInstance().getCampaigns.mockRejectedValueOnce({
        message: 'Datapower service error occurred: The backend system to DataPower responded with an application level error. Backend: IMS_IMU1-2. Transaction error: [RLI-ERR: 12] [KTMGD-ACCT-SOMETHING]',
      });
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      mockContentService.getContentsByIds.mockResolvedValue(mockContentResponse);

      const endpoint = getCampaigns({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        containerCacheService: mockContainerCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockTargetedCampaignService.getInstance().getCampaigns).toHaveBeenCalledTimes(1);
      expect(mockLogger.error).toHaveBeenCalledWith(expect.objectContaining({
        message: 'unable to get insights campaigns',
        description: 'Getting campaign details fails',
      }));
      expect(mockRes.status).toBeCalledWith(200);
      expect(mockRes.json).toBeCalledWith({
        data: defaultResponse,
        notifications: [], // TODO: should notification be returned?
      });
    });

    test('Variables service error - should return notification on failed variable replacement', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();

      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns: {
          [mockTargetedCampaignRule.id]: mockTargetedCampaignRule,
        },
      });
      mockTargetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce(mockInsightsResponse);
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      mockContentService.getContentsByIds.mockResolvedValue(mockContentResponse);
      const mockVariableServiceError = new Error('failed');
      mockVariablesService.mockRejectedValueOnce(mockVariableServiceError);

      const endpoint = getCampaigns({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        containerCacheService: mockContainerCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockLogger.warn).toHaveBeenCalledWith(expect.objectContaining({
        err: mockVariableServiceError,
        message: 'failed to replace variables',
        description: 'Variable replacement fails',
      }));
      expect(mockRes.status).toBeCalledWith(200);
      const response = mockRes.json.mock.calls[0][0];
      expect(response.data.total).toEqual(1);
      expect(response.notifications[0].message).toEqual('failed to replace variables');
      const campaign = response.data.items[0];
      const insightsCampaign = mockInsightsResponse.data.find(c => c.campaign_id === mockTargetedCampaignRule.external_ref);
      expect(campaign).toEqual(expect.objectContaining({
        id: mockTargetedCampaignRule.id,
        content: mockContentResponse.data.items.find(i => i.id === mockTargetedCampaignRule.content_id).content,
        external_ref: {
          campaign_id: insightsCampaign.campaign_id,
          message_id: insightsCampaign.message_id,
          message_source: insightsCampaign.message_source,
        },
      }));
    });
  });

  describe('Mass campaigns', () => {
    test('scene points - should only return campaigns that satisfy scene+ rewards points targeting', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();

      const mockCampaignRules = [ {
        id: 'bbb0001',
        external_ref: 'MASS',
        content_space: '4szkx38resvm',
        content_type: 'nova__campaign',
        content_id: '3JNYCPNdDFVnSLRHkBlwgk',
        platforms_targeting: [
          { v: 1, platform: 'ios', items: [] },
          { v: 1, platform: 'android', items: [] },
        ],
        mass_targeting: {
          by_product: {
            any_of: [ { ownership: 'R' }, { ownership: 'B' } ],
          },
          by_scene_points: {
            targeting_criteria: 'greater',
            points: 1000,
          },
          enrollment_status: [],
        },
        container: mockReq.query.container,
        pages: [ mockReq.query.page ],
        application: 'nova',
        type: 'campaign',
      },
      {
        id: 'ccc0001',
        external_ref: 'MASS',
        content_space: '4szkx38resvm',
        content_type: 'nova__campaign',
        content_id: '3JNYCPNdDFVnSLRHkBlwgk',
        platforms_targeting: [
          { v: 1, platform: 'ios', items: [] },
          { v: 1, platform: 'android', items: [] },
        ],
        mass_targeting: {
          by_product: {
            any_of: [ { ownership: 'R' }, { ownership: 'B' } ],
          },
          by_scene_points: {
            targeting_criteria: 'greater',
            points: 3000,
          },
          enrollment_status: [],
        },
        container: mockReq.query.container,
        pages: [ mockReq.query.page ],
        application: 'nova',
        type: 'campaign',
      } ];
      const campaigns = mockCampaignRules.reduce((acc, curVal, curIndex) => {
        acc[curVal.id] = curVal;
        return acc;
      }, {});
      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns,
      });
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockMarvelService.getRewardPoints.mockResolvedValueOnce(2000);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      mockContentService.getContentsByIds.mockResolvedValueOnce(mockContentResponse);

      const endpoint = getCampaigns({
        logger: mockLogger,
        contentService: mockContentService,
        containerCacheService: mockContainerCacheService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        campaignCacheService: mockCampaignCacheService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockMarvelService.getAccounts).toHaveBeenCalledTimes(1);
      expect(mockMarvelService.getRewardPoints).toHaveBeenCalledTimes(1);
      expect(mockRes.status).toBeCalledWith(200);
      const response = mockRes.json.mock.calls[0][0];
      expect(response.notifications).toEqual([]);
      // customer only has sufficient scene points to be presented with 1 of the 2 rules
      expect(response.data.total).toEqual(1);
    });

    test('Launch Darkly - should not call Marvel Accounts service if flag is disabled', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();

      mockLaunchDarklyService.isFeatureEnabled.mockImplementation((arg1, arg2) => {
        if (arg1 === 'pigeon-api.downstreams.marvel-account-cache') return false;
        return true;
      });
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {
          [mockMassCampaignRule.id]: mockMassCampaignRule,
        },
      });

      const endpoint = getCampaigns({
        logger: mockLogger,
        contentService: mockContentService,
        containerCacheService: mockContainerCacheService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        campaignCacheService: mockCampaignCacheService,
        marvelService: mockMarvelService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockMarvelService.getAccounts).not.toBeCalled();
      expect(mockContentService.getContentsByIds).not.toBeCalled();
      expect(mockRes.status).toBeCalledWith(200);
      expect(mockRes.json).toBeCalledWith({
        data: defaultResponse,
        notifications: [],
      });
    });

    test('Launch Darkly - should not call Marvel Rewards service if flag is disabled', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();

      mockLaunchDarklyService.isFeatureEnabled.mockImplementation((arg1, arg2) => {
        if (arg1 === 'pigeon-api.downstreams.rewards') return false;
        return true;
      });
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {
          [mockMassCampaignRule.id]: mockMassCampaignRule,
        },
      });
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);

      const endpoint = getCampaigns({
        logger: mockLogger,
        contentService: mockContentService,
        containerCacheService: mockContainerCacheService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        campaignCacheService: mockCampaignCacheService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockMarvelService.getAccounts).toBeCalledTimes(1);
      expect(mockMarvelService.getRewardPoints).not.toBeCalled();
      expect(mockContentService.getContentsByIds).not.toBeCalled();
      expect(mockRes.status).toBeCalledWith(200);
      expect(mockRes.json).toBeCalledWith({
        data: defaultResponse,
        notifications: [],
      });
    });
  });

  describe('Dispositions', () => {
    test('Pigeon db dispositions - should not return dismissed mass message', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();

      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {
          [mockPriorityMessageRule.id]: mockPriorityMessageRule,
        },
      });
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([
        { rule_id: mockPriorityMessageRule.id, message_id: null, disposition: 'D' },
      ]);

      const endpoint = getCampaigns({
        logger: mockLogger,
        contentService: mockContentService,
        containerCacheService: mockContainerCacheService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        campaignCacheService: mockCampaignCacheService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockContentService.getContentsByIds).not.toHaveBeenCalled(); // dismissed rule filtered out before calling content service
      expect(mockRes.status).toBeCalledWith(200);
      expect(mockRes.json).toBeCalledWith({
        data: defaultResponse,
        notifications: [],
      });
    });

    test('Pigeon db dispositions - should return viewed mass message', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();

      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {
          [mockPriorityMessageRule.id]: mockPriorityMessageRule,
        },
      });
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([
        { rule_id: mockPriorityMessageRule.id, message_id: null, disposition: 'V' },
      ]);
      mockContentService.getContentsByIds.mockResolvedValueOnce(mockContentResponse);

      const endpoint = getCampaigns({
        logger: mockLogger,
        contentService: mockContentService,
        containerCacheService: mockContainerCacheService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        campaignCacheService: mockCampaignCacheService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockContentService.getContentsByIds).toHaveBeenCalled();
      expect(mockRes.status).toBeCalledWith(200);
      const response = mockRes.json.mock.calls[0][0];
      expect(response.data.total).toEqual(1);
      expect(response.notifications).toEqual([]);
    });

    test('Insights dispositions - should send viewed disposition to Insights if rule is viewed in Pigeon db', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();

      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns: {
          [mockTargetedCampaignRule.id]: mockTargetedCampaignRule,
        },
      });
      const setDispositionMock = jest.fn();
      mockTargetedCampaignService.getInstance.mockReturnValue({
        getCampaigns: jest.fn().mockResolvedValueOnce(mockInsightsResponse),
        setDisposition: setDispositionMock,
      });
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([ {
        rule_id: mockTargetedCampaignRule.id,
        message_id: mockInsightsResponse.data.find(i => i.campaign_id === mockTargetedCampaignRule.external_ref).message_id,
        disposition: 'V',
      } ]);
      mockContentService.getContentsByIds.mockResolvedValueOnce(mockContentResponse);

      const endpoint = getCampaigns({
        logger: mockLogger,
        contentService: mockContentService,
        containerCacheService: mockContainerCacheService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        campaignCacheService: mockCampaignCacheService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(setDispositionMock).toHaveBeenCalledWith(expect.objectContaining({
        id: mockInsightsResponse.data.find(i => i.campaign_id === mockTargetedCampaignRule.external_ref).message_id,
        disposition: 'V',
      }));
      expect(mockRes.status).toBeCalledWith(200);
      const response = mockRes.json.mock.calls[0][0];
      expect(response.data.total).toEqual(1);
      expect(response.notifications).toEqual([]);
    });

    test('Insights dispositions - should send viewed disposition to Insights if rule is dismissed in Pigeon db', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();

      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns: {
          [mockTargetedCampaignRule.id]: mockTargetedCampaignRule,
        },
      });
      const setDispositionMock = jest.fn();
      mockTargetedCampaignService.getInstance.mockReturnValue({
        getCampaigns: jest.fn().mockResolvedValueOnce(mockInsightsResponse),
        setDisposition: setDispositionMock,
      });
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([ {
        rule_id: mockTargetedCampaignRule.id,
        message_id: mockInsightsResponse.data.find(i => i.campaign_id === mockTargetedCampaignRule.external_ref).message_id,
        disposition: 'D',
      } ]);

      const endpoint = getCampaigns({
        logger: mockLogger,
        contentService: mockContentService,
        containerCacheService: mockContainerCacheService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        campaignCacheService: mockCampaignCacheService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(setDispositionMock).toHaveBeenCalledWith(expect.objectContaining({
        id: mockInsightsResponse.data.find(i => i.campaign_id === mockTargetedCampaignRule.external_ref).message_id,
        disposition: 'V',
      }));
      expect(mockContentService.getContentsByIds).not.toHaveBeenCalled();
      expect(mockRes.status).toBeCalledWith(200);
      const response = mockRes.json.mock.calls[0][0];
      expect(response.data.total).toEqual(0);
      expect(response.notifications).toEqual([]);
    });

    test('Insights dispositions - should also check Pigeon Db for disposition values of old message Id for Pega campaigns', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();

      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns: {
          [mockTargetedCampaignRule.id]: mockTargetedCampaignRule,
        },
      });
      const setDispositionMock = jest.fn();
      mockTargetedCampaignService.getInstance.mockReturnValue({
        getCampaigns: jest.fn().mockResolvedValueOnce(mockInsightsResponse),
        setDisposition: setDispositionMock,
      });
      const insightsCampaign = mockInsightsResponse.data.find(i => i.campaign_id === mockTargetedCampaignRule.external_ref);
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([ {
        rule_id: mockTargetedCampaignRule.id,
        message_id: generateMessageIdsPega(insightsCampaign.campaign_id, insightsCampaign.language, insightsCampaign.message_source),
        disposition: 'D',
      } ]);

      const endpoint = getCampaigns({
        logger: mockLogger,
        contentService: mockContentService,
        containerCacheService: mockContainerCacheService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        campaignCacheService: mockCampaignCacheService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(setDispositionMock).toHaveBeenCalledWith(expect.objectContaining({
        id: mockInsightsResponse.data.find(i => i.campaign_id === mockTargetedCampaignRule.external_ref).message_id,
        disposition: 'V',
      }));
      expect(mockRes.status).toBeCalledWith(200);
      const response = mockRes.json.mock.calls[0][0];
      expect(response.data.total).toEqual(0);
      expect(response.notifications).toEqual([]);
    });

    test('Insights dispositions - should handle error when sending viewed disposition to Insights', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();

      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns: {
          [mockTargetedCampaignRule.id]: mockTargetedCampaignRule,
        },
      });
      const mockDispositionsError = new Error('failed');
      mockTargetedCampaignService.getInstance.mockReturnValue({
        getCampaigns: jest.fn().mockResolvedValueOnce(mockInsightsResponse),
        setDisposition: jest.fn().mockRejectedValueOnce(mockDispositionsError),
      });
      const messageId = mockInsightsResponse.data.find(i => i.campaign_id === mockTargetedCampaignRule.external_ref).message_id;
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([ {
        rule_id: mockTargetedCampaignRule.id,
        message_id: messageId,
        disposition: 'V',
      } ]);
      mockContentService.getContentsByIds.mockResolvedValueOnce(mockContentResponse);

      const endpoint = getCampaigns({
        logger: mockLogger,
        contentService: mockContentService,
        containerCacheService: mockContainerCacheService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        campaignCacheService: mockCampaignCacheService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockLogger.error).toHaveBeenCalledWith({
        message: 'Error in updating campaigns to db disposition value',
        err: mockDispositionsError,
        messageId: messageId,
      });
      expect(mockRes.status).toBeCalledWith(200);
      const response = mockRes.json.mock.calls[0][0];
      expect(response.data.total).toEqual(1);
      expect(response.notifications).toEqual([]);
    });

    test('Launch Darkly - should not call Pigeon DB service if flag is disabled', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();

      mockLaunchDarklyService.isFeatureEnabled.mockImplementation((arg1, arg2) => {
        if (arg1 === 'pigeon-api.features.dispositions.db') return false;
        return true;
      });
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {
          [mockPriorityMessageRule.id]: mockPriorityMessageRule,
          [mockMassCampaignRule.id]: mockMassCampaignRule,
        },
      });
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockMarvelService.getRewardPoints.mockResolvedValueOnce(2000);
      mockContentService.getContentsByIds.mockResolvedValueOnce(mockContentResponse);

      const endpoint = getCampaigns({
        logger: mockLogger,
        contentService: mockContentService,
        containerCacheService: mockContainerCacheService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        campaignCacheService: mockCampaignCacheService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockDispositionsService.getDispositions).not.toHaveBeenCalled();
      expect(mockRes.status).toBeCalledWith(200);
      const response = mockRes.json.mock.calls[0][0];
      expect(response.data.total).toEqual(2);
      expect(response.notifications).toEqual([]);
      expect(response.data.items[0]).toEqual(expect.objectContaining({
        viewed: true, // if dispositions are turned off, rule's are considered viewed
        dismissable: false, // even if rule is configured as dismissible, if dispositions are turned off it will not be dismissible
      }));
    });
  });

  describe('Other', () => {
    test('should successfully get campaigns for ios/android platform without applicaiton query param', async () => {
      const mockReq = {
        get: jest.fn().mockReturnValueOnce('istgreen'),
        query: { container: 'abc123', page: 'test', platform: 'ios' },
      };
      const mockRes = { locals: { auth: { token: defaultToken, claims: { sub: '****************' } } }, status: mockStatus };
      const mockInsightsPayload = {
        message_id: 'ABCDEF123456',
        campaign_id: 'PAD01',
        message_source: 'KT',
        data: null,
        start_date: '2018-01-05',
        data_context: null,
      };
      mockTargetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce({ data: [ mockInsightsPayload ] });
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);

      const mockCampaignRules = [ {
        id: 'aaaa0001',
        external_ref: 'PAD01',
        content_space: 'samplespace',
        content_type: 'creditCardOffer',
        content_id: 'aaaa1111',
        platforms_targeting: [
          { v: 1, platform: 'phoenix', items: [] },
        ],
        container: mockReq.query.container,
        pages: [ mockReq.query.page ],
        application: 'phoenix',
      },
      {
        id: 'bbb0001',
        external_ref: 'PAD01',
        content_space: 'alternateSpace',
        content_type: 'creditCardOffer',
        content_id: 'aaaa1111',
        platforms_targeting: [
          { v: 1, platform: 'ios', items: [] },
          { v: 1, platform: 'android', items: [] },
        ],
        container: mockReq.query.container,
        pages: [ mockReq.query.page ],
        application: 'nova',
      } ];

      const campaigns = mockCampaignRules.reduce((acc, curVal, curIndex) => {
        acc[curVal.id] = curVal;
        return acc;
      }, {});

      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns,
      });
      const mockContentResponse = {
        data: {
          items: [
            {
              id: 'aaaa1111',
              type: 'creditCardOffer',
              content: {
                title: defaultTitle,
                message: defaultMessage,
              },
            },
          ],
        },
      };
      mockContentService.getContentsByIds.mockResolvedValueOnce(mockContentResponse);
      mockStatus.mockReturnValueOnce({ json: mockJson });

      const endpoint = getCampaigns({
        logger: mockLogger,
        contentService: mockContentService,
        containerCacheService: mockContainerCacheService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        campaignCacheService: mockCampaignCacheService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockStatus).toBeCalled();
      expect(mockStatus).toBeCalledWith(200);
      expect(mockJson).toBeCalled();
      const result = mockJson.mock.calls[0][0];
      expect(result).toHaveProperty('notifications');
      expect(result.notifications.length).toEqual(0);
      expect(result).toHaveProperty('data');
      expect(result.data).toHaveProperty('items');
      expect(result.data.items.length).toEqual(1);
      const campaign = result.data.items[0];
      expect(campaign).toHaveProperty('id');
      expect(campaign.id).toEqual('bbb0001');
      expect(campaign).toHaveProperty('content');
      expect(campaign.content).toEqual(mockContentResponse.data.items[0].content);
      expect(campaign).toHaveProperty('external_ref');
      expect(campaign.external_ref.id).toEqual(mockInsightsPayload.id);
    });

    test('should successfully get campaigns for phoenix platform without applicaiton query param', async () => {
      const mockReq = {
        get: jest.fn().mockReturnValueOnce('istgreen'),
        query: { container: 'abc123', page: 'test', platform: 'phoenix' },
      };
      const mockRes = { locals: { auth: { token: defaultToken, claims: { sub: '****************' } } }, status: mockStatus };
      const mockInsightsPayload = {
        message_id: 'ABCDEF123456',
        campaign_id: 'PAD01',
        message_source: 'KT',
        data: null,
        start_date: '2018-01-05',
        data_context: null,
      };
      mockTargetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce({ data: [ mockInsightsPayload ] });
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);

      const mockCampaignRules = [ {
        id: 'aaa0001',
        external_ref: 'PAD01',
        content_space: 'samplespace',
        content_type: 'creditCardOffer',
        content_id: 'aaaa1111',
        platforms_targeting: [
          { v: 1, platform: 'web', items: [] },
        ],
        container: mockReq.query.container,
        pages: [ mockReq.query.page ],
        application: 'phoenix',
      },
      {
        id: 'bbb0001',
        external_ref: 'PAD01',
        content_space: 'alternateSpace',
        content_type: 'creditCardOffer',
        content_id: 'aaaa1111',
        platforms_targeting: [
          { v: 1, platform: 'ios', items: [] },
          { v: 1, platform: 'android', items: [] },
        ],
        container: mockReq.query.container,
        pages: [ mockReq.query.page ],
        application: 'nova',
      } ];

      const campaigns = mockCampaignRules.reduce((acc, curVal, curIndex) => {
        acc[curVal.id] = curVal;
        return acc;
      }, {});

      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns,
      });
      const mockContentResponse = {
        data: {
          items: [
            {
              id: 'aaaa1111',
              type: 'creditCardOffer',
              content: {
                title: defaultTitle,
                message: defaultMessage,
              },
            },
          ],
        },
      };
      mockContentService.getContentsByIds.mockResolvedValueOnce(mockContentResponse);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaigns({
        logger: mockLogger,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        containerCacheService: mockContainerCacheService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        campaignCacheService: mockCampaignCacheService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockStatus).toBeCalled();
      expect(mockStatus).toBeCalledWith(200);
      expect(mockJson).toBeCalled();
      const result = mockJson.mock.calls[0][0];
      expect(result).toHaveProperty('notifications');
      expect(result.notifications.length).toEqual(0);
      expect(result).toHaveProperty('data');
      expect(result.data).toHaveProperty('items');
      expect(result.data.items.length).toEqual(1);
      const campaign = result.data.items[0];
      expect(campaign).toHaveProperty('id');
      expect(campaign.id).toEqual('aaa0001');
      expect(campaign).toHaveProperty('content');
      expect(campaign.content).toEqual(mockContentResponse.data.items[0].content);
      expect(campaign).toHaveProperty('external_ref');
      expect(campaign.external_ref.id).toEqual(mockInsightsPayload.id);
    });

    test('should successfully get campaigns for ios platform with application query param', async () => {
      const mockReq = {
        get: jest.fn().mockReturnValueOnce('istgreen'),
        query: { container: 'abc123', page: 'test', platform: 'ios', application: 'nova' },
      };
      const mockRes = { locals: { auth: { token: defaultToken, claims: { sub: '****************' } } }, status: mockStatus };
      const mockInsightsPayload = {
        message_id: 'ABCDEF123456',
        campaign_id: 'PAD01',
        message_source: 'KT',
        data: null,
        start_date: '2018-01-05',
        data_context: null,
      };
      mockTargetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce({ data: [ mockInsightsPayload ] });
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);

      const mockCampaignRules = [ {
        id: 'aaaa0001',
        external_ref: 'PAD01',
        content_space: 'samplespace',
        content_type: 'creditCardOffer',
        content_id: 'aaaa1111',
        platforms_targeting: [
          { v: 1, platform: 'phoenix', items: [] },
        ],
        container: mockReq.query.container,
        pages: [ mockReq.query.page ],
        application: 'phoenix',
      },
      {
        id: 'bbb0001',
        external_ref: 'PAD01',
        content_space: 'alternateSpace',
        content_type: 'creditCardOffer',
        content_id: 'aaaa1111',
        platforms_targeting: [
          { v: 1, platform: 'ios', items: [] },
          { v: 1, platform: 'android', items: [] },
        ],
        container: mockReq.query.container,
        pages: [ mockReq.query.page ],
        application: 'nova',
      },
      {
        id: 'ccc0001',
        external_ref: 'PAD01',
        content_space: 'alternateSpace',
        content_type: 'creditCardOffer',
        content_id: 'aaaa1111',
        platforms_targeting: [
          { v: 1, platform: 'ios', items: [] },
          { v: 1, platform: 'android', items: [] },
        ],
        container: mockReq.query.container,
        pages: [ mockReq.query.page ],
        application: 'nova',
      },
      {
        id: 'ddd0001',
        external_ref: 'PAD01',
        content_space: 'alternateSpace',
        content_type: 'creditCardOffer',
        content_id: 'aaaa1111',
        platforms_targeting: [
          { v: 1, platform: 'phoenix', items: [] },
        ],
        container: mockReq.query.container,
        pages: [ mockReq.query.page ],
      } ];

      const campaigns = mockCampaignRules.reduce((acc, curVal, curIndex) => {
        acc[curVal.id] = curVal;
        return acc;
      }, {});

      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns,
      });
      const mockContentResponse = {
        data: {
          items: [
            {
              id: 'aaaa1111',
              type: 'creditCardOffer',
              content: {
                title: defaultTitle,
                message: defaultMessage,
              },
            },
          ],
        },
      };
      mockContentService.getContentsByIds.mockResolvedValueOnce(mockContentResponse);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaigns({
        logger: mockLogger,
        contentService: mockContentService,
        containerCacheService: mockContainerCacheService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        campaignCacheService: mockCampaignCacheService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockStatus).toBeCalled();
      expect(mockStatus).toBeCalledWith(200);
      expect(mockJson).toBeCalled();
      const result = mockJson.mock.calls[0][0];
      expect(result).toHaveProperty('notifications');
      expect(result.notifications.length).toEqual(0);
      expect(result).toHaveProperty('data');
      expect(result.data).toHaveProperty('items');
      expect(result.data.items.length).toEqual(2);
      const campaign = result.data.items[1];
      expect(campaign).toHaveProperty('id');
      expect(campaign.id).toEqual('ccc0001');
      expect(campaign).toHaveProperty('content');
      expect(campaign.content).toEqual(mockContentResponse.data.items[0].content);
      expect(campaign).toHaveProperty('external_ref');
      expect(campaign.external_ref.id).toEqual(mockInsightsPayload.id);
    });

    test('should successfully get campaign with correct EST campaign start date', async () => {
      const mockReq = {
        get: jest.fn().mockReturnValueOnce('istgreen'),
        query: { container: 'abc123', page: 'test', platform: 'ios' },
      };
      const mockRes = { locals: { auth: { token: defaultToken, claims: { sub: '****************' } } }, status: mockStatus };
      const mockInsightsPayload = {
        message_id: 'ABCDEF123456',
        campaign_id: 'PAD01',
        message_source: 'KT',
        data: null,
        start_date: '2018-01-05',
        data_context: null,
      };

      mockTargetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce({ data: [ mockInsightsPayload ] });
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      const mockCampaignRule = {
        id: 'aaaa0001',
        external_ref: 'PAD01',
        content_space: 'samplespace',
        content_type: 'creditCardOffer',
        content_id: 'aaaa1111',
        platforms_targeting: [
          { v: 1, platform: 'ios', items: [] },
        ],
        container: mockReq.query.container,
        pages: [ mockReq.query.page ],
      };
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {
          [mockCampaignRule.id]: mockCampaignRule,
        },
      });
      const mockContentResponse = {
        data: {
          items: [
            {
              id: 'aaaa1111',
              type: 'creditCardOffer',
              content: {
                title: defaultTitle,
                message: defaultMessage,
              },
            },
          ],
        },
      };
      mockContentService.getContentsByIds.mockResolvedValue(mockContentResponse);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaigns({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        containerCacheService: mockContainerCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
      }, config);

      await endpoint(mockReq, mockRes, mockNext);
      const expectedOutputDate = momentTz(mockInsightsPayload.start_date, 'YYYY-MM-DD hh:mm:ss').tz('America/Toronto').utc().format();
      expect(mockStatus).toBeCalled();
      expect(mockStatus).toBeCalledWith(200);
      expect(mockJson).toBeCalled();
      const result = mockJson.mock.calls[0][0];
      expect(result).toHaveProperty('notifications');
      expect(result.notifications.length).toEqual(0);
      expect(result).toHaveProperty('data');
      expect(result.data).toHaveProperty('items');
      expect(result.data.items.length).toEqual(1);
      const campaign = result.data.items[0];
      expect(campaign).toHaveProperty('id');
      expect(campaign.id).toEqual(mockCampaignRule.id);
      expect(campaign).toHaveProperty('content');
      expect(campaign.content).toEqual(mockContentResponse.data.items[0].content);
      expect(campaign).toHaveProperty('external_ref');
      expect(campaign.start_date).toEqual(expectedOutputDate);
    });

    test('should successfully get campaign with correct EDT campaign start date', async () => {
      const mockReq = {
        get: jest.fn().mockReturnValueOnce('istgreen'),
        query: { container: 'abc123', page: 'test', platform: 'ios' },
      };
      const mockRes = { locals: { auth: { token: defaultToken, claims: { sub: '****************' } } }, status: mockStatus };
      const mockInsightsPayload = {
        message_id: 'ABCDEF123456',
        campaign_id: 'PAD01',
        message_source: 'KT',
        data: null,
        start_date: '2020-03-22',
        data_context: null,
      };

      mockTargetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce({ data: [ mockInsightsPayload ] });
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      const mockCampaignRule = {
        id: 'aaaa0001',
        external_ref: 'PAD01',
        content_space: 'samplespace',
        content_type: 'creditCardOffer',
        content_id: 'aaaa1111',
        platforms_targeting: [
          { v: 1, platform: 'ios', items: [] },
        ],
        container: mockReq.query.container,
        pages: [ mockReq.query.page ],
      };
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {
          [mockCampaignRule.id]: mockCampaignRule,
        },
      });
      const mockContentResponse = {
        data: {
          total: 1,
          items: [
            {
              id: 'aaaa1111',
              type: 'creditCardOffer',
              content: {
                title: defaultTitle,
                message: defaultMessage,
              },
            },
          ],
        },
      };
      mockContentService.getContentsByIds.mockResolvedValue(mockContentResponse);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaigns({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        containerCacheService: mockContainerCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      const expectedOutputDate = momentTz(mockInsightsPayload.start_date, 'YYYY-MM-DD hh:mm:ss').tz('America/Toronto').utc().format();
      expect(mockStatus).toBeCalled();
      expect(mockStatus).toBeCalledWith(200);
      expect(mockJson).toBeCalled();
      const result = mockJson.mock.calls[0][0];
      expect(result).toHaveProperty('notifications');
      expect(result.notifications.length).toEqual(0);
      expect(result).toHaveProperty('data');
      expect(result.data).toHaveProperty('items');
      expect(result.data.items.length).toEqual(1);
      const campaign = result.data.items[0];
      expect(campaign).toHaveProperty('id');
      expect(campaign.id).toEqual(mockCampaignRule.id);
      expect(campaign).toHaveProperty('content');
      expect(campaign.content).toEqual(mockContentResponse.data.items[0].content);
      expect(campaign).toHaveProperty('external_ref');
      expect(campaign.start_date).toEqual(expectedOutputDate);
    });

    test('should return campaigns with disabled application', async () => {
      const mockReq = {
        get: jest.fn().mockReturnValueOnce('istgreen'),
        query: { container: 'abc123', page: 'test', platform: 'ios', application: 'nova' },
      };
      const mockRes = { locals: { auth: { token: defaultToken, claims: { sub: '****************' } } }, status: mockStatus };
      const mockInsightsPayload = {
        message_id: 'ABCDEF123456',
        campaign_id: 'PAD01',
        message_source: 'KT',
        data: null,
        start_date: '2018-01-05',
        data_context: null,
      };
      mockTargetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce({ data: [ mockInsightsPayload ] });
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);

      const mockCampaignRules = [ {
        id: 'aaaa0001',
        external_ref: 'PAD01',
        content_space: 'samplespace',
        content_type: 'creditCardOffer',
        content_id: 'aaaa1111',
        platforms_targeting: [
          { v: 1, platform: 'phoenix', items: [] },
        ],
        container: mockReq.query.container,
        pages: [ mockReq.query.page ],
        application: 'phoenix',
      },
      {
        id: 'bbb0001',
        external_ref: 'PAD01',
        content_space: 'alternateSpace',
        content_type: 'creditCardOffer',
        content_id: 'aaaa1111',
        platforms_targeting: [
          { v: 1, platform: 'ios', items: [] },
          { v: 1, platform: 'android', items: [] },
        ],
        container: mockReq.query.container,
        pages: [ mockReq.query.page ],
        application: 'nova',
      },
      {
        id: 'ccc0001',
        external_ref: 'PAD01',
        content_space: 'alternateSpace',
        content_type: 'creditCardOffer',
        content_id: 'aaaa1111',
        platforms_targeting: [
          { v: 1, platform: 'ios', items: [] },
          { v: 1, platform: 'android', items: [] },
        ],
        container: mockReq.query.container,
        pages: [ mockReq.query.page ],
        application: 'nova',
      },
      {
        id: 'ddd0001',
        external_ref: 'PAD01',
        content_space: 'alternateSpace',
        content_type: 'creditCardOffer',
        content_id: 'aaaa1111',
        platforms_targeting: [
          { v: 1, platform: 'phoenix', items: [] },
        ],
        container: mockReq.query.container,
        pages: [ mockReq.query.page ],
      } ];

      const campaigns = mockCampaignRules.reduce((acc, curVal, curIndex) => {
        acc[curVal.id] = curVal;
        return acc;
      }, {});

      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns,
      });
      const mockContentResponse = {
        data: {
          total: 1,
          items: [
            {
              id: 'aaaa1111',
              type: 'creditCardOffer',
              content: {
                title: defaultTitle,
                message: defaultMessage,
              },
            },
          ],
        },
      };
      mockContentService.getContentsByIds.mockResolvedValueOnce(mockContentResponse);
      mockStatus.mockReturnValueOnce({ json: mockJson });

      const endpoint = getCampaigns({
        logger: mockLogger,
        contentService: mockContentService,
        containerCacheService: mockContainerCacheService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        campaignCacheService: mockCampaignCacheService,
      }, {
        ...config,
        features: { application: false },
      });
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockStatus).toBeCalled();
      expect(mockStatus).toBeCalledWith(200);
      expect(mockJson).toBeCalled();
      const result = mockJson.mock.calls[0][0];
      expect(result).toHaveProperty('notifications');
      expect(result.notifications.length).toEqual(0);
      expect(result).toHaveProperty('data');
      expect(result.data).toHaveProperty('items');
      expect(result.data.items.length).toEqual(2);
      const campaign = result.data.items[1];
      expect(campaign).toHaveProperty('id');
      expect(campaign.id).toEqual('ccc0001');
      expect(campaign).toHaveProperty('content');
      expect(campaign.content).toEqual(mockContentResponse.data.items[0].content);
      expect(campaign).toHaveProperty('external_ref');
      expect(campaign.external_ref.id).toEqual(mockInsightsPayload.id);
    });

    test('should not call marvel and insight when requesting for anonymous customer', async () => {
      const mockReq = {
        get: (k) => k.toLowerCase() === 'preferred-environment' ? 'istgreen' : undefined,
        query: { page: 'test', platform: 'ios' },
      };
      const mockRes = { locals: { auth: { token: defaultToken, claims: { scope: 'cdb.pigeon.campaigns.read' } } }, status: mockStatus };
      mockTargetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce({ data: [] });
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {},
      });
      const endpoint = getCampaigns({
        logger: mockLogger,
        contentService: mockContentService,
        containerCacheService: mockContainerCacheService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        launchDarklyService: mockLaunchDarklyService,
        campaignCacheService: mockCampaignCacheService,
        marvelService: mockMarvelService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockNext).not.toBeCalled();
      expect(mockTargetedCampaignService.getInstance().getCampaigns).not.toBeCalled();
      expect(mockMarvelService.getAccounts).not.toBeCalled();
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
    });

    test('should not call marvel and insight when requesting from anonymous client id', async () => {
      const mockReq = {
        get: (k) => k.toLowerCase() === 'preferred-environment' ? 'istgreen' : undefined,
        query: { page: 'test', platform: 'ios' },
      };
      const mockRes = {
        locals:
        {
          auth:
          {
            token: defaultToken,
            // phoenix bug situation below
            claims: {
              scope: 'standard cdb.pigeon.campaigns.read',
              sub: 'testAnonymousClientId',
              client_id: 'testAnonymousClientId',
            },
          },
        },
        status: mockStatus,
      };
      mockTargetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce({ data: [] });
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {},
      });
      const endpoint = getCampaigns({
        logger: mockLogger,
        contentService: mockContentService,
        containerCacheService: mockContainerCacheService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        launchDarklyService: mockLaunchDarklyService,
        campaignCacheService: mockCampaignCacheService,
        marvelService: mockMarvelService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockNext).not.toBeCalled();
      expect(mockTargetedCampaignService.getInstance().getCampaigns).not.toBeCalled();
      expect(mockMarvelService.getAccounts).not.toBeCalled();
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
    });

    test('check for marvel call using the profile.customerInfo.cardNumber from the customer token', async () => {
      const mockReq = {
        get: jest.fn().mockReturnValueOnce('istgreen'),
        query: { page: 'test', platform: 'ios' },
      };
      const mockRes = { locals: { auth: { token: defaultToken, claims: { scope: 'standard', profile: { customerInfo: { cardNumber: '****************' } } } } }, status: mockStatus };
      mockStatus.mockReturnValueOnce({ json: mockJson });
      mockCampaignCacheService.get.mockResolvedValueOnce({
        campaigns: {
          [mockPriorityMessageRule.id]: mockPriorityMessageRule,
        },
      });
      mockTargetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce(Promise.resolve({ data: [], notification: null }));
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockContentService.getContentsByIds.mockResolvedValue(mockContentResponse);

      const endpoint = getCampaigns({
        logger: mockLogger,
        contentService: mockContentService,
        containerCacheService: mockContainerCacheService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        campaignCacheService: mockCampaignCacheService,
        marvelService: mockMarvelService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockTargetedCampaignService.getInstance().getCampaigns).not.toBeCalled();
      expect(mockMarvelService.getAccounts).toBeCalled();
      expect(mockCampaignCacheService.get).toBeCalled();
      expect(mockJson).toBeCalledTimes(1);
      expect(mockJson.mock.calls[0][0]).toHaveProperty('data');
      expect(mockJson.mock.calls[0][0].data).toHaveProperty('total');
    });

    test('should resolve dynamic page ids to account keys for targeted campaigns', async () => {
      const mockReq = {
        get: jest.fn().mockReturnValueOnce('istgreen'),
        query: { container: 'abc123', page: 'test', platform: 'ios', application: 'nova' },
      };
      const mockRes = { locals: { auth: { token: defaultToken, claims: { scope: 'standard', profile: { customerInfo: { cardNumber: '****************' } } } } }, status: mockStatus };
      const mockInsightsPayload = {
        message_id: 'ABCDEF123456',
        campaign_id: 'PAD01',
        message_source: 'KT',
        data: null,
        start_date: '2018-01-05',
        data_context: null,
      };
      mockTargetedCampaignService.getInstance.mockReturnValue({ getCampaigns: jest.fn().mockImplementation(() => Promise.resolve({ data: [ mockInsightsPayload ] })) });
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      const mockProduct = { ownership: 'B', code: 'SAV' };
      const mockCampaignRules = [ {
        id: 'bbb0001',
        external_ref: 'PAD01',
        content_space: 'alternateSpace',
        content_type: 'creditCardOffer',
        content_id: 'aaaa1111',
        platforms_targeting: [
          { v: 1, platform: 'ios', items: [] },
          { v: 1, platform: 'android', items: [] },
        ],
        mass_targeting: {
          product_pages: [ mockProduct ],
        },
        container: mockReq.query.container,
        pages: [ mockReq.query.page, 'account-key' ],
        application: 'nova',
      } ];

      const campaigns = mockCampaignRules.reduce((acc, curVal, curIndex) => {
        acc[curVal.id] = curVal;
        return acc;
      }, {});

      mockCampaignCacheService.get.mockReturnValueOnce({
        campaigns,
      });
      const mockContentResponse = {
        data: {
          total: 1,
          items: [
            {
              id: 'aaaa1111',
              type: 'creditCardOffer',
              content: {
                title: defaultTitle,
                message: defaultMessage,
              },
            },
          ],
        },
      };
      mockContentService.getContentsByIds.mockResolvedValueOnce(mockContentResponse);
      mockStatus.mockReturnValueOnce({ json: mockJson });

      const mockAcctRes = {
        accountList: [ { ...mockProduct, accountUniqueId: '123', ciProductCode: mockProduct.code } ],
      };

      const mockMarvelService = {
        getAccounts: jest.fn().mockImplementation(() => Promise.resolve(mockAcctRes)),
        getRewardPoints: () => 2000,
        getCardProfile: jest.fn().mockImplementation(() => Promise.resolve(null)),
      };

      const endpoint = getCampaigns({
        logger: mockLogger,
        contentService: mockContentService,
        containerCacheService: mockContainerCacheService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        campaignCacheService: mockCampaignCacheService,
      }, {
        ...config,
        features: { application: false },
      });
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockStatus).toBeCalled();
      expect(mockStatus).toBeCalledWith(200);
      expect(mockJson).toBeCalled();
      const result = mockJson.mock.calls[0][0];
      expect(result).toHaveProperty('notifications');
      expect(result.notifications.length).toEqual(0);
      expect(result).toHaveProperty('data');
      expect(result.data).toHaveProperty('items');
      expect(result.data.items.length).toEqual(1);
      const campaign = result.data.items[0];
      expect(campaign).toHaveProperty('pages');
      expect(campaign.pages).toEqual([ 'test', mockAcctRes.accountList[0].accountUniqueId ]);
    });

    test('should successfully filter rules if language targeting configured', async () => {
      const mockReq = {
        get: jest.fn().mockReturnValueOnce('istgreen'),
        query: { container: 'abc123', page: 'test', platform: 'ios' },
      };
      const mockRes = { locals: { auth: { token: defaultToken, claims: { sub: '****************' } }, language: 'en' }, status: mockStatus };
      const mockInsightsPayload = {
        message_id: 'ABCDEF123456',
        campaign_id: 'PAD01',
        message_source: 'KT',
        data: null,
        start_date: '2020-03-22',
        data_context: null,
      };

      mockTargetedCampaignService.getInstance().getCampaigns.mockResolvedValueOnce({ data: [ mockInsightsPayload ] });
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      const mockCampaignRule = {
        id: 'aaaa0001',
        external_ref: 'PAD01',
        content_space: 'samplespace',
        content_type: 'creditCardOffer',
        content_id: 'aaaa1111',
        platforms_targeting: [
          { v: 1, platform: 'ios', items: [] },
        ],
        container: mockReq.query.container,
        pages: [ mockReq.query.page ],
        mass_targeting: {
          languages: [ 'fr' ],
        },
      };
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {
          [mockCampaignRule.id]: mockCampaignRule,
        },
      });
      const mockContentResponse = {
        data: {
          total: 1,
          items: [
            {
              id: 'aaaa1111',
              type: 'creditCardOffer',
              content: {
                title: defaultTitle,
                message: defaultMessage,
              },
            },
          ],
        },
      };
      mockContentService.getContentsByIds.mockResolvedValue(mockContentResponse);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaigns({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        containerCacheService: mockContainerCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
      }, config);
      await endpoint(mockReq, mockRes, mockNext);
      expect(mockStatus).toBeCalled();
      expect(mockStatus).toBeCalledWith(200);
      expect(mockJson).toBeCalled();
      const result = mockJson.mock.calls[0][0];
      expect(result).toHaveProperty('notifications');
      expect(result.notifications.length).toEqual(0);
      expect(result).toHaveProperty('data');
      expect(result.data).toHaveProperty('items');
      expect(result.data.items.length).toEqual(0);
    });
  });

  describe('Targeting by defined demographic criteria', () => {
    test('should filter rules by defined geographic criteria when demographic targeting is enabled', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();

      const demographicRule = {
        ...mockMassCampaignRule,
        id: 'demographicRule1',
        mass_targeting: {
          by_demographic: {
            provinces: [ 'ON', 'BC' ],
            languages: [ 'en', 'fr' ],
            gender: 'ms',
            age_min: 19,
            age_max: 65,
          },
        },
      };

      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {
          [demographicRule.id]: demographicRule,
        },
      });
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      mockContentService.getContentsByIds.mockResolvedValueOnce(mockContentResponse);

      const mockProdigyWealthService = {
        customerInfo: jest.fn().mockResolvedValue({
          language: 'English',
          country: 'CA',
          province: 'ON',
          gender: 'MRS',
          date_of_birth: '1990-01-01',
          first_name: 'LYNDA',
          last_name: 'V',
        }),
      };

      const endpoint = getCampaigns({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        containerCacheService: mockContainerCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        prodigyWealthService: mockProdigyWealthService,
      }, { ...config, prodigyWealthAPI: { uri: 'test-uri' } });

      await endpoint(mockReq, mockRes, mockNext);

      expect(mockRes.status).toBeCalledWith(200);
      const response = mockRes.json.mock.calls[0][0];
      expect(response.data.total).toEqual(1);
    });

    test('should return internal server error when prodigy wealth service fails', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();

      const demographicRule = {
        ...mockMassCampaignRule,
        id: 'demographicRule1',
        external_ref: 'WEALTH',
        mass_targeting: {
          by_demographic: {
            provinces: [ 'ON' ],
          },
        },
      };

      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {
          [demographicRule.id]: demographicRule,
        },
      });
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);

      const mockProdigyWealthService = {
        customerInfo: jest.fn().mockRejectedValue(new Error('Service unavailable')),
      };

      const endpoint = getCampaigns({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        containerCacheService: mockContainerCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        prodigyWealthService: mockProdigyWealthService,
      }, { ...config, prodigyWealthAPI: { uri: 'test-uri' } });

      await endpoint(mockReq, mockRes, mockNext);

      expect(mockLogger.error).toHaveBeenCalledWith({
        message: 'Error fetching customer info',
        err: expect.any(Error),
      });
      expect(mockNext).toHaveBeenCalledWith(
        expect.objectContaining({
          statusCode: 500,
          message: 'Error fetching customer info',
        }),
      );
    });

    test('should skip demographic filtering when no rules have demographic targeting', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();

      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {
          [mockPriorityMessageRule.id]: mockPriorityMessageRule,
        },
      });
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);

      const mockProdigyWealthService = {
        customerInfo: jest.fn(),
      };

      const endpoint = getCampaigns({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        containerCacheService: mockContainerCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        prodigyWealthService: mockProdigyWealthService,
      }, config);

      await endpoint(mockReq, mockRes, mockNext);

      expect(mockProdigyWealthService.customerInfo).not.toHaveBeenCalled();
      expect(mockRes.status).toBeCalledWith(200);
    });

    test('should call extractCustomerDataForDemographicTargeting with prodigy response', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();

      const demographicRule = {
        ...mockMassCampaignRule,
        id: 'demographicRule1',
        mass_targeting: {
          by_demographic: {
            provinces: [ 'AB' ],
          },
        },
      };

      const prodigyResponse = {
        language: 'English',
        country: 'CA',
        province: 'AB',
        gender: 'MRS',
        date_of_birth: '1990-01-01',
        first_name: 'LYNDA',
        last_name: 'V',
      };

      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {
          [demographicRule.id]: demographicRule,
        },
      });
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);

      const mockProdigyWealthService = {
        customerInfo: jest.fn().mockResolvedValue(prodigyResponse),
      };

      const endpoint = getCampaigns({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        containerCacheService: mockContainerCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        prodigyWealthService: mockProdigyWealthService,
      }, { ...config, prodigyWealthAPI: { uri: 'test-uri' } });

      await endpoint(mockReq, mockRes, mockNext);

      expect(mockRes.status).toBeCalledWith(200);
    });

    test('should pass correct parameters to prodigy wealth service', async () => {
      const mockReq = {
        ...mockRequest(),
        get: jest.fn().mockImplementation((arg) => {
          if (arg === 'x-customer-scotiacard') return '************3456';
          if (arg === 'x-b3-spanid') return 'test-span-id';
          if (arg === 'x-b3-traceid') return 'test-trace-id';
          if (arg === 'x-originating-appl-code') return 'TEST';
        }),
      };
      const mockRes = {
        ...mockResponse(),
        locals: {
          ...mockResponse().locals,
          country: 'CA',
        },
      };

      const demographicRule = {
        ...mockMassCampaignRule,
        id: 'demographicRule1',
        external_ref: 'WEALTH',
        mass_targeting: {
          by_demographic: {
            provinces: [ 'ON' ],
          },
        },
      };

      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {
          [demographicRule.id]: demographicRule,
        },
      });
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);

      const mockProdigyWealthService = {
        customerInfo: jest.fn().mockResolvedValue({ address: { province: 'ON' } }),
      };

      const endpoint = getCampaigns({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        containerCacheService: mockContainerCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        prodigyWealthService: mockProdigyWealthService,
      }, { ...config, prodigyWealthAPI: { uri: 'test-prodigy-uri' } });

      await endpoint(mockReq, mockRes, mockNext);

      expect(mockProdigyWealthService.customerInfo).toHaveBeenCalledWith({
        cardNumber: '************3456',
        channelId: expect.any(String),
        country: 'CA',
        xOriginatingApplCode: 'TEST',
        traceId: 'test-trace-id',
        spanId: 'test-span-id',
      });
    });
  });

  describe('Targeting by ITrade LOB', () => {
    test('should filter rules by defined criteria when investment knowledge targeting is enabled', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();

      const mockRule = {
        ...mockMassCampaignRule,
        id: 'investment rule 1',
        mass_targeting: {
          investment_knowledge: {
            mf_knowledge: [ 'L', 'M' ],
            fix_income_knowledge: [ 'L', 'M' ],
            stock_knowledge: [ 'L' ],
            margin_knowledge: [ 'L' ],
            equity_options_knowledge: [ 'L' ],
            short_sale_knowledge: [ 'L' ],
          },
        },
      };

      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {
          [mockRule.id]: mockRule,
        },
      });
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      mockContentService.getContentsByIds.mockResolvedValueOnce(mockContentResponse);

      const mockProdigyWealthService = {
        customerInfo: jest.fn().mockResolvedValue({
          language: 'English',
          country: 'CA',
          province: 'ON',
          gender: 'MRS',
          date_of_birth: '1990-01-01',
          first_name: 'LYNDA',
          last_name: 'V',
          mf_knowledge: 'L',
          fix_income_knowledge: 'L',
          stock_knowledge: 'L',
          margin_knowledge: 'L',
          equity_options_knowledge: 'L',
          short_sale_knowledge: 'L',
        }),
      };

      const endpoint = getCampaigns({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        containerCacheService: mockContainerCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        prodigyWealthService: mockProdigyWealthService,
      }, { ...config, prodigyWealthAPI: { uri: 'test-uri' } });

      await endpoint(mockReq, mockRes, mockNext);

      expect(mockRes.status).toBeCalledWith(200);
      const response = mockRes.json.mock.calls[0][0];
      expect(response.data.total).toEqual(1);
    });

    test('should filter rules by defined criteria when iCLubTiers & segment_ids targeting is enabled', async () => {
      const mockReq = mockRequest();
      const mockRes = mockResponse();

      const mockRule = {
        ...mockMassCampaignRule,
        id: 'investment rule 1',
        mass_targeting: {
          iclub_tiers: [ 'Gold' ],
          segment_ids: [ '1', '2' ],
        },
      };

      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {
          [mockRule.id]: mockRule,
        },
      });
      mockMarvelService.getAccounts.mockResolvedValueOnce(mockAccountsResponse);
      mockDispositionsService.getDispositions.mockResolvedValueOnce([]);
      mockContentService.getContentsByIds.mockResolvedValueOnce(mockContentResponse);

      const mockProdigyWealthService = {
        customerInfo: jest.fn().mockResolvedValue({
          language: 'English',
          country: 'CA',
          province: 'ON',
          gender: 'MRS',
          date_of_birth: '1990-01-01',
          first_name: 'LYNDA',
          last_name: 'V',
          iclub: 'Gold',
          segment: '2',
        }),
      };

      const endpoint = getCampaigns({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        containerCacheService: mockContainerCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
        marvelService: mockMarvelService,
        prodigyWealthService: mockProdigyWealthService,
      }, { ...config, prodigyWealthAPI: { uri: 'test-uri' } });

      await endpoint(mockReq, mockRes, mockNext);

      expect(mockRes.status).toBeCalledWith(200);
      const response = mockRes.json.mock.calls[0][0];
      expect(response.data.total).toEqual(1);
    });
  });
});
