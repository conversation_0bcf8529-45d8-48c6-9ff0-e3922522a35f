const httpMocks = require('node-mocks-http');
const getCampaign = require('./get-campaign');
const { HttpError } = require('../../errors');

// Mock constants
const defaultToken = 'opaque-token';
const defaultTitle = 'some title';
const defaultMessage = 'some message';
const xB3TraceId = 'fd891ff840c5fed4d0b40c118cf674b5';
const xB3SpanId = 'd9dfe1006bc577a8';
const userAgent = 'iPhone';
const config = {
  tokenPath: 'auth.token',
  tokenClaimsPath: 'auth.claims',
  anonymousClientIds: [ 'testAnonymousClientId' ],
};
const mockPigeonMessageRule = {
  id: 'massMsgRuleId',
  content_space: '4szkx38resvm',
  content_type: 'priorityCampaign',
  content_id: '4mscapqZ4dt4GfZmTd5qgP',
  disabled: false,
  dismissable_flag: true,
  external_ref: 'MESSAGE',
  status: 'published',
  type: 'campaign',
  application: 'nova',
  container: 'priority-box',
  pages: [ 'accounts' ],
  platforms: [ 'ios', 'android' ],
  start_at: '2020-07-21T04:00:00.000Z',
  end_at: '2025-12-31T05:00:00.000Z',
};
const mockPigeonTargetedRule = {
  id: 'targetedRuleId',
  content_space: '4szkx38resvm',
  content_type: 'targetedCampaign',
  content_id: '25N3EYcsIaZ0Xv2PnEeKW6',
  disabled: false,
  dismissable_flag: true,
  external_ref: 'PAD01',
  status: 'published',
  type: 'campaign',
  application: 'nova',
  container: 'offers-and-programs',
  pages: [ 'accounts' ],
  platforms: [ 'ios', 'android' ],
  start_at: '2020-07-21T04:00:00.000Z',
  end_at: '2025-12-31T05:00:00.000Z',
};
const requestHeaders = {
  'Preferred-Environment': 'istgreen',
  'x-channel-id': 'Mobile',
  'User-Agent': userAgent,
  'x-b3-traceId': xB3TraceId,
  'x-b3-spanId': xB3SpanId,
};
const mockHttpParams = {
  headers: requestHeaders,
  params: { id: mockPigeonMessageRule.id },
  query: { message_id: mockPigeonMessageRule.id },
};
const mockHttpData = {
  locals: {
    auth: { token: defaultToken, claims: { scope: 'standard' } },
    language: 'en',
    languageContentful: 'en-US',
  },
};
const notFoundValidationMsg = 'Campaign not found';
const domainMismatchValidationMsg = 'Given country has insufficient access to rule types available';
const mockInsightsResponse = {
  data: {
    message_source: 'KT',
    message_id: 'DE4CBA6FC0F29381', // KT message_id format
    message_response: null,
    campaign_id: 'PAD01', // KT campaign_id format
    message_status: 'N',
    language: 'en',
    start_date: '2020-01-01',
    expiry_date: '2030-01-01',
  },
};
const mockContentResponse = {
  data: {
    id: 'fhdjslkhf',
    type: 'creditCardOffer',
    content: {
      preview: { title: defaultTitle, message: defaultMessage } },
  },
};

// Mock services
const mockVariablesService = jest.fn().mockResolvedValue({ content: {} });
const mockCampaignCacheService = {
  get: jest.fn(),
};
const mockDispositionsService = {
  getDispositions: jest.fn(),
  setDisposition: jest.fn(),
};
const mockTargetedCampaignService = {
  getInstance: jest.fn(),
};
mockTargetedCampaignService.getInstance.mockReturnValue({
  getCampaign: jest.fn(),
  setDisposition: jest.fn().mockResolvedValue(true),
});
const mockContentService = {
  getContentByTypeAndId: jest.fn(),
};
const mockStatus = jest.fn();
const mockJson = jest.fn();
const next = jest.fn();
const mockLogger = {
  error: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
};
const mockLaunchDarklyService = {
  init: jest.fn(),
  isFeatureEnabled: jest.fn(),
  close: jest.fn(),
};
mockLaunchDarklyService.init.mockReturnValue(true);
mockLaunchDarklyService.isFeatureEnabled.mockReturnValue(true);

describe('Campaign Routes getCampaign', () => {
  beforeEach(() => {
    mockCampaignCacheService.get.mockReset();
    mockDispositionsService.getDispositions.mockReset();
    mockDispositionsService.setDisposition.mockReset();
    mockTargetedCampaignService.getInstance().getCampaign.mockReset();
    mockTargetedCampaignService.getInstance().setDisposition.mockReset();
    mockContentService.getContentByTypeAndId.mockReset();
    mockVariablesService.mockResolvedValue({ content: {} });
    mockLaunchDarklyService.isFeatureEnabled.mockReset();
    mockLaunchDarklyService.isFeatureEnabled.mockReturnValue(true);
    next.mockReset();
    mockJson.mockReset();
    mockStatus.mockReset();
    mockLogger.error.mockReset();
  });

  test('should initialize', () => {
    expect(getCampaign).toBeDefined();
    expect(typeof getCampaign).toEqual('function');
  });

  describe('Joi Schema/Request Validation', () => {
    test('should fail on params validation error', async () => {
      const mockReq = {
        params: { id: '#abcdef1234' },
        query: {},
      };
      const mockRes = {
        locals: { auth: { token: defaultToken } },
      };
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(mockReq, mockRes, next);
      expect(next).toBeCalledTimes(1);
      const err = next.mock.calls[0][0];
      expect(err).toBeInstanceOf(HttpError);
      expect(err.statusCode).toEqual(400);
      expect(err.metadata[0].message).toEqual('"id" with value "#abcdef1234" fails to match the required pattern: /^[a-zA-Z0-9]+$/');
    });
    test('should fail on query validation error for message_id', async () => {
      const { req, res } = httpMocks.createMocks({
        params: { id: 'abcdef1234' },
        query: { message_id: '#DE4CBA6FC0F29381' },
      }, {
        locals: { auth: { token: defaultToken } },
      });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(req, res, next);
      expect(next).toBeCalledTimes(1);
      const err = next.mock.calls[0][0];
      expect(err).toBeInstanceOf(HttpError);
      expect(err.statusCode).toEqual(400);
      expect(err.metadata[0].message).toEqual('&quot;message_id&quot; with value &quot;#DE4CBA6FC0F29381&quot; fails to match the required pattern: /^[a-zA-Z0-9-]+$/');
    });
    test('should fail on query validation error for select_contents', async () => {
      const { req, res } = httpMocks.createMocks({
        params: { id: 'abcdef1234' },
        query: { message_id: 'DE4CBA6FC0F29381', select_contents: '%$#$#%^' },
      }, {
        locals: { auth: { token: defaultToken } },
      });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(req, res, next);
      expect(next).toBeCalledTimes(1);
      const err = next.mock.calls[0][0];
      expect(err).toBeInstanceOf(HttpError);
      expect(err.statusCode).toEqual(400);
      expect(err.metadata[0].message).toEqual('&quot;select_contents&quot; with value &quot;%$#$#%^&quot; fails to match the required pattern: /^[,a-zA-Z0-9_-]+$/');
    });
  });

  describe('Rule Validation', () => {
    test('should return bad request error if message_id is required (i.e. targeted campaign)', async () => {
      const { req, res } = httpMocks.createMocks({
        params: { id: mockPigeonTargetedRule.id },
        query: {},
      }, {
        locals: { auth: { token: defaultToken } },
      });
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonTargetedRule.id]: mockPigeonTargetedRule },
      });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
      }, config);
      await endpoint(req, res, next);
      expect(next).toBeCalledTimes(1);
      const err = next.mock.calls[0][0];
      expect(err).toBeInstanceOf(HttpError);
      expect(err.statusCode).toEqual(400);
      expect(err.metadata[0].message).toEqual('message_id is required');
    });
    test('should return bad request error if country header is CA but rule type is ccau', async () => {
      const mockHttpData = {
        locals: {
          language: 'en',
          languageContentful: 'en-US',
          country: 'CA',
        },
      };
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonMessageRule.id]: { ...mockPigeonMessageRule, type: 'ccau_campaign' } },
      });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
      }, config);
      await endpoint(req, res, next);
      const err = next.mock.calls[0][0];
      expect(err).toBeInstanceOf(HttpError);
      expect(err.message).toEqual(domainMismatchValidationMsg);
      expect(err.statusCode).toEqual(400);
    });
    test('should return bad request error if country header is CCAU but rule type is campaign', async () => {
      const mockHttpData = {
        locals: {
          language: 'en',
          languageContentful: 'en-DO',
          country: 'DO',
        },
      };
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonMessageRule.id]: mockPigeonMessageRule },
      });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
      }, config);
      await endpoint(req, res, next);
      const err = next.mock.calls[0][0];
      expect(err).toBeInstanceOf(HttpError);
      expect(err.message).toEqual(domainMismatchValidationMsg);
      expect(err.statusCode).toEqual(400);
    });
    test('should return not found error if past rule\'s end date', async () => {
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonMessageRule.id]: { ...mockPigeonMessageRule, end_at: '2020-01-01T00:00:00.000Z' } },
      });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
      }, config);
      await endpoint(req, res, next);
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
      expect(next).toBeCalledTimes(1);
      const err = next.mock.calls[0][0];
      expect(err).toBeInstanceOf(HttpError);
      expect(err.statusCode).toEqual(404);
      expect(err.message).toEqual(notFoundValidationMsg);
    });
    test('should return not found error if before rule\'s start date', async () => {
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonMessageRule.id]: { ...mockPigeonMessageRule, start_at: '2030-01-01T00:00:00.000Z' } },
      });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
      }, config);
      await endpoint(req, res, next);
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
      expect(next).toBeCalledTimes(1);
      const err = next.mock.calls[0][0];
      expect(err).toBeInstanceOf(HttpError);
      expect(err.statusCode).toEqual(404);
      expect(err.message).toEqual(notFoundValidationMsg);
    });
    test('should return not found error if rule is not published', async () => {
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonMessageRule.id]: { ...mockPigeonMessageRule, status: 'submitted' } },
      });
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
      }, config);
      await endpoint(req, res, next);
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
      expect(next).toBeCalledTimes(1);
      const err = next.mock.calls[0][0];
      expect(err).toBeInstanceOf(HttpError);
      expect(err.statusCode).toEqual(404);
      expect(err.message).toEqual(notFoundValidationMsg);
    });
    test('should return not found error if rule is not active', async () => {
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonMessageRule.id]: { ...mockPigeonMessageRule, disabled: true } },
      });
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
      }, config);
      await endpoint(req, res, next);
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
      expect(next).toBeCalledTimes(1);
      const err = next.mock.calls[0][0];
      expect(err).toBeInstanceOf(HttpError);
      expect(err.statusCode).toEqual(404);
      expect(err.message).toEqual(notFoundValidationMsg);
    });
    test('should return not found error if requested language is not in targetted languages', async () => {
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonMessageRule.id]: { ...mockPigeonMessageRule,
          mass_targeting: {
            languages: [ 'fr' ],
          },
        } },
      });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
      }, config);
      await endpoint(req, res, next);
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
      expect(next).toBeCalledTimes(1);
      const err = next.mock.calls[0][0];
      expect(err).toBeInstanceOf(HttpError);
      expect(err.statusCode).toEqual(404);
      expect(err.message).toEqual(notFoundValidationMsg);
    });
  });

  describe('Campaign Cache Service', () => {
    test.todo('should return not found error if campaign cache throws a not found error');

    test('should return error campaign cache throws an internal server error', async () => {
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockImplementationOnce(() => { throw HttpError.internalServer('Server error'); });
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
      }, config);
      await endpoint(req, res, next);
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
      expect(next).toBeCalledTimes(1);
      const err = next.mock.calls[0][0];
      expect(err).toBeInstanceOf(HttpError);
      expect(err.statusCode).toEqual(500);
      expect(err.message).toEqual('Server error');
    });
  });

  describe('Insights Targeted Campaigns (CA)', () => {
    test('should return the campaign details - KT campaign (default)', async () => {
      const mockHttpParams = {
        headers: requestHeaders,
        params: { id: mockPigeonTargetedRule.id },
        query: { message_id: mockInsightsResponse.data.message_id },
      };
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonTargetedRule.id]: mockPigeonTargetedRule },
      });
      mockTargetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightsResponse);
      mockContentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
      }, config);
      await endpoint(req, res, next);
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
      expect(mockTargetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
      expect(mockTargetedCampaignService.getInstance().setDisposition).toBeCalledTimes(1);
      expect(mockDispositionsService.setDisposition).toBeCalledTimes(1);
      expect(next).not.toBeCalled();
    });
    test('should return the campaign details - PEGAV2 campaign with new message_id format', async () => {
      const pegaMessageId = 'ab88104a-1486-3eef-b86d-9137a580e20d-98357178'; // new PEGAV2 msg format
      const pegaCampaignId = '5576112302';

      const mockHttpParams = {
        headers: requestHeaders,
        params: { id: mockPigeonTargetedRule.id },
        query: { message_id: pegaMessageId },
      };
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonTargetedRule.id]: { ...mockPigeonTargetedRule, external_ref: pegaCampaignId } },
      });
      const insightsResponse = {
        data: {
          ...mockInsightsResponse.data,
          message_id: pegaMessageId,
          campaign_id: pegaCampaignId,
          message_source: 'PEGAV2',
        },
      };
      mockTargetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(insightsResponse);
      mockContentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
      }, config);
      await endpoint(req, res, next);
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
      expect(mockTargetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
      expect(mockTargetedCampaignService.getInstance().setDisposition).toBeCalledTimes(1);
      expect(mockDispositionsService.setDisposition).toBeCalledTimes(1);
      const setInsightDispRequestBody = mockDispositionsService.setDisposition.mock.calls[0][5];
      expect(setInsightDispRequestBody).toEqual({
        message_id: pegaMessageId,
        container: mockPigeonTargetedRule.container,
        page: mockPigeonTargetedRule.pages[0],
        platform: null,
        application: mockPigeonTargetedRule.application,
        old_message_id: 'a8a501a8-36af-3f51-a276-91237921cf1a-98357178', // old msg id also checked in dispositions db
      });
      expect(next).not.toBeCalled();
    });
    test('should return the campaign details - PEGAV2 campaign with old message_id format', async () => {
      const pegaMessageId = 'a8a501a8-36af-3f51-a276-91237921cf1a-98357178'; // old PEGAV2 msg format
      const pegaCampaignId = '5576112302';

      const mockHttpParams = {
        headers: requestHeaders,
        params: { id: mockPigeonTargetedRule.id },
        query: { message_id: pegaMessageId },
      };
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonTargetedRule.id]: { ...mockPigeonTargetedRule, external_ref: pegaCampaignId } },
      });
      const insightsResponse = {
        data: {
          ...mockInsightsResponse.data,
          message_id: pegaMessageId,
          campaign_id: pegaCampaignId,
          message_source: 'PEGAV2',
        },
      };
      mockTargetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(insightsResponse);
      mockContentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
      }, config);
      await endpoint(req, res, next);
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
      expect(mockTargetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
      expect(mockTargetedCampaignService.getInstance().setDisposition).toBeCalledTimes(1);
      expect(mockDispositionsService.setDisposition).toBeCalledTimes(1);
      const setInsightDispRequestBody = mockDispositionsService.setDisposition.mock.calls[0][5];
      expect(setInsightDispRequestBody).toEqual({ // does not contain old_message_id
        message_id: pegaMessageId,
        container: mockPigeonTargetedRule.container,
        page: mockPigeonTargetedRule.pages[0],
        platform: null,
        application: mockPigeonTargetedRule.application,
      });
      expect(next).not.toBeCalled();
    });
    test('should not call Insights on anonymous customer request', async () => {
      const mockHttpParams = {
        headers: requestHeaders,
        params: { id: mockPigeonTargetedRule.id },
        query: { message_id: mockInsightsResponse.data.message_id },
      };
      const mockHttpData = {
        locals: { auth: { token: defaultToken, claims: { scope: 'cdb.pigeon.campaigns.read' } } }, // scope triggers anonymous request flag
      };
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonTargetedRule.id]: mockPigeonTargetedRule },
      });
      mockContentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(req, res, next);
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
      expect(mockTargetedCampaignService.getInstance().getCampaign).not.toBeCalled();
      expect(mockTargetedCampaignService.getInstance().setDisposition).not.toBeCalled();
      expect(mockDispositionsService.setDisposition).not.toBeCalled();
      expect(next).not.toBeCalled();
    });
    test('should return rule with Insight data in external_ref when `insight` query param is true', async () => {
      const mockHttpParams = {
        headers: requestHeaders,
        params: { id: mockPigeonTargetedRule.id },
        query: { message_id: mockInsightsResponse.data.message_id, insight: true },
      };
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonTargetedRule.id]: mockPigeonTargetedRule },
      });
      mockTargetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightsResponse);
      mockContentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
      const mockVariablesServiceResponse = {
        dataContext: 'test',
        transformed: {},
        content: mockContentResponse.data,
      };
      mockVariablesService.mockResolvedValueOnce(mockVariablesServiceResponse);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(req, res, next);
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
      expect(mockTargetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
      expect(mockTargetedCampaignService.getInstance().setDisposition).toBeCalledTimes(1);
      expect(mockDispositionsService.setDisposition).toBeCalledTimes(1);
      expect(next).not.toBeCalled();
      expect(res.statusCode).toEqual(200);
      const response = JSON.parse(res._getData());
      expect(response.data.external_ref.data).toEqual(mockInsightsResponse.data);
      expect(response.data.external_ref.data_context).toEqual('test');
    });
    test('should not return rule with Insight data when `insight` query param is false', async () => {
      const mockHttpParams = {
        headers: requestHeaders,
        params: { id: mockPigeonTargetedRule.id },
        query: { message_id: mockInsightsResponse.data.message_id, insight: false },
      };
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonTargetedRule.id]: mockPigeonTargetedRule },
      });
      mockTargetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightsResponse);
      mockContentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
      const mockVariablesServiceResponse = {
        dataContext: 'test',
        transformed: {},
        content: mockContentResponse.data,
      };
      mockVariablesService.mockResolvedValueOnce(mockVariablesServiceResponse);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(req, res, next);
      expect(mockTargetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
      expect(mockTargetedCampaignService.getInstance().setDisposition).toBeCalledTimes(1);
      expect(mockTargetedCampaignService.getInstance().setDisposition).toBeCalledTimes(1);
      expect(next).not.toBeCalled();
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
      expect(res.statusCode).toEqual(200);
      const response = JSON.parse(res._getData());
      expect(response.data.external_ref.data).toBeUndefined();
      expect(response.data.external_ref.data_context).toBeUndefined();
      expect(response.data.external_ref.data_transformed).toBeUndefined();
    });
    test('should not return rule with Insight data when `insight` query param is undefined', async () => {
      const mockHttpParams = {
        headers: requestHeaders,
        params: { id: mockPigeonTargetedRule.id },
        query: { message_id: mockInsightsResponse.data.message_id },
      };
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonTargetedRule.id]: mockPigeonTargetedRule },
      });
      mockTargetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightsResponse);
      mockContentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
      const mockVariablesServiceResponse = {
        dataContext: 'test',
        transformed: {},
        content: mockContentResponse.data,
      };
      mockVariablesService.mockResolvedValueOnce(mockVariablesServiceResponse);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(req, res, next);
      expect(mockTargetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
      expect(mockTargetedCampaignService.getInstance().setDisposition).toBeCalledTimes(1);
      expect(next).not.toBeCalled();
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
      expect(res.statusCode).toEqual(200);
      const response = JSON.parse(res._getData());
      expect(response.data.external_ref.data).toBeUndefined();
      expect(response.data.external_ref.data_context).toBeUndefined();
      expect(response.data.external_ref.data_transformed).toBeUndefined();
    });
    test('should return 500 error on failed Insights call', async () => {
      const mockHttpParams = {
        headers: requestHeaders,
        params: { id: mockPigeonTargetedRule.id },
        query: { message_id: mockInsightsResponse.data.message_id },
      };
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonTargetedRule.id]: mockPigeonTargetedRule },
      });
      mockTargetedCampaignService.getInstance().getCampaign.mockRejectedValueOnce(new Error('timeout'));
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
      }, config);
      await endpoint(req, res, next);
      expect(mockLogger.error).toBeCalled();
      expect(next).toBeCalledTimes(1);
      const err = next.mock.calls[0][0];
      expect(err).toBeInstanceOf(HttpError);
      expect(err.statusCode).toEqual(500);
      expect(err.metadata[0].message).toEqual('error calling downstream service to get the campaign');
    });
    test('should return not found error if Insights throws not found error', async () => {
      const mockHttpParams = {
        headers: requestHeaders,
        params: { id: mockPigeonTargetedRule.id },
        query: { message_id: mockInsightsResponse.data.message_id },
      };
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonTargetedRule.id]: mockPigeonTargetedRule },
      });
      const insightsResponse = {
        response: {
          data: null,
          notifications: [ {
            message: 'The unique ID was not found.',
            code: 'INSIGHT-001-E',
          } ],
        },
      };
      mockTargetedCampaignService.getInstance().getCampaign.mockRejectedValueOnce(insightsResponse);
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
      }, config);
      await endpoint(req, res, next);
      expect(next).toBeCalledTimes(1);
      const err = next.mock.calls[0][0];
      expect(err).toBeInstanceOf(HttpError);
      expect(err.statusCode).toEqual(404);
      expect(err.metadata[0].message).toEqual('campaign with specified message_id is not found');
    });
    test('should return not found error on missing (null) Insights data', async () => {
      const mockHttpParams = {
        headers: requestHeaders,
        params: { id: mockPigeonTargetedRule.id },
        query: { message_id: mockInsightsResponse.data.message_id },
      };
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonTargetedRule.id]: mockPigeonTargetedRule },
      });
      mockTargetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce({ data: null });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(req, res, next);
      expect(next).toBeCalledTimes(1);
      const err = next.mock.calls[0][0];
      expect(err).toBeInstanceOf(HttpError);
      expect(err.statusCode).toEqual(404);
      expect(err.metadata[0].message).toEqual('campaign with specified message_id is not found');
    });
    test('should return bad request error if Insights\' campaign_id does not match rule\'s external_ref', async () => {
      const mockHttpParams = {
        headers: requestHeaders,
        params: { id: mockPigeonTargetedRule.id },
        query: { message_id: mockInsightsResponse.data.message_id },
      };
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonTargetedRule.id]: mockPigeonTargetedRule },
      });
      const insightsResponse = {
        data: {
          message_id: mockInsightsResponse.data.message_id,
          campaign_id: 'PAD15', // mockPigeonTargetedRule's external_ref is PAD01
          message_source: 'KT',
        },
      };
      mockTargetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(insightsResponse);
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(req, res, next);
      expect(next).toBeCalledTimes(1);
      const err = next.mock.calls[0][0];
      expect(err).toBeInstanceOf(HttpError);
      expect(err.statusCode).toEqual(400);
      expect(err.metadata[0].message).toEqual('invalid message_id');
    });
  });

  describe('DC Targeted Campaigns (CCAU)', () => {
    test('Get CCAU campaign - should get campaign with ccau_campaign type', async () => {
      const { req, res } = httpMocks.createMocks({
        headers: {
          'Preferred-Environment': 'istgreen',
          'x-user-context': 'xuc-string',
          'x-country-code': 'DO',
        },
        params: { id: 'aaaa0001' },
        query: { message_id: 'ab88104a-1486-3eef-b86d-9137a580e20d-98357174', application: 'wave' },
      },
      {
        locals: {
          auth: { token: defaultToken, claims: { scope: 'standard' } },
          language: 'en',
          languageContentful: 'en-DO',
          country: 'DO',
        },
      });
      const mockDcCampaignsPayload = {
        data: {
          message_id: 'ab88104a-1486-3eef-b86d-9137a580e20d-98357174',
          campaign_id: 'PAD01',
          message_source: 'DC_CAMPAIGNS',
          message_response: 'N',
        },
      };
      mockTargetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockDcCampaignsPayload);
      const mockCampaignRule = {
        id: 'aaaa0001',
        external_ref: 'PAD01',
        content_space: 'samplespace',
        content_type: 'creditCardOffer',
        content_id: 'aaaa1111',
        status: 'published',
        disabled: false,
        application: 'nova',
        type: 'ccau_campaign',
      };
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockCampaignRule.id]: mockCampaignRule },
      });
      const mockContent = { data: { id: 'aaaaaaaa', type: 'creditCardOffer', content: { preview: { title: defaultTitle, message: defaultMessage } } } };
      mockVariablesService.mockResolvedValueOnce({ dataContext: 'test', transformed: {}, content: mockContent.data });
      mockContentService.getContentByTypeAndId.mockResolvedValueOnce(mockContent);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(req, res, next);
      expect(mockTargetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
      expect(mockTargetedCampaignService.getInstance().setDisposition).toBeCalledTimes(1);
      expect(next).not.toBeCalled();
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
    });
  });

  describe('Content & Variable Services', () => {
    test('should pass `fr` language as an option to content api', async () => {
      const mockHttpData = {
        locals: {
          auth: { token: defaultToken, claims: { scope: 'standard' } },
          language: 'fr',
          languageContentful: 'fr',
        },
      };
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {
          [mockPigeonMessageRule.id]: mockPigeonMessageRule,
        },
      });
      mockContentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(req, res, next);
      const opts = mockContentService.getContentByTypeAndId.mock.calls[0][3];
      expect(opts).toEqual({
        select: 'details',
        language: 'fr',
      });
    });
    test('should pass `interceptDetails` select option to content api', async () => {
      const mockHttpParams = {
        headers: requestHeaders,
        params: { id: mockPigeonMessageRule.id },
        query: { message_id: mockPigeonMessageRule.message_id, select_contents: 'interceptDetails' },
      };
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {
          [mockPigeonMessageRule.id]: mockPigeonMessageRule,
        },
      });
      mockContentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(req, res, next);
      const opts = mockContentService.getContentByTypeAndId.mock.calls[0][3];
      expect(opts).toEqual({
        select: 'interceptDetails',
        language: 'en-US',
      });
    });
    test('should return error on failed content data', async () => {
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: {
          [mockPigeonMessageRule.id]: mockPigeonMessageRule,
        },
      });
      mockContentService.getContentByTypeAndId.mockRejectedValueOnce(new Error('content api timeout'));
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(req, res, next);
      expect(mockLogger.error).toBeCalled();
      const loggerErr = mockLogger.error.mock.calls[0][0];
      expect(loggerErr.message).toEqual('failed to get content');
      expect(next).toBeCalledTimes(1);
      const err = next.mock.calls[0][0];
      expect(err.message).toEqual('content api timeout');
    });
    test('should handle errors from variable mapping service', async () => {
      const mockHttpParams = {
        headers: requestHeaders,
        params: { id: mockPigeonTargetedRule.id },
        query: { message_id: mockInsightsResponse.data.message_id, insight: true },
      };
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonTargetedRule.id]: mockPigeonTargetedRule },
      });
      mockTargetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightsResponse);
      mockContentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
      mockVariablesService.mockRejectedValueOnce(new Error('variable mapping service failure'));
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(req, res, next);
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
      expect(mockTargetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
      expect(mockTargetedCampaignService.getInstance().setDisposition).toBeCalledTimes(1);
      expect(mockDispositionsService.setDisposition).toBeCalledTimes(1);
      expect(next).not.toBeCalled();
      expect(mockLogger.error).toBeCalled();
      const loggerErr = mockLogger.error.mock.calls[0][0];
      expect(loggerErr.message).toEqual('failed to transform variables');
      expect(res.statusCode).toEqual(200);
      const response = JSON.parse(res._getData());
      expect(response.data.external_ref.data).toEqual(mockInsightsResponse.data);
      expect(response.data.external_ref.data_context).toEqual('unknown');
    });
  });

  describe('Dispositions', () => {
    test('should not send `viewed` disposition if ABM request', async () => {
      const mockPigeonABMRule = {
        ...mockPigeonTargetedRule,
        application: 'abm',
        container: 'mainOffer',
        pages: [ 'preMainMenu' ],
        platforms: [ 'web' ],
      };
      const mockHttpParams = {
        headers: requestHeaders,
        params: { id: mockPigeonABMRule.id },
        query: { message_id: mockInsightsResponse.data.message_id },
      };
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonABMRule.id]: mockPigeonABMRule },
      });
      mockTargetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightsResponse);
      mockContentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(req, res, next);
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
      expect(mockTargetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
      expect(mockTargetedCampaignService.getInstance().setDisposition).toBeCalledTimes(0); // disposition not sent since application=abm
      expect(mockDispositionsService.setDisposition).toBeCalledTimes(0); // disposition not sent since application=abm
      expect(next).not.toBeCalled();
    });
    test('should not send `viewed` disposition if anonymous customer request', async () => {
      const mockHttpData = {
        locals: { auth: { token: defaultToken, claims: { scope: 'some.scope' } } }, // invalid scope triggers anonymous request flag
      };
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonMessageRule.id]: mockPigeonMessageRule },
      });
      mockContentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(req, res, next);
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
      expect(mockTargetedCampaignService.getInstance().getCampaign).toBeCalledTimes(0);
      expect(mockTargetedCampaignService.getInstance().setDisposition).toBeCalledTimes(0);
      expect(mockDispositionsService.setDisposition).toBeCalledTimes(0);
      expect(next).not.toBeCalled();
    });
    test('should not send `viewed` disposition to Insights if not new campaign', async () => {
      const mockHttpParams = {
        headers: requestHeaders,
        params: { id: mockPigeonTargetedRule.id },
        query: { message_id: mockInsightsResponse.data.message_id },
      };
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonTargetedRule.id]: mockPigeonTargetedRule },
      });
      const insightsResponse = {
        data: {
          ...mockInsightsResponse.data,
          message_status: 'V',
        },
      };
      mockTargetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(insightsResponse);
      mockContentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(req, res, next);
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
      expect(mockTargetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
      expect(mockTargetedCampaignService.getInstance().setDisposition).toBeCalledTimes(0);
      expect(mockDispositionsService.setDisposition).toBeCalledTimes(1);
      expect(next).not.toBeCalled();
    });
    test('should override with `dismissable=false` if dispositions LD flag is disabled (mass message)', async () => {
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonMessageRule.id]: mockPigeonMessageRule }, // default rule has dismissable_flag: true
      });
      mockContentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
      mockLaunchDarklyService.isFeatureEnabled.mockReturnValue(false); // dispositions db disabled
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
      }, config);
      await endpoint(req, res, next);
      const result = res._getJSONData();
      expect(result.data.viewed).toEqual(true);
      expect(result.data.dismissable).toEqual(false);
    });
    test('should override with `dismissable=false` if dispositions LD flag is disabled (mass campaign)', async () => {
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      const mockPigeonMassCampaignRule = {
        ...mockPigeonMessageRule,
        external_ref: 'MASS',
      };
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonMassCampaignRule.id]: mockPigeonMassCampaignRule },
      });
      mockContentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
      mockLaunchDarklyService.isFeatureEnabled.mockReturnValue(false); // dispositions db disabled
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
      }, config);
      await endpoint(req, res, next);
      const result = res._getJSONData();
      expect(mockLaunchDarklyService.isFeatureEnabled).toBeCalledTimes(1);
      expect(result.data.viewed).toEqual(true);
      expect(result.data.dismissable).toEqual(false);
    });
    test('should log warning if targeted campaign service fails to save disposition', async () => {
      const mockHttpParams = {
        headers: requestHeaders,
        params: { id: mockPigeonTargetedRule.id },
        query: { message_id: mockInsightsResponse.data.message_id },
      };
      const { req, res } = httpMocks.createMocks(mockHttpParams, mockHttpData);
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonTargetedRule.id]: mockPigeonTargetedRule },
      });
      mockTargetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightsResponse);
      mockTargetedCampaignService.getInstance().setDisposition.mockRejectedValueOnce(
        new Error('set disposition call failed due to strike from rogue cosmic ray'),
      );
      mockContentService.getContentByTypeAndId.mockResolvedValueOnce(mockContentResponse);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(req, res, next);
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
      expect(mockTargetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
      expect(mockTargetedCampaignService.getInstance().setDisposition).toBeCalledTimes(1);
      expect(mockDispositionsService.setDisposition).toBeCalledTimes(1);
      expect(next).not.toBeCalled();
      expect(mockLogger.warn).toBeCalledWith({
        message: 'Failed to set disposition in get-campaign handler',
        error: { message: 'set disposition call failed due to strike from rogue cosmic ray' },
        traceId: xB3TraceId,
        spanId: xB3SpanId,
      });
    });
  });

  describe('Authentication', () => {
    test('should authenticate using service 2 service auth and card number', async () => {
      const serviceToken = 'sample.service.token';
      const cardNumber = '****************';
      const channelId = 'ABM';

      const { req, res } = httpMocks.createMocks({
        headers: {
          'Preferred-Environment': 'istgreen',
          Authorization: `Bearer: ${serviceToken}`,
          'x-customer-scotiacard': cardNumber,
          'x-channel-id': channelId,
          'x-b3-traceId': xB3TraceId,
          'x-b3-spanId': xB3SpanId,
        },
        params: { id: 'aaaa0001' },
        query: { message_id: 'DE4CBA6FC0F29381', application: 'abm' },
      }, {
        locals: { auth: {
          token: serviceToken,
          claims: { sub: cardNumber, scope: 'some.scope another.scope' },
        },
        language: 'en',
        languageContentful: 'en-US',
        country: 'CA',
        },
      });

      const mockInsightsResponse = {
        data: {
          message_id: 'DE4CBA6FC0F29381',
          campaign_id: 'PAD01',
          message_source: 'KT',
          message_response: '',
        },
      };
      mockTargetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightsResponse);
      const mockCampaignRule = {
        id: 'aaaa0001',
        external_ref: 'PAD01',
        content_space: 'samplespace',
        content_type: 'creditCardOffer',
        content_id: 'aaaa1111',
        status: 'published',
        disabled: false,
      };
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockCampaignRule.id]: mockCampaignRule },
      });
      const mockContent = { data: { id: 'aaaaaaaa', type: 'creditCardOffer', content: { preview: { title: defaultTitle, message: defaultMessage } } } };
      mockContentService.getContentByTypeAndId.mockResolvedValueOnce(mockContent);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(req, res, next);
      // check insights - get campaign
      expect(mockTargetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
      {
        const ps = mockTargetedCampaignService.getInstance().getCampaign.mock.calls[0];
        expect(ps[0]).toEqual({
          id: 'DE4CBA6FC0F29381',
          userIdentifier: cardNumber,
          channelId,
          language: 'en',
          preferredEnv: 'istgreen',
          useMock: false,
          xOriginatingApplCode: 'BFB6',
          country: 'CA',
          xApplication: undefined,
          traceId: xB3TraceId,
          spanId: xB3SpanId,
        });
      }
      expect(mockTargetedCampaignService.getInstance().setDisposition).toBeCalledTimes(0);
      expect(next).not.toBeCalled();
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
      expect(res.statusCode).toEqual(200);
    });
    test('should authenticate using service 2 service auth and customer token', async () => {
      const serviceToken = 'sample.service.token';
      const cardNumber = '****************';
      const channelId = 'ABM';

      const { req, res } = httpMocks.createMocks({
        headers: {
          'Preferred-Environment': 'istgreen',
          Authorization: `Bearer: ${serviceToken}`,
          'x-customer-authorization': defaultToken,
          'x-channel-id': channelId,
          'x-b3-traceId': xB3TraceId,
          'x-b3-spanId': xB3SpanId,
        },
        params: { id: 'aaaa0001' },
        query: { message_id: 'DE4CBA6FC0F29381', application: 'abm' },
      }, {
        locals: { auth: {
          token: serviceToken,
          claims: { sub: cardNumber, scope: 'some.scope another.scope' },
        },
        language: 'en',
        languageContentful: 'en-US',
        country: 'CA' },
      });

      const mockInsightsResponse = {
        data: {
          message_id: 'DE4CBA6FC0F29381',
          campaign_id: 'PAD01',
          message_source: 'KT',
          message_response: '',
        },
      };
      mockTargetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightsResponse);
      const mockCampaignRule = {
        id: 'aaaa0001',
        external_ref: 'PAD01',
        content_space: 'samplespace',
        content_type: 'creditCardOffer',
        content_id: 'aaaa1111',
        status: 'published',
        disabled: false,
      };
      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockCampaignRule.id]: mockCampaignRule },
      });
      const mockContent = { data: { id: 'aaaaaaaa', type: 'creditCardOffer', content: { preview: { title: defaultTitle, message: defaultMessage } } } };
      mockContentService.getContentByTypeAndId.mockResolvedValueOnce(mockContent);
      mockStatus.mockReturnValueOnce({ json: mockJson });
      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
      }, config);
      await endpoint(req, res, next);
      // check insights - get campaign
      expect(mockTargetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
      {
        const ps = mockTargetedCampaignService.getInstance().getCampaign.mock.calls[0];
        expect(ps[0]).toEqual({
          id: 'DE4CBA6FC0F29381',
          userIdentifier: undefined,
          channelId,
          language: 'en',
          preferredEnv: 'istgreen',
          useMock: false,
          xOriginatingApplCode: 'BFB6',
          country: 'CA',
          xApplication: undefined,
          traceId: xB3TraceId,
          spanId: xB3SpanId,
        });
      }
      expect(mockTargetedCampaignService.getInstance().setDisposition).toBeCalledTimes(0);
      expect(next).not.toBeCalled();
      expect(mockCampaignCacheService.get).toBeCalledTimes(1);
      expect(res.statusCode).toEqual(200);
    });
  });

  describe('Downstream calls parallel execution', () => {
    beforeEach(() => {
      jest.clearAllMocks();
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    test('should call campaign and content service calls in parallel', async () => {
      const { req, res } = httpMocks.createMocks({
        ...mockHttpParams,
        params: { id: mockPigeonTargetedRule.id },
        query: { message_id: 'DE4CBA6FC0F29381' },
      }, mockHttpData);

      mockCampaignCacheService.get.mockReturnValue({
        campaigns: { [mockPigeonTargetedRule.id]: mockPigeonTargetedRule },
      });

      const campaignServiceDelay = 100;
      const contentServiceDelay = 150;
      const startTime = Date.now();

      // Mock both services with delays to simulate network calls
      mockTargetedCampaignService.getInstance().getCampaign.mockImplementation(() => {
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve(mockInsightsResponse);
          }, campaignServiceDelay);
        });
      });

      mockContentService.getContentByTypeAndId.mockImplementation(() => {
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve(mockContentResponse);
          }, contentServiceDelay);
        });
      });

      mockStatus.mockReturnValueOnce({ json: mockJson });

      const endpoint = getCampaign({
        logger: mockLogger,
        campaignCacheService: mockCampaignCacheService,
        contentService: mockContentService,
        targetedCampaignService: mockTargetedCampaignService,
        dispositionsService: mockDispositionsService,
        variablesService: mockVariablesService,
        launchDarklyService: mockLaunchDarklyService,
      }, config);

      const executionPromise = endpoint(req, res, next);

      // Fast-forward timers to simulate both promises resolving
      jest.advanceTimersByTime(Math.max(campaignServiceDelay, contentServiceDelay));

      await executionPromise;

      const endTime = Date.now();
      const totalExecutionTime = endTime - startTime;

      // Verify both services were called
      expect(mockTargetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
      expect(mockContentService.getContentByTypeAndId).toBeCalledTimes(1);

      // In parallel execution, total time should be closer to the longer delay (150ms)
      // rather than sum of both delays (250ms)
      expect(totalExecutionTime).toBeLessThan(campaignServiceDelay + contentServiceDelay);
      expect(next).not.toBeCalled();
      expect(res.statusCode).toEqual(200);
    });
  });
});
