/* eslint-disable quotes */
const R = require('ramda');
const Joi = require('@hapi/joi');
const crypto = require('crypto');
const escapeHtml = require('escape-html');
const dotProp = require('dot-prop');
const { HttpError } = require('../../errors');
const { dispositionPathSchema, dispositionBodySchema } = require('./validation');
const {
  priorityMessageCampaignId,
  massCampaignId,
  isTargetedCampaign,
  getDispositionValue,
} = require('./common');
const { checkStandardScope, calcTargetedCampaignExpiry, generateMessageIdsPega } = require('../../helpers');
const {
  dispositionInsights,
  shouldStoreDispositionToDb,
  dispositionValues,
  messageSourceArr,
  validateCampaignRuleType,
} = require('../../common');
const splunkErrorCodes = require('./common').splunkErrorCodes;

const msgValidationError = 'Validation error';
const msgCampaignNotFound = 'Campaign not found';

// check for scotia card that starts with 4 and 16 digits long
// eslint-disable-next-line security/detect-unsafe-regex
const cardValidationRegex = /^(?:4[0-9]{12}(?:[0-9]{3})?)$/;

const getTargetedCampaign = async (targetedCampaignService, rule, opts, logger, headerData) => {
  const { customerToken, userIdentifier, channelId, xApplication, language, preferredEnv, useMock, spanId, traceId, xOriginatingApplCode, country, xFeatureFlagUid } = headerData;
  try {
    const getCampaignResponse = await targetedCampaignService.getInstance({ ruleType: rule.type }).getCampaign({ id: opts.message_id,
      customerToken,
      userIdentifier,
      channelId,
      xApplication,
      language,
      preferredEnv,
      useMock,
      spanId,
      traceId,
      xOriginatingApplCode,
      country,
      xFeatureFlagUid,
    });
    const targetedCampaign = getCampaignResponse.data;
    if (!targetedCampaign) {
      throw HttpError.notFound('Not found', [ { message: 'Campaign with specified message_id is not found' } ]);
    }
    if (!targetedCampaign.campaign_id || targetedCampaign.campaign_id !== rule.external_ref) {
      throw HttpError.badRequest(msgValidationError, [ { message: 'Invalid message_id' } ]);
    }
    logger.info({ message: 'Successfully retrieved  downstream targeted campaign', data: { message_id: opts.message_id } });
    return targetedCampaign;
  } catch (err) {
    if (err instanceof HttpError && err.statusCode < 500) {
      // known error
      throw err;
    }
    const originalError = R.pipe(R.pathOr([], [ 'response', 'notifications' ]), R.head)(err);
    if (originalError && originalError.code === 'INSIGHT-001-E') {
      throw HttpError.notFound('Not found', [ {
        message: `Campaign with specified message_id is not found`,
        notifications: R.pathOr([], [ 'response', 'notifications' ], err),
      } ]);
    }
    logger.error({ message: 'Failed to get downstream targeted campaign', err, message_id: escapeHtml(opts.message_id), code: 1009, description: splunkErrorCodes[1009] });

    throw HttpError.internalServer('Internal Server Error', [ {
      message: `Failed to get downstream targeted campaign`,
      notifications: R.pathOr([], [ 'response', 'notifications' ], err),
    } ]);
  }
};

const setTargetedCampaignDisposition = async (targetedCampaignService, opts, disposition, logger, headerData) => {
  const { customerToken, userIdentifier, channelId, xApplication, language, preferredEnv, useMock, spanId, traceId, xOriginatingApplCode, country, xFeatureFlagUid } = headerData;
  try {
    await targetedCampaignService.getInstance({ ruleType: headerData.type }).setDisposition({ id: opts.message_id,
      disposition,
      customerToken,
      userIdentifier,
      channelId,
      xApplication,
      language,
      preferredEnv,
      useMock,
      spanId,
      traceId,
      xOriginatingApplCode,
      country,
      xFeatureFlagUid,
    });
    logger.info({ message: 'Successfully set downstream campaign disposition', data: { message_id: opts.message_id, disposition } });
  } catch (err) {
    const originalError = R.pipe(R.pathOr([], [ 'response', 'notifications' ]), R.head)(err);
    if (originalError && originalError.code === 'INSIGHT-003-E') {
      throw HttpError.badRequest(msgValidationError, [ { message: 'Invalid disposition sent to insights', notifications: R.pathOr([], [ 'response', 'notifications' ], err) } ]);
    }
    logger.error({ message: 'Failed to set downstream campaign disposition', err, message_id: escapeHtml(opts.message_id) });
    throw HttpError.internalServer('Server error', [ { message: 'Failed to set downstream campaign disposition', error: err.message, notifications: R.pathOr([], [ 'response', 'notifications' ], err) } ]);
  }
};

const setDbDisposition = async (dispositionsService, rule, opts, targetedCampaign, userIdentifier, country) => {
  const expiryDate = calcTargetedCampaignExpiry(rule.end_at, targetedCampaign.expiry_date);
  // infer values for container & page if not sent in request
  const body = {
    message_id: opts.message_id && opts.message_id !== rule.id ? opts.message_id : null,
    container: opts.container || rule.container,
    page: opts.page || (rule.pages && rule.pages.length === 1 ? rule.pages[0] : null),
    platform: opts.platform,
    application: opts.application,
  };

  // Generate old message id
  if (messageSourceArr.includes(targetedCampaign.message_source)) {
    const oldMessageId = generateMessageIdsPega(targetedCampaign.campaign_id, targetedCampaign.language, targetedCampaign.message_source);
    if (oldMessageId !== targetedCampaign.message_id) {
      body.old_message_id = oldMessageId;
    }
  }
  await dispositionsService.setDisposition(userIdentifier, country, rule.id, expiryDate, opts.disposition, body);
};

// eslint-disable-next-line sonarjs/cognitive-complexity
const setDispositionCampaign = (deps, config) => async (req, res, next) => {
  const {
    logger,
    dispositionsService,
    targetedCampaignService,
    campaignCacheService,
  } = deps;
  // validate input parameters
  let { error, value: opts } = Joi.validate(req.params, dispositionPathSchema, { stripUnknown: true });
  if (error) {
    next(HttpError.badRequest(msgValidationError, error.details.map((err) => ({ path: err.path.join('.'), message: err.message }))));
    return;
  }
  const ruleId = opts.id;
  ({ error, value: opts } = Joi.validate(req.body, dispositionBodySchema, { stripUnknown: true }));
  if (error) {
    next(HttpError.badRequest(msgValidationError, error.details.map((err) => ({ path: err.path.join('.'), message: err.message }))));
    return;
  }

  // get the rule
  let rule;
  try {
    rule = R.clone(R.pathOr({}, [ 'campaigns', ruleId ], campaignCacheService.get()));
    if (rule && !Object.keys(rule).length) {
      next(HttpError.notFound(msgCampaignNotFound, [ { message: 'campaign rule does not exist' } ]));
      return;
    }
  } catch (err) {
    next(err);
    return;
  }

  // check if message_id is required and present
  if (rule.external_ref && (![ massCampaignId, priorityMessageCampaignId ].includes(rule.external_ref)) && !opts.message_id) {
    next(HttpError.badRequest(msgValidationError, [ { message: 'message_id is required' } ]));
    return;
  }
  // check if the request was authenticated with customer token
  const isCustomerTokenAuth = checkStandardScope(dotProp.get(res.locals, `${config.tokenClaimsPath}.scope`));
  // get request's token if it's a customer token
  const customerToken = isCustomerTokenAuth ? dotProp.get(res.locals, config.tokenPath) : req.get('x-customer-authorization');
  // get request's language
  const language = R.pathOr('en', [ 'language' ], res.locals);
  const preferredEnv = req.get('Preferred-Environment');
  // get pigeon mocked insight service flag for testing
  const mockedInsight = req.get('X-Mock-Insight') !== undefined;
  const country = req.get('x-country-code') || 'CA';
  // ccau user identifier
  const xUserContext = req.get('x-user-context');
  // get fid flag for Insights pilot testing - PIGEON-5412
  const xFeatureFlagUid = req.get('x-feature-flag-uid');

  const { error: ruleTypeError } = validateCampaignRuleType(country, rule.type);
  if (ruleTypeError) {
    return next(HttpError.badRequest(ruleTypeError));
  }

  // get scotia card number
  let cardNumber;
  if (isCustomerTokenAuth && dotProp.get(res.locals, `${config.tokenClaimsPath}.profile.customerInfo.cardNumber`)) {
    cardNumber = dotProp.get(res.locals, `${config.tokenClaimsPath}.profile.customerInfo.cardNumber`);
  } else if (isCustomerTokenAuth && cardValidationRegex.test(dotProp.get(res.locals, `${config.tokenClaimsPath}.sub`))) {
    cardNumber = dotProp.get(res.locals, `${config.tokenClaimsPath}.sub`);
  } else {
    cardNumber = req.get('x-customer-scotiacard');
  }

  if (!cardNumber && !xUserContext) {
    logger.info({ message: 'Card number or x-user-context identifier does not exists for this user - set-campaign-disposition.js' });
  }

  // get channel id
  let channelId = req.get('x-channel-id');
  if (!channelId) {
    if (opts.application === 'nova' || opts.application === 'wave') {
      channelId = 'Mobile';
    } else if (opts.application === 'abm') {
      channelId = 'ABM';
    } else {
      channelId = 'Online';
    }
  }

  // get user agent
  let userAgent = req.get('User-Agent');
  // send the x-application value
  let xApplication = req.get('x-application');
  if (!xApplication && channelId && channelId === 'Mobile') {
    userAgent = userAgent && (userAgent.indexOf('iPhone') > -1 || opts.platform === 'ios') ? 'iPhone' : 'Android';
    xApplication = userAgent === 'iPhone' ? 'N1' : 'N2';
  }

  const anonymousRequestFlag = !customerToken && !cardNumber && !xUserContext;
  if (anonymousRequestFlag) {
    next(HttpError.badRequest('set disposition is supported only for authenticated customers'));
    return;
  }

  // get spand id
  const spanId = req.get('x-b3-spanid') || crypto.randomBytes(8).toString('hex');
  // get trace id
  const traceId = req.get('x-b3-traceid') || crypto.randomBytes(16).toString('hex');
  // get x-originating-appl-code
  const xOriginatingApplCode = req.get('x-originating-appl-code') || 'BFB6';

  const userIdentifier = cardNumber || xUserContext;
  let targetedCampaign = {};
  if (isTargetedCampaign(rule) && opts.message_id !== rule.id) {
    try {
      targetedCampaign = await getTargetedCampaign(targetedCampaignService, rule, opts, logger, {
        customerToken,
        userIdentifier,
        channelId,
        xApplication,
        language,
        preferredEnv,
        useMock: mockedInsight,
        spanId,
        traceId,
        xOriginatingApplCode,
        country,
        xFeatureFlagUid,
      });
    } catch (err) {
      next(err);
      return;
    }
  }

  // update dispositions db and insights in parallel, wait until both resolve before returning call
  const promises = [];

  // save to Pigeon DB if required
  const isStoreToDatabase = shouldStoreDispositionToDb(rule.dismissable_flag, opts.disposition);
  if (isStoreToDatabase) {
    promises.push(setDbDisposition(dispositionsService, rule, opts, targetedCampaign, userIdentifier, country));
  }

  // only update insights if targeted campaign & sent disposition is different than current
  if (isTargetedCampaign(rule) && opts.message_id !== rule.id) {
    const isValid = !rule.dismissable_flag && opts.disposition === 'D' ? true : dispositionInsights.includes(opts.disposition);
    const dispositionValue = opts.disposition === dispositionValues.dismissed ? dispositionValues.viewed : opts.disposition;
    const isChanged = getDispositionValue(targetedCampaign) !== dispositionValue;

    if (isValid && isChanged) {
      promises.push(setTargetedCampaignDisposition(targetedCampaignService, opts, dispositionValue, logger, {
        customerToken,
        userIdentifier,
        channelId,
        xApplication,
        language,
        preferredEnv,
        useMock: mockedInsight,
        spanId,
        traceId,
        xOriginatingApplCode,
        country,
        type: rule.type,
        xFeatureFlagUid,
      }));
    }
  }

  try {
    await Promise.all(promises);
    res.status(200).json({ data: {}, notifications: [] });
  } catch (err) {
    next(err);
  }
};

module.exports = setDispositionCampaign;
