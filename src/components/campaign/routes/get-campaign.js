const R = require('ramda');
const { cloneDeep } = require('lodash');
const Joi = require('@hapi/joi');
const escapeHtml = require('escape-html');
const promiseAllSettled = require('promise.allsettled');
const crypto = require('crypto');

const { HttpError } = require('../../errors');
const {
  getCampaignPathSchema,
  getCampaignSchema,
} = require('./validation');
const {
  addExternalRef,
  priorityMessageCampaignId,
  massCampaignId,
  isCampaignViewed,
  isTargetedCampaign,
} = require('./common');
const {
  generateMessageIdsPega,
  getClientInfo,
  calcTargetedCampaignExpiry,
  getChannelId,
  getAgentUserAndXApplication,
  getEmbedded,
} = require('../../helpers');
const { APPS_NO_VIEWED_DISP } = require('../../helpers/constants');
const { dispositionValues, messageSourceArr, validateCampaignRuleType } = require('../../common');

const msgValidationError = 'Validation error';
const msgCampaignNotFound = 'Campaign not found';
const logFile = 'get-campaign.js';
const splunkErrorCodes = require('./common').splunkErrorCodes;

const setDbDisposition = async (dispositionsService, rule, opts, targetedCampaignData, userId, country) => {
  const expiryDate = calcTargetedCampaignExpiry(rule.end_at, targetedCampaignData.expiry_date);

  const body = {
    message_id: opts.message_id && opts.message_id !== rule.id ? opts.message_id : null,
    container: rule.container,
    page: rule.pages && rule.pages.length === 1 ? rule.pages[0] : null,
    platform: rule.platforms && rule.platforms.length === 1 ? rule.platforms[0] : null,
    application: rule.application,
  };

  // Generate old message id
  if (messageSourceArr.includes(targetedCampaignData.message_source)) {
    const oldMessageId = generateMessageIdsPega(targetedCampaignData.campaign_id, targetedCampaignData.language, targetedCampaignData.message_source);
    if (oldMessageId !== targetedCampaignData.message_id) {
      body.old_message_id = oldMessageId;
    }
  }

  await dispositionsService.setDisposition(userId, country, rule.id, expiryDate, dispositionValues.viewed, body);
};

// eslint-disable-next-line sonarjs/cognitive-complexity
const getCampaign = (deps, config) => async (req, res, next) => {
  const {
    logger,
    contentService,
    targetedCampaignService,
    variablesService,
    campaignCacheService,
    dispositionsService,
    launchDarklyService,
  } = deps;
  // ========== Joi validation ========== //
  let { error, value: opts } = Joi.validate(req.params, getCampaignPathSchema, { stripUnknown: true });
  if (error) {
    next(HttpError.badRequest(msgValidationError, error.details.map((err) => {
      return {
        path: err.path.join('.'),
        message: err.message,
      };
    })));
    return;
  }
  const ruleId = opts.id;
  ({ error, value: opts } = Joi.validate(req.query, getCampaignSchema, { stripUnknown: true }));
  if (error) {
    next(HttpError.badRequest(msgValidationError, error.details.map((err) => {
      return {
        path: err.path.join('.'),
        message: escapeHtml(err.message),
      };
    })));
    return;
  }

  // ========== Get campaign rule & validate ========== //
  let rule;
  try {
    rule = cloneDeep(R.pathOr({}, [ 'campaigns', ruleId ], campaignCacheService.get()));
    if (rule.status !== 'published' || rule.disabled) {
      next(HttpError.notFound(msgCampaignNotFound));
      return;
    }
    const now = Date.now();
    const startAt = Date.parse(rule.start_at);
    const endAt = Date.parse(rule.end_at);
    if (now < startAt || now > endAt) {
      next(HttpError.notFound(msgCampaignNotFound));
      return;
    }
  } catch (err) {
    if (err.response && err.response.code === 'HTTP_NOT_FOUND') {
      next(HttpError.notFound(msgCampaignNotFound));
    } else {
      next(err);
    }
    return;
  }

  // check if message_id is required and present
  if (rule.external_ref && isTargetedCampaign(rule) && !opts.message_id) {
    next(HttpError.badRequest(msgValidationError, [ {
      message: 'message_id is required',
    } ]));
    return;
  }

  // ========== Get params/headers ========== //
  // TODO: Implement a header processing middleware to store headers and pass down to other requests
  const spanId = req.get('x-b3-spanid') || crypto.randomBytes(8).toString('hex');
  const traceId = req.get('x-b3-traceid') || crypto.randomBytes(16).toString('hex');
  const sessionId = req.get('x-session-id');
  const xOriginatingApplCode = req.get('x-originating-appl-code') || 'BFB6';
  const language = R.pathOr('en', [ 'language' ], res.locals);
  // get pigeon mocked Insight service flag for testing
  const mockedInsight = req.get('X-Mock-Insight') !== undefined;
  const preferredEnv = req.get('Preferred-Environment');
  // get fid flag for Insights pilot testing - PIGEON-5412
  const xFeatureFlagUid = req.get('x-feature-flag-uid');
  const channelId = getChannelId({ req, application: opts.application || rule.application });
  const embedded = getEmbedded({ req });
  const { xApplication } = getAgentUserAndXApplication({ req, channelId, query: opts });
  const {
    country,
    customerToken,
    uid,
    anonymousRequestFlag,
  } = getClientInfo({ req, res, config });

  if (!uid) {
    logger.warn({
      message: 'Card number and user context both do not exists for this user - get-campaign.js',
      traceId,
      spanId,
      xOriginatingApplCode,
    });
  }

  const { error: ruleTypeError } = validateCampaignRuleType(country, rule.type);
  if (ruleTypeError) {
    return next(HttpError.badRequest(ruleTypeError));
  }

  // check for language targetting
  const targetedLanguages = R.pathOr(null, [ 'mass_targeting', 'languages' ], rule);
  if (targetedLanguages && !targetedLanguages.includes(language)) {
    next(HttpError.notFound(msgCampaignNotFound, [ {
      message: `campaign for specified language is not found`,
    } ]));
    return;
  }

  const promisesMap = new Map();
  const servicesCalled = [];
  const isMassMessageOrCampaign = [ massCampaignId, priorityMessageCampaignId ].includes(rule.external_ref);

  // ========== Get targeted campaign data ========== //
  if (isMassMessageOrCampaign) {
    // mass message or mass campaign
    rule.external_ref = {
      campaign_id: rule.external_ref,
      message_id: rule.id,
      message_source: 'DMS',
    };
  } else if (!anonymousRequestFlag && rule.external_ref) {
    promisesMap.set('campaign', targetedCampaignService.getInstance({ ruleType: rule.type }).getCampaign({
      channelId,
      country,
      id: opts.message_id,
      language,
      preferredEnv,
      spanId,
      traceId,
      useMock: mockedInsight,
      userIdentifier: uid,
      xApplication,
      xOriginatingApplCode,
      xFeatureFlagUid,
    }));
    servicesCalled.push('campaign');
  }

  // ========== Fetch Contentful content ========== //
  promisesMap.set('content', contentService.getContentByTypeAndId(
    rule.content_space,
    rule.content_type,
    rule.content_id,
    { select: opts.select_contents, language: res.locals.languageContentful },
  ));
  servicesCalled.push('content');

  const promiseResults = await promiseAllSettled(Array.from(promisesMap.values()));
  const campaignIndex = servicesCalled.indexOf('campaign');
  const contentIndex = servicesCalled.indexOf('content');
  const campaignResponse = promiseResults[campaignIndex];
  const contentResponse = promiseResults[contentIndex];

  let contentFullContent;
  if (contentResponse && contentResponse.status === 'fulfilled') {
    if (contentResponse.value) {
      contentFullContent = contentResponse.value.data;
      rule.type = contentFullContent.type;
    }
  } else {
    const err = contentResponse.reason;
    logger.error({
      message: 'failed to get content',
      err,
      rule,
      code: 1011,
      description: splunkErrorCodes[1011],
    });
    next(err);
    return;
  }

  let targetedCampaignData = {};
  if (!isMassMessageOrCampaign && !anonymousRequestFlag && rule.external_ref) {
    if (campaignResponse && campaignResponse.status === 'fulfilled') {
      targetedCampaignData = campaignResponse.value.data;
      if (!targetedCampaignData) {
        next(HttpError.notFound('Not found', [ {
          message: `campaign with specified message_id is not found`,
        } ]));
        return;
      }
      if (!targetedCampaignData.campaign_id || targetedCampaignData.campaign_id !== rule.external_ref) {
        next(HttpError.badRequest(msgValidationError, [ {
          message: 'invalid message_id',
        } ]));
        return;
      }
      rule = addExternalRef(rule, [ targetedCampaignData ]);
    } else {
      const err = campaignResponse.reason;
      const originalError = R.pipe(R.pathOr([], [ 'response', 'notifications' ]), R.head)(err);
      if (originalError && originalError.code === 'INSIGHT-001-E') {
        next(HttpError.notFound('Not found', [ {
          message: `campaign with specified message_id is not found`,
          notifications: R.pathOr([], [ 'response', 'notifications' ], err),
        } ]));
        return;
      }
      logger.error({
        message: 'failed to get downstream campaign',
        err,
        code: 1009,
        description: splunkErrorCodes[1009],
        notifications: R.pathOr([], [ 'response', 'notifications' ], err),
        params: {
          message_id: opts.message_id,
          ruleType: rule.type,
          ruleId,
        },
      });
      next(HttpError.internalServer('Internal Server Error', [ {
        message: `error calling downstream service to get the campaign`,
        notifications: R.pathOr([], [ 'response', 'notifications' ], err),
      } ]));
      return;
    }
  }

  try {
    // ========== Replace Contentful variables ========== //
    const variableTransformationHeaders = {
      channelId,
      xApplication,
      preferredEnv,
      sessionId,
      xCustomerScotiacard: uid,
      spanId,
      traceId,
      xOriginatingApplCode,
      embedded,
    };
    try {
      const variables = await variablesService(
        preferredEnv,
        customerToken,
        contentFullContent.content,
        targetedCampaignData,
        rule,
        res.locals.language,
        variableTransformationHeaders,
      );
      rule.content = variables.content;
      if (opts.insight) {
        rule.external_ref = {
          ...rule.external_ref,
          data: targetedCampaignData,
          data_context: variables.dataContext,
          data_transformed: variables.transformed,
        };
      }
    } catch (err) {
      logger.error({
        message: 'failed to transform variables',
        err,
        code: 1013,
        description: splunkErrorCodes[1013],
      });
      // return whatever was there
      rule.content = contentFullContent.content;
      if (opts.insight) {
        rule.external_ref = {
          ...rule.external_ref,
          data: targetedCampaignData,
          data_context: 'unknown',
          data_transformed: {},
        };
      }
    }
  } catch (err) {
    logger.error({
      message: 'failed to get content',
      err,
      rule,
      code: 1011,
      description: splunkErrorCodes[1011],
    });
    next(err);
    return;
  }

  // ========== Set viewed disposition ========== //
  const sendDispositionFlag = !anonymousRequestFlag && !APPS_NO_VIEWED_DISP.includes(rule.application);
  if (sendDispositionFlag) {
    // update disposition in dispositions db and targeted campaign service in parallel
    const promises = [];
    promises.push(setDbDisposition(
      dispositionsService,
      rule,
      opts,
      targetedCampaignData,
      uid,
      country,
    ));
    const isTargetedCampaign = opts.message_id && opts.message_id !== rule.id;
    if (isTargetedCampaign && !isCampaignViewed(targetedCampaignData)) {
      // send 'Viewed' status to insights if required
      // TODO for the future - replace it with a queue, so it will not affect response time
      promises.push(targetedCampaignService.getInstance({ ruleType: rule.type }).setDisposition({
        channelId,
        disposition: dispositionValues.viewed,
        id: opts.message_id,
        language,
        preferredEnv,
        spanId,
        traceId,
        useMock: mockedInsight,
        userIdentifier: uid,
        xApplication,
        xOriginatingApplCode,
        country,
        xFeatureFlagUid,
      }));
    }

    const setDispositionPromises = await promiseAllSettled(promises);
    setDispositionPromises.forEach(p => {
      if (p.status === 'rejected') {
        logger.warn({
          message: 'Failed to set disposition in get-campaign handler',
          error: { message: p.reason.message },
          ...(traceId && { traceId }),
          ...(spanId && { spanId }),
        });
      }
    });
  }

  const viewedState = sendDispositionFlag || isCampaignViewed(targetedCampaignData);

  let isDispositionsDbEnabled = true;
  try {
    isDispositionsDbEnabled = await launchDarklyService.isFeatureEnabled('pigeon-api.features.dispositions.db', true);
  } catch (err) {
    logger.error({
      message: 'unable to call Launch Darkly for dispositions db from get-campaigns',
      err,
    });
  }
  const dismissableState = isDispositionsDbEnabled ? rule.dismissable_flag : false;

  // Return start date
  let recentDate = rule.start_at;
  if (new Date(rule.start_at) < new Date(targetedCampaignData.start_date)) {
    recentDate = targetedCampaignData.start_date;
  };
  const response = {
    data: {
      id: rule.id,
      name: rule.name,
      start_date: recentDate,
      type: rule.type,
      container: rule.container,
      urgent: rule.urgent,
      viewed: viewedState,
      dismissable: dismissableState,
      external_ref: rule.external_ref,
      content: rule.content,
      application: rule.application,
    },
    notifications: [],
  };
  logger.info({
    message: 'response to client',
    logFile,
    logType: 'response to client',
    response: {
      ...response,
      data: {
        ...response.data,
        external_ref: {
          ...response.data.external_ref,
          ...(R.path([ 'data', 'external_ref', 'data', 'additional_data' ], response) && {
            data: {
              ...response.data.external_ref.data,
              additional_data: null,
            },
          }),
          ...(R.path([ 'data', 'external_ref', 'data_transformed' ], response) && {
            data_transformed: null,
          }),
        },
        content: null,
      },
    },
    request: {
      headers: {
        'x-b3-traceid': req.get('x-b3-traceid'),
      },
    },
  });
  res.status(200).json(response);
};

module.exports = getCampaign;
