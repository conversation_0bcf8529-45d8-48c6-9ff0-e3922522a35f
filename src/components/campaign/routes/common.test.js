const common = require('./common');

describe('Campaign Routes common', () => {
  test('should have addExternalRef', () => {
    expect(common).toHaveProperty('addExternalRef');
    expect(typeof common.addExternalRef).toEqual('function');
  });
  test('should add external ref data', () => {
    const mockInsights = [
      { message_id: 'ABCDEF000001', campaign_id: 'PAD01', message_source: 'KT' },
      { message_id: 'ABCDEF000002', campaign_id: 'PAD03', message_source: 'KT' },
    ];
    const mockRule = { id: 'abcd1234', external_ref: 'PAD03' };
    const result = common.addExternalRef(mockRule, mockInsights);
    expect(typeof mockRule.external_ref).toEqual('string');
    expect(result).toHaveProperty('external_ref');
    expect(typeof result.external_ref).toEqual('object');
    expect(result.external_ref).toEqual(mockInsights[1]);
  });
  test('should skip adding external ref data', () => {
    const mockRule = { id: 'abcd1234', external_ref: 'PAD03' };
    const result = common.addExternalRef(mockRule, []);
    expect(typeof mockRule.external_ref).toEqual('string');
    expect(result).toHaveProperty('external_ref');
    expect(typeof result.external_ref).toEqual('object');
    expect(Object.keys(result.external_ref).length).toEqual(0);
  });
  test('should have addContentPreview', () => {
    expect(common).toHaveProperty('addContentPreview');
    expect(typeof common.addContentPreview).toEqual('function');
  });
  test('should add content preview', () => {
    const mockContent = { id: 'aaaaaaaa', type: 'creditCardOffer', content: { preview: { title: 'some title', message: 'some message' } } };
    const mockRule = { id: 'abcd1234', container: 'my-activity', external_ref: 'PAD01' };
    const result = common.addContentPreview(mockRule, mockContent);
    expect(mockRule).not.toHaveProperty('content');
    expect(result).toHaveProperty('content');
    expect(result.type).toEqual(mockContent.type);
    expect(result.content).toEqual(mockContent.content.preview);
  });
  test('should have addContentDetails', () => {
    expect(common).toHaveProperty('addContentDetails');
    expect(typeof common.addContentPreview).toEqual('function');
  });
  test('should add content details', () => {
    const mockContent = { id: 'aaaaaaaa', type: 'creditCardOffer', content: { details: { title: 'some title', message: 'some message' } } };
    const mockRule = { id: 'abcd1234', container: 'my-activity', external_ref: 'PAD01' };
    const result = common.addContentDetails(mockRule, mockContent);
    expect(mockRule).not.toHaveProperty('content');
    expect(result).toHaveProperty('content');
    expect(result.type).toEqual(mockContent.type);
    expect(result.content).toEqual(mockContent.content.details);
  });

  test('should log LD error', () => {
    const err = new Error('Failed to call LD');
    const flag = 'pigeon-api.features.dispositions.db';
    const caller = 'get-campaigns';
    const logger = { error: jest.fn() };
    common.handleLDError({ err, flag, caller, logger });
    expect(logger.error).toHaveBeenCalledWith({
      message: 'Error calling Launch Darkly',
      details: { err, flag, caller },
    });
  });

  describe('get viewed disposition value from Insight response', () => {
    test('should return a legacy value using message_response property', () => {
      const mock = { message_response: 'V' };
      expect(common.getDispositionValue(mock)).toEqual('V');
    });
    test('should return a new value using message_status property', () => {
      const mock = { message_status: 'V' };
      expect(common.getDispositionValue(mock)).toEqual('V');
    });
    test('should return a new value using message_status property even when message_response is still present', () => {
      const mock = { message_response: 'A', message_status: 'V' };
      expect(common.getDispositionValue(mock)).toEqual('V');
    });
    test('should return an empty string when both message_status and message_response are not present', () => {
      const mock = { };
      expect(common.getDispositionValue(mock)).toEqual('');
    });
  });
});
