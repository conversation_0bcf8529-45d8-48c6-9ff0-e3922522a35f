/* eslint-disable sonarjs/no-identical-functions */
const Joi = require('@hapi/joi');
const R = require('ramda');
const escapeHtml = require('escape-html');
const dotProp = require('dot-prop');

const { getCampaignPathSchema, getSamlTokenSchema } = require('./validation');
const { HttpError } = require('../../errors');
const { checkStandardScope } = require('../../helpers');
const { isMortgageRenewal } = require('../../variables/transform-kt/transform-dynamic/targeted-campaign/mortgage-renewal');
const { isMortgageRenewalV2 } = require('../../variables/transform-pega-v2/campaigns');
const { isStepCli } = require('../../variables/transform-kt/transform-dynamic/targeted-campaign/step-cli');
const { isStepAcquisition } = require('../../variables/transform-kt/transform-dynamic/targeted-campaign/step-acquisition');
const { getAdditionalDataValue } = require('../../variables/helpers');
const { isCeba5 } = require('../../variables/transform-kt/transform-dynamic/targeted-campaign/ceba5');
const { isPreapprovedCCSB } = require('../../variables/transform-kt/transform-dynamic/targeted-campaign/preapproved-cc-sb');

// check for scotia card that starts with 4 and 16 digits long
// eslint-disable-next-line security/detect-unsafe-regex
const cardValidationRegex = /^(?:4[0-9]{12}(?:[0-9]{3})?)$/;

const generateSamlPayload = R.curry((exitUrl, cardNumber, cid, language, applicationInfo, samlAttributes = []) => ({
  issuer_name: 'www.scotiaonline.scotiabank.com',
  audience_uri: 'www.scotiaonline.scotiabank.com',
  name_id: cardNumber,
  saml_attributes: [
    // // { attribute_friendly_name: 'EXIT_URL', attribute_name: 'exitURL', attribute_value: exitUrl },
    ...samlAttributes,
    { attribute_friendly_name: 'SYSINFO_CI', attribute_name: 'CID', attribute_value: cid },
    { attribute_friendly_name: 'SYSINFO_CI', attribute_name: 'CSS', attribute_value: cid },
    { attribute_friendly_name: 'LANGUAGE', attribute_name: 'languageCode', attribute_value: language },
    { attribute_friendly_name: 'saml', attribute_name: 'saml', attribute_value: applicationInfo },
  ],
}));

const generateMortgageRenewalPayload = (campaign) => {
  const get = getAdditionalDataValue(campaign);
  return {
    info: {
      applicationInfo: {
        from: 'pigeon',
        additionalFlowInfo: {
          ApplicationType: 'mtgrenew',
          MortgageNumber: get('ACCT'),
        },
      },
    },
  };
};

const generateMortgageRenewalPayloadV2 = (campaign) => {
  const get = getAdditionalDataValue(campaign);
  return {
    info: {
      applicationInfo: {
        from: 'pigeon',
        additionalFlowInfo: {
          ApplicationType: 'mtgrenew',
          MortgageNumber: get('current_account_number'),
        },
      },
    },
  };
};

const generateStepCliPayload = (campaign) => {
  const get = getAdditionalDataValue(campaign);
  return {
    info: {
      applicationInfo: {
        from: 'pigeon',
        additionalFlowInfo: {
          ApplicationType: 'stepcli',
          CLIProductCode: get('OTHER1'),
          CLISubProductCode: get('OTHER3'),
          CLIApprovedCreditLimit: get('CREDIT'),
          CLIOfferExpiryDate: get('OTHER2'),
          CLICurrentAccountNumber: get('ACCT'),
        },
      },
    },
  };
};

const generateStepAcquisitionPayload = (campaign) => {
  const get = getAdditionalDataValue(campaign);
  return {
    info: {
      applicationInfo: {
        from: 'pigeon',
        additionalFlowInfo: {
          ApplicationType: 'newscl',
        },
      },
      campaignsInfo: {
        loginMessages: [
          {
            displayParameters: [
              { key: 'CREDIT', value: get('CREDIT') },
              { key: 'OTHER1', value: get('OTHER1') },
              { key: 'OTHER2', value: get('OTHER2') },
              { key: 'OTHER3', value: get('OTHER3') },
              { key: 'OTHER4', value: get('OTHER4') },
              { key: 'OTHER7', value: get('OTHER7') },
              { key: 'OTHER8', value: get('OTHER8') },
            ],
          },
        ],
      },
    },
  };
};

const getCID = R.path([ 'profile', 'cid' ]);

const generateCeba5Payload = (claims, campaign) => {
  const application = R.pathOr('N1', [ 'client_metadata', 'application' ], claims);
  const cid = getCID(claims);
  const custSysInfo = R.pathOr([], [ 'profile', 'customerInfo', 'custSysInfo' ], claims);
  const bidResult = custSysInfo.find((sysItem) => {
    if (sysItem.sysId === 'CI' && sysItem.infoId.length > 0) {
      return sysItem.infoId.find(infoItem => {
        if (infoItem.type === 'BID') {
          return infoItem;
        }
      });
    }
  });
  const bid = bidResult.infoId[0].id;
  const samlAttributes = [
    { attribute_friendly_name: 'BID', attribute_name: 'BusinessID', attribute_value: bid }, // comes from cutomer token playload - sysId: CID -> infold/type:BID
    { attribute_friendly_name: 'CID', attribute_name: 'CustomerID', attribute_value: cid },
    { attribute_friendly_name: 'PRODUCT', attribute_name: 'Product', attribute_value: 'CEBALOC' },
    { attribute_friendly_name: 'FLOWCODE', attribute_name: 'FlowCode', attribute_value: 'PA' },
    { attribute_friendly_name: 'APPLICATIONID', attribute_name: 'ApplicationID', attribute_value: application }, // update this based on request platform
    { attribute_friendly_name: 'CHANNEL', attribute_name: 'ChannelID', attribute_value: 'MB' },
    { attribute_friendly_name: 'KTMESSAGEID', attribute_name: 'ktMessageID', attribute_value: campaign.message_id }, // craeted based on MessageID from insights - 'message_id'
  ];
  return {
    samlAttributes,
  };
};

const generatePreApprovedCCSBPayload = (claims, campaign) => {
  const application = R.pathOr('N1', [ 'client_metadata', 'application' ], claims);
  const cid = getCID(claims);
  const custSysInfo = R.pathOr([], [ 'profile', 'customerInfo', 'custSysInfo' ], claims);

  const bidResult = custSysInfo.find((sysItem) => {
    if (sysItem.sysId === 'CI' && sysItem.infoId.length > 0) {
      return sysItem.infoId.find(infoItem => {
        if (infoItem.type === 'BID') {
          return infoItem;
        }
      });
    }
  });
  const bid = bidResult.infoId[0].id;
  const samlAttributes = [
    { attribute_friendly_name: 'BID', attribute_name: 'BusinessID', attribute_value: bid }, // comes from cutomer token playload - sysId: CID -> infold/type:BID
    { attribute_friendly_name: 'CID', attribute_name: 'CustomerID', attribute_value: cid },
    { attribute_friendly_name: 'PRODUCT', attribute_name: 'Product', attribute_value: 'CEBALOC' },
    { attribute_friendly_name: 'FLOWCODE', attribute_name: 'FlowCode', attribute_value: 'PA' },
    { attribute_friendly_name: 'APPLICATIONID', attribute_name: 'ApplicationID', attribute_value: application }, // update this based on request platform
    { attribute_friendly_name: 'CHANNEL', attribute_name: 'ChannelID', attribute_value: 'MB' },
    { attribute_friendly_name: 'KTMESSAGEID', attribute_name: 'ktMessageID', attribute_value: campaign.message_id }, // created based on MessageID from insights - 'message_id'
  ];
  return {
    samlAttributes,
  };
};

// eslint-disable-next-line sonarjs/cognitive-complexity
const getSamlToken = ({ logger, campaignCacheService, credentialsService, targetedCampaignService, fetch, jwksClient, jwt }, config) => async (req, res, next) => { // NOSONAR
  // validate input parameters
  const { error: pathError, value: path } = Joi.validate(req.params, getCampaignPathSchema, { stripUnknown: true });
  if (pathError) {
    return next(HttpError.badRequest('Validation error', pathError.details.map((err) => {
      return { path: err.path.join('.'), message: err.message };
    })));
  }
  const ruleId = path.id;
  const { error, value: query } = Joi.validate(req.query, getSamlTokenSchema, { stripUnknown: true });
  if (error) {
    return next(HttpError.badRequest('Validation error', error.details.map((err) => {
      return { path: err.path.join('.'), message: escapeHtml(err.message) };
    })));
  }
  // get rule details
  let rule;
  try {
    rule = campaignCacheService.get().campaigns[ruleId]; // eslint-disable-line
  } catch (err) {
    if (err.response && err.response.code === 'HTTP_NOT_FOUND') {
      return next(HttpError.notFound('Not found'));
    }
    return next(err);
  }
  if (rule.status !== 'published' || rule.deleted || rule.disabled) {
    return next(HttpError.notFound('Not found'));
  }
  const now = Date.now();
  const startAt = Date.parse(rule.start_at);
  const endAt = Date.parse(rule.end_at);
  if (now < startAt || now > endAt) {
    return next(HttpError.notFound('Not found'));
  }
  // check if the request was authenticated with customer token
  const isCustomerTokenAuth = checkStandardScope(dotProp.get(res.locals, `${config.tokenClaimsPath}.scope`));
  // get request's token if it's a customer token
  const customerToken = isCustomerTokenAuth ? dotProp.get(res.locals, config.tokenPath) : req.get('x-customer-authorization');
  // get request's language
  const language = R.pathOr('en', [ 'language' ], res.locals);
  // get preferred environment header value
  const preferredEnv = req.get('Preferred-Environment');
  // get mocked insights flag
  const mockedInsight = req.get('X-Mock-Insight') !== undefined;
  // get country
  const country = req.get('x-country-code') || 'CA';
  // get scotia card number
  let cardNumber;
  if (isCustomerTokenAuth && dotProp.get(res.locals, `${config.tokenClaimsPath}.profile.customerInfo.cardNumber`)) {
    cardNumber = dotProp.get(res.locals, `${config.tokenClaimsPath}.profile.customerInfo.cardNumber`);
  } else if (isCustomerTokenAuth && cardValidationRegex.test(dotProp.get(res.locals, `${config.tokenClaimsPath}.sub`))) {
    cardNumber = dotProp.get(res.locals, `${config.tokenClaimsPath}.sub`);
  } else {
    cardNumber = req.get('x-customer-scotiacard');
  }

  if (!cardNumber) {
    logger.info({ message: 'Card number does not exists for this user - get-saml-token.js' });
  }

  // get channel id
  let channelId = req.get('x-channel-id');
  if (!channelId) {
    if (query.application === 'nova') {
      channelId = 'Mobile';
    } else {
      channelId = 'Online';
    }
  }

  // get user agent
  let userAgent = req.get('User-Agent');

  // send the x-application header value
  let xApplication = req.get('x-application');

  if (!xApplication && channelId && channelId === 'Mobile') {
    userAgent = userAgent && (userAgent.indexOf('iPhone') > -1 || query.platform === 'ios') ? 'iPhone' : 'Android';
    xApplication = userAgent === 'iPhone' ? 'N1' : 'N2';
  }

  // get spand id
  const spanId = req.get('x-b3-spanid');

  // get trace id
  const traceId = req.get('x-b3-traceid');

  // get x-originating-appl-code
  const xOriginatingApplCode = req.get('x-originating-appl-code') || 'BFB6';

  // get campaign details from insight
  let campaign;
  try {
    const data = await targetedCampaignService.getInstance({ ruleType: rule.type }).getCampaign({ id: query.message_id,
      customerToken,
      userIdentifier: cardNumber,
      channelId,
      xApplication,
      language,
      preferredEnv,
      useMock: mockedInsight,
      spanId,
      traceId,
      xOriginatingApplCode,
      country,
    });
    campaign = R.prop('data', data);
  } catch (err) {
    const originalError = R.pipe(R.pathOr([], [ 'response', 'notifications' ]), R.head)(err);
    if (originalError && originalError.code === 'INSIGHT-001-E') {
      next(HttpError.notFound('Not found', [ {
        message: `campaign with specified message_id is not found`,
        notifications: R.pathOr([], [ 'response', 'notifications' ], err),
      } ]));
      return;
    }
    return next(HttpError.internalServer('Internal Server Error', [ {
      message: `error calling downstream service to get the campaign`,
      notifications: R.pathOr([], [ 'response', 'notifications' ], err),
    } ]));
  }

  const jwkResponse = await fetch(config.publicKeyJWKS, { method: 'GET' });
  if (!jwkResponse.ok) {
    next(HttpError.internalServer('Error - Could not fetch jwks'));
    return;
  }
  const jwk = await jwkResponse.json();
  const client = jwksClient({ jwksUri: config.publicKeyJWKS });
  const key = await client.getSigningKey(jwk.keys[0].kid);
  const signingKey = key.getPublicKey();
  const claims = jwt.verify(customerToken, signingKey, { algorithms: 'RS256' });

  // identify campaign and create a payload
  const curriedCeba5PayloadGenerator = R.curry(generateCeba5Payload);
  const ceba5Payload = curriedCeba5PayloadGenerator(claims);

  const curriedpreapprovedCCSBPayloadGenerator = R.curry(generatePreApprovedCCSBPayload);
  const preapprovedCCSBPayload = curriedpreapprovedCCSBPayloadGenerator(claims);

  const applicationInfo = R.cond([
    [ isMortgageRenewal, generateMortgageRenewalPayload ],
    [ isMortgageRenewalV2, generateMortgageRenewalPayloadV2 ],
    [ isStepCli, generateStepCliPayload ],
    [ isStepAcquisition, generateStepAcquisitionPayload ],
    [ isCeba5, ceba5Payload ],
    [ isPreapprovedCCSB, preapprovedCCSBPayload ],
    [ R.T, () => null ],
  ])(campaign);
  if (!applicationInfo) {
    // send error
    return next(HttpError.badRequest('Invalid campaign type'));
  }
  // verify campaign id from insight and the rule
  if (campaign.campaign_id !== rule.external_ref) {
    return next(HttpError.notFound('Not found'));
  }
  const cid = getCID(claims);
  const samlPayload = generateSamlPayload(config.saml.exitUrl, cardNumber, cid, language, JSON.stringify(applicationInfo.info || {}), applicationInfo.samlAttributes);
  // request SAML token
  let samlToken;
  try {
    samlToken = await credentialsService.createSamlToken(language, preferredEnv, samlPayload, cardNumber);
  } catch (err) {
    return next(err);
  }
  // set disposition for soctiahome campaign
  if (isMortgageRenewalV2(campaign)) {
    try {
      await targetedCampaignService.getInstance({ ruleType: campaign.type }).setDisposition({ id: query.message_id,
        disposition: 'P',
        customerToken,
        cardNumber,
        channelId,
        xApplication,
        language,
        preferredEnv,
        useMock: mockedInsight,
        spanId,
        traceId,
        xOriginatingApplCode,
        country,
      });
    } catch (err) {
      logger.error({ message: 'Error setting disposition in progress disposition for campaign.', err });
    }
  }
  return res.status(200).json({ data: { saml_token: samlToken }, notifications: [] });
};

module.exports = getSamlToken;
