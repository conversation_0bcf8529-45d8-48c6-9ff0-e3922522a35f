const flushCache = require('./flush-cache');

const redisClient = {
  flushall: jest.fn().mockResolvedValue(),
};
const json = jest.fn();
const status = jest.fn().mockReturnValue({ json });
const next = jest.fn();

describe('Campaign Routes flushCache', () => {
  beforeEach(() => {
    redisClient.flushall.mockClear();
  });
  test('should successfully call', async () => {
    const endpoint = flushCache({ redisClient }, { space: 'IST' });
    await endpoint({}, { status }, next);
    expect(next).not.toBeCalled();
    expect(status).toBeCalledTimes(1);
    expect(status).toBeCalledWith(200);
    expect(json).toBeCalledTimes(1);
  });
  test('should send 400 on PRD space', async () => {
    const endpoint = flushCache({ redisClient }, { space: 'PRD' });
    await endpoint({}, { status }, next);
    expect(next).toBeCalledTimes(1);
  });
});
