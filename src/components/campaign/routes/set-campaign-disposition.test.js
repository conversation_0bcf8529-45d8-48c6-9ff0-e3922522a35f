/* eslint-disable sonarjs/no-duplicate-string */
const httpMocks = require('node-mocks-http');
const setCampaignDisposition = require('./set-campaign-disposition');
const { HttpError } = require('../../errors');

const defaultToken = 'opaque-token';
const config = { tokenPath: 'auth.token', tokenClaimsPath: 'auth.claims' };
const channelId = 'nova';
const mockCampaignCacheService = { get: jest.fn() };
const mockDispositionsService = { getDispositions: jest.fn(), setDisposition: jest.fn() };
const mockTargetedCampaignService = {
  getInstance: jest.fn(),
};
mockTargetedCampaignService.getInstance.mockReturnValue({ getCampaign: jest.fn(), setDisposition: jest.fn().mockResolvedValue(true) });
const mockStatus = jest.fn();
const mockJson = jest.fn();
const next = jest.fn();
const mockLogger = {
  error: jest.fn(),
  info: jest.fn(),
};

describe('Set Disposition Campaign', () => {
  beforeEach(() => {
    mockDispositionsService.getDispositions.mockReset();
    mockDispositionsService.setDisposition.mockReset();
    mockCampaignCacheService.get.mockReset();
    mockTargetedCampaignService.getInstance().getCampaign.mockReset();
    mockTargetedCampaignService.getInstance().setDisposition.mockReset();
    next.mockReset();
    mockJson.mockReset();
    mockStatus.mockReset();
    mockLogger.error.mockReset();
  });

  test('should initialize', () => {
    expect(setCampaignDisposition).toBeDefined();
    expect(typeof setCampaignDisposition).toEqual('function');
  });

  test('Schema Validation - should fail on params validation error', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: {
        'Preferred-Environment': 'istgreen',
        Authorization: `Bearer: ${defaultToken}`,
        'x-channel-id': channelId,
      },
      params: { id: '#abcdef1234' },
      query: {},
    }, {
      locals: { auth: { token: defaultToken } },
    });
    const endpoint = setCampaignDisposition({
      logger: mockLogger,
      dispositionsService: mockDispositionsService,
      campaignCacheService: mockCampaignCacheService,
      targetedCampaignService: mockTargetedCampaignService,
    });
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err).toBeInstanceOf(HttpError);
    expect(err.statusCode).toEqual(400);
    expect(err.metadata[0].message).toEqual('"id" with value "#abcdef1234" fails to match the required pattern: /^[a-zA-Z0-9]+$/');
  });

  test('Schema Validation - should fail on message_id not passed in request body', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: {
        'Preferred-Environment': 'istgreen',
        Authorization: `Bearer: ${defaultToken}`,
        'x-channel-id': channelId,
      },
      params: { id: '1adzsnGZ3QWe' },
      query: {},
      body: {
        message_id: '',
        disposition: 'V',
        application: 'ios',
        platform: 'ios',
        page: 'accounts-it',
        container: 'offers-and-programs-it',
      },
    }, {
      locals: { auth: { token: defaultToken } },
    });
    const endpoint = setCampaignDisposition({
      logger: mockLogger,
      dispositionsService: mockDispositionsService,
      campaignCacheService: mockCampaignCacheService,
      targetedCampaignService: mockTargetedCampaignService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err).toBeInstanceOf(HttpError);
    expect(err.statusCode).toEqual(400);
    expect(err.metadata[0].message).toEqual('"message_id" is not allowed to be empty');
  });

  test('Schema Validation - should fail on disposition value not passed in request body', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: {
        'Preferred-Environment': 'istgreen',
        Authorization: `Bearer: ${defaultToken}`,
        'x-channel-id': channelId,
      },
      params: { id: '1adzsnGZ3QWe' },
      query: {},
      body: {
        message_id: 'D5O00O0000XEY25C',
        disposition: '',
        application: 'ios',
        platform: 'ios',
        page: 'accounts-it',
        container: 'offers-and-programs-it',
      },
    }, {
      locals: { auth: { token: defaultToken } },
    });
    const endpoint = setCampaignDisposition({
      logger: mockLogger,
      dispositionsService: mockDispositionsService,
      campaignCacheService: mockCampaignCacheService,
      targetedCampaignService: mockTargetedCampaignService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err).toBeInstanceOf(HttpError);
    expect(err.statusCode).toEqual(400);
    expect(err.metadata[0].message).toEqual('"disposition" is not allowed to be empty');
  });

  test('Schema Validation - should fail on disposition value other than valid input (Accept/View) in request body', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: {
        'Preferred-Environment': 'istgreen',
        Authorization: `Bearer: ${defaultToken}`,
        'x-channel-id': channelId,
      },
      params: { id: '1adzsnGZ3QWe' },
      query: {},
      body: {
        message_id: 'D5O00O0000XEY25C',
        disposition: 'K',
        application: 'ios',
        platform: 'ios',
        page: 'accounts-it',
        container: 'offers-and-programs-it',
      },
    }, {
      locals: { auth: { token: defaultToken } },
    });
    const endpoint = setCampaignDisposition({
      logger: mockLogger,
      dispositionsService: mockDispositionsService,
      campaignCacheService: mockCampaignCacheService,
      targetedCampaignService: mockTargetedCampaignService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err).toBeInstanceOf(HttpError);
    expect(err.statusCode).toEqual(400);
    expect(err.metadata[0].message).toEqual('"disposition" must be one of [V, D, P, A, C, Y, N, S,  ]');
  });

  test('Validation Logic - should return bad request error if message_id is required (i.e. targeted campaign)', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: {
        'Preferred-Environment': 'istgreen',
        Authorization: `Bearer: ${defaultToken}`,
        'x-channel-id': channelId,
      },
      params: { id: '1adzsnGZ3QWe' },
      query: {},
      body: {
        disposition: 'V',
        application: 'ios',
        platform: 'ios',
        page: 'accounts-it',
        container: 'offers-and-programs-it',
      },
    }, {
      locals: { auth: { token: defaultToken } },
    });
    const mockCampaignRule = {
      id: '1adzsnGZ3QWe',
      external_ref: 'PAD01',
      content_space: 'samplespace',
      content_type: 'creditCardOffer',
      content_id: 'aaaa1111',
      status: 'published',
      disabled: false,
    };
    mockCampaignCacheService.get.mockReturnValue({
      campaigns: { [mockCampaignRule.id]: mockCampaignRule },
    });
    const endpoint = setCampaignDisposition({
      logger: mockLogger,
      dispositionsService: mockDispositionsService,
      campaignCacheService: mockCampaignCacheService,
      targetedCampaignService: mockTargetedCampaignService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err).toBeInstanceOf(HttpError);
    expect(err.statusCode).toEqual(400);
    expect(err.metadata[0].message).toEqual('message_id is required');
  });

  test('Validation Logic - should return not found error if rule does not exist in camapign cache response', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: {
        'Preferred-Environment': 'istgreen',
        Authorization: `Bearer: ${defaultToken}`,
        'x-channel-id': channelId,
      },
      params: { id: '1adzsnGZ3QWe' },
      query: {},
      body: {
        message_id: 'D5O00O0000XEY25C',
        disposition: 'V',
        application: 'ios',
        platform: 'ios',
        page: 'accounts-it',
        container: 'offers-and-programs-it',
      },
    }, {
      locals: { auth: { token: defaultToken } },
    });
    const mockCampaignRule = {
      id: 'aaaa0001', // does not match the ruleId query param
      external_ref: 'PAD01',
      content_space: 'samplespace',
      content_type: 'creditCardOffer',
      content_id: 'aaaa1111',
      status: 'published',
      disabled: false,
    };
    mockCampaignCacheService.get.mockReturnValue({
      campaigns: { [mockCampaignRule.id]: mockCampaignRule },
    });
    const endpoint = setCampaignDisposition({
      logger: mockLogger,
      dispositionsService: mockDispositionsService,
      campaignCacheService: mockCampaignCacheService,
      targetedCampaignService: mockTargetedCampaignService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalled();
    const err = next.mock.calls[0][0];
    expect(err).toBeInstanceOf(HttpError);
    expect(err.statusCode).toEqual(404);
    expect(err.metadata[0].message).toEqual('campaign rule does not exist');
  });

  test('Get Insight - should return not found error on missing insights data', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: {
        'Preferred-Environment': 'istgreen',
        Authorization: `Bearer: ${defaultToken}`,
        'x-channel-id': channelId,
      },
      params: { id: 'SAYkqDfBt0e5' },
      query: {},
      body: {
        message_id: 'D4F72503AC2BFC09',
        disposition: 'V',
        application: 'ios',
        platform: 'ios',
        page: 'accounts-it',
        container: 'offers-and-programs-it',
      },
    }, {
      locals: { auth: { token: defaultToken, claims: { scope: 'standard' } } },
    });
    mockTargetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce({ data: null });
    const mockCampaignRule = {
      id: 'SAYkqDfBt0e5',
      external_ref: 'PAD01',
      content_space: 'samplespace',
      content_type: 'creditCardOffer',
      content_id: 'aaaa1111',
      status: 'published',
      disabled: false,
      dismissable_flag: true,
    };
    mockCampaignCacheService.get.mockReturnValue({
      campaigns: { [mockCampaignRule.id]: mockCampaignRule },
    });
    const endpoint = setCampaignDisposition({
      logger: mockLogger,
      campaignCacheService: mockCampaignCacheService,
      targetedCampaignService: mockTargetedCampaignService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err).toBeInstanceOf(HttpError);
    expect(err.statusCode).toEqual(404);
    expect(err.metadata[0].message).toEqual('Campaign with specified message_id is not found');
  });

  test('Get Insight - should return bad request error if insights campaign_id does not match rules external_ref', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: {
        'Preferred-Environment': 'istgreen',
        Authorization: `Bearer: ${defaultToken}`,
        'x-channel-id': channelId,
      },
      params: { id: 'SAYkqDfBt0e5' },
      query: {},
      body: {
        message_id: 'D4F72503AC2BFC09',
        disposition: 'V',
        application: 'ios',
        platform: 'ios',
        page: 'accounts-it',
        container: 'offers-and-programs-it',
      },
    }, {
      locals: { auth: { token: defaultToken, claims: { scope: 'standard' } } },
    });
    const mockInsightsPayload = {
      data: {
        message_id: 'D4F72503FC2BFC09',
        campaign_id: 'PAD15',
        message_source: 'KT',
      },
    };
    mockTargetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightsPayload);
    const mockCampaignRule = {
      id: 'SAYkqDfBt0e5',
      external_ref: 'PAD01',
      content_space: 'samplespace',
      content_type: 'creditCardOffer',
      content_id: 'aaaa1111',
      status: 'published',
      disabled: false,
      dismissable_flag: true,
    };
    mockCampaignCacheService.get.mockReturnValue({
      campaigns: { [mockCampaignRule.id]: mockCampaignRule },
    });
    const endpoint = setCampaignDisposition({
      logger: mockLogger,
      campaignCacheService: mockCampaignCacheService,
      targetedCampaignService: mockTargetedCampaignService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err).toBeInstanceOf(HttpError);
    expect(err.statusCode).toEqual(400);
    expect(err.metadata[0].message).toEqual('Invalid message_id');
  });

  test('Get Insight - should return not found error if insights throws not found error', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: {
        'Preferred-Environment': 'istgreen',
        Authorization: `Bearer: ${defaultToken}`,
        'x-channel-id': channelId,
      },
      params: { id: 'SAYkqDfBt0e5' },
      query: {},
      body: {
        message_id: 'D4F72503AC2BFC09',
        disposition: 'V',
        application: 'ios',
        platform: 'ios',
        page: 'accounts-it',
        container: 'offers-and-programs-it',
      },
    }, {
      locals: { auth: { token: defaultToken, claims: { scope: 'standard' } } },
    });
    const mockInsightsPayload = {
      response: {
        data: null,
        notifications: [ {
          message: 'The unique ID was not found.',
          code: 'INSIGHT-001-E',
        } ],
      },
    };
    mockTargetedCampaignService.getInstance().getCampaign.mockRejectedValueOnce(mockInsightsPayload);
    const mockCampaignRule = {
      id: 'SAYkqDfBt0e5',
      external_ref: 'PAD01',
      content_space: 'samplespace',
      content_type: 'creditCardOffer',
      content_id: 'aaaa1111',
      status: 'published',
      disabled: false,
      dismissable_flag: true,
    };
    mockCampaignCacheService.get.mockReturnValue({
      campaigns: { [mockCampaignRule.id]: mockCampaignRule },
    });
    const endpoint = setCampaignDisposition({
      logger: mockLogger,
      campaignCacheService: mockCampaignCacheService,
      targetedCampaignService: mockTargetedCampaignService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err).toBeInstanceOf(HttpError);
    expect(err.statusCode).toEqual(404);
    expect(err.metadata[0].message).toEqual('Campaign with specified message_id is not found');
  });

  test('Get Insight - should return internal server error if unknown error occurs', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: {
        'Preferred-Environment': 'istgreen',
        Authorization: `Bearer: ${defaultToken}`,
        'x-channel-id': channelId,
      },
      params: { id: 'SAYkqDfBt0e5' },
      query: {},
      body: {
        message_id: 'D4F72503AC2BFC09',
        disposition: 'V',
        application: 'ios',
        platform: 'ios',
        page: 'accounts-it',
        container: 'offers-and-programs-it',
      },
    }, {
      locals: { auth: { token: defaultToken, claims: { scope: 'standard' } } },
    });
    mockTargetedCampaignService.getInstance().getCampaign.mockRejectedValueOnce(new Error('unknown'));
    const mockCampaignRule = {
      id: 'SAYkqDfBt0e5',
      external_ref: 'PAD01',
      content_space: 'samplespace',
      content_type: 'creditCardOffer',
      content_id: 'aaaa1111',
      status: 'published',
      disabled: false,
      dismissable_flag: true,
    };
    mockCampaignCacheService.get.mockReturnValue({
      campaigns: { [mockCampaignRule.id]: mockCampaignRule },
    });
    const endpoint = setCampaignDisposition({
      logger: mockLogger,
      campaignCacheService: mockCampaignCacheService,
      targetedCampaignService: mockTargetedCampaignService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err).toBeInstanceOf(HttpError);
    expect(err.statusCode).toEqual(500);
    expect(err.metadata[0].message).toEqual('Failed to get downstream targeted campaign');
  });

  test('Set Disposition - parameters passed in the setDisposition calls (insights & db) to match the expected', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: {
        'Preferred-Environment': 'istgreen',
        Authorization: `Bearer: ${defaultToken}`,
        'x-channel-id': channelId,
      },
      params: { id: 'SAYkqDfBt0e5' },
      query: {},
      body: {
        message_id: 'D4F72503AC2BFC09',
        disposition: 'V',
        application: 'nova',
        platform: 'ios',
        page: 'accounts-it',
        container: 'offers-and-programs-it',
      },
    }, {
      locals: { auth: { token: defaultToken, claims: { scope: 'standard' } } },
    });
    const mockCampaignRule = {
      id: 'SAYkqDfBt0e5',
      external_ref: 'PAD15',
      content_space: 'samplespace',
      content_type: 'creditCardOffer',
      content_id: 'aaaa1111',
      status: 'published',
      disabled: false,
      end_at: '2022-09-30T07:14:00.000Z',
      container: 'offers-and-programs-it',
      pages: [ 'accounts-it' ],
      dismissable_flag: true,
      type: 'campaign',
    };
    mockCampaignCacheService.get.mockReturnValue({
      campaigns: { [mockCampaignRule.id]: mockCampaignRule },
    });
    const mockInsightsPayload = {
      data: {
        message_id: 'D4F72503FC2BFC09',
        campaign_id: 'PAD15',
        message_source: 'KT',
        expiry_date: '2022-09-29',
      },
    };
    mockTargetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightsPayload);
    const endpoint = setCampaignDisposition({
      logger: mockLogger,
      dispositionsService: mockDispositionsService,
      campaignCacheService: mockCampaignCacheService,
      targetedCampaignService: mockTargetedCampaignService,
    }, config);
    await endpoint(req, res, next);
    const insightsCall = mockTargetedCampaignService.getInstance().setDisposition.mock.calls[0];
    expect(insightsCall[0]).toEqual({
      id: 'D4F72503AC2BFC09',
      disposition: 'V',
      customerToken: defaultToken,
      cardNumber: undefined,
      channelId,
      language: 'en',
      preferredEnv: 'istgreen',
      useMock: false,
      xOriginatingApplCode: 'BFB6',
      country: 'CA',
      spanId: expect.any(String),
      traceId: expect.any(String),
      xApplication: undefined,
    });
    const dbCall = mockDispositionsService.setDisposition.mock.calls[0];
    expect(dbCall[0]).toEqual(undefined);
    expect(dbCall[1]).toEqual('CA');
    expect(dbCall[2]).toEqual('SAYkqDfBt0e5');
    expect(dbCall[3]).toEqual('2022-09-29T00:00:00.000Z');
    expect(dbCall[4]).toEqual('V');
    expect(dbCall[5]).toEqual({
      message_id: 'D4F72503AC2BFC09',
      application: 'nova',
      platform: 'ios',
      page: 'accounts-it',
      container: 'offers-and-programs-it',
    });
  });

  test('Set Database Disposition - container & page should be inferred if not passed in request body', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: {
        'Preferred-Environment': 'istgreen',
        Authorization: `Bearer: ${defaultToken}`,
        'x-channel-id': channelId,
      },
      params: { id: 'SAYkqDfBt0e5' },
      query: {},
      body: {
        message_id: 'D4F72503AC2BFC09',
        disposition: 'V',
        application: 'nova',
        platform: 'ios',
      },
    }, {
      locals: { auth: { token: defaultToken, claims: { scope: 'standard' } } },
    });
    const mockCampaignRule = {
      id: 'SAYkqDfBt0e5',
      external_ref: 'PAD15',
      content_space: 'samplespace',
      content_type: 'creditCardOffer',
      content_id: 'aaaa1111',
      status: 'published',
      disabled: false,
      end_at: '2022-09-30T07:14:00.000Z',
      container: 'offers-and-programs-it',
      pages: [ 'accounts-it' ],
      dismissable_flag: true,
    };
    mockCampaignCacheService.get.mockReturnValue({
      campaigns: { [mockCampaignRule.id]: mockCampaignRule },
    });
    const mockInsightsPayload = {
      data: {
        message_id: 'D4F72503FC2BFC09',
        campaign_id: 'PAD15',
        message_source: 'KT',
        expiry_date: '2022-09-29',
      },
    };
    mockTargetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightsPayload);
    const endpoint = setCampaignDisposition({
      logger: mockLogger,
      dispositionsService: mockDispositionsService,
      campaignCacheService: mockCampaignCacheService,
      targetedCampaignService: mockTargetedCampaignService,
    }, config);
    await endpoint(req, res, next);
    const dbCall = mockDispositionsService.setDisposition.mock.calls[0];
    expect(dbCall[0]).toEqual(undefined);
    expect(dbCall[1]).toEqual('CA');
    expect(dbCall[2]).toEqual('SAYkqDfBt0e5');
    expect(dbCall[3]).toEqual('2022-09-29T00:00:00.000Z');
    expect(dbCall[4]).toEqual('V');
    expect(dbCall[5]).toEqual({
      message_id: 'D4F72503AC2BFC09',
      application: 'nova',
      platform: 'ios',
      page: 'accounts-it',
      container: 'offers-and-programs-it',
    });
  });

  test('Set Disposition - should not set disposition for anonymous customer request', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: {
        'Preferred-Environment': 'istgreen',
        Authorization: `Bearer: ${defaultToken}`,
      },
      params: { id: 'SAYkqDfBt0e5' },
      query: {},
      body: {
        message_id: 'D4F72503AC2BFC09',
        disposition: 'Y',
        application: 'ios',
        platform: 'ios',
        page: 'accounts-it',
        container: 'offers-and-programs-it',
      },
    }, {
      locals: { auth: { token: defaultToken, claims: { scope: 'cdb.pigeon.campaigns.read' } } },
    });
    const mockCampaignRule = {
      id: 'SAYkqDfBt0e5',
      external_ref: 'PAD15',
      content_space: 'samplespace',
      content_type: 'creditCardOffer',
      content_id: 'aaaa1111',
      status: 'published',
      disabled: false,
    };
    mockCampaignCacheService.get.mockReturnValue({
      campaigns: { [mockCampaignRule.id]: mockCampaignRule },
    });
    const endpoint = setCampaignDisposition({
      logger: mockLogger,
      dispositionsService: mockDispositionsService,
      campaignCacheService: mockCampaignCacheService,
      targetedCampaignService: mockTargetedCampaignService,
    }, config);
    await endpoint(req, res, next);
    expect(mockTargetedCampaignService.getInstance().setDisposition).not.toBeCalled();
    expect(mockDispositionsService.setDisposition).not.toBeCalled();
    const err = next.mock.calls[0][0];
    expect(err.statusCode).toEqual(400);
    expect(err.message).toEqual('set disposition is supported only for authenticated customers');
  });

  test('Set Insight Disposition - should return internal server error if unknown error occurs', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: {
        'Preferred-Environment': 'istgreen',
        Authorization: `Bearer: ${defaultToken}`,
        'x-channel-id': channelId,
      },
      params: { id: 'SAYkqDfBt0e5' },
      query: {},
      body: {
        message_id: 'D4F72503AC2BFC09',
        disposition: 'V',
        application: 'ios',
        platform: 'ios',
        page: 'accounts-it',
        container: 'offers-and-programs-it',
      },
    }, {
      locals: { auth: { token: defaultToken, claims: { scope: 'standard' } } },
    });
    const mockCampaignRule = {
      id: 'SAYkqDfBt0e5',
      external_ref: 'PAD15',
      content_space: 'samplespace',
      content_type: 'creditCardOffer',
      content_id: 'aaaa1111',
      status: 'published',
      disabled: false,
      end_at: '2022-09-30T07:14:00.000Z',
      dismissable_flag: true,
    };
    mockCampaignCacheService.get.mockReturnValue({
      campaigns: { [mockCampaignRule.id]: mockCampaignRule },
    });
    const mockInsightsPayload = {
      data: {
        message_id: 'D4F72503FC2BFC09',
        campaign_id: 'PAD15',
        message_source: 'KT',
        expiry_date: '2022-09-29',
      },
    };
    mockTargetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightsPayload);
    mockTargetedCampaignService.getInstance().setDisposition.mockRejectedValueOnce(new Error('unknown'));
    const endpoint = setCampaignDisposition({
      logger: mockLogger,
      dispositionsService: mockDispositionsService,
      campaignCacheService: mockCampaignCacheService,
      targetedCampaignService: mockTargetedCampaignService,
    }, config);
    await endpoint(req, res, next);
    const err = next.mock.calls[0][0];
    expect(err).toBeInstanceOf(HttpError);
    expect(err.statusCode).toEqual(500);
    expect(err.metadata[0].message).toEqual('Failed to set downstream campaign disposition');
  });

  test('Set Database Disposition - should handle error', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: {
        'Preferred-Environment': 'istgreen',
        Authorization: `Bearer: ${defaultToken}`,
        'x-channel-id': channelId,
      },
      params: { id: 'SAYkqDfBt0e5' },
      query: {},
      body: {
        message_id: 'D4F72503AC2BFC09',
        disposition: 'V',
        application: 'ios',
        platform: 'ios',
        page: 'accounts-it',
        container: 'offers-and-programs-it',
      },
    }, {
      locals: { auth: { token: defaultToken, claims: { scope: 'standard' } } },
    });
    const mockCampaignRule = {
      id: 'SAYkqDfBt0e5',
      external_ref: 'PAD15',
      content_space: 'samplespace',
      content_type: 'creditCardOffer',
      content_id: 'aaaa1111',
      status: 'published',
      disabled: false,
      end_at: '2022-09-30T07:14:00.000Z',
      dismissable_flag: true,
    };
    mockCampaignCacheService.get.mockReturnValue({
      campaigns: { [mockCampaignRule.id]: mockCampaignRule },
    });
    const mockInsightsPayload = {
      data: {
        message_id: 'D4F72503FC2BFC09',
        campaign_id: 'PAD15',
        message_source: 'KT',
        expiry_date: '2022-09-29',
      },
    };
    mockTargetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightsPayload);
    mockDispositionsService.setDisposition.mockRejectedValueOnce(new Error('unknown'));
    const endpoint = setCampaignDisposition({
      logger: mockLogger,
      dispositionsService: mockDispositionsService,
      campaignCacheService: mockCampaignCacheService,
      targetedCampaignService: mockTargetedCampaignService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
  });

  test('Set Insight Disposition - Should reject when trying to dismiss a non-dismissble campaign', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: {
        'Preferred-Environment': 'istgreen',
        Authorization: `Bearer: ${defaultToken}`,
        'x-channel-id': channelId,
      },
      params: { id: 'SAYkqDfBt0e5' },
      query: {},
      body: {
        message_id: 'D4F72503AC2BFC09',
        disposition: 'D',
        application: 'nova',
        platform: 'ios',
        page: 'accounts-it',
        container: 'priority-box',
      },
    }, {
      locals: { auth: { token: defaultToken, claims: { scope: 'standard' } } },
    });
    const mockCampaignRule = {
      id: 'SAYkqDfBt0e5',
      external_ref: 'ORCTR',
      content_space: 'samplespace',
      content_type: 'kycCampaign',
      content_id: 'aaaa1111',
      status: 'published',
      disabled: false,
      end_at: '2022-09-30T07:14:00.000Z',
      container: 'priority-box',
      pages: [ 'accounts-it' ],
      dismissable_flag: false,
    };
    mockCampaignCacheService.get.mockReturnValue({
      campaigns: { [mockCampaignRule.id]: mockCampaignRule },
    });
    const mockInsightsPayload = {
      data: {
        message_id: 'D4F72503AC2BFC09',
        campaign_id: 'ORCTR',
        message_source: 'KT',
        expiry_date: '2022-09-29',
      },
    };
    mockTargetedCampaignService.getInstance().getCampaign.mockResolvedValueOnce(mockInsightsPayload);
    const endpoint = setCampaignDisposition({
      logger: mockLogger,
      dispositionsService: mockDispositionsService,
      campaignCacheService: mockCampaignCacheService,
      targetedCampaignService: mockTargetedCampaignService,
    }, config);
    await endpoint(req, res, next);
    const insightsCall = mockTargetedCampaignService.getInstance().setDisposition.mock.calls[0];
    expect(insightsCall[0]).toEqual({
      id: 'D4F72503AC2BFC09',
      disposition: 'V',
      customerToken: defaultToken,
      cardNumber: undefined,
      channelId,
      language: 'en',
      preferredEnv: 'istgreen',
      useMock: false,
      xOriginatingApplCode: 'BFB6',
      country: 'CA',
      spanId: expect.any(String),
      traceId: expect.any(String),
      xApplication: undefined,
    });
    expect(mockDispositionsService.setDisposition).toHaveBeenCalledTimes(0);
  });
});
