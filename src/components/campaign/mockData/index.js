
const baseRuleFields = {
  start_at: '2023-01-01T04:00:00.000Z',
  end_at: '2030-01-01T04:00:00.000Z',
  content_space: '4szkx38resvm',
  container: 'offers-and-programs',
  pages: [ 'accounts' ],
  platforms: [ 'ios', 'android' ],
  platforms_targeting: [
    { v: 1, platform: 'ios', items: [] },
    { v: 1, platform: 'android', items: [] },
  ],
  app_version: null,
  urgent: true,
  type: 'campaign',
  dismissable_flag: true,
  status: 'published',
  disabled: false,
  application: 'nova',
};

// Mass message rule
const mockPriorityMessageRule = {
  ...baseRuleFields,
  id: 'RSowWu1cvsBk',
  name: 'Mock Mass Message Rule',
  content_type: 'targetedCampaign',
  content_id: '3JNYCPNdDFVnSLRHkBlwgk',
  external_ref: 'MESSAGE',
  mass_targeting: { v: 1 },
};

// Mass campaign rule
const mockMassCampaignRule = {
  ...baseRuleFields,
  id: 'MvAEPYaM2e6T',
  name: 'Mock Mass Campaign Rule',
  content_type: 'nova_campaign',
  content_id: '5qIdrK7TBRYrn7cwBfVaWS',
  external_ref: 'MASS',
  mass_targeting: {
    by_product: {
      any_of: [ // target any/all products
        { ownership: 'R' },
        { ownership: 'B' },
      ],
    },
    by_scene_points: {
      targeting_criteria: 'greater',
      points: 1000,
    },
    enrollment_status: [],
  },
};

// Pega rule
const mockTargetedCampaignRule = {
  ...baseRuleFields,
  id: 'U8HHmCLcF2eg',
  name: '********** Mock Pega Targeted Rule',
  content_type: 'targetedCampaign',
  content_id: '1B54DYwMRVIqxtRUYtEf7r',
  external_ref: '**********',
  mass_targeting: {
    product_pages: {},
    enrollment_status: [],
  },
};

// KT Rule
const mockKTTargetedCampaignRule = {
  id: 'ewd76uUaZmPN',
  name: 'LCC01 - Targeted KT Campaign Rule',
  start_at: '2023-01-01T04:00:00.000Z',
  end_at: '2030-01-01T04:00:00.000Z',
  content_space: '4szkx38resvm',
  content_type: 'targetedCampaign',
  content_id: '2bGOEDJkF1gWhtVah9CotG',
  container: 'offers-and-programs',
  pages: [ 'accounts' ],
  external_ref: 'LCC01',
  platforms: [ 'ios', 'android' ],
  platforms_targeting: [
    { v: 1, platform: 'ios', items: [] },
    { v: 1, platform: 'android', items: [] },
  ],
  mass_targeting: { product_pages: {}, enrollment_status: [] },
  app_version: null,
  urgent: false,
  type: 'campaign',
  dismissable_flag: true,
  status: 'published',
  disabled: false,
  application: 'nova',
};

const mockContentResponse = {
  data: {
    total: 3,
    items: [
      {
        id: mockPriorityMessageRule.content_id,
        type: 'targetedCampaignPreview',
        language: 'en-US',
        content: {
          name: 'Priority Message Name',
          title: 'Priority Message Title',
        },
      },
      {
        id: mockMassCampaignRule.content_id,
        type: 'nova__rewards-campaign-preview',
        language: 'en-US',
        content: {
          name: 'Mass Campaign Name',
          title: 'Mass Campaign Title',
        },
      },
      {
        id: mockTargetedCampaignRule.content_id,
        type: 'targetedCampaignPreview',
        language: 'en-US',
        content: {
          name: 'Pega Targeted Campaign Name',
          title: 'Pega Targeted Campaign Title',
        },
      },
      {
        id: mockKTTargetedCampaignRule.content_id,
        type: 'targetedCampaignPreview',
        language: 'en-US',
        created_at: '2019-04-16T14:59:40.277Z',
        updated_at: '2019-04-16T14:59:40.277Z',
        content: {
          name: 'KT Targeted Campaign Name',
          title: 'KT Targeted Campaign Title',
        },
      },
    ],
  },
  notifications: [],
};

const mockAccountsResponse = {
  accountList: [
    {
      accountUniqueId: 'AAAAAAAAAAAAAAAAAAAA=',
      type: 'Saving',
      ciProductCode: 'AAA',
      ciProductSubCode: 'A1',
      productSystem: 'AAA',
      ownership: 'R',
      dateOpened: '2018-01-08T00:00:00-0500',
      dateClosed: '',
    },
    {
      accountUniqueId: 'AAAAAAAAAAAAAAAAAAAB=',
      type: 'Borrowing',
      ciProductCode: 'BBB',
      ciProductSubCode: 'B1',
      productSystem: 'BBB',
      ownership: 'B',
      dateOpened: '2018-01-09T00:00:00-0500',
      dateClosed: '',
    },
  ],
};

const mockInsightsResponse = {
  data: [
    {
      message_id: '6c9d31ad-24c3-33e4-a68d-dc4a5c6393ee-********',
      message_source: 'PEGAV2',
      message_status: 'N',
      message_response: null,
      subject_line: 'Pre-approved unsecured ScotiaLine Personal line of credit, competitive rate of interest, (refer to offer interest rate field), time limited',
      language: 'en',
      campaign_id: mockTargetedCampaignRule.external_ref,
      campaign_code: 'OP12020',
      message_category: null,
      pointer_text: null,
      start_date: '2020-01-01',
      expiry_date: '2030-01-31',
      conf_number: null,
      additional_data: [
        { name: 'savings', value: '1124' },
        { name: 'uspo_key', value: 'USV000003908' },
        { name: 'prime_as_of_date', value: '2023-01-26' },
        { name: 'primary_source_code', value: '106' },
        { name: 'primary_product_service_code', value: 'VIC' },
      ],
    },
    {
      message_id: 'DCCB7BBE387EE593',
      message_source: 'KT',
      message_status: 'N',
      message_response: null,
      subject_line: 'PACC_MOMENTUM VISA INFINITE_UAT',
      language: 'en',
      campaign_id: mockKTTargetedCampaignRule.external_ref,
      campaign_code: null,
      message_category: 'ALT',
      pointer_text: '',
      start_date: '2020-01-01',
      expiry_date: '2030-01-01',
      conf_number: null,
      additional_data: [
        { name: 'CREDIT', value: '005500' },
        { name: 'NAME', value: 'MR PRAHLADQATB SOLQAT' },
        { name: 'OTHER1', value: '' },
        { name: 'OTHER2', value: '' },
        { name: 'OTHER3', value: '20231212' },
        { name: 'OTHER4', value: '' },
        { name: 'OTHER5', value: '313' },
        { name: 'OTHER6', value: '' },
        { name: 'OTHER7', value: '' },
        { name: 'OTHER8', value: '20140401' },
        { name: 'OTHER9', value: 'UM112942' },
        { name: 'OTHER10', value: '' },
        { name: 'OTHER11', value: '' },
        { name: 'OTHER12', value: '' },
        { name: 'OTHER13', value: 'SIX' },
        { name: 'OTHER14', value: '315' },
        { name: 'OTHER15', value: 'VCLDM' },
        { name: 'OTHER16', value: '380' },
        { name: 'OTHER17', value: 'AXSSC' },
        { name: 'OTHER18', value: 'ABMFRENCH7' },
        { name: 'PROD', value: 'VCLDM' },
      ],
    },
  ],
};

module.exports = {
  mockPriorityMessageRule,
  mockMassCampaignRule,
  mockTargetedCampaignRule,
  mockContentResponse,
  mockInsightsResponse,
  mockAccountsResponse,
};
