const crypto = require('crypto');
const R = require('ramda');
const { orderBy } = require('lodash');
const momentTz = require('moment-timezone');

const {
  isTargetedCampaign,
} = require('../campaign/routes/common');
const {
  pickTruthy,
  resolveDynamicPages,
  filterRulesByVersion,
  filterByContainerAndPage,
  trim,
  filterByApplication,
} = require('.');

const reDash = /-/g;

const mapMassCampaigns = (rules) => rules.map((rule) => ({
  ...rule,
  external_ref: {
    campaign_id: rule.external_ref,
    message_id: rule.id,
    message_source: 'DMS',
  },
  start_date: rule.start_at,
}));

const mapTargetedCampaigns = (campaigns, rules, mapExtRefData, customerProducts) => {
  const mapped = [];
  campaigns.forEach(campaign => {
    const campaignId = R.prop('campaign_id', campaign);

    return rules
      .filter(rule => campaignId === R.prop([ 'external_ref' ], rule))
      .map(rule => {
        let recentDate = campaign.start_date.replace(reDash, '');
        if (new Date(rule.start_at) > new Date(campaign.start_date)) {
          recentDate = rule.start_at.split('T')[0].replace(reDash, '');
        }
        const data = {
          start_date: recentDate,
          external_ref: pickTruthy({
            campaign_id: campaignId,
            message_id: campaign.message_id,
            message_source: campaign.message_source,
            data: mapExtRefData ? campaign : null,
          }),
        };
        mapped.push(customerProducts ? { ...resolveDynamicPages(rule, customerProducts), ...data } : data);
      });
  });
  return mapped;
};

const sortCampaignsByStartDate = (rules) => {
  return orderBy(rules, [ 'start_at' ], [ 'desc' ]);
};

/**
 * Paginates an array of rules based on the provided query parameters.
 *
 * @param {Object} query - The query object containing pagination info.
 * @param {string|number} [query.offset=0] - The starting index for pagination.
 * @param {string|number} [query.limit] - The number of items to return.
 * @param {Array} rules - The array of rules to paginate.
 *
 * @returns {Array} A slice of the rules array based on the offset and limit.
 *
 * @example
 * paginateRules({ query: { offset: '2', limit: '3' }, rules: ['a', 'b', 'c', 'd', 'e'] });
 * // Returns: ['c', 'd', 'e']
 */
const paginateRules = ({ query, rules }) => {
  const offset = Number(query.offset) || 0;
  const limit = Number(query.limit) || rules.length;

  const start = Math.max(0, offset);
  const end = Math.min(rules.length, start + limit);

  return rules.slice(start, end);
};

const mapContentAndVariables = async ({
  query,
  rules,
  contents,
  campaigns,
  reqHeaders,
  variablesService,
  preferredEnv,
  customerToken,
  language,
  catchVarSvcErr,
  isDispositionsDbEnabled,
  notifications,
}) => {
  const items = [];
  // Using for ...of to remove eslint warning Variable Assigned to Object Injection Sinkeslint(security/detect-object-injection)
  for (const rule of rules) {
    const content = contents.find((c) => c && c.id === rule.content_id);
    if (!content || !content.content) {
      notifications.push({
        message: 'failed to get content',
        metadata: [ {
          rule_id: rule.id,
          content_space: rule.content_space,
          content_type: rule.content_type,
          content_id: rule.content_id,
        } ],
      });
      continue;
    }
    if (isTargetedCampaign(rule)) {
      rule.start_date = rule.start_date.includes('T') ? momentTz(rule.start_date, 'YYYY-MM-DD hh:mm:ss').tz('America/Toronto').utc().format() : rule.start_date;
    }

    const campaign = campaigns.find((c) => c.message_id === R.path([ 'external_ref', 'message_id' ], rule));

    const varsReplaced = await variablesService(
      preferredEnv,
      customerToken,
      content.content,
      campaign,
      rule,
      language,
      reqHeaders,
    ).catch(catchVarSvcErr(rule, content));

    const externalRef = { ...rule.external_ref };
    if (query.insight) {
      externalRef.data_context = R.pathOr(null, [ 'dataContext' ], varsReplaced);
      externalRef.data_transformed = R.pathOr({}, [ 'transformed' ], varsReplaced);
    }

    const dismissable = isDispositionsDbEnabled ? rule.dismissable_flag : false;

    const varsMapped = {
      id: rule.id,
      name: rule.name,
      type: content.type,
      container: rule.container,
      pages: rule.pages,
      urgent: rule.urgent,
      viewed: rule.viewed,
      dismissable,
      external_ref: externalRef,
      start_date: rule.start_date,
      content: varsReplaced.content,
      application: rule.application,
    };
    items.push(varsMapped);
  }
  return items;
};

const accessHeadersAndParams = async (
  res,
  req,
  query,
) => {
  let cardNumber = req.get('x-customer-scotiacard');
  const country = req.get('x-country-code');
  const channelId = req.get('x-channel-id');
  const xApplication = req.get('x-application');
  const { language, languageContentful } = res.locals; // req headers already read and parsed by middlewares
  const preferredEnv = req.get('preferred-environment'); // get preferred environment header value
  const mockedCampaign = req.get('x-mock-insight') !== undefined; // get mocked campaign flag
  const spanId = req.get('x-b3-spanid') || crypto.randomBytes(8).toString('hex'); // get spand id
  const traceId = req.get('x-b3-traceid') || crypto.randomBytes(16).toString('hex'); // get trace id
  const xOriginatingApplCode = req.get('x-originating-appl-code'); // get x-originating-appl-code
  const xFeatureFlagUid = req.get('x-feature-flag-uid'); // get fid flag for Insights pilot testing - PIGEON-5412
  const { application } = query;
  return {
    country,
    cardNumber,
    application,
    channelId,
    xApplication,
    language,
    languageContentful,
    preferredEnv,
    mockedCampaign,
    spanId,
    traceId,
    xOriginatingApplCode,
    xFeatureFlagUid,
  };
};

/**
 * map campaign and product data to rule, then sort them.
 *
 * @param {Array} rules
 * @param {Object} query
 * @param {Object} config
 * @param {boolean} filterByVersion - For Nova
 * @returns {array} - Filtered rules based on query passed
 */
const filterRulesByQuery = (rules, query, config, filterByVersion) => {
  rules = filterByContainerAndPage(rules, query.page, query.container);
  if (R.pathOr(false, [ 'features', 'application' ], config)) {
    rules = filterByApplication(rules, query.application);
  }
  if (filterByVersion) {
    rules = filterRulesByVersion(rules, {
      platform: trim(query.platform),
      appVersion: trim(query.app_version),
      osVersion: trim(query.os_version),
      deviceModel: trim(query.device_model),
    });
  }
  return rules;
};

const mapCustomerProducts = (accountResponse) => {
  let customerProducts = [];
  if (accountResponse && accountResponse.status === 'fulfilled') {
    customerProducts = (accountResponse.value.accountList || [])
      .reduce((products, accountProduct) => {
        const {
          ownership,
          ciProductCode,
          ciProductSubCode,
          accountUniqueId,
        } = accountProduct;

        const product = {
          ownership,
          code: ciProductCode,
          sub_code: ciProductSubCode,
          account_key: accountUniqueId,
        };
        return [ ...products, product ];
      }, []);
  }
  return customerProducts;
};

module.exports = {
  sortCampaignsByStartDate,
  accessHeadersAndParams,
  filterRulesByQuery,
  paginateRules,
  mapTargetedCampaigns,
  mapCustomerProducts,
  mapContentAndVariables,
  mapMassCampaigns,
};
