/* eslint-disable sonarjs/no-duplicate-string */
const {
  decodeUserContext,
  matchVersionTargeting,
  filterByContainerAndPage,
  filterByExternalRef,
  filterByApplication,
  getApplicationPlatform,
  generateMessageIdsPega,
  isCCAU,
  getEmbedded,
  extractCustomerDataForDemographicTargeting,
  calculateAge,
  filterByWealthTargeting,
} = require('./index');

const appVersion = {
  eq222: '2.2.2',
  lt222: '<2.2.2',
  lte222: '<=2.2.2',
  gt222: '>2.2.2',
  gte222: '>=2.2.2',
  rn222333: '2.2.2 - 3.3.3',
};

const osVersion = {
  eq2: '2',
  lt22: '<2.2',
  gte: '>=2.2.2',
  eq333: '3.3.3',
};

const deviceModel = {
  iphoneXR: 'iPhone XR',
  iphoneXS: 'iPhone XS',
  androidS2: 'Samsung Galaxy S2',
  androidS4: 'Samsung Galaxy S4',
};

describe('helpers', () => {
  describe('version targeting', () => {
    describe('invalid requests', () => {
      test('should match if platform not present', () => {
        expect(matchVersionTargeting({}, { platforms: [ 'ios' ] })).toEqual(true);
      });
      test('should not match if targeting schema version is not supported', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
        }, {
          platforms_targeting: [
            { v: 2, platform: 'ios', items: [] },
          ],
        })).toEqual(false);
      });
    });
    describe('old platform', () => {
      test('should match platform', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
        }, {
          platforms: [ 'ios' ],
        })).toEqual(true);
      });
      test('should match platform from multiple platforms', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
        }, {
          platforms: [ 'android', 'ios' ],
        })).toEqual(true);
      });
      test('should match platform with app_version', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          appVersion: appVersion.eq222,
        }, {
          platforms: [ 'ios' ],
          app_version: appVersion.eq222,
        })).toEqual(true);
      });
      test('should not match platform with mismatched app_version', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          appVersion: appVersion.eq222,
        }, {
          platforms: [ 'ios' ],
          app_version: '2.2.3',
        })).toEqual(false);
      });
    });
    describe('no targeting', () => {
      test('should match on platform and empty request version targeting', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [] },
          ],
        })).toEqual(true);
      });
      test('should match on platform and empty request version targeting with multiple platforms', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
        }, {
          platforms_targeting: [
            { v: 1, platform: 'android', items: [] },
            { v: 1, platform: 'ios', items: [] },
          ],
        })).toEqual(true);
      });
      test('should not match on platform and empty request app version targeting', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { app_version: appVersion.eq222 } ] },
          ],
        })).toEqual(false);
      });
      test('should not match on platform and empty request os version targeting', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { os_version: osVersion.eq333 } ] },
          ],
        })).toEqual(false);
      });
      test('should not match on platform and empty request device model targeting', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { device_model: deviceModel.iphoneXR } ] },
          ],
        })).toEqual(false);
      });
    });

    describe('app_version', () => {
      test('should match on platform and empty version targeting', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          appVersion: appVersion.eq222,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [] },
          ],
        })).toEqual(true);
      });
      test('should match on platform and satisfying app version targeting', () => {
        const tests = [
          // match
          '2.2.2',
          // less than
          '<2.2.3', '<2.3.1', '<3.1.1', '<2.2.3-1',
          // less than or equal
          '<=2.2.2', '<=2.2.3', '<=2.3.1', '<=3.1.1', '<=2.2.3-1',
          // greater than
          '>2.2.1', '>2.1.9', '>1.10.5', '>2.2.2-1',
          // greater than or equal
          '>=2.2.2', '>=2.2.1', '>=2.1.9', '>=1.10.5', '>=2.2.2-1',
          // range
          '2.2.1 - 2.2.3', '2.2.1 - 2.2.2', '2.1.0 - 2.3.0', '1.9.5 - 3.8.4', '2.2.2-1 - 2.2.3-1',
        ];
        tests.forEach((test) => {
          expect(matchVersionTargeting({
            platform: 'ios',
            appVersion: appVersion.eq222,
          }, {
            platforms_targeting: [
              { v: 1, platform: 'ios', items: [ { app_version: test } ] },
            ],
          })).toEqual(true);
        });
      });
      test('should match with multiple targeting parameters', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          appVersion: appVersion.eq222,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { app_version: '1.0.0' } ] },
            { v: 1, platform: 'ios', items: [ { app_version: appVersion.eq222 } ] },
          ],
        })).toEqual(true);
      });
      test('should not match if os_version is set', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          appVersion: appVersion.eq222,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { os_version: osVersion.eq333 } ] },
          ],
        })).toEqual(false);
      });
      test('should not match if app_version and os_version are set', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          appVersion: appVersion.eq222,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { app_version: appVersion.eq222, os_version: osVersion.eq333 } ] },
          ],
        })).toEqual(false);
      });
      test('should not match if device_model is set', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          appVersion: appVersion.eq222,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { device_model: deviceModel.iphoneXR } ] },
          ],
        })).toEqual(false);
      });
      test('should not match if app_version and device_model are set', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          appVersion: appVersion.eq222,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { app_version: appVersion.eq222, device_model: deviceModel.iphoneXR } ] },
          ],
        })).toEqual(false);
      });
      test('should not match on platform and app version targeting', () => {
        const tests = [
          // match
          '2.2.1', '2.3.2', '3.2.2', '2.2.2-1',
          // less than
          '<2.2.2', '<2.2.1', '<2.1.2', '<1.2.9', '<2.2.2-1',
          // less than or equal
          '<=2.2.1', '<=2.1.2', '<=1.2.9', '<=2.2.2-1',
          // greater than
          '>2.2.2', '>2.2.3', '>2.3.9', '>3.1.5', '>2.2.3-1',
          // greater than or equal
          '>=2.2.3', '>=2.3.9', '>=3.1.5', '>=2.2.3-1',
          // range
          '2.2.0 - 2.2.1', '2.2.3 - 2.2.5', '1.1.0 - 2.2.1', '2.2.3 - 5.1.0', '2.2.3-1 - 2.2.3-2',
        ];
        tests.forEach((test) => {
          expect(matchVersionTargeting({
            platform: 'ios',
            appVersion: appVersion.eq222,
          }, {
            platforms_targeting: [
              { v: 1, platform: 'ios', items: [ { app_version: test } ] },
            ],
          })).toEqual(false);
        });
      });
    });

    describe('os_version', () => {
      test('should match on platform and empty version targeting', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          osVersion: osVersion.eq333,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [] },
          ],
        })).toEqual(true);
      });
      test('should match on platform and satisfying os version targeting', () => {
        const tests = [
          // match
          '2', '2.0', '2.0.0',
          // less than
          '<2.2.3', '<2.3', '<3',
          // less than or equal
          '<=2.0.0', '<=2.2.3', '<=2.3', '<=3',
          // greater than
          '>1.9.9', '>1.8', '>1',
          // greater than or equal
          '>=2.0.0', '>=1.9.9', '>=1.8', '>=1',
          // range
          '2.0.0 - 2.1.2', '1.9.9 - 2.3.4', '1.9 - 2.3', '1 - 3',
        ];
        tests.forEach((test) => {
          expect(matchVersionTargeting({
            platform: 'ios',
            osVersion: osVersion.eq2,
          }, {
            platforms_targeting: [
              { v: 1, platform: 'ios', items: [ { os_version: test } ] },
            ],
          })).toEqual(true);
        });
      });
      test('should not match on platform and non-satisfying os version targeting', () => {
        const tests = [
          // match
          '1', '1.0', '1.0.0',
          // less than
          '<2.0.0', '<1.9', '<1',
          // less than or equal
          '<=1.9.9', '<=1.8', '<=1',
          // greater than
          '>2.0.0', '>2.1', '>3',
          // greater than or equal
          '>=2.0.1', '>=2.1', '>=3',
          // range
          '2.0.1 - 2.1.2', '2.1 - 2.3', '3 - 4',
        ];
        tests.forEach((test) => {
          expect(matchVersionTargeting({
            platform: 'ios',
            osVersion: osVersion.eq2,
          }, {
            platforms_targeting: [
              { v: 1, platform: 'ios', items: [ { os_version: test } ] },
            ],
          })).toEqual(false);
        });
      });
      test('should match with multiple targeting parameters', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          osVersion: osVersion.eq333,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { os_version: osVersion.eq2 } ] },
            { v: 1, platform: 'ios', items: [ { os_version: osVersion.eq333 } ] },
          ],
        })).toEqual(true);
      });
      test('should not match if app_version is set', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          osVersion: osVersion.eq333,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { app_version: appVersion.eq222 } ] },
          ],
        })).toEqual(false);
      });
      test('should not match if os_version and app_version are set', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          osVersion: osVersion.eq333,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { app_version: appVersion.eq222, os_version: osVersion.eq333 } ] },
          ],
        })).toEqual(false);
      });
      test('should not match if device_model is set', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          osVersion: osVersion.eq333,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { device_model: deviceModel.iphoneXR } ] },
          ],
        })).toEqual(false);
      });
      test('should not match if os_version and device_model are set', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          osVersion: osVersion.eq333,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { os_version: osVersion.eq333, device_model: deviceModel.iphoneXR } ] },
          ],
        })).toEqual(false);
      });
      test('should not match on platform and app version targeting', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          osVersion: osVersion.eq333,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { os_version: osVersion.eq2 } ] },
          ],
        })).toEqual(false);
      });
    });

    describe('device_model', () => {
      test('should match on platform and empty version targeting', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          deviceModel: deviceModel.iphoneXR,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [] },
          ],
        })).toEqual(true);
      });
      test('should match on platform and os version targeting', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          deviceModel: deviceModel.iphoneXR,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { device_model: deviceModel.iphoneXR } ] },
          ],
        })).toEqual(true);
      });
      test('should match with multiple targeting parameters', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          deviceModel: deviceModel.iphoneXR,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { device_model: deviceModel.iphoneXS } ] },
            { v: 1, platform: 'ios', items: [ { device_model: deviceModel.iphoneXR } ] },
          ],
        })).toEqual(true);
      });
      test('should not match if app_version is set', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          deviceModel: deviceModel.iphoneXR,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { app_version: appVersion.eq222 } ] },
          ],
        })).toEqual(false);
      });
      test('should not match if device_model and app_version are set', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          deviceModel: deviceModel.iphoneXR,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { app_version: appVersion.eq222, device_model: deviceModel.iphoneXR } ] },
          ],
        })).toEqual(false);
      });
      test('should not match if os_version is set', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          deviceModel: deviceModel.iphoneXR,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { os_version: osVersion.eq333 } ] },
          ],
        })).toEqual(false);
      });
      test('should not match if os_version and device_model are set', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          deviceModel: deviceModel.iphoneXR,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { os_version: osVersion.eq333, device_model: deviceModel.iphoneXR } ] },
          ],
        })).toEqual(false);
      });
      test('should not match on platform and device model targeting', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          deviceModel: deviceModel.iphoneXR,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { device_model: deviceModel.iphoneXS } ] },
          ],
        })).toEqual(false);
      });
    });

    describe('app_version, os_version and device_model', () => {
      test('should match if app_version, os_version and device_model are set', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          appVersion: appVersion.eq222,
          osVersion: osVersion.eq333,
          deviceModel: deviceModel.iphoneXR,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { app_version: appVersion.eq222, os_version: osVersion.eq333, device_model: deviceModel.iphoneXR } ] },
          ],
        })).toEqual(true);
      });
      test('should match if app_version and os_version are set', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          appVersion: appVersion.eq222,
          osVersion: osVersion.eq333,
          deviceModel: deviceModel.iphoneXR,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { app_version: appVersion.eq222, os_version: osVersion.eq333 } ] },
          ],
        })).toEqual(true);
      });
      test('should match if app_version and device_model are set', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          appVersion: appVersion.eq222,
          osVersion: osVersion.eq333,
          deviceModel: deviceModel.iphoneXR,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { app_version: appVersion.eq222, device_model: deviceModel.iphoneXR } ] },
          ],
        })).toEqual(true);
      });
      test('should match if os_version and device_model are set', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          appVersion: appVersion.eq222,
          osVersion: osVersion.eq333,
          deviceModel: deviceModel.iphoneXR,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { os_version: osVersion.eq333, device_model: deviceModel.iphoneXR } ] },
          ],
        })).toEqual(true);
      });
      test('should not match if app_version, os_version and device_model are set and app_version does not match', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          appVersion: appVersion.eq222,
          osVersion: osVersion.eq333,
          deviceModel: deviceModel.iphoneXR,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { app_version: '2.2.1', os_version: osVersion.eq333, device_model: deviceModel.iphoneXR } ] },
          ],
        })).toEqual(false);
      });
      test('should not match if app_version, os_version and device_model are set and os_version does not match', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          appVersion: appVersion.eq222,
          osVersion: osVersion.eq333,
          deviceModel: deviceModel.iphoneXR,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { app_version: appVersion.eq222, os_version: osVersion.eq2, device_model: deviceModel.iphoneXR } ] },
          ],
        })).toEqual(false);
      });
      test('should not match if app_version, os_version and device_model are set and device_model does not match', () => {
        expect(matchVersionTargeting({
          platform: 'ios',
          appVersion: appVersion.eq222,
          osVersion: osVersion.eq333,
          deviceModel: deviceModel.iphoneXR,
        }, {
          platforms_targeting: [
            { v: 1, platform: 'ios', items: [ { app_version: appVersion.eq222, os_version: osVersion.eq333, device_model: deviceModel.iphoneXS } ] },
          ],
        })).toEqual(false);
      });
    });
  });

  describe('filters', () => {
    test('filter container and pages', () => {
      const rules = [
        { name: 'rule0', pages: [ 'accounts' ], external_ref: 'MASS' },
        { name: 'rule1', pages: [ 'activities', 'cc' ], container: 'priority-box', external_ref: 'MASS' },
        { name: 'rule1', pages: [ ' cc  ', ' SAV ' ], container: 'priority-box', external_ref: 'CC0101' },
      ];
      // Params - rules, included pages, container, excluded pages
      expect(filterByContainerAndPage(rules, 'accounts')).toHaveLength(1);
      expect(filterByContainerAndPage(rules, 'login')).toHaveLength(0);
      expect(filterByContainerAndPage(rules, ' cc        ')).toHaveLength(2);
      expect(filterByContainerAndPage(rules, 'SAV')).toHaveLength(1);
      expect(filterByContainerAndPage(rules, 'SAV', 'priority-box')).toHaveLength(1);
      expect(filterByContainerAndPage(rules, 'sav', 'PRIORITY-BOX')).toHaveLength(1);
      expect(filterByContainerAndPage(rules, 'SAV', 'box-priority')).toHaveLength(0);
      expect(filterByContainerAndPage(rules, undefined, 'not-existant-container')).toHaveLength(0);
      expect(filterByContainerAndPage(rules, undefined, 'priority-box')).toHaveLength(2);
      expect(filterByContainerAndPage(rules, undefined, undefined, undefined)).toHaveLength(rules.length);
      expect(filterByContainerAndPage(rules, 'activities,pages', undefined, undefined)).toHaveLength(1);
      expect(filterByContainerAndPage(rules, undefined, undefined, 'cc')).toHaveLength(1);
    });

    test('filter external_ref', () => {
      const rules = [
        { name: 'rule0', pages: [ 'accounts' ], external_ref: 'MASS' },
        { name: 'rule1', pages: [ 'activities', 'cc' ], container: 'priority-box', external_ref: 'MASS' },
        { name: 'rule1', pages: [ ' cc  ', ' SAV ' ], container: 'priority-box', external_ref: 'CC0101' },
      ];
      expect(filterByExternalRef(rules, [ 'MASS' ])).toHaveLength(2);
      expect(filterByExternalRef(rules, [ 'CC0101' ])).toHaveLength(1);
      expect(filterByExternalRef(rules, [ 'message' ], undefined)).toHaveLength(0);
      expect(filterByExternalRef(rules, [ null ])).toHaveLength(0);
    });

    test('filter application when application is provided', () => {
      const rules = [
        { name: 'rule0', pages: [ 'accounts' ], external_ref: 'MASS', application: 'nova' },
        { name: 'rule1', pages: [ 'activities', 'cc' ], container: 'priority-box', external_ref: 'MASS', application: 'nova' },
        { name: 'rule1', pages: [ ' cc  ', ' SAV ' ], container: 'priority-box', external_ref: 'CC0101', application: 'phoenix' },
      ];
      expect(filterByApplication(rules, 'nova')).toHaveLength(2);
      expect(filterByApplication(rules, 'Nova')).toHaveLength(2);
    });

    test('filter by application when application is undefined', () => {
      const rules = [
        { name: 'rule0', pages: [ 'accounts' ], external_ref: 'MASS' },
        { name: 'rule1', pages: [ 'activities', 'cc' ], container: 'priority-box', external_ref: 'MASS' },
        { name: 'rule1', pages: [ ' cc  ', ' SAV ' ], container: 'priority-box', external_ref: 'CC0101' },
      ];
      expect(filterByApplication(rules, 'nova')).toHaveLength(3);
    });
  });

  describe('empty strings as version targeting query params behaviour', () => {
    test('should match a campaign with no app_version if app_version query param is empty string', () => {
      expect(matchVersionTargeting({
        platform: 'ios',
        osVersion: osVersion.eq333,
        appVersion: '',
      }, {
        platforms_targeting: [
          { v: 1, platform: 'ios', items: [ { os_version: osVersion.eq333 } ] },
        ],
      })).toEqual(true);
    });

    test('should match a campaign with no os_version if os_version query param is empty string', () => {
      expect(matchVersionTargeting({
        platform: 'ios',
        appVersion: appVersion.eq222,
        osVersion: '',
      }, {
        platforms_targeting: [
          { v: 1, platform: 'ios', items: [ { app_version: appVersion.lte222 } ] },
        ],
      })).toEqual(true);
    });

    test('should match a campaign with no os_version if os_version query param is undefined (proves consistent behaviour)', () => {
      expect(matchVersionTargeting({
        platform: 'ios',
        appVersion: appVersion.eq222,
        osVersion: undefined,
      }, {
        platforms_targeting: [
          { v: 1, platform: 'ios', items: [ { app_version: appVersion.lte222 } ] },
        ],
      })).toEqual(true);
    });

    test('should match a campaign when os_version/app_version/device_model are empty strings and rule has no VT', () => {
      expect(matchVersionTargeting({
        platform: 'ios',
        appVersion: '',
        osVersion: '',
        deviceModel: '',
      }, {
        platforms_targeting: [
          { v: 1, platform: 'ios', items: [ ] },
        ],
      })).toEqual(true);
    });

    test('should not match campaign when os_version query param is empty string and os_version is present in rule targeting', () => {
      expect(matchVersionTargeting({
        platform: 'ios',
        osVersion: '',
      }, {
        platforms_targeting: [
          { v: 1, platform: 'ios', items: [ { os_version: '13.1.1' } ] },
        ],
      })).toEqual(false);
    });
  });

  describe('application and platform name matching', () => {
    test('should return nova for unsupported platform', () => {
      const result = getApplicationPlatform('asdfasdf', undefined);
      expect(result).toEqual({ application: 'nova', platform: 'asdfasdf' });
    });
    test('should return phoenix when platform is phoenix', () => {
      const result = getApplicationPlatform('phoenix', undefined);
      expect(result).toEqual({ application: 'phoenix', platform: 'web' });
    });
    test('should return application and platform unmodified for phoenix', () => {
      const result = getApplicationPlatform('web', 'phoenix');
      expect(result).toEqual({ application: 'phoenix', platform: 'web' });
    });
    test('should return nova when platofmr is ios', () => {
      const result = getApplicationPlatform('ios', undefined);
      expect(result).toEqual({ application: 'nova', platform: 'ios' });
    });
    test('should return application and platform unmodified for nova', () => {
      const result = getApplicationPlatform('ios', 'nova');
      expect(result).toEqual({ application: 'nova', platform: 'ios' });
    });
    test('should return application and platform unmodified for another app', () => {
      const result = getApplicationPlatform('web', 'hubble');
      expect(result).toEqual({ application: 'hubble', platform: 'web' });
    });
  });

  describe('Generating Message IDs', () => {
    test('Check if correct message id is generated for french', () => {
      const campaign = {
        message_source: 'PEGAV2',
        campaign_id: '5576032301',
        language: 'fr',
      };
      const oldMessageId = generateMessageIdsPega(campaign.campaign_id, campaign.language, campaign.message_source);
      expect(oldMessageId).toBe('4684ee38-46f8-385f-b696-6599a7dcc9e9-98357178');
    });

    test('Check if correct message id is generated for english', () => {
      const campaign = {
        message_source: 'PEGAV2',
        campaign_id: '5576032301',
        language: 'en',
      };
      const oldMessageId = generateMessageIdsPega(campaign.campaign_id, campaign.language, campaign.message_source);
      expect(oldMessageId).toBe('66b61d28-55de-3b1a-b71b-cfb3f0b4df96-98357178');
    });
  });

  describe('utilities', () => {
    it('decodeUserContext - happy path', () => {
      const xuc = '************************************************************************************************************************************************************************************************************************************************************';
      const xucDecoded = decodeUserContext({}, xuc);
      const expected = {
        'countryCode': 'DO',
        'cid': 'c665e9b9-fa83-4a05-a429-f95e4f3c9fe3',
        'customerId': 'c665e9b9-fa83-4a05-a429-f95e4f3c9fe4',
        'locale': 'es_DO',
        'login': 'undefined',
        'remoteAddress': '**************',
      };
      expect(xucDecoded).toStrictEqual(expected);
    });

    it('decodeUserContext - supplied with undefined', () => {
      const logger = { warn: jest.fn() };
      const xucDecoded = decodeUserContext(logger, undefined);
      expect(xucDecoded).toBeFalsy();
      expect(logger.warn).toBeCalled();
    });

    it('decodeUserContext - supplied with non-b64-encoded', () => {
      const logger = { warn: jest.fn(arg => expect(arg).toMatchObject({
        message: 'Failed to parse user context',
      })) };
      const xucDecoded = decodeUserContext(logger, 'this is definitely not b64 encoded');
      expect(xucDecoded).toBeFalsy();
      expect(logger.warn).toBeCalled();
    });

    it('isCCAU', () => {
      // by country
      expect(isCCAU({ country: 'DO' })).toBeTruthy(); // happy path
      expect(isCCAU({ country: 'CA' })).toBeFalsy(); // non-CCAU
      expect(isCCAU({ country: 'XYZ123' })).toBeFalsy(); // invalid input

      // by campaign rule type
      expect(isCCAU({ campaignRuleType: 'ccau_campaign' })).toBeTruthy(); // happy path
      expect(isCCAU({ campaignRuleType: 'campaign' })).toBeFalsy(); // non-CCAU
      expect(isCCAU({ campaignRuleType: 'XYZ123' })).toBeFalsy(); // invalid input

      // country takes precedence
      expect(isCCAU({ country: 'DO', campaignRuleType: 'XYZ123' })).toBeTruthy();
    });
  });

  describe('getEmbedded', () => {
    it('should return Y when passed value is true', () => {
      const req = { get: jest.fn().mockReturnValueOnce('true') };
      expect(getEmbedded({ req })).toEqual('Y');
    });

    it('should return N when passed value is false', () => {
      const req = { get: jest.fn().mockReturnValueOnce('false') };
      expect(getEmbedded({ req })).toEqual('N');
    });
  });

  describe('calculateAge', () => {
    const defaultDateOfBirth = '1990-01-01';

    beforeEach(() => {
    // Mock current date to 2024-01-15 for consistent testing
      jest.useFakeTimers();
      jest.setSystemTime(new Date('2024-01-15'));
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    test('calculates age correctly for a birthday that has passed this year', () => {
      const result = calculateAge(defaultDateOfBirth);
      expect(result).toBe(34);
    });

    test('calculates age correctly for a birthday that has not passed this year', () => {
      const result = calculateAge('1990-06-01');
      expect(result).toBe(33);
    });

    test('calculates age correctly for a birthday today', () => {
      const result = calculateAge('1990-01-15');
      expect(result).toBe(34);
    });

    test('returns null for null input', () => {
      const result = calculateAge(null);
      expect(result).toBeNull();
    });

    test('returns null for undefined input', () => {
      const result = calculateAge(undefined);
      expect(result).toBeNull();
    });

    test('returns null for empty string', () => {
      const result = calculateAge('');
      expect(result).toBeNull();
    });

    test('calculates age correctly for leap year birthday', () => {
      const result = calculateAge('2000-02-29');
      expect(result).toBe(23);
    });
  });

  describe('extractCustomerDataForDemographicTargeting', () => {
    const missingFieldsError = 'Missing required customer data fields';
    const defaultDateOfBirth = '1990-01-01';

    beforeEach(() => {
      jest.useFakeTimers();
      jest.setSystemTime(new Date('2024-01-15'));
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    test('transforms complete customer data correctly', () => {
      const rawData = {
        language: 'English',
        country: 'CA',
        province: 'ON',
        gender: 'miss',
        date_of_birth: defaultDateOfBirth,
      };

      const result = extractCustomerDataForDemographicTargeting(rawData);

      expect(result).toEqual({
        language: 'en',
        country: 'canada',
        province: 'ON',
        gender: 'ms',
        age: 34,
      });
    });

    test('transforms French language correctly', () => {
      const rawData = {
        language: 'French',
        country: 'Canada',
        province: 'QC',
        gender: 'Ms',
        date_of_birth: '1985-06-15',
      };

      const result = extractCustomerDataForDemographicTargeting(rawData);

      expect(result.language).toBe('fr');
    });

    test('transforms female gender variations correctly', () => {
      const testCases = [ 'Miss', 'Mrs', 'Ms' ];

      testCases.forEach((gender) => {
        const rawData = {
          language: 'English',
          country: 'CA',
          province: 'BC',
          gender,
          date_of_birth: defaultDateOfBirth,
        };

        const result = extractCustomerDataForDemographicTargeting(rawData);
        expect(result.gender).toBe('ms');
      });
    });

    test('transforms male gender correctly', () => {
      const rawData = {
        language: 'English',
        country: 'CA',
        province: 'AB',
        gender: 'Mr',
        date_of_birth: defaultDateOfBirth,
      };

      const result = extractCustomerDataForDemographicTargeting(rawData);
      expect(result.gender).toBe('mr');
    });

    test('handles unrecognized gender as undisclosed', () => {
      const rawData = {
        language: 'English',
        country: 'CA',
        province: 'SK',
        gender: 'Other',
        date_of_birth: defaultDateOfBirth,
      };

      const result = extractCustomerDataForDemographicTargeting(rawData);
      expect(result.gender).toBe('undisclosed');
    });

    test('transforms country variations correctly', () => {
      const countryVariations = [ 'CA', 'CAN', 'Canada' ];

      countryVariations.forEach((country) => {
        const rawData = {
          language: 'English',
          country,
          province: 'MB',
          gender: 'Mr',
          date_of_birth: defaultDateOfBirth,
        };

        const result = extractCustomerDataForDemographicTargeting(rawData);
        expect(result.country).toBe('canada');
      });
    });

    test('handles non-Canada country as non-canada', () => {
      const rawData = {
        language: 'English',
        country: 'US',
        province: 'NY',
        gender: 'Mr',
        date_of_birth: defaultDateOfBirth,
      };

      const result = extractCustomerDataForDemographicTargeting(rawData);
      expect(result.country).toBe('non-canada');
    });

    test('returns null for null input', () => {
      const result = extractCustomerDataForDemographicTargeting(null);
      expect(result).toBeNull();
    });

    test('returns null for undefined input', () => {
      const result = extractCustomerDataForDemographicTargeting(undefined);
      expect(result).toBeNull();
    });

    test('throws error for missing language field', () => {
      const rawData = {
        country: 'CA',
        province: 'ON',
        gender: 'Mr',
        date_of_birth: defaultDateOfBirth,
      };

      expect(() => extractCustomerDataForDemographicTargeting(rawData)).toThrow(
        missingFieldsError,
      );
    });

    test('throws error for missing country field', () => {
      const rawData = {
        language: 'English',
        province: 'ON',
        gender: 'Mr',
        date_of_birth: defaultDateOfBirth,
      };

      expect(() => extractCustomerDataForDemographicTargeting(rawData)).toThrow(
        missingFieldsError,
      );
    });

    test('throws error for missing province field', () => {
      const rawData = {
        language: 'English',
        country: 'CA',
        gender: 'Mr',
        date_of_birth: defaultDateOfBirth,
      };

      expect(() => extractCustomerDataForDemographicTargeting(rawData)).toThrow(
        missingFieldsError,
      );
    });

    test('throws error for missing gender field', () => {
      const rawData = {
        language: 'English',
        country: 'CA',
        province: 'ON',
        date_of_birth: defaultDateOfBirth,
      };

      expect(() => extractCustomerDataForDemographicTargeting(rawData)).toThrow(
        missingFieldsError,
      );
    });

    test('throws error for missing date_of_birth field', () => {
      const rawData = {
        language: 'English',
        country: 'CA',
        province: 'ON',
        gender: 'Mr',
      };

      expect(() => extractCustomerDataForDemographicTargeting(rawData)).toThrow(
        missingFieldsError,
      );
    });

    test('handles case-insensitive transformations', () => {
      const rawData = {
        language: 'ENGLISH',
        country: 'ca',
        province: 'on',
        gender: 'MR',
        date_of_birth: defaultDateOfBirth,
      };

      const result = extractCustomerDataForDemographicTargeting(rawData);

      expect(result).toEqual({
        language: 'en',
        country: 'canada',
        province: 'on',
        gender: 'mr',
        age: 34,
      });
    });
  });

  describe('wealth targeting', () => {
    const mockCustomerData = {
      'business_line': [ {
        'business_line': 'SDBI',
      }, {
        'business_line': 'SMI',
      } ],
      'segment': '1',
      'mf_knowledge': 'M',
      'fix_income_knowledge': 'M',
      'stock_knowledge': 'M',
      'margin_knowledge': 'M',
      'equity_options_knowledge': 'M',
      'short_sale_knowledge': 'M',
      'iclub': 'Gold',
    };

    const mockRules = [ {
      mass_targeting: {
        id: 'itrade_lob_only',
        wealth_lobs: [ 'SDBI' ],
        segment_ids: [ '1', '2' ],
        iclub_tiers: [ 'Gold' ],
        investment_knowledge: {
          mf_knowledge: [ 'L', 'M', 'H' ],
        },
      },
    }, {
      mass_targeting: {
        id: 'itrade_smi_lob',
        wealth_lobs: [ 'SDBI', 'SMI' ],
        segment_ids: [ '1', '2' ],
        iclub_tiers: [ 'Gold' ],
        investment_knowledge: {
          mf_knowledge: [ 'L', 'M', 'H' ],
        },
      },
    }, {
      mass_targeting: {
        id: 'smi_lob',
        wealth_lobs: [ 'SMI' ],
        segment_ids: [ '1', '2' ],
        iclub_tiers: [ 'Gold' ],
        investment_knowledge: {
          mf_knowledge: [ 'L', 'M', 'H' ],
        },
      },
    }, {
      mass_targeting: {
        id: 'trust_lob',
        wealth_lobs: [ 'TRST' ],
        segment_ids: [ '1', '2' ],
        iclub_tiers: [ 'Gold' ],
        investment_knowledge: {
          mf_knowledge: [ 'L', 'M', 'H' ],
        },
      },
    } ];

    test('should return empty if customerdata is empty', () => {
      expect(filterByWealthTargeting(mockRules, {})).toEqual([]);
    });

    test('should return itrade rules', () => {
      expect(filterByWealthTargeting(mockRules, mockCustomerData)).toEqual([]);
    });
  });
});
