/**
 * Create a hash map that contains max rules per container for fast lookup
 *
 * @param {Array<string>} containerNameList - List of container slugs that we want to search their rule limits
 * @param {Array<Object>} containers - List of container objects from the rule API
 * @returns {Map<string, number>} - Map of containerSlug to maxRulePerContainer
 */
const buildRuleLimitPerContainerMap = (containerNameList, containers) => {
  const ruleLimitPerContainerMap = new Map();

  // Preprocess containers into a lookup map for constant access
  const containerLookup = new Map(
    containers.map(
      ({ admin_container_slug: adminContainerSlug, admin_container_limit: adminContainerLimit }) => [
        adminContainerSlug,
        adminContainerLimit,
      ],
    ),
  );

  containerNameList.forEach((slug) => {
    if (!ruleLimitPerContainerMap.has(slug)) {
      const limit = containerLookup.get(slug);
      ruleLimitPerContainerMap.set(slug, limit);
    }
  });

  return ruleLimitPerContainerMap;
};

/**
 * Apply container limit to list of rules
 *
 * @param {Array<Object>} rules - list of rules
 * @param {Map<string, number>} maxRulePerContainerMap - Map of containerSlug to maxRulePerContainer.
 * @returns {Array<Object>} - Filtered list of rules with limits applied per container

 */
const applyContainerLimitToRuleList = (rules, maxRulePerContainerMap) => {
  const groupedRules = new Map();

  // Grouped rules by container
  rules.forEach(rule => {
    const container = rule.container;
    if (!groupedRules.has(container)) {
      groupedRules.set(container, []);
    }
    groupedRules.get(container).push(rule);
  });

  // Apply limites per container
  const limitedRules = [];

  groupedRules.forEach((rulesInContainer, container) => {
    const limit = maxRulePerContainerMap.get(container);
    if (typeof limit === 'number') {
      limitedRules.push(...rulesInContainer.slice(0, limit));
    } else {
      // If no limit is not defined, include all rules
      limitedRules.push(...rulesInContainer);
    }
  });

  return limitedRules;
};

module.exports = {
  buildRuleLimitPerContainerMap,
  applyContainerLimitToRuleList,
};
