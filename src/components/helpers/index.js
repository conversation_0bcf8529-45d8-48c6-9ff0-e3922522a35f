const R = require('ramda');
const dotProp = require('dot-prop');
const semverSatisfies = require('semver/functions/satisfies');
const semverCoerce = require('semver/functions/coerce');
const { includes, isEmpty } = require('lodash');
const crypto = require('crypto');
const atob = require('atob');

const {
  filterArrayByCriteria,
  filterRulesByCustomerProducts,
  filterRulesByCustomerScenePoints,
  filterRulesByLanguageTargetting,
  filterRulesByDefinedGeographicCriteria,
} = require('./massTargeting');
const {
  buildRuleLimitPerContainerMap,
  applyContainerLimitToRuleList,
} = require('./maxRulePerContainer');
const { isTargetedCampaign, isMassCampaign } = require('../campaign/routes/common');

const satisfies = (version, range, opts) => semverSatisfies(version, range, { includePrerelease: true, ...opts });

const matchVersionTargetingV0 = ({ platform, appVersion }, rule) => {
  return rule.platforms.includes(platform) && (rule.app_version ? satisfies(appVersion, rule.app_version) : true);
};

const trim = (s) => typeof s === 'string' ? s.trim() : s;
const cardValidationRegex = /^(?:4[0-9]{12}(?:[0-9]{3})?)$/;

const getFormattedTargetingOsVersion = (targetingOsVersion) => {
  let formattedTargetingOsVersion;
  const targetingOsArr = targetingOsVersion.split(' - ');
  if (targetingOsArr.length === 1) {
    // Not in hyphenated range format, extract the comparison operator, corece the value & re add the operator
    const operator = targetingOsArr[0].match('>=') || targetingOsArr[0].match('<=') || targetingOsArr[0].match('>') || targetingOsArr[0].match('<');
    if (operator && operator.length) {
      formattedTargetingOsVersion = operator[0] + semverCoerce(targetingOsArr[0]).raw;
    } else {
      formattedTargetingOsVersion = semverCoerce(targetingOsArr[0]).raw;
    }
  } else {
    formattedTargetingOsVersion = `${semverCoerce(targetingOsArr[0]).raw} - ${semverCoerce(targetingOsArr[1]).raw}`;
  }
  return formattedTargetingOsVersion;
};

const getFormattedTargetingAppVersion = (targetingAppVersion) => {
  let formattedTargetingAppVersion;
  const targetingAppArr = targetingAppVersion.split(' - ');
  if (targetingAppArr.length > 1) {
    // When using hyphenated ranges with includePrerelease, semver appends -0 to releases making a prerelease greater than a X.Y.Z version -
    // need to override this behaviour as it is inconsistent with other comparisons
    formattedTargetingAppVersion = `>=${targetingAppArr[0]} <=${targetingAppArr[1]}`;
    return formattedTargetingAppVersion;
  }
  return targetingAppVersion;
};

// eslint-disable-next-line sonarjs/cognitive-complexity
const matchItemVersionTargetingV1 = R.curry(({ appVersion, osVersion, deviceModel }, targeting) => {
  const targetingAppVersion = trim(targeting.app_version);
  const targetingOsVersion = trim(targeting.os_version);
  const targetingDeviceModel = trim(targeting.device_model);

  let formattedTargetingOsVersion;
  if (osVersion && targetingOsVersion) {
    formattedTargetingOsVersion = getFormattedTargetingOsVersion(targetingOsVersion);
  }

  let formattedTargetingAppVersion;
  if (appVersion && targetingAppVersion) {
    formattedTargetingAppVersion = getFormattedTargetingAppVersion(targetingAppVersion);
  }

  if (
    // app version set and doesn't match
    (appVersion && targetingAppVersion && !satisfies(appVersion, formattedTargetingAppVersion)) ||
    // os version set and doesn't match
    (osVersion && targetingOsVersion && osVersion !== targetingOsVersion && !satisfies(semverCoerce(osVersion), formattedTargetingOsVersion)) ||
    // device model set and doesn't match
    (deviceModel && targetingDeviceModel && deviceModel !== targetingDeviceModel) ||
    (!appVersion && targetingAppVersion) ||
    (!osVersion && targetingOsVersion) ||
    (!deviceModel && targetingDeviceModel)) {
    // specified parameter doesn't match
    return false;
  }
  return true;
});

const matchVersionTargetingV1 = ({ platform, appVersion, osVersion, deviceModel }, targeting) => {
  if (targeting.platform !== platform) {
    // platform doesn't match
    return false;
  }
  if (targeting.items.length === 0) {
    // no targeting criterias
    return true;
  }
  return targeting.items.some(matchItemVersionTargetingV1({ appVersion, osVersion, deviceModel }));
};

const matchVersionTargeting = R.curry(({ platform, appVersion, osVersion, deviceModel }, rule) => {
  if (!platform) {
    return true;
  }
  if (!rule.platforms_targeting) {
    // old format
    return matchVersionTargetingV0({ platform, appVersion }, rule);
  }
  // new format
  return rule.platforms_targeting.some((r) => {
    if (r.v === 1) {
      return matchVersionTargetingV1({ platform, appVersion, osVersion, deviceModel }, r);
    }
    return false;
  });
});

const filterRulesByVersion = (rules, opts) => rules.filter(matchVersionTargeting(opts));

const filterByContainerAndPage = (
  rules = [],
  targetInclPages = undefined,
  targetContainer = undefined,
  targetExclPages = undefined,
) => {
  if (!targetContainer && !targetInclPages && !targetExclPages) {
    return rules;
  }

  const targetPageArr = getTargetPageArray(targetInclPages, targetExclPages);
  return rules.filter(({ container, pages }) => {
    const pageMatch = targetPageArr.length && pages && pages.length > 0 &&
      (targetInclPages
        ? pages.some(page => targetPageArr.includes(page.trim().toLowerCase()))
        : !pages.some(page => targetPageArr.includes(page.trim().toLowerCase()))
      );
    const containerMatch = container && targetContainer &&
      container.trim().toLowerCase() === targetContainer.trim().toLowerCase();
    if (targetPageArr.length) {
      return targetContainer ? pageMatch && containerMatch : pageMatch;
    } else {
      return containerMatch;
    }
  });
};

const filterByEnrollmentStatus = ({ rules, deviceLock, twoStepVerificationStatus }) => {
  // it means this filter is disabled or can not get the 2SV for this user
  if (twoStepVerificationStatus === undefined) {
    return rules;
  }

  return rules.filter(rule => {
    const ruleTwoStepVerificationStatus = R.pathOr(undefined, [ 'mass_targeting', 'enrollment_status' ], rule);
    const ruleDeviceLock = R.pathOr(undefined, [ 'mass_targeting', 'device_lock' ], rule);
    // it means this rule will not filter out against 2SV or Device lock so it's not applicable for this filter
    const shouldFilter = (isMassCampaign(rule) || isTargetedCampaign(rule)) && rule.application === 'nova';
    if (!shouldFilter) return true;
    // it means if the user's 2SV status is of selected 2SV of the rule AND user's device lock is equal the selected device lock for the rule
    // Note: ruleDeviceLock === undefined means thes user selected it as "none"
    // Note: ruleTwoStepVerificationStatus === undefined is 2SV is not selected ( optional )
    if ((isEmpty(ruleTwoStepVerificationStatus) || includes(ruleTwoStepVerificationStatus, twoStepVerificationStatus)) && (ruleDeviceLock === undefined || ruleDeviceLock === deviceLock)) {
      return true;
    }
    // it means if the user's 2SV status is of selected 2SV opf the rule
    return false;
  });
};

/**
 * Return target include or exclude pages parsed into array of page ids.
 * Results are converted to lowercase for ease of case insensitive matching.
 *
 * @param {*} targetInclPages
 * @param {*} targetExclPages
 * @returns
 */
const getTargetPageArray = (targetInclPages, targetExclPages) => {
  let targetPageArr = [];
  if (targetInclPages || targetExclPages) {
    targetPageArr = targetInclPages ? targetInclPages.trim().split(',') : targetExclPages.trim().split(',');
  }
  return targetPageArr.map(p => p.toLowerCase());
};

const filterByExternalRef = (rules, refs) => (
  // eslint-disable-next-line camelcase
  rules.filter(({ external_ref }) => refs.includes(external_ref))
);

/**
 * Filter pigeon rules by application id
 *
 * @param {{ application }} rules - pigeon rules
 * @param {*} application - expected application
 * @returns
 */
const filterByApplication = (rules, application) => (
  rules.filter(r => {
    return !r.application || // backward compatibility for pass through rules without application set
      (application && application.toLowerCase() === r.application.toLowerCase()); // application id match
  })
);

const filterRulesByIClubTiers = (rule, customerData) => {
  if (!customerData || R.isEmpty(customerData)) {
    return [];
  }
  const ruleIclubTiers = R.pathOr([], [ 'mass_targeting', 'iclub_tiers' ], rule);
  if (R.isEmpty(ruleIclubTiers)) {
    return true;
  }

  const { iclub } = customerData;

  if (!iclub) {
    return false;
  }

  return R.includes(iclub, ruleIclubTiers);
};

const filterRulesBySegmentIds = (rule, customerData) => {
  if (!customerData || R.isEmpty(customerData)) {
    return [];
  }
  const ruleSegmentIds = R.pathOr([], [ 'mass_targeting', 'segment_ids' ], rule);
  if (R.isEmpty(ruleSegmentIds)) {
    return true;
  }
  const { segment } = customerData;

  if (!segment) {
    return false;
  }

  return R.includes(segment, ruleSegmentIds);
};

const filterRulesByInvestmentKnowledgeLevels = (rule, customerData) => {
  const ruleKnowledge = R.pathOr(null, [ 'mass_targeting', 'investment_knowledge' ], rule);

  if (!ruleKnowledge || R.isEmpty(ruleKnowledge)) {
    return true;
  }

  const ruleKnowledgePairs = R.toPairs(ruleKnowledge);

  return R.any(([ type, requiredLevels ]) => {
    const customerLevel = R.pathOr(null, [ type ], customerData);
    return customerLevel !== null && R.includes(customerLevel, requiredLevels);
  }, ruleKnowledgePairs);
};

const filterByWealthTargeting = (rules, customerData) => {
  if (!customerData || R.isEmpty(customerData)) {
    return [];
  }

  return R.filter(rule => {
    const { business_line: businessLine } = customerData;
    const customerLOB = businessLine.map(b => b.business_unit);
    const ruleLobs = R.pathOr([], [ 'mass_targeting', 'wealth_lobs' ], rule);

    const lobIntersection = R.intersection(customerLOB, ruleLobs);
    if (!R.isEmpty(ruleLobs) && R.isEmpty(lobIntersection)) {
      return false;
    }

    const isITradeOnly = R.and(
      R.equals(R.length(lobIntersection), 1),
      R.equals(lobIntersection[0], 'SDBI'),
    );

    if (isITradeOnly) {
      return filterRulesByIClubTiers(rule, customerData) &&
        filterRulesBySegmentIds(rule, customerData) &&
        filterRulesByInvestmentKnowledgeLevels(rule, customerData);
    }
    return true;
  }, rules);
};

/**
 * Resolve dynamic page placeholders to real pages consumable by client apps.
 * E.g. for 'account-key' page, resolve to collection of marvel account ids.
 * @param { object } rule
 * @param { array } customerProducts
 */
const resolveDynamicPages = (rule, customerProducts) => {
  // check pages for account-key and replace with an appropriate accountKey from customer's products
  const acctKeyPageIdx = rule.pages.indexOf('account-key');
  if (acctKeyPageIdx !== -1) {
    const productsFilterCriteria = R.pathOr({}, [ 'mass_targeting', 'product_pages' ], rule);
    const productsThatPassRuleFilter = filterArrayByCriteria(customerProducts, productsFilterCriteria);

    // map products to just their account ids
    const accountKeys = productsThatPassRuleFilter
      .filter(p => p['account_key'])
      .map(p => p['account_key']);
    rule.pages.splice(acctKeyPageIdx, 1, ...accountKeys);
  }
  return rule;
};

const isPhoenix = R.equals('phoenix');

// TODO: should be removed after Phoenix and Nova update their requests with `application` query param
const getApplicationPlatform = R.curry((platform, application) => R.cond([
  [ R.isNil, () => R.cond([
    [ isPhoenix, R.always({ application: 'phoenix', platform: 'web' }) ],
    [ R.T, R.always({ application: 'nova', platform }) ],
  ])(platform) ],
  [ R.T, R.always({ application, platform }) ],
])(application));

const getClientInfo = ({ req, res, config }) => {
  // check if the scope in the token has standard scope
  const isCustomerTokenAuth = checkStandardScope(dotProp.get(res.locals, `${config.tokenClaimsPath}.scope`));
  // get customerToken either from x-customer-authorization header or res.locals
  const customerToken = isCustomerTokenAuth ? dotProp.get(res.locals, config.tokenPath) : req.get('x-customer-authorization');
  let cardNumber = req.get('x-customer-scotiacard');
  if (isCustomerTokenAuth && dotProp.get(res.locals, `${config.tokenClaimsPath}.profile.customerInfo.cardNumber`)) {
    cardNumber = dotProp.get(res.locals, `${config.tokenClaimsPath}.profile.customerInfo.cardNumber`);
  } else if (isCustomerTokenAuth && cardValidationRegex.test(dotProp.get(res.locals, `${config.tokenClaimsPath}.sub`))) {
    cardNumber = dotProp.get(res.locals, `${config.tokenClaimsPath}.sub`);
  }
  const xUserContext = req.get('x-user-context'); // ccau user identifier
  const country = res.locals.country;
  const uid = isCCAU({ country: country }) ? xUserContext : cardNumber; // user identifier
  const clientIdentifier = dotProp.get(res.locals, `${config.tokenClaimsPath}.client_id`);
  const isAnonymousClient = config.anonymousClientIds ? config.anonymousClientIds.includes(clientIdentifier) : false;
  const anonymousRequestFlag = (!customerToken && !uid) || isAnonymousClient;

  return {
    country,
    customerToken,
    cardNumber,
    isAnonymousClient,
    anonymousRequestFlag,
    uid,
  };
};

const getChannelId = ({ req, application }) => {
  let channelId = req.get('x-channel-id');
  if (!channelId) {
    if (application && [ 'nova', 'starburst', 'wave' ].includes(application)) {
      channelId = 'Mobile';
    } else if (application === 'abm') {
      channelId = 'ABM';
    } else {
      channelId = 'Online';
    }
  }
  return channelId;
};

const getAgentUserAndXApplication = ({ req, channelId, query }) => {
  // get channel id
  let userAgent = req.get('User-Agent'); // get user agent
  let xApplication = req.get('x-application'); // send the x-application value
  if (!xApplication && channelId && channelId === 'Mobile') {
    userAgent = userAgent && (userAgent.indexOf('iPhone') > -1 || query.platform === 'ios') ? 'iPhone' : 'Android';
    xApplication = userAgent === 'iPhone' ? 'N1' : 'N2';
  }
  return { userAgent, xApplication };
};

const _isCCAUCountry = (c = '') => {
  return [ 'BS', 'BB', 'KY', 'DO', 'JM', 'TT', 'TC', 'PA', 'GY' ].includes(c.toUpperCase());
};

const getEmbedded = ({ req }) => {
  let embedded = req.get('Embedded');
  return embedded === 'false' ? 'N' : 'Y';
};

/**
 * Check supplied args in sequence if belonging to CCAU region.
 * Stops on first positive match.
 * Order: country, campaignRuleType.
 *
 * @param {Object} criteria
 * @param {string} criteria.country - two letter country code
 * @param {string} criteria.campaignRuleType - campaign rule type
 */
const isCCAU = (opts = {}) => {
  const { country, campaignRuleType } = opts;
  if (country) return _isCCAUCountry(country);
  if (campaignRuleType) return campaignRuleType === 'ccau_campaign';
  return false;
};

/**
 * Return only defined properties (that has not `null` or `undefined` values) of an object
 * @param {object} o Object
 * @returns {object} Object with only defined properties
 */
const pickTruthy = (o) => JSON.parse(JSON.stringify(o, (k, v) => v === null || v === undefined ? undefined : v));

/**
 * Check if object has a key, case insensitive
 * @param {string} key Key to check for
 * @param {object} o Object
 * @returns {boolean} Result
 */
const hasKey = R.curry((key, o) => Object.keys(o).map((s) => s.toLowerCase()).includes(key));

const checkStandardScope = R.cond([
  [ R.is(String), R.pipe(R.split(' '), R.includes('standard')) ],
  [ R.T, R.F ],
]);

const getHash = (input) => {
  let md5Bytes = crypto.createHash('md5').update(input).digest();
  md5Bytes[6] &= 0x0f; /* clear version        */
  md5Bytes[6] |= 0x30; /* set to version 3     */
  md5Bytes[8] &= 0x3f; /* clear variant        */
  md5Bytes[8] |= 0x80; /* set to IETF variant  */
  const hex = md5Bytes.toString('hex');
  return hex.replace(/(\w{8})(\w{4})(\w{4})(\w{4})(\w{12})/, '$1-$2-$3-$4-$5');
};

const getBytes = (input) => {
  return new TextEncoder().encode(input);
};

/**
 * Generate old message ID based on campaign ID, language and message source
 * @param {string} campaignId Campaign ID
 * @param {string} language Campaign's language
 * @param {string} messageSource Campaign's message source
 * @returns {string} Old message ID
 */
const generateMessageIdsPega = (campaignId, language, messageSource) => {
  try {
    const pegaHex = (messageSource === 'PEGAV2') ? '98357178' : '50454741';
    const uuidBase = campaignId + '_' + language;
    return getHash(getBytes(uuidBase)) + '-' + pegaHex;
  } catch (error) {
    return false;
  }
};

/**
 * Calculate disposition's expiration date using rule's end date and campaign's expiration data
 * @param {string} ruleEndDate Rule's end date
 * @param {string} campaignExpiryDate Campaign's expiration date
 * @returns {boolean} Disposition's expiration date
 */
const calcTargetedCampaignExpiry = (ruleEndDate, campaignExpiryDate) => {
  if (campaignExpiryDate) {
    // Insights date in YYYY-MM-DD format, rule date in YYYY-MM-DDThh:mm:ss.sssZ format
    // NOTE: need to verify actual values of hh:mm:ss for Insight's YYYY-MM-DD
    const ced = new Date(campaignExpiryDate);
    if (ced < new Date(ruleEndDate)) {
      return ced.toISOString();
    }
  }
  return ruleEndDate;
};

/**
 * Base 64 decode user context, and return in JSON parsed object.
 * If encounters failure, log error and return undefined.
 *
 * @param {Object} logger
 * @param {string} xUserContext - b64 encoded x-user-context request header
 * @returns
 */
const decodeUserContext = (logger, xUserContext) => {
  try {
    const decoded = atob(xUserContext);
    return JSON.parse(decoded);
  } catch (e) {
    logger.warn({ message: 'Failed to parse user context', error: e.message });
  }
};

const getUniqueCampaignIds = R.pipe(R.map(R.prop('campaign_id')), R.uniq);

/**
 * Calculate age based on date of birth
 *
 * @param {string} dateOfBirth - Date of birth in 'YYYY-MM-DD' format
 * @returns {number} - Age in years
 */
const calculateAge = (dateOfBirth) => {
  if (!dateOfBirth) return null;

  const today = new Date();
  const birthDate = new Date(dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDifference = today.getMonth() - birthDate.getMonth();

  // Adjust age if the birthday hasn't occurred yet this year
  if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }

  return age;
};

/**
 * Transform raw customer data from Prodigy API to demographic targeting format
 * @param {object} rawData - Raw customer data from Prodigy API
 * @returns {object} - Transformed customer data with demographic information
 */
const extractCustomerDataForDemographicTargeting = (rawData) => {
  if (!rawData || Object.keys(rawData).length === 0) {
    return null;
  }

  const { language, country, province, gender, date_of_birth: dateOfBirth } = rawData;
  if (!language || !country || !province || !gender || !dateOfBirth) {
    throw new Error('Missing required customer data fields');
  }

  const extractedData = {};

  // Transform language
  const lowerCaseLanguage = language.toLowerCase();
  if (lowerCaseLanguage === 'english') {
    extractedData.language = 'en';
  } else if (lowerCaseLanguage === 'french') {
    extractedData.language = 'fr';
  }

  // Transform country
  const lowerCaseCountry = country.toLowerCase();
  if (lowerCaseCountry === 'ca' || lowerCaseCountry === 'can' || lowerCaseCountry === 'canada') {
    extractedData.country = 'canada';
  } else {
    extractedData.country = 'non-canada';
  }

  // Keep province as is
  if (province) {
    extractedData.province = province;
  }

  // Transform gender
  const lowerCaseGender = gender.toLowerCase();
  if (lowerCaseGender === 'miss' || lowerCaseGender === 'mrs' || lowerCaseGender === 'ms') {
    extractedData.gender = 'ms';
  } else if (lowerCaseGender === 'mr') {
    extractedData.gender = 'mr';
  } else {
    extractedData.gender = 'undisclosed';
  }

  // Calculate age
  if (dateOfBirth) {
    extractedData.age = calculateAge(dateOfBirth);
  }

  return extractedData;
};

module.exports = {
  decodeUserContext,
  matchVersionTargeting,
  filterRulesByVersion,
  filterByContainerAndPage,
  filterByEnrollmentStatus,
  filterByExternalRef,
  filterRulesByCustomerProducts,
  filterRulesByCustomerScenePoints,
  filterRulesByLanguageTargetting,
  filterRulesByDefinedGeographicCriteria,
  filterByWealthTargeting,
  trim,
  filterByApplication,
  getApplicationPlatform,
  pickTruthy,
  hasKey,
  checkStandardScope,
  resolveDynamicPages,
  getClientInfo,
  getChannelId,
  getAgentUserAndXApplication,
  isCCAU,
  generateMessageIdsPega,
  calcTargetedCampaignExpiry,
  getUniqueCampaignIds,
  getEmbedded,
  buildRuleLimitPerContainerMap,
  applyContainerLimitToRuleList,
  calculateAge,
  extractCustomerDataForDemographicTargeting,
};
