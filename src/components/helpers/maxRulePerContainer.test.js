const {
  buildRuleLimitPerContainerMap,
  applyContainerLimitToRuleList,
} = require('./maxRulePerContainer');

describe('Rules limit per container helpers', () => {
  const testContainerNameList = [ 'orion-test-container', 'nova-test-container' ];

  describe('buildRuleLimitPerContainer', () => {
    test('should return correct map for matching container slugs', () => {
      const containers = [
        { admin_container_slug: testContainerNameList[0], admin_container_limit: 5 },
        { admin_container_slug: testContainerNameList[1], admin_container_limit: 10 },
      ];

      const result = buildRuleLimitPerContainerMap(testContainerNameList, containers);

      expect(result instanceof Map).toBe(true);
      expect(result.get(testContainerNameList[0])).toBe(5);
      expect(result.get(testContainerNameList[1])).toBe(10);
    });

    test('should return map with only matching slugs', () => {
      const containers = [
        { admin_container_slug: testContainerNameList[0], admin_container_limit: 5 },
      ];

      const result = buildRuleLimitPerContainerMap(testContainerNameList, containers);

      expect(result.size).toBe(2);
      expect(result.get(testContainerNameList[0])).toBe(5);
      expect(result.get(testContainerNameList[1])).toBeUndefined();
    });

    test('should return empty map when inputs are empty', () => {
      const result = buildRuleLimitPerContainerMap([], []);
      expect(result.size).toBe(0);
    });

    test('should handle duplicate slugs in containerNameList gracefully', () => {
      const duplicateSlugList = [ testContainerNameList[0], testContainerNameList[0] ];
      const containers = [
        { admin_container_slug: testContainerNameList[0], admin_container_limit: 5 },
      ];

      const result = buildRuleLimitPerContainerMap(duplicateSlugList, containers);

      expect(result.size).toBe(1);
      expect(result.get(testContainerNameList[0])).toBe(5);
    });

    test('should handle containers missing admin_container_limit', () => {
      const containers = [
        { admin_container_slug: testContainerNameList[0] }, // no limit
      ];

      const result = buildRuleLimitPerContainerMap(testContainerNameList, containers);

      expect(result.get(testContainerNameList[0])).toBeUndefined();
    });
  });

  describe('applyContainerLimitToRuleList', () => {
    const mockRules = [
      {
        id: '1',
        name: 'Rule A1',
        container: testContainerNameList[0],
        urgent: true,
        viewed: false,
        content: {
          title: 'Title A1',
          detailLink: {
            text: 'Learn More',
            ctaLink: {
              name: 'CTA A1',
              url: 'https://example.com/a1',
            },
          },
        },
        metadata: {
          tags: [ 'promo', 'urgent' ],
          priority: 1,
        },
      },
      {
        id: '2',
        name: 'Rule A2',
        container: testContainerNameList[0],
        urgent: false,
        viewed: true,
        content: {
          title: 'Title A2',
          detailLink: {
            text: 'Details',
            ctaLink: {
              name: 'CTA A2',
              url: 'https://example.com/a2',
            },
          },
        },
        metadata: {
          tags: [ 'info' ],
          priority: 2,
        },
      },
      {
        id: '3',
        name: 'Rule B1',
        container: testContainerNameList[1],
        content: {
          title: 'Title B1',
        },
      },
      {
        id: '4',
        name: 'Rule B2',
        container: testContainerNameList[1],
        content: {
          title: 'Title B2',
        },
      },
      {
        id: '5',
        name: 'Rule C1',
        container: 'container-c',
        content: {
          title: 'Title C1',
        },
      },
    ];

    test('should limit rules per container based on containerLimitMap', () => {
      const containerLimitMap = new Map([
        [ testContainerNameList[0], 1 ],
        [ testContainerNameList[1], 1 ],
        [ 'container-c', 2 ],
      ]);

      const result = applyContainerLimitToRuleList(mockRules, containerLimitMap);
      const resultIds = result.map(r => r.id);

      expect(resultIds).toEqual(expect.arrayContaining([ '1', '3', '5' ]));
      expect(resultIds).not.toContain('2');
      expect(resultIds).not.toContain('4');
      expect(result.length).toBe(3);
    });

    test('should return all rules if no limits are defined', () => {
      const containerLimitMap = new Map();
      const result = applyContainerLimitToRuleList(mockRules, containerLimitMap);
      expect(result.length).toBe(mockRules.length);
    });

    test('should handle missing containers in limit map gracefully', () => {
      const containerLimitMap = new Map([
        [ testContainerNameList[0], 1 ],
      ]);
      const result = applyContainerLimitToRuleList(mockRules, containerLimitMap);
      const resultIds = result.map(r => r.id);
      expect(resultIds).toEqual(expect.arrayContaining([ '1', '3', '4', '5' ]));
      expect(result.length).toBe(4);
    });

    test('should handle empty rule list', () => {
      const containerLimitMap = new Map([ [ testContainerNameList[0], 2 ] ]);
      const result = applyContainerLimitToRuleList([], containerLimitMap);
      expect(result).toEqual([]);
    });

    test('should handle non-numeric limits as undefined', () => {
      const containerLimitMap = new Map([
        [ testContainerNameList[0], 'two' ],
        [ testContainerNameList[1], null ],
      ]);
      const result = applyContainerLimitToRuleList(mockRules, containerLimitMap);
      expect(result.length).toBe(mockRules.length);
    });
  });
});
