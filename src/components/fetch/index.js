const R = require('ramda');
const { hasKey } = require('../helpers');

/**
 * Checks if object has an 'authorization` key, case insensitive
 * @param {object} Object
 * @returns {boolean} Result
 */
const hasAuthorizationKey = hasKey('authorization');

/**
 * Initializes and returns hystrixfied fetch
 * @param {object} cbopts HystrixJS options
 */
const init = ({ fetch, agent = null, authorizer = null, timeout = 10000 }) => {
  return async (uri, opts) => {
    const o = R.clone(opts);
    if (agent !== null && !o.agent) {
      o.agent = agent;
    }
    if (o.timeout === undefined) {
      o.timeout = timeout;
    }
    if (authorizer && typeof authorizer === 'function' && !hasAuthorizationKey(o.headers)) {
      o.headers['Authorization'] = `Bearer ${await authorizer()}`;
    }
    return fetch(uri, o);
  };
};

module.exports = init;
