/* eslint-disable sonarjs/no-duplicate-string */
const Fetch = require('./index');

const mockFetch = jest.fn();
const mockAuthorizer = jest.fn();

describe('Fetch', () => {
  beforeEach(() => {
    mockFetch.mockClear();
    mockAuthorizer.mockClear();
  });
  test('should initialize', () => {
    const fetch = Fetch({ fetch: mockFetch });
    expect(fetch).toBeDefined();
    expect(typeof fetch).toEqual('function');
  });
  test('should initialize with agent', async () => {
    const mockAgent = {};
    const mockUri = 'https://cloud.bns';
    const mockResult = { status: 200, json: jest.fn() };
    mockFetch.mockResolvedValueOnce(mockResult);
    const fetch = Fetch({ fetch: mockFetch, agent: mockAgent });
    const res = await fetch(mockUri, { headers: {} });
    expect(res).toBeDefined();
    expect(mockFetch).toBeCalled();
    expect(mockFetch.mock.calls[0][0]).toEqual(mockUri);
    expect(mockFetch.mock.calls[0][1]).toHaveProperty('agent');
    expect(mockFetch.mock.calls[0][1].agent).toEqual(mockAgent);
  });
  test('should initialize with timeout', async () => {
    const mockUri = 'https://cloud.bns';
    const mockResult = { status: 200, json: jest.fn() };
    mockFetch.mockResolvedValueOnce(mockResult);
    const fetch = Fetch({ fetch: mockFetch, timeout: 1000 });
    const res = await fetch(mockUri, { headers: {} });
    expect(res).toBeDefined();
    expect(mockFetch).toBeCalled();
    expect(mockFetch.mock.calls[0][0]).toEqual(mockUri);
    expect(mockFetch.mock.calls[0][1]).toHaveProperty('timeout');
    expect(mockFetch.mock.calls[0][1].timeout).toEqual(1000);
  });
  test('should initialize with default timeout', async () => {
    const mockUri = 'https://cloud.bns';
    const mockResult = { status: 200, json: jest.fn() };
    mockFetch.mockResolvedValueOnce(mockResult);
    const fetch = Fetch({ fetch: mockFetch });
    const res = await fetch(mockUri, { headers: {} });
    expect(res).toBeDefined();
    expect(mockFetch).toBeCalled();
    expect(mockFetch.mock.calls[0][0]).toEqual(mockUri);
    expect(mockFetch.mock.calls[0][1]).toHaveProperty('timeout');
    expect(mockFetch.mock.calls[0][1].timeout).toEqual(10000);
  });
  test('should initialize with authorizer', async () => {
    const mockUri = 'https://cloud.bns';
    const mockToken = 'sample-token';
    const mockResult = { status: 200, json: jest.fn() };
    mockAuthorizer.mockResolvedValueOnce(mockToken);
    mockFetch.mockResolvedValueOnce(mockResult);
    const fetch = Fetch({ fetch: mockFetch, authorizer: mockAuthorizer });
    const res = await fetch(mockUri, { headers: {} });
    expect(res).toBeDefined();
    expect(mockFetch).toBeCalled();
    expect(mockFetch.mock.calls[0][0]).toEqual(mockUri);
    expect(mockFetch.mock.calls[0][1]).toHaveProperty('headers');
    expect(mockFetch.mock.calls[0][1].headers).toHaveProperty('Authorization');
    expect(mockFetch.mock.calls[0][1].headers['Authorization']).toEqual(`Bearer ${mockToken}`);
  });
});
