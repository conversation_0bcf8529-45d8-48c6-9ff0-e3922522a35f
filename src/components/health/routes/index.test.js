const { Router } = require('express');
const routes = require('./index');

describe('Health routes', () => {
  test('should be an express.Router object', () => {
    const router = routes({});
    expect(router.prototype).toEqual(Router.prototype);
    expect(typeof router.get).toBe('function');
    expect(typeof router.use).toBe('function');
    expect(typeof router.handle).toBe('function');
  });
  test('should have POST /health endpoint', () => {
    const router = routes({});
    const found = router.stack.some((item) => {
      return item.route.path === '/' && item.route.methods.get;
    });
    expect(found).toBe(true);
  });
});
