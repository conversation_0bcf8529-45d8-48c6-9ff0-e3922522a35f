const route = require('./get-health');
const { name: appName, version: appVersion } = require('../../../../package.json');

const mockNext = jest.fn();
const mockJson = jest.fn();
const mockStatus = jest.fn().mockReturnValue({
  json: mockJson,
});

describe('Health Routes', () => {
  test('should get proper response for GET /health endpoint', () => {
    const mockRes = { status: mockStatus };
    const getHealth = route();
    getHealth({}, mockRes, mockNext);
    expect(mockStatus).toBeCalledWith(200);
    expect(mockJson).toBeCalledWith({ status: 'UP', serviceId: appName, version: appVersion });
    expect(mockNext).not.toBeCalled();
  });
});
