const { getResponse } = require('../common');
const { pickTruthy } = require('../helpers');

const setDisposition = async ({
  // dependencies
  fetch,
  logger,
  basePath,
  mockPath,
  // data
  id,
  messageCategory,
  disposition,
  customerToken,
  userIdentifier,
  channelId,
  xApplication,
  language,
  preferredEnv,
  spanId,
  traceId,
  xOriginatingApplCode,
  country,
}) => {
  const url = `${basePath}/v2/campaigns/${id}`;
  const headers = {
    'Content-Type': 'application/json; charset=utf-8',
    Accept: 'application/json',
    Authorization: customerToken ? `Bearer ${customerToken}` : undefined,
    'x-channel-id': channelId,
    'x-application': xApplication,
    'x-user-context': userIdentifier,
    'x-language': language,
    'preferred-environment': preferredEnv,
    'x-b3-traceid': traceId,
    'x-b3-spanid': spanId,
    'x-originating-appl-code': xOriginatingApplCode,
    'x-country-code': country,
  };

  let body = {
    message_status: disposition,
  };

  const opts = {
    method: 'PUT',
    headers: pickTruthy(headers),
    body: JSON.stringify(pickTruthy(body)),
  };

  const loggableReq = {
    url,
    method: opts.method,
    request: {
      headers: {
        ...opts.headers,
        Authorization: undefined,
        'x-customer-authorization': undefined,
        'x-user-context': undefined,
      },
      body: opts.body,
    },
  };

  try {
    const tStart = new Date();
    const result = await fetch(url, opts);
    const response = await getResponse(result);
    logger.info({
      message: 'Successfully set disposition on dc-campaigns',
      ...loggableReq,
      response_time: new Date() - tStart,
    });
    return response;
  } catch (err) {
    logger.error({
      message: 'Failed to set disposition on dc-campaigns',
      ...loggableReq,
      error: JSON.parse(JSON.stringify(err)),
    });
    return Promise.reject(err);
  }
};

module.exports = setDisposition;
