const getCampaign = require('./get-campaign');

const mockFetch = jest.fn();
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

let basePath = 'https://dc-campaigns.apps.cloud.bns';
let mockPath = 'https://dc-campaigns-mock.apps.cloud.bns';

const mockMessageId = 'a1b2c3d4';
const mockLanguage = 'en';
const mockPreferredEnv = 'istgreen';
const mockxUserContext = '4536000008338308';
const mockChannelId = 'Mobile';
const mockXApplication = 'N1';
const mockSpanId = '12bhgtn44';
const mockTraceId = '567hyg78';
const MockXOriginatingApplCode = 'BFB6';
const mockCountry = 'DO';

describe('DC Campaigns API Client Get Single Campaign', () => {
  beforeEach(() => {
    mockFetch.mockClear();
  });
  test('should throw an error on reponse status >= 400', async () => {
    const mockResponse = { status: 400, ok: false, statusText: 'bad request', text: jest.fn().mockResolvedValueOnce(JSON.stringify({})) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    try {
      const res = await getCampaign({ fetch: mockFetch,
        logger: mockLogger,
        basePath,
        mockPath,
        mockMessageId,
        userIdentifier: mockxUserContext,
        channelId: mockChannelId,
        xApplication: mockXApplication,
        language: mockLanguage,
        preferredEnv: mockPreferredEnv,
        spanId: mockSpanId,
        traceId: mockTraceId,
        xOriginatingApplCode: MockXOriginatingApplCode,
        country: mockCountry });
      throw new Error(`successful response: ${JSON.stringify(res)}`);
    } catch (err) {
      expect(err).toBeDefined();
      expect(err.message).toEqual(mockResponse.statusText);
      expect(err).toHaveProperty('response');
    }
  });
  test('should throw an error on reponse status 500', async () => {
    const mockResponse = {
      status: 500,
      statusText: 'bad request',
      text: jest.fn().mockResolvedValueOnce(JSON.stringify({
        data: null,
        notifications: [
          {
            code: '9999-E',
            message: 'Datapower service error occurred: The backend system to DataPower responded with an application level error. Backend: IMS_IMU1-2. Transaction error: [RLI-ERR: 12] [KTMGD-ACCT-NOT-FOUND]',
            uuid: 'f6050c3d-7e12-4bc9-b123-7a853e42e4a0',
            timestamp: '2018-11-18T16:11:07.211-0500',
            metadata: null,
          },
        ],
      })),
    };
    mockFetch.mockResolvedValueOnce(mockResponse);
    try {
      const res = await getCampaign({ fetch: mockFetch,
        logger: mockLogger,
        basePath,
        mockPath,
        mockMessageId,
        userIdentifier: mockxUserContext,
        channelId: mockChannelId,
        xApplication: mockXApplication,
        language: mockLanguage,
        preferredEnv: mockPreferredEnv,
        spanId: mockSpanId,
        traceId: mockTraceId,
        xOriginatingApplCode: MockXOriginatingApplCode,
        country: mockCountry });
      throw new Error(`successful response: ${JSON.stringify(res)}`);
    } catch (err) {
      expect(err).toBeDefined();
      expect(mockResponse.text).toBeCalled();
    }
  });
  test('should successfully call', async () => {
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify({ data: {} })) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const res = await getCampaign({ fetch: mockFetch,
      logger: mockLogger,
      basePath,
      mockPath,
      mockMessageId,
      userIdentifier: mockxUserContext,
      channelId: mockChannelId,
      xApplication: mockXApplication,
      language: mockLanguage,
      preferredEnv: mockPreferredEnv,
      spanId: mockSpanId,
      traceId: mockTraceId,
      xOriginatingApplCode: MockXOriginatingApplCode,
      country: mockCountry });
    expect(mockResponse.text).toBeCalled();
    expect(res).toHaveProperty('data');
  });
  test('should successfully call mocked', async () => {
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify({ data: {} })) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const res = await getCampaign({ fetch: mockFetch,
      logger: mockLogger,
      basePath,
      mockPath,
      mockMessageId,
      userIdentifier: mockxUserContext,
      channelId: mockChannelId,
      xApplication: mockXApplication,
      language: mockLanguage,
      preferredEnv: 'MOCK',
      spanId: mockSpanId,
      traceId: mockTraceId,
      xOriginatingApplCode: MockXOriginatingApplCode,
      country: mockCountry });
    expect(mockResponse.text).toBeCalled();
    expect(res).toHaveProperty('data');
  });
});
