const setDisposition = require('./set-disposition');

const mockFetch = jest.fn();
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};
// mock data
let mockBasePath = 'https://dc-campaigns.apps.cloud.bns';
let mockPath = 'https://dc-campaigns-mock.apps.cloud.bns';
const customerToken = 'abcdef';
const id = 'a1b2c3d4';
const language = 'en';
const preferredEnv = 'istgreen';
const messageCategory = 'KYC01';
const channelId = 'Mobile';
const xApplication = 'N1';
const disposition = 'S';
const xUserContext = '4567897056784567';
const spanId = '12bhgtn44';
const traceId = '567hyg78';
const xOriginatingApplCode = 'BFB6';
const country = 'DO';

const happyPathArgs = {
  // dependencies args
  fetch: mockFetch,
  logger: mockLogger,
  mockBasePath,
  // data args
  mockPath,
  id,
  messageCategory,
  disposition,
  customerToken,
  xUserContext,
  channelId,
  xApplication,
  language,
  preferredEnv,
  spanId,
  traceId,
  xOriginatingApplCode,
  country,
};

describe('DC Campaigns API Client Set Campaign Disposition', () => {
  beforeEach(() => {
    mockFetch.mockClear();
  });
  test('should throw an error on reponse status >= 400', async () => {
    const mockResponse = { status: 400, ok: false, statusText: 'bad request', text: jest.fn().mockResolvedValueOnce(JSON.stringify({})) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    try {
      const res = await setDisposition(happyPathArgs);
      throw new Error(`successful response: ${JSON.stringify(res)}`);
    } catch (err) {
      expect(err).toBeDefined();
      expect(err.message).toEqual(mockResponse.statusText);
    }
  });
  test('should throw an error on reponse status 500', async () => {
    const mockResponse = {
      status: 500,
      ok: false,
      statusText: 'bad request',
      text: jest.fn().mockResolvedValueOnce(JSON.stringify({
        data: null,
        notifications: [
          {
            code: '9999-E',
            message: 'Datapower service error occurred: The backend system to DataPower responded with an application level error. Backend: IMS_IMU1-2. Transaction error: [RLI-ERR: 12] [KTMGD-ACCT-NOT-FOUND]',
            uuid: 'f6050c3d-7e12-4bc9-b123-7a853e42e4a0',
            timestamp: '2018-11-18T16:11:07.211-0500',
            metadata: null,
          },
        ],
      })),
    };
    mockFetch.mockResolvedValueOnce(mockResponse);
    try {
      const res = await setDisposition(happyPathArgs);
      throw new Error(`successful response: ${JSON.stringify(res)}`);
    } catch (err) {
      expect(err).toBeDefined();
      expect(mockResponse.text).toBeCalled();
    }
  });
  test('should successfully call', async () => {
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify({ data: {} })) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const res = await setDisposition({ ...happyPathArgs, mockPath: undefined });
    expect(mockResponse.text).toBeCalled();
    expect(res).toHaveProperty('data');
  });
  test('should successfully call mocked', async () => {
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify({ data: {} })) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const res = await setDisposition(happyPathArgs);
    expect(mockResponse.text).toBeCalled();
    expect(res).toHaveProperty('data');
  });
  test('should successfully call with disposition `S`', async () => {
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify({ data: {} })) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const res = await setDisposition({ ...happyPathArgs, dispostion: 'S' });
    expect(mockResponse.text).toBeCalled();
    expect(res).toHaveProperty('data');
  });
  test('should successfully call with message Category when present and message id is ignored', async () => {
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify({ data: {} })) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const res = await setDisposition({ ...happyPathArgs, id: '' });
    expect(mockResponse.text).toBeCalled();
    expect(res).toHaveProperty('data');
  });
});
