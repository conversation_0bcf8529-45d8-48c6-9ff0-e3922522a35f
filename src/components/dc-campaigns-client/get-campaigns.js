const { getResponse } = require('../common');
const { pickTruthy } = require('../helpers');
const { getCampaignsMeta } = require('./common');

const getCampaigns = async ({ fetch, logger, basePath, mockPath }, { userIdentifier, channelId, xApplication, language, country, preferredEnv, spanId, traceId, xOriginatingApplCode }) => {
  const url = `${basePath}/v2/campaigns/`;
  const headers = {
    'Content-Type': 'application/json; charset=utf-8',
    Accept: 'application/json',
    'x-channel-id': channelId,
    'x-application': xApplication,
    'x-user-context': userIdentifier,
    'x-language': language,
    'preferred-environment': mockPath ? 'MOCK' : preferredEnv,
    'x-b3-traceid': traceId,
    'x-b3-spanid': spanId,
    'x-originating-appl-code': xOriginatingApplCode,
    'x-country-code': country,
  };
  const opts = {
    method: 'GET',
    headers: pickTruthy(headers),
  };
  const now = new Date();
  try {
    const result = await fetch(url, opts);
    const payload = pickTruthy({
      request: {
        url,
        method: opts.method,
        headers: {
          ...opts.headers,
          Authorization: undefined,
          'x-customer-authorization': undefined,
          'x-user-context': undefined,
        },
      },
    });
    const response = await getResponse(result);
    logger.info({ message: 'dc-campaigns request: get campaigns', ...payload, response: getCampaignsMeta(response), response_time: new Date() - now });
    return response;
  } catch (err) {
    logger.error({ message: 'Error: getting campaigns ', err });
    return Promise.reject(err);
  }
};

module.exports = getCampaigns;
