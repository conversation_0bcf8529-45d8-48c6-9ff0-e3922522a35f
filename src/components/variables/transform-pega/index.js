const R = require('ramda');
const {
  processAsUnknown,
  processAsIs,
  processAsCurrency,
  processAsMaskedCardNumber,
  processAsExpiryDate,
  processAsHidden,
} = require('../processors');

const dataContext = 'pega';

/**
 * Creates a function that transforms a static SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {Rule} rule - Rule
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Transformed variable
 */
const processVariable = (deps, campaign, rule, language) => R.cond([
  [ R.equals('SOLUI_NAME_END'), (x) => processAsIs(deps, campaign, x, 'CUST_FULL_NM') ],
  [ R.equals('SOLUI_FIRSTNAME_END'), (x) => processAsIs(deps, campaign, x, 'CUST_FRST_NM') ],
  [ R.equals('SOLUI_LASTNAME_END'), (x) => processAsIs(deps, campaign, x, 'CUST_LAST_NM') ],
  [ R.equals('SOLUI_APPROVEDCREDITLIMIT_END'), (x) => processAsCurrency(deps, campaign, language, x, 'ApprovedCreditLimit') ],
  [ R.equals('SOLUI_CURRENTACCOUNTNUMBER_END'), (x) => processAsMaskedCardNumber(deps, campaign, x, 'CurrentAccountNumber') ],
  [ R.equals('SOLUI_PROD_END'), (x) => processAsIs(deps, campaign, x, 'PROD_NAME') ],
  [ R.equals('SOLUI_PRIMARYPRODUCTSERVICECODE_END'), processAsHidden ],
  [ R.equals('SOLUI_PRIMARYPRODUCTSERVICESUBCODE_END'), processAsHidden ],
  [ R.equals('SOLUI_EXPIRYDATE_END'), (x) => processAsExpiryDate(deps, campaign, language, x) ],
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for a PEGA campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {Rule} rule - Rule
 * @param {string} language - Target language
 * @param {string[]} variables - List of SOLUI variables to transform
 * @returns {Promise<Object>} - Transformed mappings of variables as a key-value object
 */
const transformPEGA = async (deps, campaign, rule, language, variables) => {
  const transformed = await Promise.all(variables.map((variable) => processVariable(deps, campaign, rule, language)(variable)));
  return {
    transformed: R.zipObj(variables, transformed),
    dataContext,
  };
};

module.exports = {
  transformPEGA,
};
