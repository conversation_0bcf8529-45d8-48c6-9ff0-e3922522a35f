const { transformPEGA } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  expiry_date: '2019-12-31',
  additional_data: [
    { name: 'CUST_FULL_NM', value: '<PERSON>' },
    { name: 'CUST_FRST_NM', value: '<PERSON>' },
    { name: 'CUST_LAST_NM', value: 'Do<PERSON>' },
    { name: 'ApprovedCreditLimit', value: '8500' },
    { name: 'CurrentAccountNumber', value: '*************' },
    { name: 'PrimaryProductServiceCode', value: 'VCL' },
    { name: 'PrimaryProductServiceSubCode', value: 'ZZ' },
    { name: 'PROD_NAME', value: 'SCENE VISA card' },
  ],
};

const mockRule = {
  id: 'pAqozbdSmFut',
};

// https://confluence.agile.bns/display/PIGEON/PEGA+VARIABLES
const mockVariables = [
  'SOLUI_NAME_END',
  'SOL<PERSON>_FIRSTNAME_END',
  'SOLUI_LASTNAME_END',
  'SOLUI_APPROVEDCREDITLIMIT_END',
  'SOLUI_CURRENTACCOUNTNUMBER_END',
  'SOLUI_PROD_END',
  'SOLUI_PRIMARYPRODUCTSERVICECODE_END',
  'SOLUI_PRIMARYPRODUCTSERVICESUBCODE_END',
  'SOLUI_EXPIRYDATE_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transformStatic', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be a function', () => {
    expect(transformPEGA).toBeInstanceOf(Function);
  });
  test('should successfully transform', async () => {
    const { transformed } = await transformPEGA({ logger }, mockCampaign, mockRule, 'en', mockVariables);
    expect(transformed.SOLUI_NAME_END).toEqual('John Doe');
    expect(transformed.SOLUI_FIRSTNAME_END).toEqual('John');
    expect(transformed.SOLUI_LASTNAME_END).toEqual('Doe');
    expect(transformed.SOLUI_APPROVEDCREDITLIMIT_END).toEqual('$8,500');
    expect(transformed.SOLUI_CURRENTACCOUNTNUMBER_END).toEqual('4537\\*\\*\\*\\*7069\\*\\*\\*\\*');
    expect(transformed.SOLUI_PROD_END).toEqual('SCENE VISA card');
    expect(transformed.SOLUI_PRIMARYPRODUCTSERVICECODE_END).toEqual('');
    expect(transformed.SOLUI_PRIMARYPRODUCTSERVICESUBCODE_END).toEqual('');
    expect(transformed.SOLUI_EXPIRYDATE_END).toEqual('December 31, 2019');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log error on unknown variable', async () => {
    const { transformed, dataContext } = await transformPEGA({ logger }, mockCampaign, mockRule, 'en', [ 'SOLUI_UNKNOWN_END' ]);
    expect(dataContext).toEqual('pega');
    expect(transformed.SOLUI_UNKNOWN_END).toEqual('SOLUI_UNKNOWN_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
