/* eslint-disable no-unused-vars */
const R = require('ramda');
const markdownEscape = require('markdown-escape');
const moment = require('moment');

const isString = R.is(String);

const isArray = R.is(Array);

const isObject = (x) => R.and(R.is(Object)(x), R.not(isArray(x)));

const getId = R.prop('id');

const getMessageSource = R.pipe(R.propOr('', 'message_source'), R.toUpper);

const getCampaignId = R.prop('campaign_id');

const getCampaignCode = R.pipe(R.propOr('', 'campaign_code'), R.toUpper);

const getMessageId = R.prop('message_id');

const getAdditionalData = R.pipe(
  R.propOr([], 'additional_data'),
  R.map(R.evolve({
    name: R.toUpper,
  })),
);

const getAdditionalDataValue = R.curry((campaign, name) => R.pipe(
  getAdditionalData,
  R.find(R.propEq(R.toUpper(name), 'name')),
  <PERSON>.prop('value'),
  R.cond([
    [ R.is(String), R.trim ],
    [ R.T, R.identity ],
  ]),
)(campaign));

const matchVarFormat = R.match(/(SOLUI_[A-Z0-9_]*_END)|({{header:[a-zA-Z0-9-]+}})/g);

const escapeMarkdown = R.cond([
  [ R.is(String), markdownEscape ],
  [ R.T, R.identity ],
]);

const startsWithCampaignId = R.curry((validIds, campaign) => R.pipe(
  getCampaignId,
  R.flip(R.startsWith),
  R.flip(R.any)(validIds),
)(campaign));

const containsCampaignCode = R.curry((validCodes, campaign) => R.pipe(
  getCampaignCode,
  R.flip(R.includes)(validCodes),
)(campaign));

const getData = R.propOr({}, 'data');
const getGicRates = R.propOr([], 'gic_rates');
const getTermRates = R.propOr([], 'term_rates');
const getStepRates = R.propOr([], 'step_rates');
const getFrequencyRates = R.propOr([], 'frequency_rates');
const getBranchDiscretionLimit = R.prop('branch_discretion_limit');
const isRecommended = R.propOr(false, 'is_recommended');
const isCompound = R.propOr(false, 'is_compound');
const isFirstStep = R.pipe(R.prop('step_number'), R.equals(0));
const isFrequencyAnnual = R.pipe(
  R.propOr('', 'interest_payment_frequency'),
  R.toLower,
  R.equals('annually'),
);

const isCompoundAndAnnual = (x) => R.and(isCompound(x), isFrequencyAnnual(x));

const filterFrequencyRates = R.pipe(
  R.filter(isCompoundAndAnnual),
);

const transformationFrequencyRates = {
  step_number: R.identity,
  frequency_rates: filterFrequencyRates,
};

const filterStepRates = R.pipe(
  R.filter(isFirstStep),
  R.map(R.evolve(transformationFrequencyRates)),
  R.filter(R.pipe(getFrequencyRates, R.length)),
);

const transformationTermRates = {
  term: R.identity,
  is_recommended: R.identity,
  step_rates: filterStepRates,
};

const filterTermRates = R.pipe(
  R.filter(isRecommended),
  R.map(R.evolve(transformationTermRates)),
  R.filter(R.pipe(getStepRates, R.length)),
);

const transformationGicRates = {
  code: R.identity,
  type: R.identity,
  expiry_date: R.identity,
  term_rates: filterTermRates,
};

const getGicRate = R.pipe(
  getData,
  getGicRates,
  // get only recommended and compound rates
  R.map(R.evolve(transformationGicRates)),
  // remove `gic_rates` without `term_rates`
  R.filter(R.pipe(getTermRates, R.length)),
  R.head,
);

const removeFalsyValues = (opts = {}) => {
  return obj => {
    const isFalsy = f => {
      if (opts.allowEmptyString && f === '') return false;
      return !f;
    };
    return R.reject(isFalsy, obj);
  };
};

// This function is to return the total no of days (current date - campaign end date) . This takes into account leap years as well
const getNoOfDaysBetweenDates = (campaign, currentDate) => {
  const campaignEndDate = moment(campaign.expiry_date, 'YYYY-MM-DD');

  // Difference in number of days (inclusive of leap year)
  const diffInDays = Math.round(moment.duration(campaignEndDate.diff(currentDate)).asDays());

  // Add +1 to include the end date to the calculation
  return diffInDays + 1;
};

// This function will return the hard stop date for Soft intercept campaigns i.e Campaign end/anniversary date
const getHardStopDate = (campaign, locale = 'en') => {
  return moment(campaign.expiry_date)
    .locale(locale)
    .format(`${locale.toLowerCase().startsWith('fr') ? 'D MMMM' : 'MMMM D,'} YYYY`);
};

// eslint-disable-next-line sonarjs/cognitive-complexity
const getReminderPeriod = (campaign, language, currentDate) => {
  const highRiskCampaignIds = [ 'ORCHR', 'ORBH1', 'ORBH2', 'ORBH3' ];
  const lowMediumRiskCampaignIds = [ 'ORCDL', 'ORBL1', 'ORBL2', 'ORBL3' ];
  let reminderPeriod = language.toLowerCase().startsWith('fr') ? 'demain' : 'tomorrow';
  let minDaysForAlert;
  let nearestAlertTrigger;

  if (highRiskCampaignIds.includes(campaign.campaign_id) && getNoOfDaysBetweenDates(campaign, currentDate) > 30) {
    // get the alert trigger date based on the risk level
    const alert90DaysDateBeforeAnniversary = moment(campaign.expiry_date).subtract(90, 'days');
    const alert60DaysDateBeforeAnniversary = moment(campaign.expiry_date).subtract(60, 'days');
    const alert30DaysDateBeforeAnniversary = moment(campaign.expiry_date).subtract(30, 'days');

    // check no of days from the current date which is closest to either of the alert trigger dates
    const diffIn90Days = Math.round(moment.duration(alert90DaysDateBeforeAnniversary.diff(currentDate)).asDays()) + 1;
    const diffIn60Days = Math.round(moment.duration(alert60DaysDateBeforeAnniversary.diff(currentDate)).asDays()) + 1;
    const diffIn30Days = Math.round(moment.duration(alert30DaysDateBeforeAnniversary.diff(currentDate)).asDays()) + 1;

    // get the nearest alert trigger
    nearestAlertTrigger = [ diffIn90Days, diffIn60Days, diffIn30Days ].filter(val => val > 0);
    minDaysForAlert = Math.min(...nearestAlertTrigger);
  }

  if (lowMediumRiskCampaignIds.includes(campaign.campaign_id) && getNoOfDaysBetweenDates(campaign, currentDate) > 90) {
    // get the alert trigger date based on the risk level
    const alert1YearDateBeforeAnniversary = moment(campaign.expiry_date).subtract(1, 'years');
    const alert6MonthsDateBeforeAnniversary = moment(campaign.expiry_date).subtract(6, 'months');
    const alert90DaysDateBeforeAnniversary = moment(campaign.expiry_date).subtract(90, 'days');

    // check no of days from the current date which is closest to either of the alert trigger dates
    const diffIn1year = Math.round(moment.duration(alert1YearDateBeforeAnniversary.diff(currentDate)).asDays()) + 1;
    const diffIn6Months = Math.round(moment.duration(alert6MonthsDateBeforeAnniversary.diff(currentDate)).asDays()) + 1;
    const diffIn30Days = Math.round(moment.duration(alert90DaysDateBeforeAnniversary.diff(currentDate)).asDays()) + 1;

    // get the nearest alert trigger
    nearestAlertTrigger = [ diffIn1year, diffIn6Months, diffIn30Days ].filter(val => val > 0);
    minDaysForAlert = Math.min(...nearestAlertTrigger);
  }

  if (minDaysForAlert < 2) {
    reminderPeriod = language.toLowerCase().startsWith('fr') ? 'demain' : 'tomorrow';
  } else if (minDaysForAlert <= 60) {
    reminderPeriod = language.toLowerCase().startsWith('fr') ? `en ${minDaysForAlert} jours` : `in ${minDaysForAlert} days`;
  } else if (minDaysForAlert > 350) {
    const newReminderDate = moment(currentDate).add(minDaysForAlert, 'days');
    const yearsCalculation = `${Math.round(moment.duration(newReminderDate.diff(currentDate)).asYears())}`;
    reminderPeriod = language.toLowerCase().startsWith('fr') ? `en ${yearsCalculation} an(s)` : `in ${yearsCalculation} year(s)`;
  } else if (minDaysForAlert > 60) {
    const newReminderDate = moment(currentDate).add(minDaysForAlert, 'days');
    const monthsCalculation = `${Math.round(moment.duration(newReminderDate.diff(currentDate)).asMonths())}`;
    reminderPeriod = language.toLowerCase().startsWith('fr') ? `en ${monthsCalculation} mois` : `in ${monthsCalculation} months`;
  }
  return reminderPeriod;
};

const formatRateByLanguage = (logger, value, language, variable, name, campaignId, messageId) => {
  // Check if string
  if (typeof value !== 'string' || value.length === 0 || value.split('.').length !== 2 || value.split('.')[1].length !== 2) {
    logger.warn({
      message: 'transformation error',
      err: { message: 'Incorrect format' },
      variable,
      name,
      value,
      campaign_id: campaignId,
      message_id: messageId,
    });
    return variable;
  };
  // Check for valid format
  if (language === 'fr') {
    return value.replace('.', ',');
  } else {
    return value;
  }
};

module.exports = {
  escapeMarkdown,
  getId,
  getMessageSource,
  getCampaignId,
  getCampaignCode,
  getMessageId,
  getAdditionalData,
  getAdditionalDataValue,
  matchVarFormat,
  startsWithCampaignId,
  containsCampaignCode,
  isString,
  isObject,
  isArray,
  getGicRate,
  getFrequencyRates,
  getBranchDiscretionLimit,
  getTermRates,
  getStepRates,
  removeFalsyValues,
  // isFirstStep,
  // isFrequencyAnnual,
  getNoOfDaysBetweenDates,
  getHardStopDate,
  getReminderPeriod,
  formatRateByLanguage,
};
