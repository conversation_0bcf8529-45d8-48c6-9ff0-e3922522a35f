const { transformStatic } = require('./index');

const mockProductBookResponse = {
  productId: '580040',
  productTypes: [
    {
      productDomain: 'default',
      productCode: 'AFB',
      subProductCode: 'B1',
    },
    {
      productDomain: 'KT',
      productCode: 'VAX',
      subProductCode: 'B1',
    },
  ],
  productSystem: 'VAX',
  currency: 'CAD',
  ownership: 'B',
  properties: [
    { type: 'CATEGORY', value: 'CREDITCARDS' },
    { type: 'DEFAULT_CLASSIFICATION', value: 'Amex' },
    { type: 'DETAIL_CLASSIFICATION', value: 'Amex' },
    { type: 'REL_ACTIVITY_CLASSIFICATION', value: 'BusinessAmex' },
    { type: 'CORNERSTONE_ID', value: 'Borrowing' },
    { type: 'ASSET_LIABILITY', value: 'L' },
  ],
  sortWeight: '4000',
  accountNumberLength: 15,
  accountNumberMask: '####*****######',
  descriptions: [
    { locale: 'en_CA', value: 'Future Amex SB B1' },
    { locale: 'fr_CA', value: 'F-Future Amex SB B1' },
  ],
};

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  expiry_date: '2122-08-09',
  additional_data: [
    { name: 'ACCT', value: '****************' },
    { name: 'AMT', value: '001000' },
    { name: 'BTFEE', value: 'X1' },
    { name: 'CREDIT', value: '1000.99' },
    { name: 'DATE', value: '********' },
    { name: 'NAME', value: 'John Doe' },
    { name: 'PAYCO', value: 'Scotiabank' },
    { name: 'PROD', value: 'AFBB1' },
    { name: 'RRC', value: 'X2' },
  ],
};

const mockRule = {
  id: 'pAqozbdSmFut',
};

const mockVariables = [
  'SOLUI_ACCT_END',
  'SOLUI_AMT_END',
  'SOLUI_ANALYTICS_CAMPID_END',
  'SOLUI_BTFEE_END',
  'SOLUI_CAMPAIGNID_END',
  'SOLUI_CREDIT_END',
  'SOLUI_DATE_END',
  'SOLUI_NAME_END',
  'SOLUI_PAYCO_END',
  'SOLUI_PROD_END',
  'SOLUI_RRC_END',
  'SOLUI_RULEID_END',
  'SOLUI_MESSAGEID_END',
  'SOLUI_EXPDATE_END',
  'SOLUI_REMAININGDAYS_END',
  'SOLUI_REMINDER_PERIOD_END',
  'SOLUI_PROD_CODE_END',
  'SOLUI_PROD_SUBCODE_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

const productBookService = {
  getProduct: jest.fn().mockResolvedValue(mockProductBookResponse),
};

describe('Variables > transform KT > transformStatic', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
    productBookService.getProduct.mockClear();
  });
  test('should be a function', () => {
    expect(transformStatic).toBeInstanceOf(Function);
  });
  test('should successfully transform', async () => {
    const res = await transformStatic({ logger, productBookService }, mockCampaign, mockRule, 'en', mockVariables);
    expect(res.SOLUI_ACCT_END).toEqual('\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*3333');
    expect(res.SOLUI_AMT_END).toEqual('$1,000');
    expect(res.SOLUI_ANALYTICS_CAMPID_END).toEqual('ABC99');
    expect(res.SOLUI_BTFEE_END).toEqual('X1');
    expect(res.SOLUI_CAMPAIGNID_END).toEqual('ABC99');
    expect(res.SOLUI_CREDIT_END).toEqual('$1,000.99');
    expect(res.SOLUI_DATE_END).toEqual('December 31, 2019');
    expect(res.SOLUI_NAME_END).toEqual('John Doe');
    expect(res.SOLUI_PAYCO_END).toEqual('Scotiabank');
    expect(res.SOLUI_PROD_END).toEqual('Future Amex SB B1');
    expect(res.SOLUI_RRC_END).toEqual('X2');
    expect(res.SOLUI_RULEID_END).toEqual('pAqozbdSmFut');
    expect(res.SOLUI_MESSAGEID_END).toEqual('D1234567');
    expect(res.SOLUI_EXPDATE_END).toEqual('August 9, 2122');
    expect(res.SOLUI_REMAININGDAYS_END).toBeDefined();
    expect(res.SOLUI_REMINDER_PERIOD_END).toBeDefined();
    expect(res.SOLUI_PROD_CODE_END).toEqual('AFB');
    expect(res.SOLUI_PROD_SUBCODE_END).toEqual('B1');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log error on unknown variable', async () => {
    const res = await transformStatic({ logger, productBookService }, mockCampaign, mockRule, 'en', [ 'SOLUI_UNKNOWN_END' ]);
    expect(res.SOLUI_UNKNOWN_END).toEqual('SOLUI_UNKNOWN_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
