const R = require('ramda');
const {
  processAsUnknown,
  processAsIs,
  processAsAccount,
  processAsCurrency,
  processAsDate,
  processAsProduct,
} = require('../../processors');
const { getCampaignId, getId, getMessageId, getHardStopDate, getNoOfDaysBetweenDates, getReminderPeriod } = require('../../helpers');
const moment = require('moment');

/**
 * Creates a function that transforms a static SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {Rule} rule - Rule
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Transformed variable
 */
const processVariable = (deps, campaign, rule, language) => R.cond([
  [ R.equals('SOLUI_ACCT_END'), (x) => processAsAccount(deps, campaign, x, 'ACCT') ],
  [ R.equals('SOLUI_PROD_END'), (x) => processAsProduct(deps, campaign, language, x, 'PROD') ],
  [ R.equals('SOLUI_NAME_END'), (x) => processAsIs(deps, campaign, x, 'NAME') ],
  [ R.equals('SOLUI_CREDIT_END'), (x) => processAsCurrency(deps, campaign, language, x, 'CREDIT') ],
  [ R.equals('SOLUI_DATE_END'), (x) => processAsDate(deps, campaign, language, x, 'DATE') ],
  [ R.equals('SOLUI_AMT_END'), (x) => processAsCurrency(deps, campaign, language, x, 'AMT') ],
  [ R.equals('SOLUI_ANALYTICS_CAMPID_END'), () => getCampaignId(campaign) ],
  [ R.equals('SOLUI_BTFEE_END'), (x) => processAsIs(deps, campaign, x, 'BTFEE') ],
  [ R.equals('SOLUI_CAMPAIGNID_END'), () => getCampaignId(campaign) ],
  [ R.equals('SOLUI_MESSAGEID_END'), () => getMessageId(campaign) ],
  [ R.equals('SOLUI_RULEID_END'), () => getId(rule) ],
  // // [ R.equals('SOLUI_CARDIMAGE_END'), (x) => processAsIs(deps, campaign, x, 'PROD') ],
  [ R.equals('SOLUI_PAYCO_END'), (x) => processAsIs(deps, campaign, x, 'PAYCO') ],
  [ R.equals('SOLUI_RRC_END'), (x) => processAsIs(deps, campaign, x, 'RRC') ],
  [ R.equals('SOLUI_EXPDATE_END'), () => getHardStopDate(campaign, language) ],
  [ R.equals('SOLUI_REMAININGDAYS_END'), () => getNoOfDaysBetweenDates(campaign, moment(new Date(), 'YYYY-MM-DD')) ],
  [ R.equals('SOLUI_REMINDER_PERIOD_END'), () => getReminderPeriod(campaign, language, moment(new Date(), 'YYYY-MM-DD')) ],
  // product code retrieved from 'PROD' in insights payload for CTA
  [ R.equals('SOLUI_PROD_CODE_END'), async (x) => R.toUpper(await processAsIs(deps, campaign, x, 'PROD')).slice(0, 3) ],
  // sub-product code retrieved from 'PROD' in insights payload for CTA
  [ R.equals('SOLUI_PROD_SUBCODE_END'), async (x) => R.toUpper(await processAsIs(deps, campaign, x, 'PROD')).substr(3) ],
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms static variables for a standing or a targeted campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {Rule} rule - Rule
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<Object>} - Transformed mappings of variables as a key-value object
 */
const transformStatic = async (deps, campaign, rule, language, variables) => {
  const transformed = await Promise.all(variables.map((variable) => processVariable(deps, campaign, rule, language)(variable)));
  return R.zipObj(variables, transformed);
};

module.exports = {
  transformStatic,
};
