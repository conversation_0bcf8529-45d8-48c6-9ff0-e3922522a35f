const processStandingCCM01 = require('./standing-ccm01');

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'CCM01',
  additional_data: [],
};

describe('Variables > transform KT > transform dynamic > standing campaign > processStandingCCM01', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed, dataContext } = await processStandingCCM01({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER4_END' ]);
    expect(dataContext).toEqual('stccm01');
    expect(transformed[0]).toEqual('SOLUI_OTHER4_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
