const R = require('ramda');
const {
  processAsUnknown,
} = require('../../../processors');

const dataContext = 'stch002';

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @returns {(string) => string} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign) => R.cond([
  // TODO: Add dynamic variable substitution
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for CH002 campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {string[]} - Transformed variables
 */
const processStandingCH002 = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = processStandingCH002;
