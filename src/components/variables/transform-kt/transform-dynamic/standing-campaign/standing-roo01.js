const R = require('ramda');
const {
  processAsUnknown,
  processAsIs,
  processAsCurrency,
} = require('../../../processors');

const dataContext = 'stroo01';

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => string} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  [ R.equals('SOLUI_OTHER1_END'), (x) => processAsIs(deps, campaign, x, 'OTHER1') ], // ROO DATE
  [ R.equals('SOLUI_OTHER2_END'), (x) => processAsCurrency(deps, campaign, language, x, 'OTHER2') ], // TOTAL DELINQUNT AMNT
  [ R.equals('SOLUI_OTHER3_END'), (x) => processAsCurrency(deps, campaign, language, x, 'OTHER3') ], // ROO AMNT
  [ R.equals('SOLUI_OTHER4_END'), (x) => processAsIs(deps, campaign, x, 'OTHER4') ], // ACCT AFFECTED
  [ R.equals('SOLUI_OTHER5_END'), (x) => processAsIs(deps, campaign, x, 'OTHER5') ], // PRODUCT TYPE
  [ R.equals('SOLUI_OTHER6_END'), (x) => processAsCurrency(deps, campaign, language, x, 'OTHER6') ], // BALANCE AMOUNT
  [ R.equals('SOLUI_OTHER7_END'), (x) => processAsIs(deps, campaign, x, 'OTHER7') ], // DEPOSIT ACCT NAME
  [ R.equals('SOLUI_OTHER8_END'), (x) => processAsIs(deps, campaign, x, 'OTHER8') ], // 4 DIGITS OF THE ACCT
  [ R.equals('SOLUI_OTHER9_END'), (x) => processAsIs(deps, campaign, x, 'OTHER9') ], // CUSTOMER ADDRESS
  [ R.equals('SOLUI_OTHER10_END'), (x) => processAsIs(deps, campaign, x, 'OTHER10') ], // ROO DATE
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for ROO01 campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {string[]} - Transformed variables
 */
const processStandingROO01 = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = processStandingROO01;
