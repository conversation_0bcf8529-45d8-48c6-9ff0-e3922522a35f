const processStandingKS009 = require('./standing-ks009');

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

const mockCampaign = {
  message_id: 'D0O00O0OOOOKS009',
  campaign_id: 'KS009',
  additional_data: [],
};

describe('Variables > transform KT > transform dynamic > standing campaign > processStandingKS009', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed, dataContext } = await processStandingKS009({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER4_END' ]);
    expect(dataContext).toEqual('stks009');
    expect(transformed[0]).toEqual('SOLUI_OTHER4_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
