const processStandingCCV07 = require('./standing-ccv07');

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'OTHER1', value: 'X1' },
    { name: 'OTHER2', value: 'X2' },
    { name: 'OTHER3', value: 'X3' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_OTHER3_END',
];

describe('Variables > transform KT > transform dynamic > standing campaign > processStandingCCV07', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should successfully call', async () => {
    const { transformed, dataContext } = await processStandingCCV07({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('stccv07');
    expect(transformed[0]).toEqual('X1');
    expect(transformed[1]).toEqual('X2');
    expect(transformed[2]).toEqual('X3');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processStandingCCV07({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER4_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER4_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
