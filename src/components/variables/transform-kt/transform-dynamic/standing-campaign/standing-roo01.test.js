const processStandingROO01 = require('./standing-roo01');

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ROO01',
  additional_data: [
    { name: 'OTHER1', value: 'November 20' },
    { name: 'OTHER2', value: '13.67' },
    { name: 'OTHER3', value: '13.67' },
    { name: 'OTHER4', value: 'Savings' },
    { name: 'OTHER5', value: 'Borrrowing' },
    { name: 'OTHER6', value: '13.67' },
    { name: 'OTHER7', value: 'TFSA' },
    { name: 'OTHER8', value: '2345' },
    { name: 'OTHER9', value: '333 Kings Street East' },
    { name: 'OTHER10', value: '2022' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_OTHER3_END',
  'SOLUI_OTHER4_END',
  'SOLUI_OTHER5_END',
  'SOLUI_OTHER6_END',
  'SOLUI_OTHER7_END',
  'SOLUI_OTHER8_END',
  'SOLUI_OTHER9_END',
  'SOLUI_OTHER10_END',
  'SOLUI_OTHER11_END',
];

describe('Variables > transform KT > transform dynamic > standing campaign > processStandingROO01', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should successfully call Right to Offset Campaign', async () => {
    const { transformed, dataContext } = await processStandingROO01({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('stroo01');
    expect(transformed[0]).toEqual('November 20');
    expect(transformed[1]).toEqual('$13.67');
    expect(transformed[2]).toEqual('$13.67');
    expect(transformed[3]).toEqual('Savings');
    expect(transformed[4]).toEqual('Borrrowing');
    expect(transformed[5]).toEqual('$13.67');
    expect(transformed[6]).toEqual('TFSA');
    expect(transformed[7]).toEqual('2345');
    expect(transformed[8]).toEqual('333 Kings Street East');
    expect(transformed[9]).toEqual('2022');
    expect(transformed[10]).toEqual('SOLUI_OTHER11_END');
  });
});
