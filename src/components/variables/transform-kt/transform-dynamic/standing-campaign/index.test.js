const { standingCampaigns } = require('./index');

describe('Variables > transform KT > transform dynamic > standingCampaigns', () => {
  test('should have all required campaigns', () => {
    expect(standingCampaigns.CCM01).toBeInstanceOf(Function);
    expect(standingCampaigns.CCV01).toBeInstanceOf(Function);
    expect(standingCampaigns.CCV02).toBeInstanceOf(Function);
    expect(standingCampaigns.CCV03).toBeInstanceOf(Function);
    expect(standingCampaigns.CCV04).toBeInstanceOf(Function);
    expect(standingCampaigns.CCV05).toBeInstanceOf(Function);
    expect(standingCampaigns.CCV06).toBeInstanceOf(Function);
    expect(standingCampaigns.CCV07).toBeInstanceOf(Function);
    expect(standingCampaigns.CCV14).toBeInstanceOf(Function);
    expect(standingCampaigns.CCV44).toBeInstanceOf(Function);
    expect(standingCampaigns.CCV45).toBeInstanceOf(Function);
    expect(standingCampaigns.CCV46).toBeInstanceOf(Function);
    expect(standingCampaigns.CCV47).toBeInstanceOf(Function);
    expect(standingCampaigns.CCV48).toBeInstanceOf(Function);
    expect(standingCampaigns.CCV49).toBeInstanceOf(Function);
    expect(standingCampaigns.CCV50).toBeInstanceOf(Function);
    expect(standingCampaigns.CCV51).toBeInstanceOf(Function);
    expect(standingCampaigns.CCV52).toBeInstanceOf(Function);
    expect(standingCampaigns.CCV53).toBeInstanceOf(Function);
    expect(standingCampaigns.CH003).toBeInstanceOf(Function);
    expect(standingCampaigns.CH006).toBeInstanceOf(Function);
    expect(standingCampaigns.E0110).toBeInstanceOf(Function);
    expect(standingCampaigns.EDI01).toBeInstanceOf(Function);
    expect(standingCampaigns.EDI02).toBeInstanceOf(Function);
    expect(standingCampaigns.EDI03).toBeInstanceOf(Function);
    expect(standingCampaigns.EDI04).toBeInstanceOf(Function);
    expect(standingCampaigns.KS010).toBeInstanceOf(Function);
    expect(standingCampaigns.SAV01).toBeInstanceOf(Function);
    expect(standingCampaigns.SAV02).toBeInstanceOf(Function);
    expect(standingCampaigns.SAV03).toBeInstanceOf(Function);
    expect(standingCampaigns.SAV04).toBeInstanceOf(Function);
    expect(standingCampaigns.SAV05).toBeInstanceOf(Function);
    expect(standingCampaigns.SAV06).toBeInstanceOf(Function);
    expect(standingCampaigns.SAV11).toBeInstanceOf(Function);
    expect(standingCampaigns.SAV12).toBeInstanceOf(Function);
    expect(standingCampaigns.SAV13).toBeInstanceOf(Function);
    expect(standingCampaigns.SAV14).toBeInstanceOf(Function);
    expect(standingCampaigns.SAV15).toBeInstanceOf(Function);
    expect(standingCampaigns.SAV16).toBeInstanceOf(Function);
    expect(standingCampaigns.SRP02).toBeInstanceOf(Function);
  });
});
