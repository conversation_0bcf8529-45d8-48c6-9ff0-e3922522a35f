const R = require('ramda');
const {
  processAsUnknown,
} = require('../../../processors');

const dataContext = 'stccv49';

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @returns {(string) => string} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign) => R.cond([
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for CCV49 campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language (not used)
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {string[]} - Transformed variables
 */
const processStandingCCV49 = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = processStandingCCV49;
