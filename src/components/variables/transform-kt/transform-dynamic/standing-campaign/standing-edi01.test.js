const processStandingEDI01 = require('./standing-edi01');

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'OTHER1', value: '20191231' },
    { name: 'OTHER2', value: 'X2' },
  ],
};

const mockVariables = [
  'SOLUI_DATE1_END',
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
];

describe('Variables > transform KT > transform dynamic > standing campaign > processStandingEDI01', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should successfully call', async () => {
    const { transformed, dataContext } = await processStandingEDI01({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('stedi01');
    expect(transformed[0]).toEqual('December 31, 2019');
    expect(transformed[1]).toEqual('December 31, 2019');
    expect(transformed[2]).toEqual('X2');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processStandingEDI01({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER6_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER6_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
