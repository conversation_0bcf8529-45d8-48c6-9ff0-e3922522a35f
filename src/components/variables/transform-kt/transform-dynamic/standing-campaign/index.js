const R = require('ramda');

const processStandingCCM01 = require('./standing-ccm01');
const processStandingCCV01 = require('./standing-ccv01');
const processStandingCCV02 = require('./standing-ccv02');
const processStandingCCV03 = require('./standing-ccv03');
const processStandingCCV04 = require('./standing-ccv04');
const processStandingCCV05 = require('./standing-ccv05');
const processStandingCCV06 = require('./standing-ccv06');
const processStandingCCV07 = require('./standing-ccv07');
const processStandingCCV14 = require('./standing-ccv14');
const processStandingCCV43 = require('./standing-ccv43');
const processStandingCCV44 = require('./standing-ccv44');
const processStandingCCV45 = require('./standing-ccv45');
const processStandingCCV46 = require('./standing-ccv46');
const processStandingCCV47 = require('./standing-ccv47');
const processStandingCCV48 = require('./standing-ccv48');
const processStandingCCV49 = require('./standing-ccv49');
const processStandingCCV50 = require('./standing-ccv50');
const processStandingCCV51 = require('./standing-ccv51');
const processStandingCCV52 = require('./standing-ccv52');
const processStandingCCV53 = require('./standing-ccv53');
const processStandingCH001 = require('./standing-ch001');
const processStandingCH002 = require('./standing-ch002');
const processStandingCH003 = require('./standing-ch003');
const processStandingCH005 = require('./standing-ch005');
const processStandingCH006 = require('./standing-ch006');
const processStandingE0110 = require('./standing-e0110');
const processStandingEDI01 = require('./standing-edi01');
const processStandingEDI02 = require('./standing-edi02');
const processStandingEDI03 = require('./standing-edi03');
const processStandingEDI04 = require('./standing-edi04');
const processStandingKS009 = require('./standing-ks009');
const processStandingKS010 = require('./standing-ks010');
const processStandingSAVXX = require('./standing-savXX');
const processStandingSRP02 = require('./standing-srp02');
const processStandingROO01 = require('./standing-roo01');

const standingCampaigns = {
  CCM01: processStandingCCM01, // New Credit Card Activation Required
  CCV01: processStandingCCV01, // Hard Decline
  CCV02: processStandingCCV02, // Soft Decline 1
  CCV03: processStandingCCV03, // Soft Decline 2
  CCV04: processStandingCCV04, // Soft Decline 3
  CCV05: processStandingCCV05, // Approved - No Action
  CCV06: processStandingCCV06, // Approved - Income Verification
  CCV07: processStandingCCV07, // Approved - Other Verification
  CCV14: processStandingCCV14, // Soft Decline (Generic)
  CCV43: processStandingCCV43, // VISA Soft Decline - No/Min Credit History
  CCV44: processStandingCCV44, // VISA Soft Decline - Affordability/Capacity
  CCV45: processStandingCCV45, // VISA Soft Decline - Equifax for Credit Quality
  CCV46: processStandingCCV46, // VISA Soft Decline - TransUnion for Credit Queality
  CCV47: processStandingCCV47, // VISA Soft Decline - Exceed Max Allowable Limit
  CCV48: processStandingCCV48, // VISA Soft Decline - STEP/Mortgage
  CCV49: processStandingCCV49, // VISA Soft Decline - Miscellaneous
  CCV50: processStandingCCV50, // VISA Soft Decline - Missing Reason Code
  CCV51: processStandingCCV51, // Bundle - Approved / No Action
  CCV52: processStandingCCV52, // Bundle - Approved / Income Verification
  CCV53: processStandingCCV53, // Bundle - Approved / Other Verification
  CH001: processStandingCH001, // Chip Expiry - MAG to CHIP
  CH002: processStandingCH002, // Chip Expiry - CHIP to CHIP
  CH003: processStandingCH003, // AutoSwitch
  CH005: processStandingCH005, // Compromised/Data Hack
  CH006: processStandingCH006, // AutoSwitch
  EDI01: processStandingEDI01, // Bill Payee Removal
  EDI02: processStandingEDI02, // Bill Payee Name Change
  EDI03: processStandingEDI03, // Bill Payee Account Change
  EDI04: processStandingEDI04, // Bill Payee Deletion
  E0110: processStandingE0110, // Not Sufficient Funds
  KS009: processStandingKS009, // Card Activation FUA - Branch
  KS010: processStandingKS010, // Card Actuvation FUA - Home
  SAV01: processStandingSAVXX, // Getting There - Basic Banking Account
  SAV02: processStandingSAVXX, // Getting There - Basic Banking Plan
  SAV03: processStandingSAVXX, // Getting There - Scotia One
  SAV04: processStandingSAVXX, // Student Banking Advantage - Basic Banking Account
  SAV05: processStandingSAVXX, // Student Banking Advantage - Basic Banking Plan
  SAV06: processStandingSAVXX, // Student Banking Advantage - Scotia One
  SAV07: processStandingSAVXX, // Student Banking
  SAV11: processStandingSAVXX, // Welcome letter pay per use plan
  SAV12: processStandingSAVXX, // Welcome letter monthly plan
  SAV13: processStandingSAVXX, // Switch to monthly
  SAV14: processStandingSAVXX, // Switch to PPU
  SAV15: processStandingSAVXX, // Switch to modified monthly
  SAV16: processStandingSAVXX, // Pay per use plan account holder moves to Quebec
  SRP02: processStandingSRP02, // Seniors Program - New Customer
  ROO01: processStandingROO01, // Right to Offset
};

/**
 * Transforms variables for a standing campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<Object>} - Transformed mappings of variables as a key-value object
 */
const processStandingCampaign = (fn) => async (deps, campaign, language, variables) => {
  const { transformed, dataContext } = await fn(deps, campaign, language, variables);
  return {
    transformed: R.zipObj(variables, transformed),
    dataContext,
  };
};

module.exports = {
  standingCampaigns,
  processStandingCampaign,
};
