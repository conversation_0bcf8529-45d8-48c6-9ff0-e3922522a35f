const processStandingCCV52 = require('./standing-ccv52');

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'OTHER1', value: 'X1' },
    { name: 'OTHER2', value: 'X2' },
    { name: 'OTHER3', value: 'X3' },
    { name: 'OTHER4', value: 'X4' },
    { name: 'OTHER5', value: '20191231' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_OTHER3_END',
  'SOLUI_OTHER4_END',
  'SOLUI_OTHER5_END',
  'SOLUI_DATE5_END',
];

describe('Variables > transform KT > transform dynamic > standing campaign > processStandingCCV52', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should successfully call', async () => {
    const { transformed, dataContext } = await processStandingCCV52({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('stccv52');
    expect(transformed[0]).toEqual('X1');
    expect(transformed[1]).toEqual('X2');
    expect(transformed[2]).toEqual('X3');
    expect(transformed[3]).toEqual('X4');
    expect(transformed[4]).toEqual('December 31, 2019');
    expect(transformed[5]).toEqual('December 31, 2019');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processStandingCCV52({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER6_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER6_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
