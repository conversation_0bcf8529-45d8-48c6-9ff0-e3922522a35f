const processStandingSRP02 = require('./standing-srp02');

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'OTHER1', value: 'X1' },
    { name: 'OTHER2', value: '9199123.15' },
    { name: 'OTHER3', value: '20191231' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_OTHER3_END',
  'SOLUI_DATE3_END',
];

describe('Variables > transform KT > transform dynamic > standing campaign > processStandingSRP02', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should successfully call', async () => {
    const { transformed, dataContext } = await processStandingSRP02({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('stsrp02');
    expect(transformed[0]).toEqual('X1');
    expect(transformed[1]).toEqual('$9,199,123.15');
    expect(transformed[2]).toEqual('December 31, 2019');
    expect(transformed[3]).toEqual('December 31, 2019');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processStandingSRP02({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER6_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER6_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
