const processStandingCH001 = require('./standing-ch001');

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

const mockCampaign = {
  message_id: 'D0O00O0OOOOCH001',
  campaign_id: 'CH001',
  additional_data: [],
};

describe('Variables > transform KT > transform dynamic > standing campaign > processStandingCH001', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed, dataContext } = await processStandingCH001({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER4_END' ]);
    expect(dataContext).toEqual('stch001');
    expect(transformed[0]).toEqual('SOLUI_OTHER4_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
