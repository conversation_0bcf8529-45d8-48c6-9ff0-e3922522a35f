const processStandingCH002 = require('./standing-ch002');

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

const mockCampaign = {
  message_id: 'D0O00O0OOOOCH002',
  campaign_id: 'CH002',
  additional_data: [],
};

describe('Variables > transform KT > transform dynamic > standing campaign > processStandingCH002', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed, dataContext } = await processStandingCH002({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER4_END' ]);
    expect(dataContext).toEqual('stch002');
    expect(transformed[0]).toEqual('SOLUI_OTHER4_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
