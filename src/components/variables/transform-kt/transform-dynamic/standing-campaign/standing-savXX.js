const R = require('ramda');
const {
  processAsUnknown,
  processAsNumber,
  processAsDate,
} = require('../../../processors');

const dataContext = 'stsavxx';

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => string} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  [ R.equals('SOLUI_OTHER1_END'), (x) => processAsNumber(deps, campaign, x, 'OTHER1') ],
  [ R.equals('SOLUI_DATE2_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER2') ],
  [ R.equals('SOLUI_OTHER2_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER2') ],
  [ R.equals('SOLUI_DATE3_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER3') ],
  [ R.equals('SOLUI_OTHER3_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER3') ],
  [ R.equals('SOLUI_DATE4_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER4') ],
  [ R.equals('SOLUI_OTHER4_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER4') ],
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for all SAV campaigns
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {string[]} - Transformed variables
 */
const processStandingSAVXX = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = processStandingSAVXX;
