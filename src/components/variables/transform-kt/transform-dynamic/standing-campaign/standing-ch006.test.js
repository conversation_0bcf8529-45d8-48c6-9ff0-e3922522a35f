const processStandingCH006 = require('./standing-ch006');

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'OTHER1', value: '1001.42' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
];

describe('Variables > transform KT > transform dynamic > standing campaign > processStandingCH006', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should successfully call', async () => {
    const { transformed, dataContext } = await processStandingCH006({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('stch006');
    expect(transformed[0]).toEqual('$1,001.42');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processStandingCH006({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER2_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER2_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
