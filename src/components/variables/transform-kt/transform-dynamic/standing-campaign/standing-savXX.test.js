const processStandingSAVXX = require('./standing-savXX');

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'OTHER1', value: '001001' },
    { name: 'OTHER2', value: '20190101' },
    { name: 'OTHER3', value: '20191231' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_DATE2_END',
  'SOLUI_OTHER3_END',
  'SOLUI_DATE3_END',
];

describe('Variables > transform KT > transform dynamic > standing campaign > processStandingSAVXX', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should successfully call', async () => {
    const { transformed, dataContext } = await processStandingSAVXX({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('stsavxx');
    expect(transformed[0]).toEqual('1001');
    expect(transformed[1]).toEqual('January 1, 2019');
    expect(transformed[2]).toEqual('January 1, 2019');
    expect(transformed[3]).toEqual('December 31, 2019');
    expect(transformed[4]).toEqual('December 31, 2019');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processStandingSAVXX({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER6_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER6_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
