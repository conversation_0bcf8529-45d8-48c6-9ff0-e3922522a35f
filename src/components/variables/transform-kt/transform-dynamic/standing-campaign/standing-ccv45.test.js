const processStandingCCV45 = require('./standing-ccv45');

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [],
};

describe('Variables > transform KT > transform dynamic > standing campaign > processStandingCCV45', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed, dataContext } = await processStandingCCV45({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER4_END' ]);
    expect(dataContext).toEqual('stccv45');
    expect(transformed[0]).toEqual('SOLUI_OTHER4_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
