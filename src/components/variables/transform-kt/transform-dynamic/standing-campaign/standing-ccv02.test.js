const processStandingCCV02 = require('./standing-ccv02');

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'OTHER1', value: 'X1' },
    { name: 'OTHER2', value: 'SAM3' },
    { name: 'OTHER3', value: 'X2' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_REASON_END',
  'SOLUI_OTHER3_END',
  'SOLUI_OTHER2_END',
];

describe('Variables > transform KT > transform dynamic > standing campaign > processStandingCCV02', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should successfully call', async () => {
    const { transformed, dataContext } = await processStandingCCV02({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('stccv02');
    expect(transformed[0]).toEqual('X1');
    expect(transformed[1]).toEqual('home address');
    expect(transformed[2]).toEqual('X2');
    expect(transformed[3]).toEqual('home address');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processStandingCCV02({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER4_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER4_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
