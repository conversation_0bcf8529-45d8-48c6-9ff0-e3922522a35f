const R = require('ramda');
const { isCli, processCli } = require('./preapproved-cli');
const { isGicRenewal, processGicRenewal } = require('./gic-renewal');
const { isMortgageLastPayment, processMortgageLastPayment } = require('./mortgage-last-payment');
const { isMortgageRenewal, processMortgageRenewal } = require('./mortgage-renewal');
const { isMortgageRetention, processMortgageRetention } = require('./mortgage-retention');
const { isMultipleLeads, processMultipleLeads } = require('./multiple-leads');
const { isOverdraftProtection, processOverdraftProtection } = require('./overdraft-protection');
const { isOverdraftProtectionQuebec, processOverdraftProtectionQuebec } = require('./overdraft-protection-quebec');
const { isOverdraftProtectionNonQuebec, processOverdraftProtectionNonQuebec } = require('./overdraft-protection-nonquebec');
const { isPreapprovedCC, processPreapprovedCC } = require('./preapproved-cc');
const { isPreapprovedCCSB, processPreapprovedCCSB } = require('./preapproved-cc-sb');
const { isPreapprovedLOC, processPreapprovedLOC } = require('./preapproved-loc');
const { isStepCli, processStepCli } = require('./step-cli');
const { isStudentMigration, processStudentMigration } = require('./student-migration');
const { isPhysicianOffer, processPhysicianOffer } = require('./physician-offer');
const { isStepAcquisition, processStepAcquisition } = require('./step-acquisition');
const { isRspCulAcq, processRspCulAcq } = require('./rsp-cul-acq');
const { isSceneReward, processSceneReward } = require('./scene-reward');
const { isPayWise, processPayWise } = require('./paywise');
const { isValueVisaSwitch, processValueVisaSwitch } = require('./value-visa-switch');
const { isCeba5, processCeba5 } = require('./ceba5');
const { isUloc, processUloc } = require('./inactive-closure');
// const { isEmobD2d, processEmobD2D } = require('./emob-d2d-nova');
const { isMortgageSwitch, processMortgageSwitch } = require('./mortgage-switch');
const { isAmlkycH, processAmlkycH } = require('./amlkyc-hard-stop');
const { isAmlkycS, processAmlkycS } = require('./amlkyc-soft-stop');
const { isAkycSB, processAkycSB } = require('./amlkyc-soft-stop-small-business');
const { isAutopay, processAutopay } = require('./autopay');
const { isMpsaRetention, processMpsaRetention } = require('./mpsa-retention');
const { isOfsi, processOfsi } = require('./ofsi');
const { isCoBorrower, processCoBorrower } = require('./co-borrower-remed');
const { isLmt, processLmt } = require('./lmt');
const { isCCBalanceTransfer, processCCBalanceTransfer } = require('./cc-balance-transfer');
const { isUnsecuredLoc, processUnsecuredLoc } = require('./unsecured-line-of-credit');
const { isSSIRemediation, processSSI } = require('./ssi-remediation');
const { isSmbLoanRenewal, processSmbLoanRenewal } = require('./smb-term-loan-renewal');
const { isFca, processFca } = require('./client-advice-centre');
const { isEcm, processEcm } = require('./client-compliance-response-service');
const { isYkw, processYkw } = require('./high-propensity-thin-campaign');

/**
 * Transforms variables for a targeted campaign
 * @param {Object} deps - Dependencies
 * @param {string} env - `Preferred-Environment` header value
 * @param {string} reqHeaders - Request Headers
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processCampaign = (deps, env, reqHeaders, campaign, language, variables) => R.cond([
  [ isCli, () => processCli(deps, campaign, language, variables) ],
  [ isGicRenewal, () => processGicRenewal(deps, env, reqHeaders, campaign, language, variables) ],
  [ isMortgageLastPayment, () => processMortgageLastPayment(deps, campaign, language, variables) ],
  [ isMortgageRenewal, () => processMortgageRenewal(deps, campaign, language, variables) ],
  [ isMortgageRetention, () => processMortgageRetention(deps, campaign, language, variables) ],
  [ isMultipleLeads, () => processMultipleLeads(deps, campaign, language, variables) ],
  [ isOverdraftProtection, () => processOverdraftProtection(deps, campaign, language, variables) ],
  [ isOverdraftProtectionQuebec, () => processOverdraftProtectionQuebec(deps, campaign, language, variables) ],
  [ isOverdraftProtectionNonQuebec, () => processOverdraftProtectionNonQuebec(deps, campaign, language, variables) ],
  [ isPreapprovedCC, () => processPreapprovedCC(deps, campaign, language, variables) ],
  [ isPreapprovedCCSB, () => processPreapprovedCCSB(deps, campaign, language, variables) ],
  [ isPreapprovedLOC, () => processPreapprovedLOC(deps, campaign, language, variables) ],
  [ isStepCli, () => processStepCli(deps, campaign, language, variables) ],
  [ isStudentMigration, () => processStudentMigration(deps, campaign, language, variables) ],
  [ isPhysicianOffer, () => processPhysicianOffer(deps, campaign, language, variables) ],
  [ isStepAcquisition, () => processStepAcquisition(deps, campaign, language, variables) ],
  [ isRspCulAcq, () => processRspCulAcq(deps, campaign, language, variables) ],
  [ isSceneReward, () => processSceneReward(deps, campaign, language, variables) ],
  [ isPayWise, () => processPayWise(deps, campaign, language, variables) ],
  [ isValueVisaSwitch, () => processValueVisaSwitch(deps, campaign, language, variables) ],
  [ isCeba5, () => processCeba5(deps, campaign, language, variables) ],
  [ isUloc, () => processUloc(deps, campaign, language, variables) ],
  // [ isEmobD2d, () => processEmobD2D(deps, campaign, language, variables) ],
  [ isMortgageSwitch, () => processMortgageSwitch(deps, campaign, language, variables) ],
  [ isAmlkycH, () => processAmlkycH(deps, campaign, language, variables) ],
  [ isAmlkycS, () => processAmlkycS(deps, campaign, language, variables) ],
  [ isAkycSB, () => processAkycSB(deps, campaign, language, variables) ],
  [ isAutopay, () => processAutopay(deps, campaign, language, variables) ],
  [ isMpsaRetention, () => processMpsaRetention(deps, campaign, language, variables) ],
  [ isOfsi, () => processOfsi(deps, campaign, language, variables) ],
  [ isCoBorrower, () => processCoBorrower(deps, campaign, language, variables) ],
  [ isLmt, () => processLmt(deps, campaign, language, variables) ],
  [ isCCBalanceTransfer, () => processCCBalanceTransfer(deps, campaign, language, variables) ],
  [ isUnsecuredLoc, () => processUnsecuredLoc(deps, campaign, language, variables) ],
  [ isSSIRemediation, () => processSSI(deps, campaign, language, variables) ],
  [ isSmbLoanRenewal, () => processSmbLoanRenewal(deps, campaign, language, variables) ],
  [ isFca, () => processFca(deps, campaign, language, variables) ],
  [ isEcm, () => processEcm(deps, campaign, language, variables) ],
  [ isYkw, () => processYkw(deps, campaign, language, variables) ],
  [ R.T, () => ({ dataContext: 'unknown', transformed: variables }) ],
])(campaign);

/**
 * Transforms variables for a targeted campaign
 * @param {Object} deps - Dependencies
 * @param {string} env - `Preferred-Environment` header value
 * @param {string} reqHeaders - Request Headers
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<Object>} - Transformed mappings of variables as a key-value object
 */
const processTargetedCampaign = async (deps, env, reqHeaders, campaign, language, variables) => {
  const { transformed, dataContext } = await processCampaign(deps, env, reqHeaders, campaign, language, variables);
  return {
    dataContext,
    transformed: R.zipObj(variables, transformed),
  };
};

module.exports = {
  processTargetedCampaign,
};
