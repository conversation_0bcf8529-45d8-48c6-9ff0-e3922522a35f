const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const {
  processAsUnknown,
} = require('../../../../processors');

const dataContext = 'amlkycs';

const validCampaigns = [
  'ORCDL', // soft stop - low/medium risk -- will need to display date only
  'ORCHR', // soft stop - high risk -- will need to display remaining days only
];

/**
 * Returns a function that checks if the campaign is a hardstop campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isAmlkycS = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies,
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for AMLKYC hard stop campaigns
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processAmlkycS = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isAmlkycS,
  processAmlkycS,
};
