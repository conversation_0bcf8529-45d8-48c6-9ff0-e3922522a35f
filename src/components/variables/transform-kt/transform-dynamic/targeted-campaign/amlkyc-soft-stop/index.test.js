const { isAmlkycS, processAmlkycS } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ORCHR',
  additional_data: [
    { name: 'NAME', value: '<PERSON>' },
  ],
};

const mockVariables = [
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > AMLKYC soft stop campaign -- low/medium/high risk', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isAmlkycS).toBeInstanceOf(Function);
    expect(processAmlkycS).toBeInstanceOf(Function);
  });
  test('should identify ORCHR campaign as AMLKYC soft stop campaign -- high risk', () => {
    expect(isAmlkycS({ campaign_id: 'ORCHR' })).toEqual(true);
  });
  test('should identify ORCDL campaign as AMLKYC Hard stop campaign -- low/medium risk', () => {
    expect(isAmlkycS({ campaign_id: 'ORCDL' })).toEqual(true);
  });
  test('should identify campaign as not AMLKYC soft stop campaign', () => {
    expect(isAmlkycS({ campaign_id: 'ABC99' })).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { dataContext } = await processAmlkycS({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('amlkycs');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processAmlkycS({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER1_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
