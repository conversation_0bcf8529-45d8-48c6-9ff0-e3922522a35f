const { isYkw, processYkw } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'YKW01',
  additional_data: [
    { name: 'OTHER1', value: 'CODE' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
];

const logger = {
  warn: jest.fn(x => console.log(x)),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > YKW Campaigns', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });

  test('should be functions', () => {
    expect(isYkw).toBeInstanceOf(Function);
    expect(processYkw).toBeInstanceOf(Function);
  });

  test('should identify YKW campaign correctly', () => {
    expect(isYkw({ campaign_id: 'YKW01' })).toEqual(true);
    expect(isYkw({ campaign_id: 'ABC99' })).toEqual(false);
  });

  test('should successfully process a campaign', async () => {
    const { dataContext, transformed } = await processYkw(
      { logger },
      mockCampaign,
      'en',
      mockVariables,
    );
    expect(dataContext).toEqual('high-propensity-thin-campaign');
    expect(transformed).toHaveLength(1);
    expect(transformed[0]).toEqual('CODE');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });

  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processYkw({ logger }, mockCampaign, 'en', [
      'SOLUI_OTHER0_END',
    ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER0_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
