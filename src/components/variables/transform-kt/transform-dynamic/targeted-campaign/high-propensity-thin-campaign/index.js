const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const { processAsIs, processAsUnknown } = require('../../../../processors');

const dataContext = 'high-propensity-thin-campaign';

const validCampaigns = [ 'YKW' ];

/**
 * Returns a function that checks if the campaign is a YKW campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isYkw = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign) =>
  R.cond([
    // Discount code
    [ R.equals('SOLUI_OTHER1_END'), (x) => processAsIs(deps, campaign, x, 'OTHER1') ],
    // Unknown
    [ R.T, x => processAsUnknown(deps, campaign, x) ],
  ]);

/**
 * Transforms variables for YKW campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processYkw = async (deps, campaign, language, variables) => {
  const result = await Promise.all(
    variables.map(variable =>
      processVariable(deps, campaign)(variable),
    ),
  );
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isYkw,
  processYkw,
};
