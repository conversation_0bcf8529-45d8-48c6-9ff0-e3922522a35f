const { isEmobD2d, processEmobD2D } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'XXO01',
  additional_data: [
    { name: 'OTHER1', value: '7.62%' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > EMOB D2D', () => {
  beforeEach(() => {
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isEmobD2d).toBeInstanceOf(Function);
    expect(processEmobD2D).toBeInstanceOf(Function);
  });

  test('should identify campaign as EMOB D2D', () => {
    expect(isEmobD2d(mockCampaign)).toEqual(true);
  });

  test('should successfully process a campaign', async () => {
    const { transformed, dataContext } = await processEmobD2D({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('emob-d2d');
    expect(transformed[0]).toEqual('7.62%');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });

  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processEmobD2D({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER5_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER5_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
