const { isRspCulAcq, processRspCulAcq } = require('./index');

const mockDate = 'November 7, 2024';

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ULG99',
  additional_data: [
    { name: 'OTHER1', value: 'X1' },
    { name: 'OTHER2', value: 'X2' },
    { name: 'OTHER3', value: '20241107' },
    { name: 'OTHER4', value: '20241107' },
    { name: 'OTHER5', value: 'X5' },
    { name: 'OTHER6', value: 'X6' },
    { name: 'OTHER7', value: 'X7' },
    { name: 'OTHER8', value: '20241107' },
    { name: 'OTHER9', value: 'X9' },
    { name: 'OTHER10', value: 'X10' },
    { name: 'OTHER11', value: '20241107' },
    { name: 'OTHER12', value: 'X12' },
    { name: 'OTHER13', value: 'X13' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_OTHER3_END',
  'SOLUI_DATE3_END',
  'SOLUI_OTHER4_END',
  'SOLUI_DATE4_END',
  'SOLUI_OTHER5_END',
  'SOLUI_OTHER6_END',
  'SOLUI_OTHER7_END',
  'SOLUI_OTHER8_END',
  'SOLUI_DATE8_END',
  'SOLUI_OTHER9_END',
  'SOLUI_OTHER10_END',
  'SOLUI_OTHER11_END',
  'SOLUI_DATE11_END',
  'SOLUI_OTHER12_END',
  'SOLUI_OTHER13_END',
];

const properResponses = [
  'X1',
  'X2',
  '', // hidden OTHER3
  mockDate,
  '', // hidden OTHER4
  mockDate,
  'X5',
  'X6',
  'X7',
  '', // hidden OTHER8
  mockDate,
  'X9',
  'X10',
  '', // hidden OTHER11
  mockDate,
  'X12',
  'X13',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > RSP CUL Acq', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isRspCulAcq).toBeInstanceOf(Function);
    expect(processRspCulAcq).toBeInstanceOf(Function);
  });
  test('should identify campaign as RSP CUL Acq', () => {
    expect(isRspCulAcq(mockCampaign)).toEqual(true);
  });
  test('should identify campaign as not CLI', () => {
    const res = isRspCulAcq({ campaign_id: 'ABC99' });
    expect(res).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { transformed, dataContext } = await processRspCulAcq({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('rspculacq');
    for (let i = 0; i < mockVariables.length; i += 1) {
      expect(transformed[Number(i)]).toEqual(properResponses[Number(i)]);
    }
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processRspCulAcq({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER14_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER14_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
