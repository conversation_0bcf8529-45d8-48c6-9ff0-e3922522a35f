const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const {
  processAsHidden,
  processAsUnknown,
  processAsIs,
  processAsDate,
} = require('../../../../processors');

const dataContext = 'rspculacq';

const validCampaigns = [
  'ULG', 'ULI',
];

/**
 * Returns a function that checks if the campaign is a RSP CUL Acquisition
 * @returns {(Campaign) => bool} - Result functions
 */
const isRspCulAcq = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  [ R.equals('SOLUI_OTHER1_END'), (variable) => processAsIs(deps, campaign, variable, 'OTHER1') ], // Target module type
  [ R.equals('SOLUI_OTHER2_END'), (variable) => processAsIs(deps, campaign, variable, 'OTHER2') ], // Z.ZZ (+0,50)
  [ R.equals('SOLUI_OTHER3_END'), processAsHidden ],
  [ R.equals('SOLUI_DATE3_END'), (variable) => processAsDate(deps, campaign, language, variable, 'OTHER3') ], // CR Date
  [ R.equals('SOLUI_OTHER4_END'), processAsHidden ],
  [ R.equals('SOLUI_DATE4_END'), (variable) => processAsDate(deps, campaign, language, variable, 'OTHER4') ], // PURDATE
  [ R.equals('SOLUI_OTHER5_END'), (variable) => processAsIs(deps, campaign, variable, 'OTHER5') ], // SOURCE CODE
  [ R.equals('SOLUI_OTHER6_END'), (variable) => processAsIs(deps, campaign, variable, 'OTHER6') ], // Y.YY
  [ R.equals('SOLUI_OTHER7_END'), (variable) => processAsIs(deps, campaign, variable, 'OTHER7') ], // P.PP
  [ R.equals('SOLUI_OTHER8_END'), processAsHidden ],
  [ R.equals('SOLUI_DATE8_END'), (variable) => processAsDate(deps, campaign, language, variable, 'OTHER8') ], // LEGAL RATE DATE
  [ R.equals('SOLUI_OTHER9_END'), (variable) => processAsIs(deps, campaign, variable, 'OTHER9') ], // MAILID
  [ R.equals('SOLUI_OTHER10_END'), (variable) => processAsIs(deps, campaign, variable, 'OTHER10') ], // INTRO RATE
  [ R.equals('SOLUI_OTHER11_END'), processAsHidden ],
  [ R.equals('SOLUI_DATE11_END'), (variable) => processAsDate(deps, campaign, language, variable, 'OTHER11') ], // ACCEPT DATE
  [ R.equals('SOLUI_OTHER12_END'), (variable) => processAsIs(deps, campaign, variable, 'OTHER12') ], // BONUS POINTS
  [ R.equals('SOLUI_OTHER13_END'), (variable) => processAsIs(deps, campaign, variable, 'OTHER13') ], // OFFER MONTHS
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for CLI campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processRspCulAcq = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isRspCulAcq,
  processRspCulAcq,
};
