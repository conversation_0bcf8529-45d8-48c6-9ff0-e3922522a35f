const R = require('ramda');
const { getAdditionalDataValue } = require('../../../../helpers');
const {
  processAsUnknown,
  processAsHidden,
  processAsDate,
  processAsIs,
} = require('../../../../processors');

const dataContext = 'paodpn';

/**
 * Checks if the campaign is an Overdraft Protection campaign
 * @param {Campaign} campaign - Campaign
 * @return {bool} - Result
 */
const isOverdraftProtectionNonQuebec = R.pipe(
  R.flip(getAdditionalDataValue)('OTHER2'),
  R.equals('ODPN'),
);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  // ???
  [ R.equals('SOLUI_DATE1_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER1') ],
  [ R.equals('SOLUI_OTHER1_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER1') ],
  // `ODP`
  [ R.equals('SOLUI_OTHER2_END'), processAsHidden ],
  // First Name
  [ R.equals('SOLUI_OTHER3_END'), (x) => processAsIs(deps, campaign, x, 'OTHER3') ],
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for Overdraft Protection campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processOverdraftProtectionNonQuebec = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isOverdraftProtectionNonQuebec,
  processOverdraftProtectionNonQuebec,
};
