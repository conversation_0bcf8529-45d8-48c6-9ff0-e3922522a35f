const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const {
  processAsUnknown,
  processAsDate,
  processAsHidden,
  processAsIs,
} = require('../../../../processors');

const dataContext = 'pacc';

// is_recommended = true
const validCampaigns = [
  // Scene VISA PA - ACQ
  'XVS', 'XVT', 'XVU', 'XVV', 'XVW', 'XYS', 'XXH', 'XXI', 'XVQ', 'XVR',
  // Scene VISA PA - ACQ (2000 pts + $500)
  'XAN', 'XAK',
  // Passport Infinity 25K w/$1k spend 3m ACQ
  'XAG', 'XAO',
  // Value VISA PA - ACQ
  'PAH', 'PBH', 'XVO', 'XVP',
  // Momentum VISA Infinite PA - ACQ
  'PAA', 'PBF', 'XVK', 'XVL', 'XFK',
  // Red AMEX
  'XAJ', 'XHI', 'XHJ', 'XXE',
  // Passport Infinite
  'XAP', 'XXU',
  // ?
  'XFA', 'AAG', 'AAP',
  // Momentum Regular PA - ACQ
  'PAZ', 'PBG', 'XFJ',
  // AMEX Gold PA
  'PAQ', 'PAG', 'XFU', 'XFV', 'XXF', 'XXG', 'XVI', 'XVJ',
  // Passport Inf - ACQ (Dual Offer), Momentum Infinite PA - ACQ (Dual Offer)
  'XFI', 'XHG',
  // Visa Momentum (No fee)
  'PAC', 'XLD', 'XVM', 'XVN',
  // Active PA - ACQ
  'XZO', 'XZP', 'XZQ', 'XZR', 'XZS', 'XZT', 'XZU', 'XZV',
  // Inactive PA - ACQ
  'XZL', 'XZM', 'XZN',
  // NPA CC Invitation to Apply
  'YBD', 'YBE', 'YBF',
];

/**
 * Returns a function that checks if the campaign is a Pre-approved CC campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isPreapprovedCC = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  // Target Module
  // // [ R.equals('SOLUI_OTHER1_END'), (x) => processAsIs(deps, campaign, x, 'OTHER1') ],
  [ R.equals('SOLUI_OTHER1_END'), processAsHidden ],
  // +Z.ZZ (PRIME + Z.ZZ)
  // // [ R.equals('SOLUI_OTHER2_END'), (x) => processAsIs(deps, campaign, x, 'OTHER2') ],
  [ R.equals('SOLUI_OTHER2_END'), processAsHidden ],
  // Offer expiration date
  [ R.equals('SOLUI_DATE3_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER3') ],
  [ R.equals('SOLUI_OTHER3_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER3') ],
  // Purchase date ???
  // // [ R.equals('SOLUI_DATE4_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER4') ],
  // // [ R.equals('SOLUI_OTHER4_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER4') ],
  [ R.equals('SOLUI_DATE4_END'), processAsHidden ],
  [ R.equals('SOLUI_OTHER4_END'), processAsHidden ],
  // Source code
  // // [ R.equals('SOLUI_OTHER5_END'), (x) => processAsIs(deps, campaign, x, 'OTHER5') ],
  [ R.equals('SOLUI_OTHER5_END'), processAsHidden ],
  // Total rate ???
  // // [ R.equals('SOLUI_OTHER6_END'), (x) => processAsIs(deps, campaign, x, 'OTHER6') ],
  [ R.equals('SOLUI_OTHER6_END'), processAsHidden ],
  // ???
  // // [ R.equals('SOLUI_OTHER7_END'), (x) => processAsIs(deps, campaign, x, 'OTHER7') ],
  [ R.equals('SOLUI_OTHER7_END'), processAsHidden ],
  // Legal rate date
  // // [ R.equals('SOLUI_DATE8_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER8') ],
  // // [ R.equals('SOLUI_OTHER8_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER8') ],
  [ R.equals('SOLUI_DATE8_END'), processAsHidden ],
  [ R.equals('SOLUI_OTHER8_END'), processAsHidden ],
  // Mail id ???
  // // [ R.equals('SOLUI_OTHER9_END'), (x) => processAsIs(deps, campaign, x, 'OTHER9') ],
  [ R.equals('SOLUI_OTHER9_END'), processAsHidden ],
  // Intro rate ???
  // // [ R.equals('SOLUI_OTHER10_END'), (x) => processAsIs(deps, campaign, x, 'OTHER10') ],
  [ R.equals('SOLUI_OTHER10_END'), processAsHidden ],
  // Accept date
  // // [ R.equals('SOLUI_DATE11_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER11') ],
  // // [ R.equals('SOLUI_OTHER11_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER11') ],
  [ R.equals('SOLUI_DATE11_END'), processAsHidden ],
  [ R.equals('SOLUI_OTHER11_END'), processAsHidden ],
  // Bonus points
  // // [ R.equals('SOLUI_OTHER12_END'), (x) => processAsIs(deps, campaign, x, 'OTHER12') ],
  [ R.equals('SOLUI_OTHER12_END'), processAsHidden ],
  // Offer months
  // // [ R.equals('SOLUI_OTHER13_END'), (x) => processAsIs(deps, campaign, x, 'OTHER13') ],
  [ R.equals('SOLUI_OTHER13_END'), processAsHidden ],
  // Alt source code 1
  // // [ R.equals('SOLUI_OTHER14_END'), (x) => processAsIs(deps, campaign, x, 'OTHER14') ],
  [ R.equals('SOLUI_OTHER14_END'), processAsHidden ],
  // Alt prod code/subcode 1
  // // [ R.equals('SOLUI_OTHER15_END'), (x) => processAsProduct(deps, campaign, language, x, 'OTHER15') ],
  [ R.equals('SOLUI_OTHER15_END'), processAsHidden ],
  // Alt source code 2
  // // [ R.equals('SOLUI_OTHER16_END'), (x) => processAsIs(deps, campaign, x, 'OTHER16') ],
  [ R.equals('SOLUI_OTHER16_END'), processAsHidden ],
  // Alt prod code/subcode 2
  // // [ R.equals('SOLUI_OTHER17_END'), (x) => processAsProduct(deps, campaign, language, x, 'OTHER17') ],
  [ R.equals('SOLUI_OTHER17_END'), processAsHidden ],
  // First Name
  [ R.equals('SOLUI_OTHER18_END'), (x) => processAsIs(deps, campaign, x, 'OTHER18') ],
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for Pre-approved CC campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processPreapprovedCC = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isPreapprovedCC,
  processPreapprovedCC,
};
