const { isPreapprovedCC, processPreapprovedCC } = require('./index');

const mockProduct1 = {
  productId: '580040',
  productTypes: [
    {
      productDomain: 'default',
      productCode: 'AFB',
      subProductCode: 'B1',
    },
    {
      productDomain: 'KT',
      productCode: 'VAX',
      subProductCode: 'B1',
    },
  ],
  productSystem: 'VAX',
  currency: 'CAD',
  ownership: 'B',
  properties: [
    { type: 'CATEGORY', value: 'CREDITCARDS' },
    { type: 'DEFAULT_CLASSIFICATION', value: 'Amex' },
    { type: 'DETAIL_CLASSIFICATION', value: 'Amex' },
    { type: 'REL_ACTIVITY_CLASSIFICATION', value: 'BusinessAmex' },
    { type: 'CORNERSTONE_ID', value: 'Borrowing' },
    { type: 'ASSET_LIABILITY', value: 'L' },
  ],
  sortWeight: '4000',
  accountNumberLength: 15,
  accountNumberMask: '####*****######',
  descriptions: [
    { locale: 'en_CA', value: 'Future Amex SB B1' },
    { locale: 'fr_CA', value: 'F-Future Amex SB B1' },
  ],
};

const mockProduct2 = {
  productId: '580040',
  productTypes: [
    {
      productDomain: 'default',
      productCode: 'AFB',
      subProductCode: 'B2',
    },
    {
      productDomain: 'KT',
      productCode: 'VAX',
      subProductCode: 'B1',
    },
  ],
  productSystem: 'VAX',
  currency: 'CAD',
  ownership: 'B',
  properties: [
    { type: 'CATEGORY', value: 'CREDITCARDS' },
    { type: 'DEFAULT_CLASSIFICATION', value: 'Amex' },
    { type: 'DETAIL_CLASSIFICATION', value: 'Amex' },
    { type: 'REL_ACTIVITY_CLASSIFICATION', value: 'BusinessAmex' },
    { type: 'CORNERSTONE_ID', value: 'Borrowing' },
    { type: 'ASSET_LIABILITY', value: 'L' },
  ],
  sortWeight: '4000',
  accountNumberLength: 15,
  accountNumberMask: '####*****######',
  descriptions: [
    { locale: 'en_CA', value: 'Future Amex SB B2' },
    { locale: 'fr_CA', value: 'F-Future Amex SB B2' },
  ],
};

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'XAN99',
  additional_data: [
    { name: 'OTHER1', value: 'X1' },
    { name: 'OTHER2', value: 'X2' },
    { name: 'OTHER3', value: '********' },
    { name: 'OTHER4', value: '********' },
    { name: 'OTHER5', value: 'X5' },
    { name: 'OTHER6', value: 'X6' },
    { name: 'OTHER7', value: 'X7' },
    { name: 'OTHER8', value: '********' },
    { name: 'OTHER9', value: 'X9' },
    { name: 'OTHER10', value: 'X10' },
    { name: 'OTHER11', value: '********' },
    { name: 'OTHER12', value: 'X12' },
    { name: 'OTHER13', value: '********' },
    { name: 'OTHER14', value: 'X14' },
    { name: 'OTHER15', value: 'ABCZZ' },
    { name: 'OTHER16', value: 'X16' },
    { name: 'OTHER17', value: 'CBAZZ' },
    { name: 'OTHER18', value: 'ABC123' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_OTHER3_END',
  'SOLUI_DATE3_END',
  'SOLUI_OTHER4_END',
  'SOLUI_DATE4_END',
  'SOLUI_OTHER5_END',
  'SOLUI_OTHER6_END',
  'SOLUI_OTHER7_END',
  'SOLUI_OTHER8_END',
  'SOLUI_DATE8_END',
  'SOLUI_OTHER9_END',
  'SOLUI_OTHER10_END',
  'SOLUI_OTHER11_END',
  'SOLUI_DATE11_END',
  'SOLUI_OTHER12_END',
  'SOLUI_OTHER13_END',
  'SOLUI_OTHER14_END',
  'SOLUI_OTHER15_END',
  'SOLUI_OTHER16_END',
  'SOLUI_OTHER17_END',
  'SOLUI_OTHER18_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

const productBookService = {
  getProduct: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > Pre-approved Credit Card', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isPreapprovedCC).toBeInstanceOf(Function);
    expect(processPreapprovedCC).toBeInstanceOf(Function);
  });
  test('should identify campaign as Pre-approved CC', () => {
    expect(isPreapprovedCC({ campaign_id: 'XAN99' })).toEqual(true);
    expect(isPreapprovedCC({ campaign_id: 'XAK99' })).toEqual(true);
    expect(isPreapprovedCC({ campaign_id: 'XAG99' })).toEqual(true);
    expect(isPreapprovedCC({ campaign_id: 'XAO99' })).toEqual(true);
    expect(isPreapprovedCC({ campaign_id: 'PAH99' })).toEqual(true);
    expect(isPreapprovedCC({ campaign_id: 'PBH99' })).toEqual(true);
    expect(isPreapprovedCC({ campaign_id: 'PAA99' })).toEqual(true);
    expect(isPreapprovedCC({ campaign_id: 'PBF99' })).toEqual(true);
    expect(isPreapprovedCC({ campaign_id: 'PAC99' })).toEqual(true);
    expect(isPreapprovedCC({ campaign_id: 'XLD99' })).toEqual(true);
    expect(isPreapprovedCC({ campaign_id: 'XFK55' })).toEqual(true);
    expect(isPreapprovedCC({ campaign_id: 'XHJ55' })).toEqual(true);
  });
  test('should identify campaign as not Pre-approved CC', () => {
    expect(isPreapprovedCC({ campaign_id: 'ABC99' })).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    productBookService.getProduct
      .mockResolvedValueOnce(mockProduct1)
      .mockResolvedValueOnce(mockProduct2);
    const { transformed, dataContext } = await processPreapprovedCC({ logger, productBookService }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('pacc');
    expect(transformed[0]).toEqual('');
    expect(transformed[1]).toEqual('');
    expect(transformed[2]).toEqual('January 1, 2019');
    expect(transformed[3]).toEqual('January 1, 2019');
    expect(transformed[4]).toEqual('');
    expect(transformed[5]).toEqual('');
    expect(transformed[6]).toEqual('');
    expect(transformed[7]).toEqual('');
    expect(transformed[8]).toEqual('');
    expect(transformed[9]).toEqual('');
    expect(transformed[10]).toEqual('');
    expect(transformed[11]).toEqual('');
    expect(transformed[12]).toEqual('');
    expect(transformed[13]).toEqual('');
    expect(transformed[14]).toEqual('');
    expect(transformed[15]).toEqual('');
    expect(transformed[16]).toEqual('');
    expect(transformed[17]).toEqual('');
    expect(transformed[18]).toEqual('');
    expect(transformed[19]).toEqual('');
    expect(transformed[20]).toEqual('');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processPreapprovedCC({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER19_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER19_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
