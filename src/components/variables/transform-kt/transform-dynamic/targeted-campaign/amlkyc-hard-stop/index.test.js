const { isAmlkycH, processAmlkycH } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ORCTR',
  expiry_date: '2121-08-09',
  additional_data: [],
};

const mockVariables = [
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > AMLKYC Hard stop campaign', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isAmlkycH).toBeInstanceOf(Function);
    expect(processAmlkycH).toBeInstanceOf(Function);
  });
  test('should identify ORCTR campaign as AMLKYC Hard stop campaign', () => {
    expect(isAmlkycH({ campaign_id: 'ORCTR' })).toEqual(true);
  });
  test('should identify campaign as not AMLKYC Hard stop campaign', () => {
    expect(isAmlkycH({ campaign_id: 'ABC99' })).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { dataContext } = await processAmlkycH({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('amlkych');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processAmlkycH({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER1_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
