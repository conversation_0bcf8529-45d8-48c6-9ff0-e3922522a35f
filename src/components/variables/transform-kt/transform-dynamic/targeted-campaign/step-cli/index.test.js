const { isStepCli, processStepCli } = require('./index');

const mockDate = 'January 1, 2019';

const mockProduct1 = {
  productId: '580040',
  productTypes: [
    {
      productDomain: 'default',
      productCode: 'AFB',
      subProductCode: 'B1',
    },
    {
      productDomain: 'KT',
      productCode: 'VAX',
      subProductCode: 'B1',
    },
  ],
  productSystem: 'VAX',
  currency: 'CAD',
  ownership: 'B',
  properties: [
    { type: 'CATEGORY', value: 'CREDITCARDS' },
    { type: 'DEFAULT_CLASSIFICATION', value: 'Amex' },
    { type: 'DETAIL_CLASSIFICATION', value: 'Amex' },
    { type: 'REL_ACTIVITY_CLASSIFICATION', value: 'BusinessAmex' },
    { type: 'CORNERSTONE_ID', value: 'Borrowing' },
    { type: 'ASSET_LIABILITY', value: 'L' },
  ],
  sortWeight: '4000',
  accountNumberLength: 15,
  accountNumberMask: '####*****######',
  descriptions: [
    { locale: 'en_CA', value: 'Future Amex SB B1' },
    { locale: 'fr_CA', value: 'F-Future Amex SB B1' },
  ],
};

const mockProduct2 = {
  productId: '580040',
  productTypes: [
    {
      productDomain: 'default',
      productCode: 'AFB',
      subProductCode: 'B2',
    },
    {
      productDomain: 'KT',
      productCode: 'VAX',
      subProductCode: 'B1',
    },
  ],
  productSystem: 'VAX',
  currency: 'CAD',
  ownership: 'B',
  properties: [
    { type: 'CATEGORY', value: 'CREDITCARDS' },
    { type: 'DEFAULT_CLASSIFICATION', value: 'Amex' },
    { type: 'DETAIL_CLASSIFICATION', value: 'Amex' },
    { type: 'REL_ACTIVITY_CLASSIFICATION', value: 'BusinessAmex' },
    { type: 'CORNERSTONE_ID', value: 'Borrowing' },
    { type: 'ASSET_LIABILITY', value: 'L' },
  ],
  sortWeight: '4000',
  accountNumberLength: 15,
  accountNumberMask: '####*****######',
  descriptions: [
    { locale: 'en_CA', value: 'Future Amex SB B2' },
    { locale: 'fr_CA', value: 'F-Future Amex SB B2' },
  ],
};

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'JAB99',
  additional_data: [
    { name: 'OTHER1', value: 'AFBB1' },
    { name: 'OTHER2', value: '********' },
    { name: 'OTHER3', value: 'AFBB2' },
    { name: 'OTHER4', value: 'X4' },
    { name: 'OTHER5', value: 'X5' },
    { name: 'OTHER6', value: 'X6' },
    { name: 'OTHER7', value: 'X7' },
    // { name: 'OTHER8', value: '$10,000' },
    // { name: 'OTHER9', value: mockDate },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_DATE2_END',
  'SOLUI_OTHER3_END',
  'SOLUI_OTHER4_END',
  'SOLUI_OTHER5_END',
  'SOLUI_OTHER6_END',
  'SOLUI_OTHER7_END',
  // 'SOLUI_OTHER8_END',
  // 'SOLUI_OTHER9_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

const productBookService = {
  getProduct: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > Step CLI', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
    productBookService.getProduct.mockClear();
  });
  test('should be functions', () => {
    expect(isStepCli).toBeInstanceOf(Function);
    expect(processStepCli).toBeInstanceOf(Function);
  });
  test('should identify campaign as Step CLI', () => {
    const res = isStepCli(mockCampaign);
    expect(res).toEqual(true);
  });
  test('should identify campaign as not Step CLI', () => {
    const res = isStepCli({ campaign_id: 'ABC99' });
    expect(res).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    productBookService.getProduct
      .mockResolvedValueOnce(mockProduct1)
      .mockResolvedValueOnce(mockProduct2);
    const { transformed, dataContext } = await processStepCli({ logger, productBookService }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('stepcli');
    expect(transformed[0]).toEqual('Future Amex SB B1');
    expect(transformed[1]).toEqual(mockDate);
    expect(transformed[2]).toEqual(mockDate);
    expect(transformed[3]).toEqual('Future Amex SB B2');
    expect(transformed[4]).toEqual('X4');
    expect(transformed[5]).toEqual('');
    expect(transformed[6]).toEqual('');
    expect(transformed[7]).toEqual('X7');
    // expect(transformed[8]).toEqual('$10,000');
    // expect(transformed[9]).toEqual(mockDate);
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processStepCli({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER10_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER10_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
