const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const {
  processAsIs,
  processAsUnknown,
  processAsDate,
  processAsProduct,
  processAsHidden,
} = require('../../../../processors');

const dataContext = 'stepcli';

const validCampaigns = [
  // Step CLI
  'JAB',
  // Scotia Home Test
  'YFA', 'STE',
];

/**
 * Returns a function that checks if the campaign is a Step CLI campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isStepCli = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  // Product code
  [ R.equals('SOLUI_OTHER1_END'), (x) => processAsProduct(deps, campaign, language, x, 'OTHER1') ],
  // Offer expiration date
  [ R.equals('SOLUI_DATE2_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER2') ],
  [ R.equals('SOLUI_OTHER2_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER2') ],
  // Sub-product code
  [ R.equals('SOLUI_OTHER3_END'), (x) => processAsProduct(deps, campaign, language, x, 'OTHER3') ],
  // STEP number
  [ R.equals('SOLUI_OTHER4_END'), (x) => processAsIs(deps, campaign, x, 'OTHER4') ],
  // Co-borrower Name
  // // [ R.equals('SOLUI_OTHER5_END'), (x) => processAsIs(deps, campaign, x, 'OTHER5') ],
  [ R.equals('SOLUI_OTHER5_END'), processAsHidden ],
  // Co-borrower CID # DO NOT EVER RETURN
  [ R.equals('SOLUI_OTHER6_END'), processAsHidden ],
  // Interest rate
  [ R.equals('SOLUI_OTHER7_END'), (x) => processAsIs(deps, campaign, x, 'OTHER7') ],
  // descoped from April 12th-2022 release - will enable once confirmed by DM
  // // Offer Amount - Ex: $10,000
  // [ R.equals('SOLUI_OTHER8_END'), (x) => processAsIs(deps, campaign, x, 'OTHER8') ],
  // // date of extraction
  // [ R.equals('SOLUI_OTHER9_END'), (x) => processAsIs(deps, campaign, x, 'OTHER9') ],
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for Step CLI campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processStepCli = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isStepCli,
  processStepCli,
};
