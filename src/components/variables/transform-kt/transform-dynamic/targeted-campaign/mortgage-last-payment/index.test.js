const { isMortgageLastPayment, processMortgageLastPayment } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'LMP99',
  additional_data: [
    { name: 'OTHER1', value: '<PERSON>' },
    { name: 'OTHER2', value: '1 Somewhere St.' },
    { name: 'OTHER3', value: 'Toronto, ON' },
    { name: 'OTHER4', value: '(416) 123-4567' },
    { name: 'OTHER5', value: '12345' },
    { name: 'OTHER6', value: '20191231' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_OTHER3_END',
  'SOLUI_OTHER4_END',
  'SOLUI_OTHER5_END',
  'SOLUI_OTHER6_END',
  'SOLUI_DATE6_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > Mortgage Last Payment', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isMortgageLastPayment).toBeInstanceOf(Function);
    expect(processMortgageLastPayment).toBeInstanceOf(Function);
  });
  test('should identify campaign as Mortgage Last Payment', () => {
    expect(isMortgageLastPayment({ campaign_id: 'LMP99' })).toEqual(true);
  });
  test('should identify campaign as not Mortgage Last Payment', () => {
    expect(isMortgageLastPayment({ campaign_id: 'ABC99' })).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { transformed, dataContext } = await processMortgageLastPayment({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('mrglastpay');
    expect(transformed[0]).toEqual('John Doe');
    expect(transformed[1]).toEqual('1 Somewhere St.');
    expect(transformed[2]).toEqual('Toronto, ON');
    expect(transformed[3]).toEqual('\\(416\\) 123-4567');
    expect(transformed[4]).toEqual('12345');
    expect(transformed[5]).toEqual('December 31, 2019');
    expect(transformed[6]).toEqual('December 31, 2019');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processMortgageLastPayment({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER7_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER7_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
