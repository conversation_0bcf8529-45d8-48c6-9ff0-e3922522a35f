const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const {
  processAsUnknown,
  processAsAccount,
} = require('../../../../processors');

const dataContext = 'co-borrower-remed-ms';

// is_recommended = true
const validCampaigns = [
  'YCU',
];

/**
 * Returns a function that checks if the campaign is a CoBorrower campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isCoBorrower = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  [ R.equals('SOLUI_OTHER1_END'), (x) => processAsAccount(deps, campaign, x, 'OTHER1') ],
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for CoBorrower campaigns
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processCoBorrower = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isCoBorrower,
  processCoBorrower,
};
