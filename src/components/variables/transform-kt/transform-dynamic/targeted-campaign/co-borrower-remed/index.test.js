const { isCoBorrower, processCoBorrower } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'YCU',
  additional_data: [
    { name: 'OTHER1', value: '1234' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > co-borrower-remed', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isCoBorrower).toBeInstanceOf(Function);
    expect(processCoBorrower).toBeInstanceOf(Function);
  });

  test('should identify YCU01 campaign as co-borrower-remed', () => {
    expect(isCoBorrower({ campaign_id: 'YCU01' })).toEqual(true);
  });

  test('should identify CSO campaign as non  co-borrower-remed', () => {
    expect(isCoBorrower({ campaign_id: 'YAU01' })).toEqual(false);
  });

  test('should successfully process a campaign', async () => {
    const { dataContext, transformed } = await processCoBorrower({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('co-borrower-remed-ms');
    expect(transformed[0]).toEqual('1234');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });

  test('should log unknown SOLUI variable', async () => {
    await processCoBorrower({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER2_END' ]);
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
