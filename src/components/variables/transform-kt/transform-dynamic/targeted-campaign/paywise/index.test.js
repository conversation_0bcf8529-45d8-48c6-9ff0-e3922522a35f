const { isPayWise, processPayWise } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'XLX99',
  additional_data: [
    { name: 'OTHER1', value: '3' },
    { name: 'OTHER2', value: '1.1%' },
    { name: 'OTHER3', value: '6' },
    { name: 'OTHER4', value: '1.2%' },
    { name: 'OTHER5', value: '12' },
    { name: 'OTHER6', value: '1.3%' },
    { name: 'OTHER7', value: '20200101' },
    { name: 'OTHER8', value: '20201231' },
    { name: 'PROD', value: 'VGDRG' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_OTHER3_END',
  'SOLUI_OTHER4_END',
  'SOLUI_OTHER5_END',
  'SOLUI_OTHER6_END',
  'SOLUI_OTHER7_END',
  'SOLUI_OTHER8_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > PayWise', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isPayWise).toBeInstanceOf(Function);
    expect(processPayWise).toBeInstanceOf(Function);
  });
  test('should identify campaign as PayWise', () => {
    expect(isPayWise({ campaign_id: 'WXA99' })).toEqual(true);
    expect(isPayWise({ campaign_id: 'XLX99' })).toEqual(true);
    expect(isPayWise({ campaign_id: 'XLY99' })).toEqual(true);
    expect(isPayWise({ campaign_id: 'XLZ99' })).toEqual(true);
    expect(isPayWise({ campaign_id: 'XMA99' })).toEqual(true);
    expect(isPayWise({ campaign_id: 'XMB99' })).toEqual(true);
    expect(isPayWise({ campaign_id: 'XMC99' })).toEqual(true);
    expect(isPayWise({ campaign_id: 'XPK12' })).toEqual(true);
    expect(isPayWise({ campaign_id: 'XPL12' })).toEqual(true);
    expect(isPayWise({ campaign_id: 'XPM12' })).toEqual(true);
    expect(isPayWise({ campaign_id: 'XPN12' })).toEqual(true);
    expect(isPayWise({ campaign_id: 'XPO12' })).toEqual(true);
    expect(isPayWise({ campaign_id: 'XPP12' })).toEqual(true);
    expect(isPayWise({ campaign_id: 'XTD42' })).toEqual(true);
    expect(isPayWise({ campaign_id: 'XTE42' })).toEqual(true);
    expect(isPayWise({ campaign_id: 'XTF42' })).toEqual(true);
    expect(isPayWise({ campaign_id: 'XTG42' })).toEqual(true);
    expect(isPayWise({ campaign_id: 'XTH42' })).toEqual(true);
    expect(isPayWise({ campaign_id: 'XTI42' })).toEqual(true);
  });
  test('should identify campaign as not PayWise', () => {
    expect(isPayWise({ campaign_id: 'ABC99' })).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { transformed, dataContext } = await processPayWise({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('paywise');
    expect(transformed[0]).toEqual('3');
    expect(transformed[1]).toEqual('1.1%');
    expect(transformed[2]).toEqual('6');
    expect(transformed[3]).toEqual('1.2%');
    expect(transformed[4]).toEqual('12');
    expect(transformed[5]).toEqual('1.3%');
    expect(transformed[6]).toEqual('January 1, 2020');
    expect(transformed[7]).toEqual('December 31, 2020');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processPayWise({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER9_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER9_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
