const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const {
  processAsUnknown,
  processAsDate,
  processAsIs,
} = require('../../../../processors');

const dataContext = 'paywise';

// is_recommended = true
const validCampaigns = [
  // UAT
  'WXA',
  // PROD
  'XLX', 'XLY', 'XLZ', 'XMA', 'XMB', 'XMC',
  'XPK', // Transactors- paywise Cards
  'XPL', // Transactors - Mixed Cards
  'XPM', // Occasional - Paywise Cards
  'XPN', // Occasional - Mixed Cards
  'XPO', // Revolvers - Paywise Cards
  'XPP', // Revolvers - Paywise Cards
  'XTD', 'XTE', 'XTF', 'XTG', 'XTH', 'XTI',
];

/**
 * Returns a function that checks if the campaign is a PayWise campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isPayWise = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  // term 3 months
  [ R.equals('SOLUI_OTHER1_END'), (x) => processAsIs(deps, campaign, x, 'OTHER1') ],
  // fee % for 3 months
  [ R.equals('SOLUI_OTHER2_END'), (x) => processAsIs(deps, campaign, x, 'OTHER2') ],
  // term 6 months
  [ R.equals('SOLUI_OTHER3_END'), (x) => processAsIs(deps, campaign, x, 'OTHER3') ],
  // fee % for 6 months term
  [ R.equals('SOLUI_OTHER4_END'), (x) => processAsIs(deps, campaign, x, 'OTHER4') ],
  // term 12 months
  [ R.equals('SOLUI_OTHER5_END'), (x) => processAsIs(deps, campaign, x, 'OTHER5') ],
  // fee % for 12 months term
  [ R.equals('SOLUI_OTHER6_END'), (x) => processAsIs(deps, campaign, x, 'OTHER6') ],
  // pricing expiry date
  [ R.equals('SOLUI_OTHER7_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER7') ],
  // campaign expiry date
  [ R.equals('SOLUI_OTHER8_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER8') ],
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for Pre-approved CC campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processPayWise = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isPayWise,
  processPayWise,
};
