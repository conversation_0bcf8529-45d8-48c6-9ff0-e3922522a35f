const { isStepAcquisition, processStepAcquisition } = require('./index');

const mockProduct1 = {
  productId: '580040',
  productTypes: [
    {
      productDomain: 'default',
      productCode: 'AFB',
      subProductCode: 'B1',
    },
    {
      productDomain: 'KT',
      productCode: 'VAX',
      subProductCode: 'B1',
    },
  ],
  productSystem: 'VAX',
  currency: 'CAD',
  ownership: 'B',
  properties: [
    { type: 'CATEGORY', value: 'CREDITCARDS' },
    { type: 'DEFAULT_CLASSIFICATION', value: 'Amex' },
    { type: 'DETAIL_CLASSIFICATION', value: 'Amex' },
    { type: 'REL_ACTIVITY_CLASSIFICATION', value: 'BusinessAmex' },
    { type: 'CORNERSTONE_ID', value: 'Borrowing' },
    { type: 'ASSET_LIABILITY', value: 'L' },
  ],
  sortWeight: '4000',
  accountNumberLength: 15,
  accountNumberMask: '####*****######',
  descriptions: [
    { locale: 'en_CA', value: 'Future Amex SB B1' },
    { locale: 'fr_CA', value: 'F-Future Amex SB B1' },
  ],
};

const mockProduct2 = {
  productId: '580040',
  productTypes: [
    {
      productDomain: 'default',
      productCode: 'AFB',
      subProductCode: 'B2',
    },
    {
      productDomain: 'KT',
      productCode: 'VAX',
      subProductCode: 'B1',
    },
  ],
  productSystem: 'VAX',
  currency: 'CAD',
  ownership: 'B',
  properties: [
    { type: 'CATEGORY', value: 'CREDITCARDS' },
    { type: 'DEFAULT_CLASSIFICATION', value: 'Amex' },
    { type: 'DETAIL_CLASSIFICATION', value: 'Amex' },
    { type: 'REL_ACTIVITY_CLASSIFICATION', value: 'BusinessAmex' },
    { type: 'CORNERSTONE_ID', value: 'Borrowing' },
    { type: 'ASSET_LIABILITY', value: 'L' },
  ],
  sortWeight: '4000',
  accountNumberLength: 15,
  accountNumberMask: '####*****######',
  descriptions: [
    { locale: 'en_CA', value: 'Future Amex SB B2' },
    { locale: 'fr_CA', value: 'F-Future Amex SB B2' },
  ],
};

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'XEY99',
  additional_data: [
    { name: 'OTHER1', value: 'Step ACQ' },
    { name: 'OTHER2', value: '********' },
    { name: 'OTHER3', value: 'AFBB2' },
    { name: 'OTHER4', value: 'X4' },
    { name: 'OTHER5', value: 'X5' },
    { name: 'OTHER6', value: 'X6' },
    { name: 'OTHER7', value: 'X7' },
    { name: 'OTHER8', value: 'X8' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_DATE2_END',
  'SOLUI_OTHER3_END',
  'SOLUI_OTHER4_END',
  'SOLUI_OTHER5_END',
  'SOLUI_OTHER6_END',
  'SOLUI_OTHER7_END',
  'SOLUI_OTHER8_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

const productBookService = {
  getProduct: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > Step Acquisition', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
    productBookService.getProduct.mockClear();
  });
  test('should be functions', () => {
    expect(isStepAcquisition).toBeInstanceOf(Function);
    expect(processStepAcquisition).toBeInstanceOf(Function);
  });
  test('should identify campaign as Step Acquisition', () => {
    const res = isStepAcquisition(mockCampaign);
    expect(res).toEqual(true);
  });
  test('should identify campaign as not Step Acquisition', () => {
    const res = isStepAcquisition({ ...mockCampaign, campaign_id: 'ABC99' });
    expect(res).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    productBookService.getProduct
      .mockResolvedValueOnce(mockProduct1)
      .mockResolvedValueOnce(mockProduct2);
    const { transformed, dataContext } = await processStepAcquisition({ logger, productBookService }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('stepacq');
    expect(transformed[0]).toEqual('Step ACQ');
    expect(transformed[1]).toEqual('January 1, 2019');
    expect(transformed[2]).toEqual('January 1, 2019');
    expect(transformed[3]).toEqual('');
    expect(transformed[4]).toEqual('X4');
    expect(transformed[5]).toEqual('');
    expect(transformed[6]).toEqual('');
    expect(transformed[7]).toEqual('');
    expect(transformed[8]).toEqual('');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processStepAcquisition({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER9_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER9_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
