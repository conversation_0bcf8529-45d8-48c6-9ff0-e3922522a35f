const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const {
  processAsIs,
  processAsUnknown,
  processAsDate,
  processAsHidden,
} = require('../../../../processors');

const dataContext = 'stepacq';

const validCampaigns = [
  // Step Acquisition
  'XEY',
  // Scotia Home Test
  'JCA', 'SOA',
];

/**
 * Returns a function that checks if the campaign is a Step Acquisition campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isStepAcquisition = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  // Product code
  [ R.equals('SOLUI_OTHER1_END'), (x) => processAsIs(deps, campaign, x, 'OTHER1') ],
  // Offer expiration date
  [ R.equals('SOLUI_DATE2_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER2') ],
  [ R.equals('SOLUI_OTHER2_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER2') ],
  // Sub-product code
  // // [ R.equals('SOLUI_OTHER3_END'), (x) => processAsProduct(deps, campaign, language, x, 'OTHER3') ],
  [ R.equals('SOLUI_OTHER3_END'), processAsHidden ],
  // STEP number
  [ R.equals('SOLUI_OTHER4_END'), (x) => processAsIs(deps, campaign, x, 'OTHER4') ],
  // Co-borrower Name
  // // [ R.equals('SOLUI_OTHER5_END'), (x) => processAsIs(deps, campaign, x, 'OTHER5') ],
  [ R.equals('SOLUI_OTHER5_END'), processAsHidden ],
  // Co-borrower CID # DO NOT EVER RETURN
  [ R.equals('SOLUI_OTHER6_END'), processAsHidden ],
  // Bill Code
  // // [ R.equals('SOLUI_OTHER7_END'), (x) => processAsIs(deps, campaign, x, 'OTHER7') ],
  [ R.equals('SOLUI_OTHER7_END'), processAsHidden ],
  // Rate
  // // [ R.equals('SOLUI_OTHER8_END'), (x) => processAsIs(deps, campaign, x, 'OTHER8') ],
  [ R.equals('SOLUI_OTHER8_END'), processAsHidden ],
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for Step Acquisition campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processStepAcquisition = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isStepAcquisition,
  processStepAcquisition,
};
