const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const {
  processAsIs,
  processAsUnknown,
  processAsDate,
} = require('../../../../processors');

const dataContext = 'paloc';

const validCampaigns = [
  // Scotia Line of Credit PA
  'XAB', 'XAE', 'XDQ', 'XDR',
  'XAS', 'XAT',
];

/**
 * Returns a function that checks if the campaign is a Pre-approved LoC campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isPreapprovedLOC = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  // Target Module
  [ R.equals('SOLUI_OTHER1_END'), (x) => processAsIs(deps, campaign, x, 'OTHER1') ],
  // +Z.ZZ (PRIME + Z.ZZ)
  [ R.equals('SOLUI_OTHER2_END'), (x) => processAsIs(deps, campaign, x, 'OTHER2') ],
  // Offer expiration date
  [ R.equals('SOLUI_DATE3_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER3') ],
  [ R.equals('SOLUI_OTHER3_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER3') ],
  // Purchase date ???
  [ R.equals('SOLUI_DATE4_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER4') ],
  [ R.equals('SOLUI_OTHER4_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER4') ],
  // Source code
  [ R.equals('SOLUI_OTHER5_END'), (x) => processAsIs(deps, campaign, x, 'OTHER5') ],
  // Total rate ???
  [ R.equals('SOLUI_OTHER6_END'), (x) => processAsIs(deps, campaign, x, 'OTHER6') ],
  // ???
  [ R.equals('SOLUI_OTHER7_END'), (x) => processAsIs(deps, campaign, x, 'OTHER7') ],
  // Legal rate date
  [ R.equals('SOLUI_DATE8_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER8') ],
  [ R.equals('SOLUI_OTHER8_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER8') ],
  // Mail id ???
  [ R.equals('SOLUI_OTHER9_END'), (x) => processAsIs(deps, campaign, x, 'OTHER9') ],
  // Intro rate ???
  [ R.equals('SOLUI_OTHER10_END'), (x) => processAsIs(deps, campaign, x, 'OTHER10') ],
  // Accept date
  [ R.equals('SOLUI_DATE11_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER11') ],
  [ R.equals('SOLUI_OTHER11_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER11') ],
  // Bonus points
  [ R.equals('SOLUI_OTHER12_END'), (x) => processAsIs(deps, campaign, x, 'OTHER12') ],
  // Offer months
  [ R.equals('SOLUI_OTHER13_END'), (x) => processAsIs(deps, campaign, x, 'OTHER13') ],
  // Masster key
  [ R.equals('SOLUI_OTHER14_END'), (x) => processAsIs(deps, campaign, x, 'OTHER14') ],
  // PO rate billcode ???
  [ R.equals('SOLUI_OTHER15_END'), (x) => processAsIs(deps, campaign, x, 'OTHER15') ],
  // PO rate expiry ???
  [ R.equals('SOLUI_OTHER16_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER16') ],
  [ R.equals('SOLUI_DATE16_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER16') ],
  // PO rate key
  [ R.equals('SOLUI_OTHER17_END'), (x) => processAsIs(deps, campaign, x, 'OTHER17') ],
  // PO rate desc
  [ R.equals('SOLUI_OTHER18_END'), (x) => processAsIs(deps, campaign, x, 'OTHER18') ],
  // bill code
  [ R.equals('SOLUI_OTHER19_END'), (x) => processAsIs(deps, campaign, x, 'OTHER19') ],
  // PO intro desc
  [ R.equals('SOLUI_OTHER20_END'), (x) => processAsIs(deps, campaign, x, 'OTHER20') ],
  // PO intro term
  [ R.equals('SOLUI_OTHER21_END'), (x) => processAsIs(deps, campaign, x, 'OTHER21') ],
  // PO intro bill code
  [ R.equals('SOLUI_OTHER22_END'), (x) => processAsIs(deps, campaign, x, 'OTHER22') ],
  // Net interest rate
  [ R.equals('SOLUI_OTHER23_END'), (x) => processAsIs(deps, campaign, x, 'OTHER23') ],
  // LOC int 1+yr for 10k
  [ R.equals('SOLUI_OTHER24_END'), (x) => processAsIs(deps, campaign, x, 'OTHER24') ],
  // Savings
  [ R.equals('SOLUI_OTHER25_END'), (x) => processAsIs(deps, campaign, x, 'OTHER25') ],
  // Prime Rate
  [ R.equals('SOLUI_OTHER26_END'), (x) => processAsIs(deps, campaign, x, 'OTHER26') ],
  // As of Date
  [ R.equals('SOLUI_OTHER27_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER27') ],
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for Pre-approved LoC campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processPreapprovedLOC = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isPreapprovedLOC,
  processPreapprovedLOC,
};
