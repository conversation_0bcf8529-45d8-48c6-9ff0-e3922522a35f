const { isPreapprovedLOC, processPreapprovedLOC } = require('./index');

const mockProduct1 = {
  productId: '580040',
  productTypes: [
    {
      productDomain: 'default',
      productCode: 'AFB',
      subProductCode: 'B1',
    },
    {
      productDomain: 'KT',
      productCode: 'VAX',
      subProductCode: 'B1',
    },
  ],
  productSystem: 'VAX',
  currency: 'CAD',
  ownership: 'B',
  properties: [
    { type: 'CATEGORY', value: 'CREDITCARDS' },
    { type: 'DEFAULT_CLASSIFICATION', value: 'Amex' },
    { type: 'DETAIL_CLASSIFICATION', value: 'Amex' },
    { type: 'REL_ACTIVITY_CLASSIFICATION', value: 'BusinessAmex' },
    { type: 'CORNERSTONE_ID', value: 'Borrowing' },
    { type: 'ASSET_LIABILITY', value: 'L' },
  ],
  sortWeight: '4000',
  accountNumberLength: 15,
  accountNumberMask: '####*****######',
  descriptions: [
    { locale: 'en_CA', value: 'Future Amex SB B1' },
    { locale: 'fr_CA', value: 'F-Future Amex SB B1' },
  ],
};

const mockProduct2 = {
  productId: '580040',
  productTypes: [
    {
      productDomain: 'default',
      productCode: 'AFB',
      subProductCode: 'B2',
    },
    {
      productDomain: 'KT',
      productCode: 'VAX',
      subProductCode: 'B1',
    },
  ],
  productSystem: 'VAX',
  currency: 'CAD',
  ownership: 'B',
  properties: [
    { type: 'CATEGORY', value: 'CREDITCARDS' },
    { type: 'DEFAULT_CLASSIFICATION', value: 'Amex' },
    { type: 'DETAIL_CLASSIFICATION', value: 'Amex' },
    { type: 'REL_ACTIVITY_CLASSIFICATION', value: 'BusinessAmex' },
    { type: 'CORNERSTONE_ID', value: 'Borrowing' },
    { type: 'ASSET_LIABILITY', value: 'L' },
  ],
  sortWeight: '4000',
  accountNumberLength: 15,
  accountNumberMask: '####*****######',
  descriptions: [
    { locale: 'en_CA', value: 'Future Amex SB B2' },
    { locale: 'fr_CA', value: 'F-Future Amex SB B2' },
  ],
};

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'XAB99',
  additional_data: [
    { name: 'OTHER1', value: 'X1' },
    { name: 'OTHER2', value: 'X2' },
    { name: 'OTHER3', value: '********' },
    { name: 'OTHER4', value: '********' },
    { name: 'OTHER5', value: 'X5' },
    { name: 'OTHER6', value: 'X6' },
    { name: 'OTHER7', value: 'X7' },
    { name: 'OTHER8', value: '********' },
    { name: 'OTHER9', value: 'X9' },
    { name: 'OTHER10', value: 'X10' },
    { name: 'OTHER11', value: '********' },
    { name: 'OTHER12', value: 'X12' },
    { name: 'OTHER13', value: 'X13' },
    { name: 'OTHER14', value: 'X14' },
    { name: 'OTHER15', value: 'X15' },
    { name: 'OTHER16', value: '********' },
    { name: 'OTHER17', value: 'X17' },
    { name: 'OTHER18', value: 'X18' },
    { name: 'OTHER19', value: 'X19' },
    { name: 'OTHER20', value: 'X20' },
    { name: 'OTHER21', value: 'X21' },
    { name: 'OTHER22', value: 'X22' },
    { name: 'OTHER23', value: 'X23' },
    { name: 'OTHER24', value: 'X24' },
    { name: 'OTHER25', value: 'X25' },
    { name: 'OTHER26', value: 'X26' },
    { name: 'OTHER27', value: '20190506' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_OTHER3_END',
  'SOLUI_DATE3_END',
  'SOLUI_OTHER4_END',
  'SOLUI_DATE4_END',
  'SOLUI_OTHER5_END',
  'SOLUI_OTHER6_END',
  'SOLUI_OTHER7_END',
  'SOLUI_OTHER8_END',
  'SOLUI_DATE8_END',
  'SOLUI_OTHER9_END',
  'SOLUI_OTHER10_END',
  'SOLUI_OTHER11_END',
  'SOLUI_DATE11_END',
  'SOLUI_OTHER12_END',
  'SOLUI_OTHER13_END',
  'SOLUI_OTHER14_END',
  'SOLUI_OTHER15_END',
  'SOLUI_OTHER16_END',
  'SOLUI_DATE16_END',
  'SOLUI_OTHER17_END',
  'SOLUI_OTHER18_END',
  'SOLUI_OTHER19_END',
  'SOLUI_OTHER20_END',
  'SOLUI_OTHER21_END',
  'SOLUI_OTHER22_END',
  'SOLUI_OTHER23_END',
  'SOLUI_OTHER24_END',
  'SOLUI_OTHER25_END',
  'SOLUI_OTHER26_END',
  'SOLUI_OTHER27_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

const productBookService = {
  getProduct: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > Pre-approved Line of Credit', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isPreapprovedLOC).toBeInstanceOf(Function);
    expect(processPreapprovedLOC).toBeInstanceOf(Function);
  });
  test('should identify campaign as Pre-approved LOC', () => {
    expect(isPreapprovedLOC({ campaign_id: 'XAB99' })).toEqual(true);
    expect(isPreapprovedLOC({ campaign_id: 'XAE99' })).toEqual(true);
    expect(isPreapprovedLOC({ campaign_id: 'XDQ99' })).toEqual(true);
    expect(isPreapprovedLOC({ campaign_id: 'XDR99' })).toEqual(true);
    expect(isPreapprovedLOC({ campaign_id: 'XAS99' })).toEqual(true);
    expect(isPreapprovedLOC({ campaign_id: 'XAT99' })).toEqual(true);
  });
  test('should identify campaign as not Pre-approved LOC', () => {
    expect(isPreapprovedLOC({ campaign_id: 'ABC99' })).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    productBookService.getProduct
      .mockResolvedValueOnce(mockProduct1)
      .mockResolvedValueOnce(mockProduct2);
    const { transformed, dataContext } = await processPreapprovedLOC({ logger, productBookService }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('paloc');
    expect(transformed[0]).toEqual('X1');
    expect(transformed[1]).toEqual('X2');
    expect(transformed[2]).toEqual('January 1, 2019');
    expect(transformed[3]).toEqual('January 1, 2019');
    expect(transformed[4]).toEqual('December 31, 2019');
    expect(transformed[5]).toEqual('December 31, 2019');
    expect(transformed[6]).toEqual('X5');
    expect(transformed[7]).toEqual('X6');
    expect(transformed[8]).toEqual('X7');
    expect(transformed[9]).toEqual('February 2, 2019');
    expect(transformed[10]).toEqual('February 2, 2019');
    expect(transformed[11]).toEqual('X9');
    expect(transformed[12]).toEqual('X10');
    expect(transformed[13]).toEqual('March 3, 2019');
    expect(transformed[14]).toEqual('March 3, 2019');
    expect(transformed[15]).toEqual('X12');
    expect(transformed[16]).toEqual('X13');
    expect(transformed[17]).toEqual('X14');
    expect(transformed[18]).toEqual('X15');
    expect(transformed[19]).toEqual('May 5, 2019');
    expect(transformed[20]).toEqual('May 5, 2019');
    expect(transformed[21]).toEqual('X17');
    expect(transformed[22]).toEqual('X18');
    expect(transformed[23]).toEqual('X19');
    expect(transformed[24]).toEqual('X20');
    expect(transformed[25]).toEqual('X21');
    expect(transformed[26]).toEqual('X22');
    expect(transformed[27]).toEqual('X23');
    expect(transformed[28]).toEqual('X24');
    expect(transformed[29]).toEqual('X25');
    expect(transformed[30]).toEqual('X26');
    expect(transformed[31]).toEqual('May 6, 2019');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processPreapprovedLOC({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER28_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER28_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
