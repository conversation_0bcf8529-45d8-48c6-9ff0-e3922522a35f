const { isValueVisaSwitch, processValueVisaSwitch } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'XLH99',
  additional_data: [
    { name: 'OTHER1', value: '12345' },
    { name: 'OTHER2', value: '1.5%' },
    { name: 'OTHER3', value: '10' },
    { name: 'OTHER4', value: '1000' },
    { name: 'OTHER5', value: '20230720' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_OTHER3_END',
  'SOLUI_OTHER4_END',
  'SOLUI_OTHER5_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > Value Visa Switch', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isValueVisaSwitch).toBeInstanceOf(Function);
    expect(processValueVisaSwitch).toBeInstanceOf(Function);
  });
  test('should identify campaign as Value Visa Switch', () => {
    expect(isValueVisaSwitch({ campaign_id: 'XLH99' })).toEqual(true);
  });
  test('should identify campaign as not Value Visa Switch', () => {
    expect(isValueVisaSwitch({ campaign_id: 'ABC99' })).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { transformed, dataContext } = await processValueVisaSwitch({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('vvswitch');
    expect(transformed[0]).toEqual('');
    expect(transformed[1]).toEqual('');
    expect(transformed[2]).toEqual('');
    expect(transformed[3]).toEqual('');
    expect(transformed[4]).toEqual('July 20, 2023');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processValueVisaSwitch({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER6_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER6_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
