const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const {
  processAsUnknown,
  processAsHidden,
  processAsDate,
} = require('../../../../processors');

const dataContext = 'vvswitch';

// is_recommended = true
const validCampaigns = [
  'XLH',
];

/**
 * Returns a function that checks if the campaign is a PayWise campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isValueVisaSwitch = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  // CID field
  [ R.equals('SOLUI_OTHER1_END'), processAsHidden ],
  // Current interest rate
  [ R.equals('SOLUI_OTHER2_END'), processAsHidden ],
  // Last annual fee paid
  [ R.equals('SOLUI_OTHER3_END'), processAsHidden ],
  // Balance
  [ R.equals('SOLUI_OTHER4_END'), processAsHidden ],
  // Expiry date
  [ R.equals('SOLUI_OTHER5_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER5') ],
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for Pre-approved CC campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processValueVisaSwitch = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isValueVisaSwitch,
  processValueVisaSwitch,
};
