const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const {
  processAsIs,
  processAsUnknown,
} = require('../../../../processors');

const dataContext = 'physoffer';

const validCampaigns = [
  // Physicians Bank Offer - XGT
  'XGT',
];

/**
 * Returns a function that checks if the campaign is a Physicians Bank Offer
 * @returns {(Campaign) => bool} - Result functions
 */
const isPhysicianOffer = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  // Title of doctor (Mr/Mrs/Dr)
  [ R.equals('SOLUI_OTHER1_END'), (x) => processAsIs(deps, campaign, x, 'OTHER1') ],
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for Multiple Leads campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processPhysicianOffer = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isPhysicianOffer,
  processPhysicianOffer,
};
