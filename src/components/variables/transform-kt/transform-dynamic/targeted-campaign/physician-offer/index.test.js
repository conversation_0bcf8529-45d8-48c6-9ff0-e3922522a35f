const { isPhysicianOffer, processPhysicianOffer } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'XGT99',
  additional_data: [
    { name: 'OTHER1', value: 'Mrs.' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > Physician Offer', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isPhysicianOffer).toBeInstanceOf(Function);
    expect(processPhysicianOffer).toBeInstanceOf(Function);
  });
  test('should identify campaign as Physician Offer', () => {
    expect(isPhysicianOffer({ campaign_id: 'XGT99' })).toEqual(true);
  });
  test('should identify campaign as not Physician Offer', () => {
    expect(isPhysicianOffer({ campaign_id: 'ABC99' })).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { transformed, dataContext } = await processPhysicianOffer({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('physoffer');
    expect(transformed[0]).toEqual('Mrs.');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processPhysicianOffer({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER2_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER2_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
