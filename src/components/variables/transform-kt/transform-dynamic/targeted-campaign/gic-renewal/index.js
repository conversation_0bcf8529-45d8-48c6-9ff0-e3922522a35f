const R = require('ramda');
const { getAdditionalDataValue } = require('../../../../helpers');
const {
  processAsUnknown,
  processAsHidden,
  processAsNumber,
  processAsDate,
  processAsGicSpecialRate,
  processAsGicSpecialTerm,
} = require('../../../../processors');

const dataContext = 'gicrenew';

/**
 * Checks if the campaign is a GIC Renewal campaign
 * @param {Campaign} campaign - Campaign
 * @returns {bool} - Result
 */
const isGicRenewal = R.pipe(
  R.flip(getAdditionalDataValue)('OTHER10'),
  R.equals('GIC'),
);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {string} env - `Preferred-Environment` header value
 * @param {string} reqHeaders - Request Headers
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, env, reqHeaders, campaign, language) => R.cond([
  // GIC special rate
  [ R.equals('SOLUI_SPECIAL_RATE_END'), (x) => processAsGicSpecialRate(deps, env, reqHeaders, campaign, x, 'OTHER3', 'OTHER2') ],
  // GIC special term
  [ R.equals('SOLUI_SPECIAL_TERM_END'), (x) => processAsGicSpecialTerm(deps, env, reqHeaders, campaign, language, x, 'OTHER3', 'OTHER2') ],
  // GIC special product
  [ R.equals('SOLUI_SPECIAL_PRODUCT_END'), processAsHidden ],
  // GIC IP Product Code
  [ R.equals('SOLUI_OTHER1_END'), processAsHidden ],
  // Certificate Number
  [ R.equals('SOLUI_OTHER2_END'), processAsHidden ],
  // Plan Number
  [ R.equals('SOLUI_OTHER3_END'), processAsHidden ],
  // Term (in months)
  [ R.equals('SOLUI_OTHER4_END'), (x) => processAsNumber(deps, campaign, x, 'OTHER4') ],
  // Maturity Date
  [ R.equals('SOLUI_DATE5_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER5') ],
  [ R.equals('SOLUI_OTHER5_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER5') ],
  // Balance
  [ R.equals('SOLUI_OTHER6_END'), processAsHidden ],
  // GIC Ownership
  [ R.equals('SOLUI_OTHER7_END'), processAsHidden ],
  // GIC Attrition Level
  [ R.equals('SOLUI_OTHER8_END'), processAsHidden ],
  // GIC Product Name
  [ R.equals('SOLUI_OTHER9_END'), processAsHidden ],
  // GIC Tag
  [ R.equals('SOLUI_OTHER10_END'), processAsHidden ],
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for GIC Renewal campaign
 * @param {Object} deps - Dependencies
 * @param {string} env - `Preferred-Environment` header value
 * @param {string} reqHeaders - Request Headers
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processGicRenewal = async (deps, env, reqHeaders, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, env, reqHeaders, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isGicRenewal,
  processGicRenewal,
};
