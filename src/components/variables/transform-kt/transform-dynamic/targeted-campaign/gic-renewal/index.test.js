const { isGic<PERSON>enewal, processGicRenewal } = require('./index');

const mockInvestmentResponse = {
  data: {
    request_id: 16267,
    gic_rates: [
      {
        code: 'MCCLTNRC',
        type: 'LTNR',
        expiry_date: '2019-04-06',
        term_rates: [
          {
            term: { length: 1, unit: 'Year', details: { from: 365, to: 545, unit: 'Day' } },
            is_recommended: true,
            step_rates: [
              {
                step_number: 0,
                frequency_rates: [
                  { interest_payment_frequency: 'Annually', is_compound: true, offer_rate: 2.55, target_rate: 2.55, branch_discretion_limit: 2.55 },
                  { interest_payment_frequency: 'Semi-Annually', is_compound: true, offer_rate: 2.425, target_rate: 2.425, branch_discretion_limit: 2.425 },
                ],
              },
            ],
          },
          {
            term: { length: 2, unit: 'Year', details: { from: 730, to: 911, unit: 'Day' } },
            is_recommended: false,
            step_rates: [
              {
                step_number: 0,
                frequency_rates: [
                  { interest_payment_frequency: 'Annually', is_compound: true, offer_rate: 2.71, target_rate: 2.71, branch_discretion_limit: 2.71 },
                  { interest_payment_frequency: 'Semi-Annually', is_compound: true, offer_rate: 2.585, target_rate: 2.585, branch_discretion_limit: 2.585 },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
};

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'OTHER1', value: 'X1' },
    { name: 'OTHER2', value: 'certificate#' },
    { name: 'OTHER3', value: 'plan#' },
    { name: 'OTHER4', value: '18' },
    { name: 'OTHER5', value: '20191231' },
    { name: 'OTHER6', value: '31337.42' },
    { name: 'OTHER7', value: 'X7' },
    { name: 'OTHER8', value: 'X8' },
    { name: 'OTHER9', value: 'X9' },
    { name: 'OTHER10', value: 'GIC' },
  ],
};

const mockVariables = [
  'SOLUI_SPECIAL_RATE_END',
  'SOLUI_SPECIAL_TERM_END',
  'SOLUI_SPECIAL_PRODUCT_END',
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_OTHER3_END',
  'SOLUI_OTHER4_END',
  'SOLUI_OTHER5_END',
  'SOLUI_DATE5_END',
  'SOLUI_OTHER6_END',
  'SOLUI_OTHER7_END',
  'SOLUI_OTHER8_END',
  'SOLUI_OTHER9_END',
  'SOLUI_OTHER10_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

const investmentService = {
  getGicRates: jest.fn().mockResolvedValue(mockInvestmentResponse),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > GIC Renewal', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
    investmentService.getGicRates.mockClear();
  });
  test('should be functions', () => {
    expect(isGicRenewal).toBeInstanceOf(Function);
    expect(processGicRenewal).toBeInstanceOf(Function);
  });
  test('should identify campaign as GIC Renewal', () => {
    const res = isGicRenewal(mockCampaign);
    expect(res).toEqual(true);
  });
  test('should identify campaign as not GIC Renewal', () => {
    const res = isGicRenewal({ additional_data: [ { name: 'OTHER10', value: 'ZZZ' } ] });
    expect(res).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { transformed, dataContext } = await processGicRenewal({ logger, investmentService }, 'env', 'token', mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('gicrenew');
    expect(transformed[0]).toEqual('2.55%');
    expect(transformed[1]).toEqual('1 year');
    expect(transformed[2]).toEqual('');
    expect(transformed[3]).toEqual('');
    expect(transformed[4]).toEqual('');
    expect(transformed[5]).toEqual('');
    expect(transformed[6]).toEqual('18');
    expect(transformed[7]).toEqual('December 31, 2019');
    expect(transformed[8]).toEqual('December 31, 2019');
    expect(transformed[9]).toEqual('');
    expect(transformed[10]).toEqual('');
    expect(transformed[11]).toEqual('');
    expect(transformed[12]).toEqual('');
    expect(transformed[13]).toEqual('');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processGicRenewal({ logger, investmentService }, 'env', 'token', mockCampaign, 'en', [ 'SOLUI_OTHER11_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER11_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
