const { isOverdraftProtection, processOverdraftProtection } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ODP99',
  additional_data: [
    { name: 'OTHER1', value: '20190101' },
    { name: 'OTHER2', value: 'ODP' },
    { name: 'OTHER3', value: '18' },
    { name: 'OTHER4', value: '7.62%' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_DATE1_END',
  'SOLUI_OTHER2_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > Overdraft Protection', () => {
  beforeEach(() => {
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isOverdraftProtection).toBeInstanceOf(Function);
    expect(processOverdraftProtection).toBeInstanceOf(Function);
  });
  test('should identify campaign as Overdraft Protection', () => {
    expect(isOverdraftProtection(mockCampaign)).toEqual(true);
  });
  test('should identify campaign as not Overdraft Protection', () => {
    expect(isOverdraftProtection({ additional_data: { name: 'OTHER2', value: 'ABC' } })).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { transformed, dataContext } = await processOverdraftProtection({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('paodp');
    expect(transformed[0]).toEqual('January 1, 2019');
    expect(transformed[1]).toEqual('January 1, 2019');
    expect(transformed[2]).toEqual('');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processOverdraftProtection({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER3_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER3_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
