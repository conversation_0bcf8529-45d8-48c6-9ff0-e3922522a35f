const { isAutopay, processAutopay } = require('./index');

const mockCampaign = {
  message_id: 'D12345678',
  campaign_id: 'XVX99',
  additional_data: [
    { name: 'PROD', value: 'ABCDE' },
    { name: 'OTHER1', value: 'abc' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > Autopay', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isAutopay).toBeInstanceOf(Function);
    expect(processAutopay).toBeInstanceOf(Function);
  });
  test('should identify XVX campaign as Autopay', () => {
    expect(isAutopay({ campaign_id: 'XVX99' })).toEqual(true);
  });
  test('should identify XVY campaign as Autopay', () => {
    expect(isAutopay({ campaign_id: 'XVY99' })).toEqual(true);
  });
  test('should identify campaign as not Autopay', () => {
    expect(isAutopay({ campaign_id: 'ABC99' })).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { dataContext, transformed } = await processAutopay({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('autopay');
    expect(transformed).toEqual([ 'ABC' ]);
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processAutopay({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER2_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER2_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
