const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const {
  processAsIs,
  // processAsAccountKey,
  processAsUnknown,
} = require('../../../../processors');

const dataContext = 'autopay';

const validCampaigns = [
  'XVX',
  'XVY',
];

/**
 * Returns a function that checks if the campaign is a Autopay campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isAutopay = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  // product code retrieved from 'OTHER1' in insights payload for CTA - only for autopay since it's different
  [ R.equals('SOLUI_OTHER1_END'), async (x) => R.toUpper(await processAsIs(deps, campaign, x, 'OTHER1')).slice(0, 3) ],
  // Account Key - commented out for the timebeing until scope is clarified
  // [ R.equals('SOLUI_ACCOUNT_KEY_END'), (x) => processAsAccountKey(deps, campaign, language, x, 'ACCT', 'OTHER1') ],
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for Autopay campaigns
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processAutopay = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isAutopay,
  processAutopay,
};
