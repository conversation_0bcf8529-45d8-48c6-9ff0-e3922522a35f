const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const {
  processAsIs,
  processAsUnknown,
  processAsDate,
  processAsHidden,
} = require('../../../../processors');

const dataContext = 'paccsb';

// is_recommended = true
const validCampaigns = [
  'CA0', 'CAA', 'CAB', 'CB0', 'CC0', 'CCD', 'CD0', 'CVA', 'CVB', 'CVD', 'CVC', 'SBV',
  // New Acquisition
  'CZA', // ScotiaLine Visa for business - non MD
  'CZB', // Scotia Momentum Visa for business - non MD
  'CZC', // Scotia Passport Visa for business - non MD
  'CZD', // ScotiaLine Visa for business - MD
  'CZE', // Scotia Momentum Visa for business - MD
  'CZF', // Scotia Passport Visa for business - MD

  // X-sell
  'CZG', // ScotiaLine Visa for business - non MD
  'CZH', // Scotia Momentum Visa for business - non MD
  'CZI', // Scotia Passport Visa for business - non MD
  'CZJ', // ScotiaLine Visa for business - MD
  'CZK', // Scotia Momentum Visa for business - MD
  'CZL', // Scotia Passport Visa for business - MD
];

/**
 * Returns a function that checks if the campaign is a Pre-approved CC campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isPreapprovedCCSB = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  // Target Module
  [ R.equals('SOLUI_OTHER1_END'), processAsHidden ],
  // +Z.ZZ (PRIME + Z.ZZ)
  [ R.equals('SOLUI_OTHER2_END'), processAsHidden ],
  // Offer expiration date
  [ R.equals('SOLUI_DATE3_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER3') ],
  [ R.equals('SOLUI_OTHER3_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER3') ],
  // Purchase date ???
  [ R.equals('SOLUI_DATE4_END'), processAsHidden ],
  [ R.equals('SOLUI_OTHER4_END'), processAsHidden ],
  // Source code
  [ R.equals('SOLUI_OTHER5_END'), processAsHidden ],
  // Total rate ???
  [ R.equals('SOLUI_OTHER6_END'), (x) => processAsIs(deps, campaign, x, 'OTHER6') ],
  // ???
  [ R.equals('SOLUI_OTHER7_END'), processAsHidden ],
  // Legal rate date
  [ R.equals('SOLUI_DATE8_END'), processAsHidden ],
  [ R.equals('SOLUI_OTHER8_END'), processAsHidden ],
  // Mail id ???
  [ R.equals('SOLUI_OTHER9_END'), processAsHidden ],
  // Intro rate ???
  [ R.equals('SOLUI_OTHER10_END'), processAsHidden ],
  // Accept date
  [ R.equals('SOLUI_DATE11_END'), processAsHidden ],
  [ R.equals('SOLUI_OTHER11_END'), processAsHidden ],
  // Bonus points
  [ R.equals('SOLUI_OTHER12_END'), processAsHidden ],
  // Rate date
  [ R.equals('SOLUI_DATE13_END'), processAsHidden ],
  [ R.equals('SOLUI_OTHER13_END'), processAsHidden ],
  // Credit expiry date display
  [ R.equals('SOLUI_DATE14_END'), processAsHidden ],
  [ R.equals('SOLUI_OTHER14_END'), processAsHidden ],
  // ???
  [ R.equals('SOLUI_OTHER15_END'), processAsHidden ],
  [ R.equals('SOLUI_EXPDATE15_END'), processAsHidden ],
  // Small business name
  [ R.equals('SOLUI_OTHER16_END'), processAsHidden ],
  // Business id
  [ R.equals('SOLUI_OTHER17_END'), processAsHidden ],
  // Embossed name (personal)
  [ R.equals('SOLUI_OTHER18_END'), processAsHidden ],
  // Embossed name (business)
  [ R.equals('SOLUI_OTHER19_END'), processAsHidden ],
  // BLV type
  [ R.equals('SOLUI_OTHER20_END'), processAsHidden ],
  // Ownership
  [ R.equals('SOLUI_OTHER21_END'), processAsHidden ],
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for Pre-approved CC campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processPreapprovedCCSB = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isPreapprovedCCSB,
  processPreapprovedCCSB,
};
