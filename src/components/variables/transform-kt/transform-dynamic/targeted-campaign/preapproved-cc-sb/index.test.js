const { isPreapprovedCCSB, processPreapprovedCCSB } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'CC099',
  additional_data: [
    { name: 'OTHER1', value: 'X1' },
    { name: 'OTHER2', value: 'X2' },
    { name: 'OTHER3', value: '20190101' },
    { name: 'OTHER4', value: 'X4' },
    { name: 'OTHER5', value: 'X5' },
    { name: 'OTHER6', value: '2.49' },
    { name: 'OTHER7', value: 'X7' },
    { name: 'OTHER8', value: 'X8' },
    { name: 'OTHER9', value: 'X9' },
    { name: 'OTHER10', value: 'X10' },
    { name: 'OTHER11', value: 'X11' },
    { name: 'OTHER12', value: 'X12' },
    { name: 'OTHER13', value: 'X13' },
    { name: 'OTHER14', value: 'X14' },
    { name: '<PERSON>THER15', value: 'X15' },
    { name: '<PERSON>THER16', value: 'X16' },
    { name: '<PERSON>THER17', value: 'X17' },
    { name: '<PERSON>THER18', value: 'X18' },
    { name: 'OTHER19', value: 'X19' },
    { name: 'OTHER20', value: 'X20' },
    { name: 'OTHER21', value: 'X21' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_OTHER3_END',
  'SOLUI_DATE3_END',
  'SOLUI_OTHER4_END',
  'SOLUI_DATE4_END',
  'SOLUI_OTHER5_END',
  'SOLUI_OTHER6_END',
  'SOLUI_OTHER7_END',
  'SOLUI_OTHER8_END',
  'SOLUI_DATE8_END',
  'SOLUI_OTHER9_END',
  'SOLUI_OTHER10_END',
  'SOLUI_OTHER11_END',
  'SOLUI_DATE11_END',
  'SOLUI_OTHER12_END',
  'SOLUI_OTHER13_END',
  'SOLUI_DATE13_END',
  'SOLUI_OTHER14_END',
  'SOLUI_OTHER15_END',
  'SOLUI_EXPDATE15_END',
  'SOLUI_OTHER16_END',
  'SOLUI_OTHER17_END',
  'SOLUI_OTHER18_END',
  'SOLUI_OTHER19_END',
  'SOLUI_OTHER20_END',
  'SOLUI_OTHER21_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > Pre-approved Credit Card Small Business', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isPreapprovedCCSB).toBeInstanceOf(Function);
    expect(processPreapprovedCCSB).toBeInstanceOf(Function);
  });
  test('should identify campaign as Pre-approved CC Small Business', () => {
    expect(isPreapprovedCCSB({ campaign_id: 'CA099' })).toEqual(true);
    expect(isPreapprovedCCSB({ campaign_id: 'CAA99' })).toEqual(true);
    expect(isPreapprovedCCSB({ campaign_id: 'CAB99' })).toEqual(true);
    expect(isPreapprovedCCSB({ campaign_id: 'CB099' })).toEqual(true);
    expect(isPreapprovedCCSB({ campaign_id: 'CC099' })).toEqual(true);
    expect(isPreapprovedCCSB({ campaign_id: 'CCD99' })).toEqual(true);
    expect(isPreapprovedCCSB({ campaign_id: 'CD099' })).toEqual(true);
    expect(isPreapprovedCCSB({ campaign_id: 'CVA99' })).toEqual(true);
    expect(isPreapprovedCCSB({ campaign_id: 'CVB99' })).toEqual(true);
    expect(isPreapprovedCCSB({ campaign_id: 'SBV' })).toEqual(true);
    expect(isPreapprovedCCSB({ campaign_id: 'CZA11' })).toEqual(true);
    expect(isPreapprovedCCSB({ campaign_id: 'CZB12' })).toEqual(true);
    expect(isPreapprovedCCSB({ campaign_id: 'CZJ13' })).toEqual(true);
    expect(isPreapprovedCCSB({ campaign_id: 'CZL15' })).toEqual(true);
  });
  test('should identify campaign as not Pre-approved CC Small Business', () => {
    expect(isPreapprovedCCSB({ campaign_id: 'ABC99' })).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { transformed, dataContext } = await processPreapprovedCCSB({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('paccsb');
    expect(transformed[0]).toEqual('');
    expect(transformed[1]).toEqual('');
    expect(transformed[2]).toEqual('January 1, 2019');
    expect(transformed[3]).toEqual('January 1, 2019');
    expect(transformed[4]).toEqual('');
    expect(transformed[5]).toEqual('');
    expect(transformed[6]).toEqual('');
    expect(transformed[7]).toEqual('2.49');
    expect(transformed[8]).toEqual('');
    expect(transformed[9]).toEqual('');
    expect(transformed[10]).toEqual('');
    expect(transformed[11]).toEqual('');
    expect(transformed[12]).toEqual('');
    expect(transformed[13]).toEqual('');
    expect(transformed[14]).toEqual('');
    expect(transformed[15]).toEqual('');
    expect(transformed[16]).toEqual('');
    expect(transformed[17]).toEqual('');
    expect(transformed[18]).toEqual('');
    expect(transformed[19]).toEqual('');
    expect(transformed[20]).toEqual('');
    expect(transformed[21]).toEqual('');
    expect(transformed[22]).toEqual('');
    expect(transformed[23]).toEqual('');
    expect(transformed[24]).toEqual('');
    expect(transformed[25]).toEqual('');
    expect(transformed[26]).toEqual('');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processPreapprovedCCSB({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER22_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER22_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
