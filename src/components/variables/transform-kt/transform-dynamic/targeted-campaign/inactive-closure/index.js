const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const { processAsIs, processAsUnknown } = require('../../../../processors');

const dataContext = 'inactiveuloc';
const validCampaigns = [
  'XNE',
  'XNF',
];

/**
 * Returns a function that checks if the campaign is an inactive closure campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isUloc = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to tranform a single SOLUI variable
 * @param {Object} deps - Depedencies
 * @param {Campaign} campaign - Campaign
 * @returns {(string) => Promise<string>} - Function that transofmrs a signle SOLUI variable
 */
const processVariable = (deps, campaign) => R.cond([
  [ R.equals('SOLUI_OTHER2_END'), (x) => processAsIs(deps, campaign, x, 'OTHER2') ],
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for Inactive Closures (ULOC) campaign
 * @param {Object} deps
 * @param {Campaign} campaign
 * @param {string} language
 * @param {string[]} variables - SOLUI variables to transform
 * @param {Promise<string[]>} variables - Transformed variables
 */
const processUloc = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};
module.exports = {
  isUloc,
  processUloc,
};
