const { isUloc, processUloc } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'CTN99',
  additional_data: [
    { name: 'NAME', value: '<PERSON>' },
    { name: 'OTHER2', value: 'ABC123' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER2_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > Inactive Accounts - ULOC', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isUloc).toBeInstanceOf(Function);
    expect(processUloc).toBeInstanceOf(Function);
  });
  test('should identify XNE campaign as ULOC', () => {
    expect(isUloc({ campaign_id: 'XNE02' })).toEqual(true);
  });
  test('should identify XNF campaign as ULOC', () => {
    expect(isUloc({ campaign_id: 'XNF12' })).toEqual(true);
  });
  test('should identify campaign as not ULOC', () => {
    expect(isUloc({ campaign_id: 'ABC99' })).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { dataContext, transformed } = await processUloc({ logger }, mockCampaign, 'en', mockVariables);
    expect(transformed.length).toEqual(1);
    const transformedValue = mockCampaign.additional_data.find((x) => x.name === 'OTHER2');
    expect(transformed[0]).toEqual(transformedValue.value);
    expect(dataContext).toEqual('inactiveuloc');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processUloc({ logger }, mockCampaign, 'fr', [ 'SOLUI_OTHER1_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER1_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
