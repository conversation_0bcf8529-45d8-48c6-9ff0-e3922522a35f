const { isMpsaRetention, processMpsaRetention } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'WRA01',
  additional_data: [
    { name: 'OTHER1', value: '20190101' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_DATE1_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > MPSA Retention', () => {
  beforeEach(() => {
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isMpsaRetention).toBeInstanceOf(Function);
    expect(processMpsaRetention).toBeInstanceOf(Function);
  });

  test('should identify campaign as EMOB D2D', () => {
    expect(isMpsaRetention(mockCampaign)).toEqual(true);
    expect(isMpsaRetention({ campaign_id: 'WRB01' })).toEqual(true);
    expect(isMpsaRetention({ campaign_id: 'WRC01' })).toEqual(true);
    expect(isMpsaRetention({ campaign_id: 'WRD01' })).toEqual(true);
  });

  test('should identify campaign as not MPSA Retention', () => {
    expect(isMpsaRetention({ campaign_id: 'XXX01' })).toEqual(false);
    expect(isMpsaRetention({ campaign_id: 'ABC99' })).toEqual(false);
  });

  test('should successfully process a campaign', async () => {
    const { transformed, dataContext } = await processMpsaRetention({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('mpsareten');
    expect(transformed[0]).toEqual('January 1, 2019');
    expect(transformed[1]).toEqual('January 1, 2019');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });

  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processMpsaRetention({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER5_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER5_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
