const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const {
  processAsDate,
  processAsUnknown,
} = require('../../../../processors');

const dataContext = 'mpsareten';

const validCampaigns = [
  'WRA', 'WRB', 'WRC', 'WRD',
];

/**
 * Returns a function that checks if the campaign is a MPSA Retention Campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isMpsaRetention = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  // Start date
  [ R.equals('SOLUI_DATE1_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER1') ],
  [ R.equals('SOLUI_OTHER1_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER1') ],
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for MPSA Retention campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processMpsaRetention = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isMpsaRetention,
  processMpsaRetention,
};
