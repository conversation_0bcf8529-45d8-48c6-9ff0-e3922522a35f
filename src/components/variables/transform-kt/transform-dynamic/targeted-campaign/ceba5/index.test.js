const { isCeba5, processCeba5 } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'CTN99',
  additional_data: [
    { name: 'NAME', value: '<PERSON>' },
  ],
};

const mockVariables = [
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > CEBA 5.0', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isCeba5).toBeInstanceOf(Function);
    expect(processCeba5).toBeInstanceOf(Function);
  });
  test('should identify CTN campaign as CEBA 5.0', () => {
    expect(isCeba5({ campaign_id: 'CTN99' })).toEqual(true);
  });
  test('should identify CSO campaign as CEBA 5.0', () => {
    expect(isCeba5({ campaign_id: 'CSO199' })).toEqual(true);
  });
  test('should identify campaign as not CEBA 5.0', () => {
    expect(isCeba5({ campaign_id: 'ABC99' })).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { dataContext } = await processCeba5({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('ceba5');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processCeba5({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER1_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
