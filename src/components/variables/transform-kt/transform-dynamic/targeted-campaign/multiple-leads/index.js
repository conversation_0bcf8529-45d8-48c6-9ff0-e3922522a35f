const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const {
  processAsIs,
  processAsUnknown,
  processAsDate,
} = require('../../../../processors');

const dataContext = 'multileads';

const validCampaigns = [
  // Passport VISA Infinite PA - ML ACQ
  'MLC',
  // Value VISA - ML ACQ
  'MLB',
];

/**
 * Returns a function that checks if the campaign is a Multiple Lead campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isMultipleLeads = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  // Target Module
  [ R.equals('SOLUI_OTHER1_END'), (x) => processAsIs(deps, campaign, x, 'OTHER1') ],
  // +Z.ZZ (PRIME + Z.ZZ)
  [ R.equals('SOLUI_OTHER2_END'), (x) => processAsIs(deps, campaign, x, 'OTHER2') ],
  // Offer expiration date
  [ R.equals('SOLUI_DATE3_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER3') ],
  [ R.equals('SOLUI_OTHER3_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER3') ],
  // Purchase date ???
  [ R.equals('SOLUI_DATE4_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER4') ],
  [ R.equals('SOLUI_OTHER4_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER4') ],
  // Source code
  [ R.equals('SOLUI_OTHER5_END'), (x) => processAsIs(deps, campaign, x, 'OTHER5') ],
  // Total rate ???
  [ R.equals('SOLUI_OTHER6_END'), (x) => processAsIs(deps, campaign, x, 'OTHER6') ],
  // ???
  [ R.equals('SOLUI_OTHER7_END'), (x) => processAsIs(deps, campaign, x, 'OTHER7') ],
  // Legal rate date
  [ R.equals('SOLUI_DATE8_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER8') ],
  [ R.equals('SOLUI_OTHER8_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER8') ],
  // Mail id ???
  [ R.equals('SOLUI_OTHER9_END'), (x) => processAsIs(deps, campaign, x, 'OTHER9') ],
  // Intro rate ???
  [ R.equals('SOLUI_OTHER10_END'), (x) => processAsIs(deps, campaign, x, 'OTHER10') ],
  // Accept date
  [ R.equals('SOLUI_DATE11_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER11') ],
  [ R.equals('SOLUI_OTHER11_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER11') ],
  // Bonus points
  [ R.equals('SOLUI_OTHER12_END'), (x) => processAsIs(deps, campaign, x, 'OTHER12') ],
  // Offer months
  [ R.equals('SOLUI_OTHER13_END'), (x) => processAsIs(deps, campaign, x, 'OTHER13') ],
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for Multiple Leads campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processMultipleLeads = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isMultipleLeads,
  processMultipleLeads,
};
