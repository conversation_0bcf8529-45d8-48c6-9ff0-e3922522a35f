const { isMultipleLeads, processMultipleLeads } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'MLC99',
  additional_data: [
    { name: 'OTHER1', value: 'X1' },
    { name: 'OTHER2', value: 'X2' },
    { name: 'OTHER3', value: '20190101' },
    { name: 'OTHER4', value: '20190202' },
    { name: 'OTHER5', value: 'X5' },
    { name: 'OTHER6', value: 'X6' },
    { name: 'OTHER7', value: 'X7' },
    { name: 'OTHER8', value: '20190303' },
    { name: 'OTHER9', value: 'X9' },
    { name: 'OTHER10', value: 'X10' },
    { name: 'OTHER11', value: '20190404' },
    { name: 'OTHER12', value: 'X12' },
    { name: 'OTHER13', value: 'X13' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_OTHER3_END',
  'SOLUI_DATE3_END',
  'SOLUI_OTHER4_END',
  'SOLUI_DATE4_END',
  'SOLUI_OTHER5_END',
  'SOLUI_OTHER6_END',
  'SOLUI_OTHER7_END',
  'SOLUI_OTHER8_END',
  'SOLUI_DATE8_END',
  'SOLUI_OTHER9_END',
  'SOLUI_OTHER10_END',
  'SOLUI_OTHER11_END',
  'SOLUI_DATE11_END',
  'SOLUI_OTHER12_END',
  'SOLUI_OTHER13_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > Multiple Leads', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isMultipleLeads).toBeInstanceOf(Function);
    expect(processMultipleLeads).toBeInstanceOf(Function);
  });
  test('should identify campaign as Multiple Leads', () => {
    expect(isMultipleLeads({ campaign_id: 'MLC99' })).toEqual(true);
    expect(isMultipleLeads({ campaign_id: 'MLB99' })).toEqual(true);
  });
  test('should identify campaign as not Multiple Leads', () => {
    const res = isMultipleLeads({ campaign_id: 'ABC99' });
    expect(res).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { transformed, dataContext } = await processMultipleLeads({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('multileads');
    expect(transformed[0]).toEqual('X1');
    expect(transformed[1]).toEqual('X2');
    expect(transformed[2]).toEqual('January 1, 2019');
    expect(transformed[3]).toEqual('January 1, 2019');
    expect(transformed[4]).toEqual('February 2, 2019');
    expect(transformed[5]).toEqual('February 2, 2019');
    expect(transformed[6]).toEqual('X5');
    expect(transformed[7]).toEqual('X6');
    expect(transformed[8]).toEqual('X7');
    expect(transformed[9]).toEqual('March 3, 2019');
    expect(transformed[10]).toEqual('March 3, 2019');
    expect(transformed[11]).toEqual('X9');
    expect(transformed[12]).toEqual('X10');
    expect(transformed[13]).toEqual('April 4, 2019');
    expect(transformed[14]).toEqual('April 4, 2019');
    expect(transformed[15]).toEqual('X12');
    expect(transformed[16]).toEqual('X13');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processMultipleLeads({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER14_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER14_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
