const { isLmt, processLmt } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'LQA',
  additional_data: [
    { name: 'OTHER1', value: 'C123456' },
    { name: 'OTHER2', value: '<EMAIL>' },
    { name: 'OTHER3', value: '2000' },
    { name: 'OTHER4', value: '1000' },
    { name: 'OTHER5', value: '4.62%' },
    { name: 'OTHER6', value: '4%' },
    { name: 'OTHER7', value: '2' },
    { name: 'OTHER8', value: 'RC' },
    { name: 'OTHER9', value: 'PI' },
    { name: 'OTHER10', value: '5.62%' },
    { name: 'OTHER11', value: '2' },
    { name: 'OTHER12', value: 'BC' },
    { name: 'OTHER13', value: '12' },
    { name: 'OTHER14', value: '20240630' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_OTHER3_END',
  'SOLUI_OTHER4_END',
  'SOLUI_OTHER5_END',
  'SOLUI_OTHER6_END',
  'SOLUI_OTHER7_END',
  'SOLUI_OTHER8_END',
  'SOLUI_OTHER9_END',
  'SOLUI_OTHER10_END',
  'SOLUI_OTHER11_END',
  'SOLUI_OTHER12_END',
  'SOLUI_OTHER13_END',
  'SOLUI_OTHER14_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > LMT', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isLmt).toBeInstanceOf(Function);
    expect(processLmt).toBeInstanceOf(Function);
  });
  test('should identify campaign as LMT', () => {
    expect(isLmt(mockCampaign)).toEqual(true);
  });
  test('should identify campaign as not LMT', () => {
    const res = isLmt({ campaign_id: 'ABC99', additional_data: [ { name: 'OTHER1', value: 'C123456' } ] });
    expect(res).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { transformed, dataContext } = await processLmt({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('lmt');
    expect(transformed[0]).toEqual('C123456');
    expect(transformed[1]).toEqual('<EMAIL>');
    expect(transformed[2]).toEqual('2000');
    expect(transformed[3]).toEqual('1000');
    expect(transformed[4]).toEqual('4.62%');
    expect(transformed[5]).toEqual('4%');
    expect(transformed[6]).toEqual('2');
    expect(transformed[7]).toEqual('RC');
    expect(transformed[8]).toEqual('PI');
    expect(transformed[9]).toEqual('5.62%');
    expect(transformed[10]).toEqual('2');
    expect(transformed[11]).toEqual('BC');
    expect(transformed[12]).toEqual('12');
    expect(transformed[13]).toEqual('June 30, 2024');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processLmt({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER16_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER16_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should successfully process a campaign- fr', async () => {
    const { transformed, dataContext } = await processLmt({ logger }, mockCampaign, 'fr', mockVariables);
    expect(dataContext).toEqual('lmt');
    expect(transformed[0]).toEqual('C123456');
    expect(transformed[2]).toEqual('2000');
    expect(transformed[3]).toEqual('1000');
    expect(transformed[4]).toEqual('4.62%');
    expect(transformed[5]).toEqual('4%');
    expect(transformed[6]).toEqual('2');
    expect(transformed[7]).toEqual('RC');
    expect(transformed[8]).toEqual('PI');
    expect(transformed[9]).toEqual('5.62%');
    expect(transformed[10]).toEqual('2');
    expect(transformed[11]).toEqual('BC');
    expect(transformed[12]).toEqual('12');
    expect(transformed[13]).toEqual('30 juin 2024');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
});
