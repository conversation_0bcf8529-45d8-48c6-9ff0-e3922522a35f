const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const {
  processAsUnknown,
  processAsIs,
  processAsDate,
} = require('../../../../processors');

const dataContext = 'lmt';

// is_recommended = true
const validCampaigns = [
  'LQA',
];

/**
 * Returns a function that checks if the campaign is a LMT campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isLmt = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  // Customer ID
  [ R.equals('SOLUI_OTHER1_END'), (x) => processAsIs(deps, campaign, x, 'OTHER1') ],
  // Stored Email
  [ R.equals('SOLUI_OTHER2_END'), (x) => processAsIs(deps, campaign, x, 'OTHER2') ],
  // Current Credit Limit
  [ R.equals('SOLUI_OTHER3_END'), (x) => processAsIs(deps, campaign, x, 'OTHER3') ],
  // Current Acc Balance
  [ R.equals('SOLUI_OTHER4_END'), (x) => processAsIs(deps, campaign, x, 'OTHER4') ],
  // Current Interest Rate
  [ R.equals('SOLUI_OTHER5_END'), (x) => processAsIs(deps, campaign, x, 'OTHER5') ],
  // Current Prime Rate
  [ R.equals('SOLUI_OTHER6_END'), (x) => processAsIs(deps, campaign, x, 'OTHER6') ],
  // Current Adj Factor
  [ R.equals('SOLUI_OTHER7_END'), (x) => processAsIs(deps, campaign, x, 'OTHER7') ],
  // Current Repay Code
  [ R.equals('SOLUI_OTHER8_END'), (x) => processAsIs(deps, campaign, x, 'OTHER8') ],
  // Progra Indicator
  [ R.equals('SOLUI_OTHER9_END'), (x) => processAsIs(deps, campaign, x, 'OTHER9') ],
  // New Interest Rate
  [ R.equals('SOLUI_OTHER10_END'), (x) => processAsIs(deps, campaign, x, 'OTHER10') ],
  // New Adj Factor
  [ R.equals('SOLUI_OTHER11_END'), (x) => processAsIs(deps, campaign, x, 'OTHER11') ],
  // New Bill Code
  [ R.equals('SOLUI_OTHER12_END'), (x) => processAsIs(deps, campaign, x, 'OTHER12') ],
  // Relief Period
  [ R.equals('SOLUI_OTHER13_END'), (x) => processAsIs(deps, campaign, x, 'OTHER13') ],
  // Expiry Date
  [ R.equals('SOLUI_OTHER14_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER14') ],
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for LMT campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processLmt = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isLmt,
  processLmt,
};
