const { isCCBalanceTransfer, processCCBalanceTransfer } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  // Any random campaign ID in the RM0-9 series
  campaign_id: `RM${Math.floor(Math.random() * 10)}`,
  additional_data: [
    { name: 'OTHER1', value: '<PERSON>' },
    { name: 'OTHER2', value: '5.62%' },
    { name: 'OTHER3', value: '5.62%' },
    { name: 'OTHER4', value: '200' },
    { name: 'OTHER5', value: '200' },
    { name: 'OTHER6', value: '200' },
    { name: 'OTHER7', value: '200' },
    { name: 'OTHER8', value: '200' },
    { name: 'OTHER9', value: '200' },
    { name: 'OTHER10', value: '20190101' },
    { name: 'OTHER11', value: '20190101' },
    { name: '<PERSON><PERSON>ER<PERSON>', value: '20190101' },
    { name: '<PERSON><PERSON>ER<PERSON>', value: '1.23' },
    { name: 'OTHER14', value: '1.23' },
    { name: 'OTHER15', value: '200' },
    { name: '<PERSON>THER16', value: '200' },
    { name: 'OTHER17', value: '5.62%' },
    { name: 'OTHER18', value: '200' },
    { name: 'OTHER19', value: '200' },
    { name: 'OTHER20', value: '5.62%' },
    { name: 'OTHER21', value: '4567' },
    { name: 'OTHER22', value: 'hello' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_OTHER3_END',
  'SOLUI_OTHER4_END',
  'SOLUI_OTHER5_END',
  'SOLUI_OTHER6_END',
  'SOLUI_OTHER7_END',
  'SOLUI_OTHER8_END',
  'SOLUI_OTHER9_END',
  'SOLUI_OTHER10_END',
  'SOLUI_DATE10_END',
  'SOLUI_OTHER11_END',
  'SOLUI_DATE11_END',
  'SOLUI_OTHER12_END',
  'SOLUI_DATE12_END',
  'SOLUI_OTHER13_END',
  'SOLUI_OTHER14_END',
  'SOLUI_OTHER15_END',
  'SOLUI_OTHER16_END',
  'SOLUI_OTHER17_END',
  'SOLUI_OTHER18_END',
  'SOLUI_OTHER19_END',
  'SOLUI_OTHER20_END',
  'SOLUI_OTHER21_END',
  'SOLUI_OTHER22_END',
  'SOLUI_BTFEETEXT_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > RM7', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isCCBalanceTransfer).toBeInstanceOf(Function);
    expect(processCCBalanceTransfer).toBeInstanceOf(Function);
  });
  test('should identify and match any valid campaign id in the series RM0-9', () => {
    expect(isCCBalanceTransfer({ campaign_id: `RM${Math.floor(Math.random() * 10)}` })).toEqual(true);
  });
  test('should NOT identify and match invalid campaign id RMT', () => {
    expect(isCCBalanceTransfer({ campaign_id: 'RMT' })).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { dataContext, transformed } = await processCCBalanceTransfer({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('ccbl');
    const testDate = 'January 1, 2019';
    expect(transformed[0]).toEqual('John Doe');
    expect(transformed[1]).toEqual('5.62%');
    expect(transformed[2]).toEqual('5.62%');
    expect(transformed[3]).toEqual('200');
    expect(transformed[4]).toEqual('200');
    expect(transformed[5]).toEqual('200');
    expect(transformed[6]).toEqual('200');
    expect(transformed[7]).toEqual('200');
    expect(transformed[8]).toEqual('200');
    expect(transformed[9]).toEqual(testDate);
    expect(transformed[10]).toEqual(testDate);
    expect(transformed[11]).toEqual(testDate);
    expect(transformed[12]).toEqual(testDate);
    expect(transformed[13]).toEqual(testDate);
    expect(transformed[14]).toEqual(testDate);
    expect(transformed[15]).toEqual('1.23');
    expect(transformed[16]).toEqual('1.23');
    expect(transformed[17]).toEqual('200');
    expect(transformed[18]).toEqual('200');
    expect(transformed[19]).toEqual('5.62%');
    expect(transformed[20]).toEqual('200');
    expect(transformed[21]).toEqual('200');
    expect(transformed[22]).toEqual('5.62%');
    expect(transformed[23]).toEqual('4567');
    expect(transformed[24]).toEqual('hello');
    expect(transformed[25]).toEqual('hello');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processCCBalanceTransfer({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER0_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER0_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
