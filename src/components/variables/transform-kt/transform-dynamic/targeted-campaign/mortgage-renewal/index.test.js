const { isMortgageRenewal, processMortgageRenewal } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'MRV99',
  additional_data: [
    { name: 'OTHER1', value: '<PERSON>' },
    { name: 'OTHER2', value: '20190101' },
    { name: 'OTHER3', value: '18' },
    { name: 'OTHER4', value: '2.13%' },
    { name: 'OTHER5', value: '5' },
    { name: 'OTHER6', value: '2.63%' },
    { name: 'OTHER7', value: '2' },
    { name: 'OTHER8', value: '2.42%' },
    { name: 'OTHER9', value: '20190520' },
    { name: 'OTHER10', value: 'X10' },
    { name: 'OTHER11', value: 'X11' },
    { name: 'OTHER12', value: '4000111122223333' },
    { name: 'OTHER13', value: '20190622' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_DATE2_END',
  'SOLUI_OTHER3_END',
  'SOLUI_OTHER4_END',
  'SOLUI_OTHER5_END',
  'SOLUI_OTHER6_END',
  'SOLUI_OTHER7_END',
  'SOLUI_OTHER8_END',
  'SOLUI_OTHER9_END',
  'SOLUI_DATE9_END',
  'SOLUI_OTHER10_END',
  'SOLUI_OTHER11_END',
  'SOLUI_OTHER12_END',
  'SOLUI_OTHER13_END',
  'SOLUI_DATE13_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > Mortgage Renewal', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isMortgageRenewal).toBeInstanceOf(Function);
    expect(processMortgageRenewal).toBeInstanceOf(Function);
  });
  test('should identify campaign as Mortgage Last Payment', () => {
    expect(isMortgageRenewal({ campaign_id: 'MRV99' })).toEqual(true);
    expect(isMortgageRenewal({ campaign_id: 'MTB99' })).toEqual(true);
    expect(isMortgageRenewal({ campaign_id: 'MRC99' })).toEqual(true);
  });
  test('should identify campaign as not Mortgage Last Payment', () => {
    expect(isMortgageRenewal({ campaign_id: 'ABC99' })).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { transformed, dataContext } = await processMortgageRenewal({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('mrgrenew');
    expect(transformed[0]).toEqual('John Doe');
    expect(transformed[1]).toEqual('January 1, 2019');
    expect(transformed[2]).toEqual('January 1, 2019');
    expect(transformed[3]).toEqual('18');
    expect(transformed[4]).toEqual('2.13%');
    expect(transformed[5]).toEqual('');
    expect(transformed[6]).toEqual('');
    expect(transformed[7]).toEqual('');
    expect(transformed[8]).toEqual('');
    expect(transformed[9]).toEqual('May 20, 2019');
    expect(transformed[10]).toEqual('May 20, 2019');
    expect(transformed[11]).toEqual('');
    expect(transformed[12]).toEqual('');
    expect(transformed[13]).toEqual('');
    expect(transformed[14]).toEqual('June 22, 2019');
    expect(transformed[15]).toEqual('June 22, 2019');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processMortgageRenewal({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER14_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER14_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
