const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const {
  processAsIs,
  processAsUnknown,
  processAsNumber,
  processAsDate,
  processAsHidden,
} = require('../../../../processors');

const dataContext = 'mrgrenew';

const validCampaigns = [
  // Mortgage Renewal (Fixed vs Fixed)
  'MRV',
  // Mortgage Renewal T3 (Fixed vs Fixed)
  'MTB', 'MRC',
  // Mortgage Renew Now T6
  'XGC', 'XOO',
  // Mortgage Renew Now T5
  'XGD',
  // Mortgage Renew Not T4
  'XGE',
  // Mortgage Renew Not T3
  'XGF',
  // Scotia Home Test
  'WFA', 'WFB', 'WFC', 'WFD', 'MOB',
  // Mortgage Renew T1
  'XGI',
  // Mortgage Renew T2
  'XGV',
];

/**
 * Returns a function that checks if the campaign is a Mortgage Renewal campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isMortgageRenewal = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  // Co-borrower name
  [ R.equals('SOLUI_OTHER1_END'), (x) => processAsIs(deps, campaign, x, 'OTHER1') ],
  // Maturity date
  [ R.equals('SOLUI_DATE2_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER2') ],
  [ R.equals('SOLUI_OTHER2_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER2') ],
  // Term (in months)
  [ R.equals('SOLUI_OTHER3_END'), (x) => processAsNumber(deps, campaign, x, 'OTHER3') ],
  // Current interest rate (%)
  [ R.equals('SOLUI_OTHER4_END'), (x) => processAsIs(deps, campaign, x, 'OTHER4') ],
  // ??? pick second character
  // // [ R.equals('SOLUI_OTHER5_END'), (x) => processAsSubstring(deps, campaign, x, 'OTHER5', 1, 2) ],
  // // [ R.equals('SOLUI_OTHER5_END'), (x) => processAsNumber(deps, campaign, x, 'OTHER5') ],
  [ R.equals('SOLUI_OTHER5_END'), processAsHidden ],
  // ??? pick last 5 characters
  // // [ R.equals('SOLUI_OTHER6_END'), (x) => processAsSubstring(deps, campaign, x, 'OTHER6', -5) ],
  // // [ R.equals('SOLUI_OTHER6_END'), (x) => processAsIs(deps, campaign, x, 'OTHER6') ],
  [ R.equals('SOLUI_OTHER6_END'), processAsHidden ],
  // ??? pick second character
  // // [ R.equals('SOLUI_OTHER7_END'), (x) => processAsSubstring(deps, campaign, x, 'OTHER7', 1, 2) ],
  // // [ R.equals('SOLUI_OTHER7_END'), (x) => processAsNumber(deps, campaign, x, 'OTHER7') ],
  [ R.equals('SOLUI_OTHER7_END'), processAsHidden ],
  // ??? pick last 5 characters
  // // [ R.equals('SOLUI_OTHER8_END'), (x) => processAsSubstring(deps, campaign, x, 'OTHER8', -5) ],
  // // [ R.equals('SOLUI_OTHER8_END'), (x) => processAsIs(deps, campaign, x, 'OTHER8') ],
  [ R.equals('SOLUI_OTHER8_END'), processAsHidden ],
  // Offer expiration date
  [ R.equals('SOLUI_DATE9_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER9') ],
  [ R.equals('SOLUI_OTHER9_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER9') ],
  // ???
  // // [ R.equals('SOLUI_OTHER10_END'), (x) => processAsIs(deps, campaign, x, 'OTHER10') ],
  [ R.equals('SOLUI_OTHER10_END'), processAsHidden ],
  // ???
  // // [ R.equals('SOLUI_OTHER11_END'), (x) => processAsIs(deps, campaign, x, 'OTHER11') ],
  [ R.equals('SOLUI_OTHER11_END'), processAsHidden ],
  // Co-borrower CID# DO NOT EVER RETURN
  [ R.equals('SOLUI_OTHER12_END'), processAsHidden ],
  // Campaign start date
  [ R.equals('SOLUI_DATE13_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER13') ],
  [ R.equals('SOLUI_OTHER13_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER13') ],
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for Mortgage Renewal campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processMortgageRenewal = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isMortgageRenewal,
  processMortgageRenewal,
};
