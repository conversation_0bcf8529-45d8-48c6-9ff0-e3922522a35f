const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const { processAsIs, processAsUnknown, processAsNumber, processAsAccount } = require('../../../../processors');

const dataContext = 'client-compliance-response-service';

const validCampaigns = [ 'ECM' ];

/**
 * Returns a function that checks if the campaign is a ECM campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isEcm = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) =>
  R.cond([
    // Complaint Date
    [ R.equals('SOLUI_OTHER1_END'), (x) => processAsIs(deps, campaign, x, 'OTHER1') ],
    // Case Number
    [ R.equals('SOLUI_OTHER2_END'), x => processAsNumber(deps, campaign, x, 'OTHER2') ],
    // CurrentDate
    [ R.equals('SOLUI_OTHER3_END'), x => processAsIs(deps, campaign, x, 'OTHER3') ],
    // Category
    [ R.equals('SOLUI_OTHER4_END'), x => processAsIs(deps, campaign, x, 'OTHER4') ],
    // Issue
    [ R.equals('SOLUI_OTHER5_END'), x => processAsIs(deps, campaign, x, 'OTHER5') ],
    // CustomerProduct
    [ R.equals('SOLUI_OTHER6_END'), x => processAsIs(deps, campaign, x, 'OTHER6') ],
    // AccountNumber
    [ R.equals('SOLUI_OTHER7_END'), x => processAsAccount(deps, campaign, x, 'OTHER7') ],
    // Unknown
    [ R.T, x => processAsUnknown(deps, campaign, x) ],
  ]);

/**
 * Transforms variables for ECM campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processEcm = async (deps, campaign, language, variables) => {
  const result = await Promise.all(
    variables.map(variable =>
      processVariable(deps, campaign, language)(variable),
    ),
  );
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isEcm,
  processEcm,
};
