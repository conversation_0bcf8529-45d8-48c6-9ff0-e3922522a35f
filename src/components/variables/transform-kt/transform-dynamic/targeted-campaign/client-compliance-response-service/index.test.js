const { isEcm, processEcm } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ECM01',
  additional_data: [
    { name: 'OTHER1', value: '15/04/2025' },
    { name: '<PERSON>THER2', value: '123456789' },
    { name: 'OTHER3', value: '15/04/2025' },
    { name: 'OTHER4', value: 'Category' },
    { name: 'OTHER5', value: 'Issue' },
    { name: 'OTHER6', value: 'Product' },
    { name: 'OTHER7', value: '352236235232' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_OTHER3_END',
  'SOLUI_OTHER4_END',
  'SOLUI_OTHER5_END',
  'SOLUI_OTHER6_END',
  'SOLUI_OTHER7_END',
];

const logger = {
  warn: jest.fn(x => console.log(x)),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > ECM Campaigns', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });

  test('should be functions', () => {
    expect(isEcm).toBeInstanceOf(Function);
    expect(processEcm).toBeInstanceOf(Function);
  });

  test('should identify ECM campaign correctly', () => {
    expect(isEcm({ campaign_id: 'ECM01' })).toEqual(true);
    expect(isEcm({ campaign_id: 'ABC99' })).toEqual(false);
  });

  test('should successfully process a campaign', async () => {
    const { dataContext, transformed } = await processEcm(
      { logger },
      mockCampaign,
      'en',
      mockVariables,
    );
    expect(dataContext).toEqual('client-compliance-response-service');
    expect(transformed).toHaveLength(7);
    expect(transformed[0]).toEqual('15\\/04\\/2025');
    expect(transformed[1]).toEqual('123456789');
    expect(transformed[2]).toEqual('15\\/04\\/2025');
    expect(transformed[3]).toEqual('Category');
    expect(transformed[4]).toEqual('Issue');
    expect(transformed[5]).toEqual('Product');
    expect(transformed[6]).toEqual('\\*\\*\\*\\*\\*\\*\\*\\*5232');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });

  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processEcm({ logger }, mockCampaign, 'en', [
      'SOLUI_OTHER0_END',
    ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER0_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
