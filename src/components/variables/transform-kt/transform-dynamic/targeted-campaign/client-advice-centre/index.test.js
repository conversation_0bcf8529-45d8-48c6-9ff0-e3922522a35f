const { isFca, processFca } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'FCA99',
  additional_data: [
    { name: 'OTHER1', value: '<PERSON>' },
    { name: 'OTHER2', value: '123 Fake Street' },
    { name: 'OTHER3', value: 'Toronto' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_OTHER3_END',
];

const logger = {
  warn: jest.fn(x => console.log(x)),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > FCA Campaigns', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });

  test('should be functions', () => {
    expect(isFca).toBeInstanceOf(Function);
    expect(processFca).toBeInstanceOf(Function);
  });

  test('should identify FCA campaign correctly', () => {
    expect(isFca({ campaign_id: 'FCA99' })).toEqual(true);
    expect(isFca({ campaign_id: 'FCA12' })).toEqual(true);
    expect(isFca({ campaign_id: 'ABC99' })).toEqual(false);
  });

  test('should NOT identify ZZ786 campaign correctly', () => {
    expect(isFca({ campaign_id: 'ZZ786' })).toEqual(false);
  });

  test('should successfully process a campaign', async () => {
    const { dataContext, transformed } = await processFca(
      { logger },
      mockCampaign,
      'en',
      mockVariables,
    );
    expect(dataContext).toEqual('client-advice-centre');
    expect(transformed).toHaveLength(3);
    expect(transformed[0]).toEqual('John Doe');
    expect(transformed[1]).toEqual('123 Fake Street');
    expect(transformed[2]).toEqual('Toronto');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });

  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processFca({ logger }, mockCampaign, 'en', [
      'SOLUI_OTHER0_END',
    ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER0_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
