const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const { processAsIs, processAsUnknown } = require('../../../../processors');

const dataContext = 'client-advice-centre';

const validCampaigns = [ 'FCA' ];

/**
 * Returns a function that checks if the campaign is a FCA campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isFca = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) =>
  R.cond([
    [
      R.equals('SOLUI_OTHER1_END'),
      x => processAsIs(deps, campaign, x, 'OTHER1'),
    ],
    [
      R.equals('SOLUI_OTHER2_END'),
      x => processAsIs(deps, campaign, x, 'OTHER2'),
    ],
    [
      R.equals('SOLUI_OTHER3_END'),
      x => processAsIs(deps, campaign, x, 'OTHER3'),
    ],
    // Unknown
    [ R.T, x => processAsUnknown(deps, campaign, x) ],
  ]);

/**
 * Transforms variables for FCA campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processFca = async (deps, campaign, language, variables) => {
  const result = await Promise.all(
    variables.map(variable =>
      processVariable(deps, campaign, language)(variable),
    ),
  );
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isFca,
  processFca,
};
