const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const { processAsUnknown } = require('../../../../processors');

const dataContext = 'akycsb';

const validCampaigns = [
  'ORBL1', // soft stop - low/medium risk
  'ORBL2', // soft stop - low/medium risk
  'ORBL3', // soft stop - low/medium risk
  'ORBH1', // soft stop - high risk
  'ORBH2', // soft stop - high risk
  'ORBH3', // soft stop - high risk
];

/**
 * Returns a function that checks if the campaign is a soft stop (small business) campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isAkycSB = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies,
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) =>
  R.cond([
    // Unknown
    [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
  ]);

/**
 * Transforms variables for AMLKYC hard stop campaigns
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processAkycSB = async (deps, campaign, language, variables) => {
  const transformed = await Promise.all(
    variables.map((variable) =>
      processVariable(deps, campaign, language)(variable),
    ),
  );
  return {
    transformed,
    dataContext,
  };
};

module.exports = {
  isAkycSB,
  processAkycSB,
};
