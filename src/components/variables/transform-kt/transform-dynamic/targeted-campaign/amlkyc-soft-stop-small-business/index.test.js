const { isAkycSB, processAkycSB } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ORBL1',
  expiry_date: '2121-08-09',
  additional_data: [],
};

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > AMLKYC soft small business stop campaign -- low/medium/high risk', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
    logger.info.mockClear();
  });
  test('should be functions', () => {
    expect(isAkycSB).toBeInstanceOf(Function);
    expect(processAkycSB).toBeInstanceOf(Function);
  });

  test('should identify ORCHR campaign as AMLKYC soft stop campaign -- low/medium risk', () => {
    expect(isAkycSB(mockCampaign)).toEqual(true);
  });

  test('should identify ORCDL campaign as AMLKYC soft stop campaign -- high risk', () => {
    expect(isAkycSB({ ...mockCampaign, campaign_id: 'ORBH1' })).toEqual(true);
  });

  test('should identify campaign as not AMLKYC soft stop campaign', () => {
    expect(isAkycSB({ campaign_id: 'ABC99' })).toEqual(false);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processAkycSB({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER2_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER2_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
