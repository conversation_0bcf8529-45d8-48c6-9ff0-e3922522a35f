const { isSceneReward, processSceneReward } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'XKE99',
  additional_data: [
    { name: 'OTHER1', value: '1234' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > Scene Reward', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isSceneReward).toBeInstanceOf(Function);
    expect(processSceneReward).toBeInstanceOf(Function);
  });
  test('should identify campaign as Scene Reward', () => {
    expect(isSceneReward({ campaign_id: 'XKE99' })).toEqual(true);
  });
  test('should identify campaign as not Scene Reward', () => {
    expect(isSceneReward({ campaign_id: 'ABC99' })).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { transformed, dataContext } = await processSceneReward({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('scenerwrd');
    expect(transformed[0]).toEqual('1234');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processSceneReward({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER2_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER2_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
