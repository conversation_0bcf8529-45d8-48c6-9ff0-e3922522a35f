const { isMortgageRetention, processMortgageRetention } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'MRT99',
  additional_data: [
    { name: 'OTHER1', value: '20190101' },
    { name: 'OTHER2', value: '20191231' },
    { name: 'OTHER3', value: '18' },
    { name: 'OTHER4', value: '7.62%' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_DATE1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_DATE2_END',
  'SOLUI_OTHER3_END',
  'SOLUI_OTHER4_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > Mortgage Retention', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isMortgageRetention).toBeInstanceOf(Function);
    expect(processMortgageRetention).toBeInstanceOf(Function);
  });
  test('should identify campaign as Mortgage Last Payment', () => {
    expect(isMortgageRetention({ campaign_id: 'MRT99' })).toEqual(true);
    expect(isMortgageRetention({ campaign_id: 'MTA99' })).toEqual(true);
  });
  test('should identify campaign as not Mortgage Last Payment', () => {
    expect(isMortgageRetention({ campaign_id: 'ABC99' })).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { transformed, dataContext } = await processMortgageRetention({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('mrgreten');
    expect(transformed[0]).toEqual('January 1, 2019');
    expect(transformed[1]).toEqual('January 1, 2019');
    expect(transformed[2]).toEqual('December 31, 2019');
    expect(transformed[3]).toEqual('December 31, 2019');
    expect(transformed[4]).toEqual('18');
    expect(transformed[5]).toEqual('7.62%');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processMortgageRetention({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER7_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER7_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
