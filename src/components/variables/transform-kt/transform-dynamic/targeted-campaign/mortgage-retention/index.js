const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const {
  processAsIs,
  processAsUnknown,
  processAsDate,
  processAsNumber,
} = require('../../../../processors');

const dataContext = 'mrgreten';

// is_recommended = true
const validCampaigns = [
  // Mortgage retention
  'MRT',
  // 3 Month renewal
  'MTA',
];

/**
 * Returns a function that checks if the campaign is a Mortgage Retention campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isMortgageRetention = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  // Offer expiration date
  [ R.equals('SOLUI_DATE1_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER1') ],
  [ R.equals('SOLUI_OTHER1_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER1') ],
  // Maturity date
  [ R.equals('SOLUI_DATE2_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER2') ],
  [ R.equals('SOLUI_OTHER2_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER2') ],
  // Current term (in moths)
  [ R.equals('SOLUI_OTHER3_END'), (x) => processAsNumber(deps, campaign, x, 'OTHER3') ],
  // Current interest rate
  [ R.equals('SOLUI_OTHER4_END'), (x) => processAsIs(deps, campaign, x, 'OTHER4') ],
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for Mortgage Retention campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processMortgageRetention = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isMortgageRetention,
  processMortgageRetention,
};
