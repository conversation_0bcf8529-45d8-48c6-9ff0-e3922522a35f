const { isMortgageSwitch, processMortgageSwitch } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'XND99',
  additional_data: [
    { name: 'OTHER1', value: '20210630' },
  ],
};

const mockVariables = [
  'SOLUI_DATE1_END',
  'SOLUI_OTHER1_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > Mortgage Switch', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isMortgageSwitch).toBeInstanceOf(Function);
    expect(processMortgageSwitch).toBeInstanceOf(Function);
  });
  test('should identify campaign as Mortgage Switch', () => {
    expect(isMortgageSwitch({ campaign_id: 'XND99' })).toEqual(true);
  });
  test('should identify campaign as not Mortgage Switch', () => {
    expect(isMortgageSwitch({ campaign_id: 'ABC99' })).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { transformed, dataContext } = await processMortgageSwitch({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('mtgswitch');
    expect(transformed[0]).toEqual('June 30, 2021');
    expect(transformed[1]).toEqual('June 30, 2021');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processMortgageSwitch({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER7_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER7_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
