const { processTargetedCampaign } = require('./index');

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
};

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [],
};

describe('Variables > transform KT > transform dynamic > processTargetedCampaign', () => {
  test('should by function', () => {
    expect(processTargetedCampaign).toBeInstanceOf(Function);
  });
  test('should successfully call processCli', async () => {
    const campaign = { ...mockCampaign, additional_data: [ { name: 'OTHER2', value: 'CLI' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER2_END' ]);
    expect(transformed.SOLUI_OTHER2_END).toEqual('CLI');
  });
  test('should successfully call processGicRenewal', async () => {
    const campaign = { ...mockCampaign, additional_data: [ { name: 'OTHER10', value: 'GIC' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER10_END' ]);
    expect(transformed.SOLUI_OTHER10_END).toEqual('');
  });
  test('should successfully call processMortgageLastPayment', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'LMP99', additional_data: [ { name: 'OTHER1', value: 'X1' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(transformed.SOLUI_OTHER1_END).toEqual('X1');
  });
  test('should successfully call processMortgageRenewal', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'MRV99', additional_data: [ { name: 'OTHER1', value: 'X1' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(transformed.SOLUI_OTHER1_END).toEqual('X1');
  });
  test('should successfully call processMortgageRetention', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'MRT99', additional_data: [ { name: 'OTHER4', value: 'X4' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER4_END' ]);
    expect(transformed.SOLUI_OTHER4_END).toEqual('X4');
  });
  test('should successfully call processMultipleLeads', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'MLC99', additional_data: [ { name: 'OTHER1', value: 'X1' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(transformed.SOLUI_OTHER1_END).toEqual('X1');
  });
  test('should successfully call proecssOverdraftProtection', async () => {
    const campaign = { ...mockCampaign, additional_data: [ { name: 'OTHER2', value: 'ODP' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER2_END' ]);
    expect(transformed.SOLUI_OTHER2_END).toEqual('');
  });
  test('should successfully call proecssOverdraftProtection for quebec', async () => {
    const campaign = { ...mockCampaign, additional_data: [ { name: 'OTHER2', value: 'ODPQ' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER2_END' ]);
    expect(transformed.SOLUI_OTHER2_END).toEqual('');
  });
  test('should successfully call proecssOverdraftProtection for non-quebec', async () => {
    const campaign = { ...mockCampaign, additional_data: [ { name: 'OTHER2', value: 'ODPN' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER2_END' ]);
    expect(transformed.SOLUI_OTHER2_END).toEqual('');
  });
  test('should successfully call processPreapprovedCC', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'XAN99', additional_data: [ { name: 'OTHER1', value: 'X1' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(transformed.SOLUI_OTHER1_END).toEqual('');
  });
  test('should successfully call processPreapprovedLOC', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'XAB99', additional_data: [ { name: 'OTHER1', value: 'X1' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(transformed.SOLUI_OTHER1_END).toEqual('X1');
  });
  test('should successfully call processStepCli', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'JAB99', additional_data: [ { name: 'OTHER4', value: 'X4' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER4_END' ]);
    expect(transformed.SOLUI_OTHER4_END).toEqual('X4');
  });
  test('should successfully call processStudentMigration', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'SE099', additional_data: [ { name: 'OTHER1', value: 'January 2019' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(transformed.SOLUI_OTHER1_END).toEqual('January 2019');
  });
  test('should successfully call processStepAcquisition', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'XEY99', additional_data: [ { name: 'OTHER1', value: 'X1' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(transformed.SOLUI_OTHER1_END).toEqual('X1');
  });
  test('should successfully call processRspCulAcq', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'ULG99', additional_data: [ { name: 'OTHER1', value: 'X1' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(transformed.SOLUI_OTHER1_END).toEqual('X1');
  });
  test('should successfully call processSceneReward', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'XKE99', additional_data: [ { name: 'OTHER1', value: 'X1' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(transformed.SOLUI_OTHER1_END).toEqual('X1');
  });
  test('should successfully call processPayWise', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'XLX99', additional_data: [ { name: 'OTHER1', value: 'X1' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(transformed.SOLUI_OTHER1_END).toEqual('X1');
  });
  test('should successfully call Value Visa Switch', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'XLH99', additional_data: [ { name: 'OTHER1', value: 'X1' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(transformed.SOLUI_OTHER1_END).toEqual('');
  });
  test('should successfully call CEBA 5.0', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'CTN99', additional_data: [ {} ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', []);
    expect(Object.keys(transformed).length).toEqual(0);
  });
  test('should successfully call ULOC', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'XNE01', additional_data: [ { name: 'OTHER2', value: 'TEST' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER2_END' ]);
    expect(Object.keys(transformed).length).toEqual(1);
  });
  test('should successfully call Hard Intercept AMLKYC', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'ORCTR', additional_data: [ {} ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ ]);
    expect(Object.keys(transformed).length).toEqual(0);
  });
  test('should successfully call Soft Intercept AMLKYC - ORCDL', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'ORCDL', additional_data: [ { } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ ]);
    expect(Object.keys(transformed).length).toEqual(0);
  });
  test('should successfully call Soft Intercept AMLKYC - ORCHR', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'ORCHR', additional_data: [ { } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ ]);
    expect(Object.keys(transformed).length).toEqual(0);
  });
  test('should successfully call Pre-approved CCSB campaigns', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'CZA01', additional_data: [ { name: 'OTHER6', value: '4' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER6_END' ]);
    expect(Object.keys(transformed).length).toEqual(1);
  });
  test('should successfully call Physician Offer campaigns', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'XGT01', additional_data: [ { name: 'OTHER1', value: 'Dr' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(Object.keys(transformed).length).toEqual(1);
  });
  test('should successfully call EMOB D2D Nova campaigns', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'XXO01', additional_data: [ { name: 'OTHER1', value: '5' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(Object.keys(transformed).length).toEqual(1);
  });
  test('should successfully call Mortgage Switch campaigns', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'XND01', additional_data: [ { name: 'OTHER1', value: '20210630' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(Object.keys(transformed).length).toEqual(1);
  });
  test('should successfully call unknown type of campaign', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'ABC99', additional_data: [ { name: 'OTHER1', value: 'X1' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(transformed.SOLUI_OTHER1_END).toEqual('SOLUI_OTHER1_END');
  });
  test('should successfully call AKYC Small business campaign', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'ORBL1', additional_data: [ { name: 'OTHER1', value: 'X1' } ] };
    const { transformed } = await processTargetedCampaign({ logger }, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(transformed).toEqual({ 'SOLUI_OTHER1_END': 'SOLUI_OTHER1_END' });
  });
  test('should successfully call AutoPay campaign', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'XVX', additional_data: [ { name: 'OTHER1', value: 'X1' } ] };
    const { transformed } = await processTargetedCampaign({ logger }, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(Object.keys(transformed).length).toEqual(1);
  });
  test('should successfully call Mpsa Retention campaign', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'WRA', additional_data: [ { name: 'OTHER1', value: 'X1' } ] };
    const { transformed } = await processTargetedCampaign({ logger }, 'env', 'token', campaign, 'en', [ 'SOLUI_DATE1_END' ]);
    expect(Object.keys(transformed).length).toEqual(1);
  });
  test('should successfully call isOfsi campaign', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'YAJ', additional_data: [ { name: 'OTHER1', value: 'X1' } ] };
    const { transformed } = await processTargetedCampaign({ logger }, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(Object.keys(transformed).length).toEqual(1);
  });
  test('should successfully call CoBorrower campaign', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'YCU', additional_data: [ { name: 'OTHER1', value: 'X1' } ] };
    const { transformed } = await processTargetedCampaign({ logger }, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(Object.keys(transformed).length).toEqual(1);
  });
  test('should successfully call LMT campaign', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'LQA', additional_data: [ { name: 'OTHER1', value: 'X1' } ] };
    const { transformed } = await processTargetedCampaign({ logger }, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(Object.keys(transformed).length).toEqual(1);
  });
  test('should successfully call CC balance transfer campaign', async () => {
    const randomCCBalanceTransferCampaignID = `RM${Math.floor(Math.random() * 10)}`;
    const campaign = { ...mockCampaign, campaign_id: randomCCBalanceTransferCampaignID, additional_data: [ { name: 'OTHER1', value: 'X1' } ] };
    const { transformed } = await processTargetedCampaign({ logger }, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(Object.keys(transformed).length).toEqual(1);
  });
  test('should successfully call Unsecured loan campaign', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'XCY34', additional_data: [ { name: 'OTHER1', value: 'X1' } ] };
    const { transformed } = await processTargetedCampaign({ logger }, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(Object.keys(transformed).length).toEqual(1);
  });

  test('should successfully call SSI Remediation campaiggn', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'SFB34', additional_data: [ { name: 'OTHER1', value: 'X1' } ] };
    const { transformed } = await processTargetedCampaign({ logger }, 'env', 'token', campaign, 'en', [ 'SOLUI_OTHER1_END' ]);
    expect(Object.keys(transformed).length).toEqual(1);
  });
  test('should successfully call SMB Loan Renewal campaigns', async () => {
    const campaign = { ...mockCampaign, campaign_id: 'CTA12', additional_data: [ { name: 'OTHER1', value: '20210630' } ] };
    const { transformed } = await processTargetedCampaign({}, 'env', 'token', campaign, 'en', [ 'SOLUI_DATE1_END' ]);
    expect(Object.keys(transformed).length).toEqual(1);
  });
  test('should successfully call processFca', async () => {
    const campaign = {
      ...mockCampaign,
      campaign_id: 'FCA99',
      additional_data: [
        { name: 'OTHER1', value: 'X1' },
      ],
    };
    const { transformed } = await processTargetedCampaign(
      {},
      'env',
      'token',
      campaign,
      'en',
      [ 'SOLUI_OTHER1_END' ],
    );
    expect(Object.keys(transformed).length).toEqual(1);
  });

  test('should successfully call processEcm', async () => {
    const campaign = {
      ...mockCampaign,
      campaign_id: 'ECM01',
      additional_data: [
        { name: 'OTHER1', value: '20210630' },
      ],
    };
    const { transformed } = await processTargetedCampaign(
      {},
      'env',
      'token',
      campaign,
      'en',
      [ 'SOLUI_OTHER1_END' ],
    );
    expect(Object.keys(transformed).length).toEqual(1);
  });

  test('should successfully call processYkw', async () => {
    const campaign = {
      ...mockCampaign,
      campaign_id: 'YKW01',
      additional_data: [
        { name: 'OTHER1', value: 'CODE' },
      ],
    };
    const { transformed } = await processTargetedCampaign(
      {},
      'env',
      'token',
      campaign,
      'en',
      [ 'SOLUI_OTHER1_END' ],
    );
    expect(Object.keys(transformed).length).toEqual(1);
  });
});
