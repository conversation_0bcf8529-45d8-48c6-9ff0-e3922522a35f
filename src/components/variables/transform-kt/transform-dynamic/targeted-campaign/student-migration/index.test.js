const { isStudentMigration, processStudentMigration } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'SE099',
  additional_data: [
    { name: 'NAME', value: '<PERSON>' },
    { name: 'ACCT', value: '4537335392012' },
    { name: 'OTHER1', value: 'June 2019' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_DATE1_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > Student Migration', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isStudentMigration).toBeInstanceOf(Function);
    expect(processStudentMigration).toBeInstanceOf(Function);
  });
  test('should identify campaign as Student Migration', () => {
    expect(isStudentMigration(mockCampaign)).toEqual(true);
  });
  test('should identify campaign as not CLI', () => {
    const res = isStudentMigration({ campaign_id: 'ABC99', additional_data: [ { name: 'OTHER1', value: 'June 2019' } ] });
    expect(res).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { transformed, dataContext } = await processStudentMigration({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('studentmig');
    expect(transformed[0]).toEqual('June 2019');
    expect(transformed[1]).toEqual('June 2019');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processStudentMigration({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER6_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER6_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
