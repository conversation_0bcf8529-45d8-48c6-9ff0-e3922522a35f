const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const {
  processAsUnknown,
  processAsIs,
} = require('../../../../processors');

const dataContext = 'studentmig';

// is_recommended = true
const validCampaigns = [
  'SE0', 'SE1',
];

/**
 * Returns a function that checks if the campaign is a Student Migration campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isStudentMigration = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  // Offer expiration date that is sent as a "June 2019" or "juin 2019" in french, so no need to transform
  [ R.equals('SOLUI_DATE1_END'), (x) => processAsIs(deps, campaign, x, 'OTHER1') ],
  [ R.equals('SOLUI_OTHER1_END'), (x) => processAsIs(deps, campaign, x, 'OTHER1') ],
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for Student Migration campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processStudentMigration = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isStudentMigration,
  processStudentMigration,
};
