const { isOfsi, processOfsi } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'YAI01',
  additional_data: [
    { name: 'OTHER1', value: '123456' },
    { name: 'OTHER2', value: '65432' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > OFSI Changes', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isOfsi).toBeInstanceOf(Function);
    expect(processOfsi).toBeInstanceOf(Function);
  });
  test('should identify campaign as OFSI Changes', () => {
    expect(isOfsi({ campaign_id: 'YAI01' })).toEqual(true);
  });
  test('should identify campaign as not OFSI Changes', () => {
    expect(isOfsi({ campaign_id: 'ABC99' })).toEqual(false);
  });

  test('should identify campaign (YBM) as OFSI Changes', () => {
    expect(isOfsi({ campaign_id: 'YBM' })).toEqual(true);
  });

  test('should map OTHER2 to the correct value', async () => {
    const { transformed, dataContext } = await processOfsi({ logger }, { ...mockCampaign, campaign_id: 'YBM' }, 'en', mockVariables);
    expect(dataContext).toEqual('ofsistep');
    expect(transformed[1]).toEqual('$65,432');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });

  test('should successfully process a campaign', async () => {
    const { transformed, dataContext } = await processOfsi({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('ofsistep');
    expect(transformed[0]).toEqual('123456');
    expect(transformed[1]).toEqual('$65,432');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processOfsi({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER3_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER3_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
