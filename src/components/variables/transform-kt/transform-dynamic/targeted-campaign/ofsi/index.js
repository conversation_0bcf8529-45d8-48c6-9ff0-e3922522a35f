const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const {
  processAsIs,
  processAsUnknown,
  processAsCurrency,
} = require('../../../../processors');

const dataContext = 'ofsistep';

const validCampaigns = [
  'YAI', 'YAJ', 'YAK', 'YAL', 'YBM',
];

/**
 * Returns a function that checks if the campaign is a OFSI Changes campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isOfsi = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  // STEP plan number
  [ R.equals('SOLUI_OTHER1_END'), (x) => processAsIs(deps, campaign, x, 'OTHER1') ],
  // Monthly reduction $ amount
  [ R.equals('SOLUI_OTHER2_END'), (x) => processAsCurrency(deps, campaign, language, x, 'OTHER2') ],
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for Step Acquisition campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processOfsi = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isOfsi,
  processOfsi,
};
