const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const {
  processAsIs,
  processAsUnknown,
} = require('../../../../processors');

const dataContext = 'ssi-remediation';

const validCampaigns = [
  'SFA',
  'SFB',
  'SFC',
  'SFD',
  'SFE',
  'SFF',
  'SFG',
  'SFH',
  'SFI',
  'SFJ',
];

/**
 * Returns a function that checks if the campaign is a RM7 campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isSSIRemediation = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  // Joint1 Account Customer Name
  [ R.equals('SOLUI_OTHER1_END'), (x) => processAsIs(deps, campaign, x, 'OTHER1') ],
  // Joint2 Account Customer Name
  [ R.equals('SOLUI_OTHER2_END'), (x) => processAsIs(deps, campaign, x, 'OTHER2') ],
  // Joint3 Account Customer Name
  [ R.equals('SOLUI_OTHER3_END'), (x) => processAsIs(deps, campaign, x, 'OTHER3') ],
  // Commission fee reported
  [ R.equals('SOLUI_OTHER4_END'), (x) => processAsIs(deps, campaign, x, 'OTHER4') ],
  // Commission fee fixed
  [ R.equals('SOLUI_OTHER5_END'), (x) => processAsIs(deps, campaign, x, 'OTHER5') ],
  // Acct #
  [ R.equals('SOLUI_OTHER6_END'), (x) => processAsIs(deps, campaign, x, 'OTHER6') ],
  // Letter_Ref_No
  [ R.equals('SOLUI_OTHER7_END'), (x) => processAsIs(deps, campaign, x, 'OTHER7') ],
  // Address Line 1
  [ R.equals('SOLUI_OTHER8_END'), (x) => processAsIs(deps, campaign, x, 'OTHER8') ],
  // Address Line 2
  [ R.equals('SOLUI_OTHER9_END'), (x) => processAsIs(deps, campaign, x, 'OTHER9') ],
  // City
  [ R.equals('SOLUI_OTHER10_END'), (x) => processAsIs(deps, campaign, x, 'OTHER10') ],
  // Province Code
  [ R.equals('SOLUI_OTHER11_END'), (x) => processAsIs(deps, campaign, x, 'OTHER11') ],
  // Postal Code
  [ R.equals('SOLUI_OTHER12_END'), (x) => processAsIs(deps, campaign, x, 'OTHER12') ],
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for RM7 campaigns
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processSSI = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isSSIRemediation,
  processSSI,
};
