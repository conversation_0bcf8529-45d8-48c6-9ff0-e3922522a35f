const { isSSIRemediation, processSSI } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'SFA11',
  additional_data: [
    { name: 'OTHER1', value: '<PERSON>' },
    { name: '<PERSON>THER2', value: '<PERSON>' },
    { name: '<PERSON>THER3', value: '<PERSON>' },
    { name: '<PERSON>THER4', value: '9.9' },
    { name: 'OTHER5', value: '5' },
    { name: 'OTHER6', value: '****************' },
    { name: 'OTHER7', value: '107' },
    { name: 'OTHER8', value: '123 Fake Street' },
    { name: 'OTHER9', value: 'Unit 777' },
    { name: 'OTHER10', value: 'Toronto' },
    { name: 'OTHER11', value: 'ON' },
    { name: 'OTHER12', value: 'M5A999' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_OTHER3_END',
  'SOL<PERSON>_OTHER4_END',
  'SOLUI_OTHER5_END',
  'SOLUI_OTHER6_END',
  'SOLUI_OTHER7_END',
  'SOLUI_OTHER8_END',
  'SOLUI_OTHER9_END',
  'SOLUI_OTHER10_END',
  'SOLUI_OTHER11_END',
  'SOLUI_OTHER12_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > SSI Campaigns', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isSSIRemediation).toBeInstanceOf(Function);
    expect(processSSI).toBeInstanceOf(Function);
  });
  test('should identify SFA campaign as SSI Remediation', () => {
    expect(isSSIRemediation({ campaign_id: 'SFA11' })).toEqual(true);
  });
  test('should NOT identify ZZ786 campaign as unsecured line of credit', () => {
    expect(isSSIRemediation({ campaign_id: 'ZZ786' })).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { dataContext, transformed } = await processSSI({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('ssi-remediation');
    expect(transformed[0]).toEqual('John Doe');
    expect(transformed[1]).toEqual('Jane Doe');
    expect(transformed[2]).toEqual('James Doe');
    expect(transformed[3]).toEqual('9.9');
    expect(transformed[4]).toEqual('5');
    expect(transformed[5]).toEqual('****************');
    expect(transformed[6]).toEqual('107');
    expect(transformed[7]).toEqual('123 Fake Street');
    expect(transformed[8]).toEqual('Unit 777');
    expect(transformed[9]).toEqual('Toronto');
    expect(transformed[10]).toEqual('ON');
    expect(transformed[11]).toEqual('M5A999');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processSSI({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER0_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER0_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
