const R = require('ramda');
const { getAdditionalDataValue } = require('../../../../helpers');
const {
  processAsUnknown,
  processAsIs,
  processAsDate,
  processAsCurrency,
} = require('../../../../processors');

// https://confluence.agile.bns/display/PIGEON/CLI++FOR+SOLE+BORROWERS+VISA+SAS%2C+MASTERCARD%2C+AMEX%2C+OTHER
const dataContext = 'pacli';

/**
 * Checks if the campaign is a PA CLI campaign
 * @param {Campaign} campaign - Campaign
 * @returns {bool} - Result
 */
const isCli = R.pipe(
  R.flip(getAdditionalDataValue)('OTHER2'),
  R.equals('CLI'),
);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  // Offer expiration date
  [ R.equals('SOLUI_DATE1_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER1') ],
  [ R.equals('SOLUI_OTHER1_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER1') ],
  // `CLI` identifier
  [ R.equals('SOLUI_OTHER2_END'), (x) => processAsIs(deps, campaign, x, 'OTHER2') ],
  // Masked account number
  [ R.equals('SOLUI_OTHER3_END'), (x) => processAsIs(deps, campaign, x, 'OTHER3') ],
  // Old credit limit
  [ R.equals('SOLUI_OTHER4_END'), (x) => processAsCurrency(deps, campaign, language, x, 'OTHER4') ],
  // Unknown
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for PA CLI campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processCli = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isCli,
  processCli,
};
