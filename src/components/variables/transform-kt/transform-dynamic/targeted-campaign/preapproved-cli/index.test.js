const { isCli, processCli } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'OTHER1', value: '20190101' },
    { name: 'OTHER2', value: 'CLI' },
    { name: 'OTHER3', value: '1234****5678****' },
    { name: 'OTHER4', value: '1001.42' },
  ],
};

const mockVariables = [
  'SOLUI_OTHER1_END',
  'SOLUI_DATE1_END',
  'SOLUI_OTHER2_END',
  'SOLUI_OTHER3_END',
  'SOLUI_OTHER4_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > CLI', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isCli).toBeInstanceOf(Function);
    expect(processCli).toBeInstanceOf(Function);
  });
  test('should identify campaign as CLI', () => {
    expect(isCli(mockCampaign)).toEqual(true);
  });
  test('should identify campaign as not CLI', () => {
    const res = isCli({ additional_data: [ { name: 'OTHER2', value: 'ZZZ' } ] });
    expect(res).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { transformed, dataContext } = await processCli({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('pacli');
    expect(transformed[0]).toEqual('January 1, 2019');
    expect(transformed[1]).toEqual('January 1, 2019');
    expect(transformed[2]).toEqual('CLI');
    expect(transformed[3]).toEqual('1234\\*\\*\\*\\*5678\\*\\*\\*\\*');
    expect(transformed[4]).toEqual('$1,001.42');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processCli({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER6_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER6_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
