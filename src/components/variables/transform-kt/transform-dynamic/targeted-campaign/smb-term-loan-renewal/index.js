const R = require('ramda');
const { startsWithCampaignId } = require('../../../../helpers');
const {
  processAsUnknown,
  processAsDate,
} = require('../../../../processors');

const dataContext = 'smblrenew';

const validCampaigns = [
  'CTA',
];

/**
 * Returns a function that checks if the campaign is a SMB Loan Renewal campaign
 * @returns {(Campaign) => bool} - Result functions
 */
const isSmbLoanRenewal = startsWithCampaignId(validCampaigns);

/**
 * Creates a function to transform a single SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Function that transforms a single SOLUI variable
 */
const processVariable = (deps, campaign, language) => R.cond([
  [ R.equals('SOLUI_DATE1_END'), (x) => processAsDate(deps, campaign, language, x, 'OTHER1') ],
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms variables for SMB Loan Renewal campaign
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<string[]>} - Transformed variables
 */
const processSmbLoanRenewal = async (deps, campaign, language, variables) => {
  const result = await Promise.all(variables.map((variable) => processVariable(deps, campaign, language)(variable)));
  return {
    transformed: result,
    dataContext,
  };
};

module.exports = {
  isSmbLoanRenewal,
  processSmbLoanRenewal,
};
