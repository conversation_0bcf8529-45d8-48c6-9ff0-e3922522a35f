const { isSmbLoanRenewal, processSmbLoanRenewal } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'CTA12',
  additional_data: [
    { name: 'OTHER1', value: '20210630' },
  ],
};

const mockVariables = [
  'SOLUI_DATE1_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic > targeted campaign > SMB Loan Renewal', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be functions', () => {
    expect(isSmbLoanRenewal).toBeInstanceOf(Function);
    expect(processSmbLoanRenewal).toBeInstanceOf(Function);
  });
  test('should identify campaign as SMB Loan Renewal', () => {
    expect(isSmbLoanRenewal({ campaign_id: 'CTA12' })).toEqual(true);
  });
  test('should identify campaign as not SMB Loan Renewal', () => {
    expect(isSmbLoanRenewal({ campaign_id: 'ABC99' })).toEqual(false);
  });
  test('should successfully process a campaign', async () => {
    const { transformed, dataContext } = await processSmbLoanRenewal({ logger }, mockCampaign, 'en', mockVariables);
    expect(dataContext).toEqual('smblrenew');
    expect(transformed[0]).toEqual('June 30, 2021');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log unknown SOLUI variable', async () => {
    const { transformed } = await processSmbLoanRenewal({ logger }, mockCampaign, 'en', [ 'SOLUI_OTHER7_END' ]);
    expect(transformed[0]).toEqual('SOLUI_OTHER7_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
