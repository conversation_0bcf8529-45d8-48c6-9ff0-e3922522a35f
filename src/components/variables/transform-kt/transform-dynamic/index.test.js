const { isVariableDynamic, transformDynamic } = require('./index');

const logger = {
  error: jest.fn(),
};

describe('Variables > transform KT > transform dynamic', () => {
  test('should be functions', () => {
    expect(isVariableDynamic).toBeInstanceOf(Function);
    expect(transformDynamic).toBeInstanceOf(Function);
  });
  test('should properly identify dynamic variables', () => {
    expect(isVariableDynamic('SOLUI_DATE_END')).toEqual(false);
    expect(isVariableDynamic('SOLUI_OTHER1_END')).toEqual(true);
    expect(isVariableDynamic('SOLUI_DATE1_END')).toEqual(true);
    expect(isVariableDynamic('SOLUI_REASON_END')).toEqual(true);
    expect(isVariableDynamic('SOLUI_SPECIAL_RATE_END')).toEqual(true);
    expect(isVariableDynamic('SOLUI_SPECIAL_TERM_END')).toEqual(true);
    expect(isVariableDynamic('SOLUI_LEGALTEXT_END')).toEqual(true);
    expect(isVariableDynamic('SOLUI_CARDIMAGE_END')).toEqual(true);
    expect(isVariableDynamic('SOLUI_BTFEETEXT_END')).toEqual(true);
  });
  test('should process as a standing campaign', async () => {
    const campaign = { campaign_id: 'CCV01', additional_data: [ { name: 'OTHER1', value: 'X1' } ] };
    const variables = [ 'SOLUI_OTHER1_END' ];
    const { transformed } = await transformDynamic({ logger }, 'env', 'token', campaign, 'en', variables);
    expect(transformed.SOLUI_OTHER1_END).toEqual('X1');
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should process as a targeted campaign', async () => {
    const campaign = { campaign_id: 'ABC99', additional_data: [ { name: 'OTHER2', value: 'CLI' } ] };
    const variables = [ 'SOLUI_OTHER2_END' ];
    const { transformed } = await transformDynamic({ logger }, 'env', 'token', campaign, 'en', variables);
    expect(transformed.SOLUI_OTHER2_END).toEqual('CLI');
    expect(logger.error).toBeCalledTimes(0);
  });
});
