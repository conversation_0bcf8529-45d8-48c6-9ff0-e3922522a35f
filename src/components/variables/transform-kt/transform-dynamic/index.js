const R = require('ramda');
const { getCampaignId } = require('../../helpers');
const { standingCampaigns, processStandingCampaign } = require('./standing-campaign');
const { processTargetedCampaign } = require('./targeted-campaign');

const dynamicVariables = [
  'SOLUI_EXPDATE15_END',
  'SOLUI_REASON_END',
  'SOLUI_SPECIAL_RATE_END',
  'SOLUI_SPECIAL_TERM_END',
  'SOLUI_SPECIAL_PRODUCT_END',
  'SOLUI_LEGALTEXT_END',
  'S<PERSON><PERSON>_CARDIMAGE_END',
  'SOLUI_BTFEETEXT_END',
];

/**
 * Checks if variable is dynamic
 * @param {string} s - Variable name
 * @returns {bool} - Result
 */
const isVariableDynamic = (s) => (
  s.startsWith('SOLUI_OTHER') || s.startsWith('SOLUI_ACCOUNT_KEY_END') ||
  (s.startsWith('SOLUI_DATE') && s !== 'SOLUI_DATE_END') ||
  dynamicVariables.indexOf(s) !== -1);

/**
 * Transforms dynamic variables for a standing or a targeted campaign
 * @param {Object} deps - Dependencies
 * @param {string} env - `Preferred-Environment` header value
 * @param {string} reqHeaders - Request Headers
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<Object>} - Transformed mappings of variables as a key-value object
 */
const transformDynamic = (deps, env, reqHeaders, campaign, language, variables) => R.pipe(
  // get campaign id
  getCampaignId,
  // R.toUpper,
  // get a function for a standing campaign
  R.flip(R.prop)(standingCampaigns),
  R.cond([
    // function for standing campaign is found
    [ R.is(Function), (fn) => processStandingCampaign(fn)(deps, campaign, language, variables) ],
    // not found, assume that it's a targeted campaign
    [ R.T, () => processTargetedCampaign(deps, env, reqHeaders, campaign, language, variables) ],
  ]),
)(campaign);

module.exports = {
  isVariableDynamic,
  transformDynamic,
};
