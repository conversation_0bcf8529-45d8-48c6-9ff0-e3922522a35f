const { transformStatic } = require('./transform-static');
const { isVariableDynamic, transformDynamic } = require('./transform-dynamic');

/**
 * Transforms variables for a KT campaign
 * @param {Object} deps - Dependencies
 * @param {string} env - `Preferred-Environment` header value
 * @param {string} reqHeaders - Request Headers
 * @param {Campaign} campaign - Campaign
 * @param {Rule} rule - Rule
 * @param {string} language - Target language
 * @param {string[]} variables - List of SOLUI variables to transform
 * @returns {Promise<Object>} - Transformed mappings of variables as a key-value object
 */
const transformKT = async (deps, env, reqHeaders, campaign, rule, language, variables) => {
  const variablesStatic = variables.filter((v) => !isVariableDynamic(v));
  const variablesDynamic = variables.filter((v) => isVariableDynamic(v));
  // transform static variables
  const transformedStatic = await transformStatic(deps, campaign, rule, language, variablesStatic);
  // transform dynamic variables
  const { dataContext, transformed: transformedDynamic } = await transformDynamic(deps, env, reqHeaders, campaign, language, variablesDynamic);
  // return combined result
  return {
    dataContext,
    transformed: {
      ...transformedStatic,
      ...transformedDynamic,
    },
  };
};

module.exports = {
  transformKT,
};
