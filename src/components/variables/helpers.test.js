const helpers = require('./helpers');
const moment = require('moment');

const mockCampaign = {
  message_source: 'KT',
  message_id: 'D012345ABCDE',
  campaign_id: 'ABC99',
  campaign_code: 'CAMPCODE',
  expiry_date: '2121-08-09',
  additional_data: [
    { name: 'OTHER1', value: '123' },
    { name: 'OTHER2', value: '321' },
  ],
};

const mockDateFormat = 'YYYY-MM-DD';
let language = 'en';

const mockCurrentDate = moment('2021-09-16', mockDateFormat);
const mockLogger = {
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > helpers', () => {
  test('should validate isString', () => {
    expect(helpers.isString).toBeInstanceOf(Function);
    let res = helpers.isString('');
    expect(res).toEqual(true);
    res = helpers.isString('abc');
    expect(res).toEqual(true);
  });
  test('should not validate isString', () => {
    let res = helpers.isString(true);
    expect(res).toEqual(false);
    res = helpers.isString(9);
    expect(res).toEqual(false);
    res = helpers.isString(null);
    expect(res).toEqual(false);
    res = helpers.isString(undefined);
    expect(res).toEqual(false);
  });
  test('should validate isObject', () => {
    expect(helpers.isObject).toBeInstanceOf(Function);
    let res = helpers.isObject({});
    expect(res).toEqual(true);
    res = helpers.isObject({ a: 'b' });
    expect(res).toEqual(true);
  });
  test('should not validate isObject', () => {
    let res = helpers.isObject(true);
    expect(res).toEqual(false);
    res = helpers.isObject([]);
    expect(res).toEqual(false);
    res = helpers.isObject('string');
    expect(res).toEqual(false);
    res = helpers.isObject(9);
    expect(res).toEqual(false);
    res = helpers.isObject(null);
    expect(res).toEqual(false);
    res = helpers.isObject(undefined);
    expect(res).toEqual(false);
  });
  test('should validate isArray', () => {
    expect(helpers.isArray).toBeInstanceOf(Function);
    let res = helpers.isArray([]);
    expect(res).toEqual(true);
    res = helpers.isArray([ 0, 1, 'abc' ]);
    expect(res).toEqual(true);
  });
  test('should not validate isArray', () => {
    let res = helpers.isArray(true);
    expect(res).toEqual(false);
    res = helpers.isArray({});
    expect(res).toEqual(false);
    res = helpers.isArray('string');
    expect(res).toEqual(false);
    res = helpers.isArray(9);
    expect(res).toEqual(false);
    res = helpers.isArray(null);
    expect(res).toEqual(false);
    res = helpers.isArray(undefined);
    expect(res).toEqual(false);
  });
  test('should escape markdown', () => {
    expect(helpers.escapeMarkdown('********0987')).toEqual('\\*\\*\\*\\*\\*\\*\\*\\*0987');
    expect(helpers.escapeMarkdown('_12345')).toEqual('\\_12345');
  });
  test('should get message source', () => {
    const res = helpers.getMessageSource(mockCampaign);
    expect(res).toEqual('KT');
  });
  test('should get message id', () => {
    expect(helpers.getMessageId(mockCampaign)).toEqual('D012345ABCDE');
  });
  test('should get campaign id', () => {
    expect(helpers.getCampaignId(mockCampaign)).toEqual('ABC99');
  });
  test('should get campaign code', () => {
    expect(helpers.getCampaignCode(mockCampaign)).toEqual('CAMPCODE');
  });
  test('should get additional data', () => {
    const res = helpers.getAdditionalData(mockCampaign);
    expect(res).toBeInstanceOf(Array);
    expect(res.length).toEqual(2);
  });
  test('should get additional data value by name', () => {
    const res = helpers.getAdditionalDataValue(mockCampaign)('OTHER2');
    expect(res).toEqual('321');
  });
  test('should check if campaign id starts with', () => {
    let res = helpers.startsWithCampaignId([ 'ODP', 'ABC' ], mockCampaign);
    expect(res).toEqual(true);
    res = helpers.startsWithCampaignId([ 'ODP', 'CCV' ], mockCampaign);
    expect(res).toEqual(false);
  });
  test('should check if campaign code is in the list and find it', () => {
    expect(helpers.containsCampaignCode([ 'ODP', 'ABC', 'CAMPCODE' ], mockCampaign)).toEqual(true);
  });
  test('should check if campaign code is in the list and not find it', () => {
    expect(helpers.containsCampaignCode([ 'ODP', 'CCV' ], mockCampaign)).toEqual(false);
  });
  test('should remove properties from object with falsy values', () => {
    let f = helpers.removeFalsyValues;
    expect(f()({ a: 1, b: false, c: undefined, d: '', e: 'hi' })).toEqual({ a: 1, e: 'hi' });
    expect(f({ allowEmptyString: true })({ empty: '' })).toEqual({ empty: '' });
  });
  test('Should calculate total no of days between dates and return the days', () => {
    const res = helpers.getNoOfDaysBetweenDates(mockCampaign, mockCurrentDate);
    expect(res).toBeDefined();
  });

  test('Hard stop date > french', () => {
    const campaign = { expiry_date: '2022-09-01' };
    const result = helpers.getHardStopDate(campaign, 'fr');
    expect(result).toEqual('1 septembre 2022');

    const resultWithCountry = helpers.getHardStopDate(campaign, 'fr_CA');
    expect(resultWithCountry).toEqual('1 septembre 2022');
  });

  test('Hard stop date > multi-digit day of months', () => {
    const campaign = { expiry_date: '2022-09-01' };
    const result = helpers.getHardStopDate(campaign, 'en');
    expect(result).toEqual('September 1, 2022');

    const campaignTwoDigitDay = { expiry_date: '2022-09-21' };
    const resultTwoDigitDay = helpers.getHardStopDate(campaignTwoDigitDay, 'en');
    expect(resultTwoDigitDay).toEqual('September 21, 2022');
  });

  test('Hard stop date > default to english', () => {
    const campaign = { expiry_date: '2022-09-21' };
    const result = helpers.getHardStopDate(campaign);
    expect(result).toEqual('September 21, 2022');
  });

  test('Edge Case : Leap year calculation ; Should calculate total no of days between dates and return the days', () => {
    const mockCampaign = {
      message_source: 'KT',
      message_id: 'D012345ABCDE',
      campaign_id: 'ABC99',
      campaign_code: 'CAMPCODE',
      expiry_date: '2024-02-28',
      additional_data: [
        { name: 'OTHER1', value: '123' },
        { name: 'OTHER2', value: '321' },
      ],
    };
    const res = helpers.getNoOfDaysBetweenDates(mockCampaign, mockCurrentDate);
    expect(res).toEqual(896);
    mockCampaign.expiry_date = '2024-02-29'; // leap day
    expect(helpers.getNoOfDaysBetweenDates(mockCampaign, mockCurrentDate)).toEqual(897);
    mockCampaign.expiry_date = '2024-03-01';
    expect(helpers.getNoOfDaysBetweenDates(mockCampaign, mockCurrentDate)).toEqual(898);
  });

  test('ORCDL - KYC -Remind me later experience test', () => {
    const mockKycCampaign = {
      message_id: 'CM21110109485867176063',
      campaign_id: 'ORCDL',
      start_date: '2021-11-01',
    };
    const mockKycCurrentDate = moment('2021-11-22', mockDateFormat);
    mockKycCampaign.expiry_date = '2022-11-22';
    expect(helpers.getReminderPeriod(mockKycCampaign, language, mockKycCurrentDate)).toEqual('tomorrow');
    expect(helpers.getReminderPeriod(mockKycCampaign, language = 'fr', mockKycCurrentDate)).toEqual('demain');
    mockKycCampaign.expiry_date = '2022-12-22';
    expect(helpers.getReminderPeriod(mockKycCampaign, language = 'en', mockKycCurrentDate)).toEqual('in 31 days');
    expect(helpers.getReminderPeriod(mockKycCampaign, language = 'fr', mockKycCurrentDate)).toEqual('en 31 jours');
    mockKycCampaign.expiry_date = '2023-01-30';
    expect(helpers.getReminderPeriod(mockKycCampaign, language = 'en', mockKycCurrentDate)).toEqual('in 2 months');
    expect(helpers.getReminderPeriod(mockKycCampaign, language = 'fr', mockKycCurrentDate)).toEqual('en 2 mois');
  });

  test('ORCHR - KYC -Remind me later experience test', () => {
    const mockKycCampaign = {
      message_id: 'CM21110109485867176063',
      campaign_id: 'ORCHR',
      start_date: '2020-12-01',
    };
    const mockKycCurrentDate = moment('2021-11-22', mockDateFormat);
    mockKycCampaign.expiry_date = '2022-02-01';
    expect(helpers.getReminderPeriod(mockKycCampaign, language = 'en', mockKycCurrentDate)).toEqual('in 12 days');
    expect(helpers.getReminderPeriod(mockKycCampaign, language = 'fr', mockKycCurrentDate)).toEqual('en 12 jours');
    mockKycCampaign.expiry_date = '2025-12-01';
    expect(helpers.getReminderPeriod(mockKycCampaign, language = 'en', mockKycCurrentDate)).toEqual('in 4 year(s)');
    expect(helpers.getReminderPeriod(mockKycCampaign, language = 'fr', mockKycCurrentDate)).toEqual('en 4 an(s)');
  });

  test('ORCDL - KYC -Remind me later experience for 11 months scenario', () => {
    const mockKycCampaign = {
      message_id: 'CM00000000O0000ORCDL29',
      campaign_id: 'ORCDL',
      start_date: '2021-12-21',
    };
    const mockKycCurrentDate = moment('2021-12-22', mockDateFormat);
    mockKycCampaign.expiry_date = '2023-12-01';
    expect(helpers.getReminderPeriod(mockKycCampaign, language = 'en', mockKycCurrentDate)).toEqual('in 11 months');
    expect(helpers.getReminderPeriod(mockKycCampaign, language = 'fr', mockKycCurrentDate)).toEqual('en 11 mois');
  });

  test('ORCDL - KYC -Remind me later experience for 1 year scenario', () => {
    const mockKycCampaign = {
      message_id: 'CM00000000O0000ORCDL27',
      campaign_id: 'ORCDL',
      start_date: '2021-12-21',
    };
    const mockKycCurrentDate = moment('2021-12-22', mockDateFormat);
    mockKycCampaign.expiry_date = '2023-12-21';
    expect(helpers.getReminderPeriod(mockKycCampaign, language = 'en', mockKycCurrentDate)).toEqual('in 1 year(s)');
    expect(helpers.getReminderPeriod(mockKycCampaign, language = 'fr', mockKycCurrentDate)).toEqual('en 1 an(s)');
  });

  test('format rate with language - invalid type', () => {
    const formattedValue = helpers.formatRateByLanguage(mockLogger, 1, 'en', 'SOLUI_NET_RATE', 'Test', 'abc123', 'abc123');
    expect(formattedValue).toBe('SOLUI_NET_RATE');
  });

  test('format rate with language - length zeros', () => {
    const formattedValue = helpers.formatRateByLanguage(mockLogger, '', 'en', 'SOLUI_NET_RATE', 'Test', 'abc123', 'abc123');
    expect(formattedValue).toBe('SOLUI_NET_RATE');
  });

  test('format rate with language - correct format for fr', () => {
    const formattedValue = helpers.formatRateByLanguage(mockLogger, '1.23', 'fr', 'SOLUI_NET_RATE', 'Test', 'abc123', 'abc123');
    expect(formattedValue).toBe('1,23');
  });

  test('format rate with language - incorrect format for fr', () => {
    const formattedValue = helpers.formatRateByLanguage(mockLogger, '1.2', 'fr', 'SOLUI_NET_RATE', 'Test', 'abc123', 'abc123');
    expect(formattedValue).toBe('SOLUI_NET_RATE');
  });

  test('format rate with language - correct format for en', () => {
    const formattedValue = helpers.formatRateByLanguage(mockLogger, '1.23', 'en', 'SOLUI_NET_RATE', 'Test', 'abc123', 'abc123');
    expect(formattedValue).toBe('1.23');
  });

  test('format rate with language - incorrect format for en', () => {
    const formattedValue = helpers.formatRateByLanguage(mockLogger, '1.2', 'en', 'SOLUI_NET_RATE', 'Test', 'abc123', 'abc123');
    expect(formattedValue).toBe('SOLUI_NET_RATE');
  });
});
