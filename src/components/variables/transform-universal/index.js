const R = require('ramda');
/**
 * Creates a function that transforms a static SOLUI variable
 * @param {Rule} rule - Rule
 * @param {reqHeaders} reqHeaders - request headers
 */
const processVariable = (rule, reqHeaders) => R.cond([
  [ R.equals('SOLUI_RULENAME_END'), () => R.path([ 'name' ], rule) ],
  [ R.equals('{{header:x-channel-id}}'), () => R.propOr('', 'channelId', reqHeaders) ],
  [ R.equals('{{header:x-application}}'), () => R.propOr('', 'xApplication', reqHeaders) ],
  [ R.equals('{{header:x-session-id}}'), () => R.propOr('', 'sessionId', reqHeaders) ],
  [ R.equals('{{header:x-country-code}}'), () => R.propOr('', 'country', reqHeaders) ],
  // preferred-environment header is removed by early middleware in prod env, see api/src/server.js
  [ R.equals('{{header:preferred-environment}}'), () => R.propOr('', 'preferredEnv', reqHeaders) ],
  [ R.equals('{{header:embedded}}'), () => R.propOr('Y', 'embedded', reqHeaders) ],
]);

/**
 * Transforms variables for all campaigns
 * @param {string} rule - Campaign Rule
 * @param {string} reqHeaders - request headers
 * @param {string[]} variables - List of SOLUI variables to transform
 * @returns {Promise<Object>} - Transformed mappings of variables as a key-value object
 */
const transformUniversal = async (rule, reqHeaders, variables) => {
  const transformed = await Promise.all(variables.map((variable) => processVariable(rule, reqHeaders)(variable)));
  return R.zipObj(variables, transformed);
};

module.exports = {
  transformUniversal,
};
