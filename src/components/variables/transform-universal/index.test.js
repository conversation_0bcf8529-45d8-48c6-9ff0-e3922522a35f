const { transformUniversal } = require('./index');

const mockRule = {
  id: 'sadflkjasdfljk',
  name: 'Sample Rule Name',
};

const mockReqHeaders = {
  channelId: 'mobile',
  xApplication: 'N1',
  sessionId: 'session-id-123',
  country: 'DO',
  preferredEnv: 'uatred',
  embedded: 'Y',
};

const HEADER_X_CHANNEL_ID = '{{header:x-channel-id}}';
const HEADER_X_APPLICATION = '{{header:x-application}}';
const HEADER_X_SESSION_ID = '{{header:x-session-id}}';
const HEADER_X_COUNTRY_CODE = '{{header:x-country-code}}';
const HEADER_X_PREFERRED_ENV = '{{header:preferred-environment}}';
const SOLUI_RULENAME_END = 'SOLUI_RULENAME_END';
const HEADER_EMBEDDED = '{{header:embedded}}';

// Usage example
const mockVariables = [
  SOLUI_RULENAME_END,
  HEADER_X_CHANNEL_ID,
  HEADER_X_APPLICATION,
  HEADER_X_SESSION_ID,
  HEADER_X_COUNTRY_CODE,
  HEADER_X_PREFERRED_ENV,
  HEADER_EMBEDDED,
];

describe('Variables > transformUniversal', () => {
  test('should successfully transform from rules and request headers', async () => {
    const transformed = await transformUniversal(mockRule, mockReqHeaders, mockVariables);
    expect(transformed.SOLUI_RULENAME_END).toEqual(mockRule.name);
    expect(transformed[mockVariables[1]]).toEqual(mockReqHeaders.channelId);
    expect(transformed[mockVariables[2]]).toEqual(mockReqHeaders.xApplication);
    expect(transformed[mockVariables[3]]).toEqual(mockReqHeaders.sessionId);
    expect(transformed[mockVariables[4]]).toEqual(mockReqHeaders.country);
    expect(transformed[mockVariables[5]]).toEqual(mockReqHeaders.preferredEnv);
    expect(transformed[mockVariables[6]]).toEqual(mockReqHeaders.embedded);
  });

  test('should default to blank string for request header variables', async () => {
    const emptyHeaders = { channelId: undefined, xApplication: undefined, sessionId: undefined, country: undefined };
    const transformed = await transformUniversal(mockRule, emptyHeaders, mockVariables);
    expect(transformed.SOLUI_RULENAME_END).toEqual(mockRule.name);
    expect(transformed[HEADER_X_APPLICATION]).toEqual('');
    expect(transformed[HEADER_X_COUNTRY_CODE]).toEqual('');
    expect(transformed[HEADER_X_SESSION_ID]).toEqual('');
    expect(transformed[HEADER_X_CHANNEL_ID]).toEqual('');
    expect(transformed[HEADER_X_PREFERRED_ENV]).toEqual('');
  });
});
