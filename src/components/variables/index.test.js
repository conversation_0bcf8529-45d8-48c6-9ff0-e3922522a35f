/* eslint-disable sonarjs/no-duplicate-string */
const variables = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  data_context: null,
  additional_data: [
    { name: 'NAME', value: '<PERSON>' },
    { name: 'CCAU_IDV_ID_TYPE', value: 'Passport' },
  ],
};

const mockVariableMappings = {
  variable_mappings: [
    {
      'variable_template': 'SOLUI_EXPIRY_DATE_END',
      'variable_campaign': 'expiry_date',
      'variable_type': 'date',
    },
    {
      'variable_template': 'SOLUI_CUST_FULL_NAME_END',
      'variable_campaign': 'cust_full_name',
      'variable_type': 'text',
    },
    {
      'variable_template': 'SOLUI_APPROVED_CREDIT_LIMIT_END',
      'variable_campaign': 'approved_credit_limit',
      'variable_type': 'currency',
    },
    {
      'variable_template': 'SOLUI_CURRENT_ACCOUNT_NUMBER_END',
      'variable_campaign': 'current_account_number',
      'variable_type': 'account-number-mask',
    },
    {
      'variable_template': 'SOLUI_CAMPAIGNID_END',
    },
    {
      'variable_template': 'SOLUI_RULEID_END',
    },
    {
      'variable_template': 'SOLUI_MESSAGEID_END',
    },
  ],
};

const variableMappingsCacheService = {
  get: async () => Promise.resolve(mockVariableMappings),
};

const mockPega2Campaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'cust_full_name', value: 'John Doe' },
  ],
};

const mockContent = {
  some: 'hello, SOLUI_NAME_END',
};

const mockMassContent = {
  some: 'id: SOLUI_CAMPAIGNID_END , mass: SOLUI_MESSAGEID_END , ruleid: SOLUI_RULEID_END',
};

const mockContentPega2 = {
  some: 'hello, SOLUI_CUST_FULL_NAME_END',
};

const mockContentCCAU = {
  some: 'please refresh your SOLUI_CCAU_IDV_ID_TYPE_END document registered to your account',
};

const mockRule = {
  name: 'Mock Rule Name',
  id: 'cgg64SrZcTzu',
};

const mockLogger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables', () => {
  test('should have createVariableReplacer', () => {
    expect(variables).toHaveProperty('createVariableReplacer');
    expect(variables.createVariableReplacer).toBeInstanceOf(Function);
  });
  test('should create a variable replacer', () => {
    const replacer = variables.createVariableReplacer({});
    expect(replacer).toBeInstanceOf(Function);
  });
  test('should process KT campaign', async () => {
    const replacer = variables.createVariableReplacer({ logger: mockLogger });
    const { content, transformed, dataContext } = await replacer('env', 'token', mockContent, { ...mockCampaign, message_source: 'KT' }, mockRule, 'en');
    expect(content).toEqual({ some: 'hello, John Doe' });
    expect(transformed).toEqual({ SOLUI_NAME_END: 'John Doe' });
    expect(dataContext).toEqual('unknown');
  });
  test('should process Pega campaign', async () => {
    const replacer = variables.createVariableReplacer({ logger: mockLogger });
    const { content, transformed, dataContext } = await replacer('env', 'token', mockContent, { ...mockCampaign, message_source: 'PEGA' }, mockRule, 'en');
    expect(content).toEqual({ some: 'hello, SOLUI_NAME_END' });
    expect(transformed).toEqual({ SOLUI_NAME_END: 'SOLUI_NAME_END' });
    expect(dataContext).toEqual('pega');
  });
  test('should process Pega v2 campaign', async () => {
    const replacer = variables.createVariableReplacer({ logger: mockLogger, variableMappingsCacheService });
    const { content, transformed } = await replacer('env', 'token', mockContentPega2, { ...mockPega2Campaign, message_source: 'PEGAV2' }, mockRule, 'en');
    expect(content).toEqual({ some: 'hello, John Doe' });
    expect(transformed).toEqual({ SOLUI_CUST_FULL_NAME_END: 'John Doe' });
  });

  test('pegav2 variable replacement alongside universal variable', async () => {
    const replacer = variables.createVariableReplacer({ logger: mockLogger, variableMappingsCacheService });
    const { content, transformed } = await replacer('env', 'token', { ...mockContentPega2, other: 'SOLUI_RULENAME_END' }, { ...mockPega2Campaign, message_source: 'PEGAV2' }, mockRule, 'en');
    expect(content).toEqual({ some: 'hello, John Doe', other: 'Mock Rule Name' });
    expect(transformed).toEqual({ SOLUI_CUST_FULL_NAME_END: 'John Doe', SOLUI_RULENAME_END: 'Mock Rule Name' });
  });

  test('should process campaign without variables', async () => {
    const replacer = variables.createVariableReplacer({ logger: mockLogger });
    const { content, transformed, dataContext } = await replacer('env', 'token', { some: 'hello, John Doe' }, { ...mockCampaign, message_source: 'KT' }, mockRule, 'en');
    expect(content).toEqual({ some: 'hello, John Doe' });
    expect(dataContext).toEqual('unknown');
    expect(transformed).toEqual({});
  });
  test('should process unknown campaign', async () => {
    const replacer = variables.createVariableReplacer({ logger: mockLogger });
    const { content, transformed, dataContext } = await replacer('env', 'token', mockContent, { ...mockCampaign, message_source: 'XXX' }, mockRule);
    expect(content).toEqual({ some: 'hello, SOLUI_NAME_END' });
    expect(dataContext).toEqual('mass');
    expect(transformed).toEqual({ SOLUI_NAME_END: 'SOLUI_NAME_END' });
  });
  test('should process campaign when content is undefined, null or empty', async () => {
    const replacer = variables.createVariableReplacer({ logger: mockLogger });
    const res = (content) => replacer('env', 'token', content, { ...mockCampaign, message_source: 'XXX' }, mockRule);
    expect(await res(undefined)).toEqual({ content: '', dataContext: 'mass', transformed: {} });
    expect(await res(null)).toEqual({ content: '', dataContext: 'mass', transformed: {} });
    expect(await res('')).toEqual({ content: '', dataContext: 'mass', transformed: {} });
    expect(await res({})).toEqual({ content: {}, dataContext: 'mass', transformed: {} });
    expect(await res([])).toEqual({ content: [], dataContext: 'mass', transformed: {} });
  });
  test('should process CCAU campaign', async () => {
    const replacer = variables.createVariableReplacer({ logger: mockLogger });
    const { content, transformed } = await replacer('env', 'token', mockContentCCAU, { ...mockCampaign, message_source: 'DC_CAMPAIGNS' }, mockRule, 'en');
    expect(content).toEqual({ some: 'please refresh your Passport document registered to your account' });
    expect(transformed).toEqual({ SOLUI_CCAU_IDV_ID_TYPE_END: 'Passport' });
  });
  test('should process mass campaign', async () => {
    const replacer = variables.createVariableReplacer({ logger: mockLogger });
    const { content, transformed, dataContext } = await replacer('env', 'token', mockMassContent, { ...mockCampaign, message_source: 'MASS' }, { ...mockRule, external_ref: { campaign_id: 'camp-id', message_id: 'mass-id' } }, 'en');
    expect(content).toEqual({ some: 'id: camp-id , mass: mass-id , ruleid: cgg64SrZcTzu' });
    expect(transformed).toEqual({ SOLUI_CAMPAIGNID_END: 'camp-id', SOLUI_MESSAGEID_END: 'mass-id', SOLUI_RULEID_END: 'cgg64SrZcTzu' });
    expect(dataContext).toEqual('mass');
  });
});
