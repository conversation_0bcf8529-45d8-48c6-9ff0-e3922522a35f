const R = require('ramda');
const {
  processAsUnknown,
} = require('../processors');
const { getId } = require('../helpers');

const dataContext = 'mass';

/**
 * Creates a function that transforms a static SOLUI variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {Rule} rule - Rule
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Transformed variable
 */
const processVariable = (deps, rule) => R.cond([
  [ R.equals('SOLUI_CAMPAIGNID_END'), () => R.path([ 'external_ref', 'campaign_id' ], rule) ],
  [ R.equals('SOLUI_RULEID_END'), () => getId(rule) ],
  [ R.equals('SOLUI_MESSAGEID_END'), () => R.path([ 'external_ref', 'message_id' ], rule) ],
  [ R.T, (x) => processAsUnknown(deps, {}, x) ],
]);

/**
 * Transforms variables for a PEGA campaign
 * @param {Object} deps - Dependencies
 * @param {string} env - `Preferred-Environment` header value
 * @param {string} token - User token
 * @param {Rule} rule - Rule
 * @param {string} language - Target language
 * @param {string[]} variables - List of SOLUI variables to transform
 * @returns {Promise<Object>} - Transformed mappings of variables as a key-value object
 */
const transformMass = async (deps, env, token, rule, language, variables) => {
  const transformed = await Promise.all(variables.map((variable) => processVariable(deps, rule)(variable)));
  return {
    transformed: R.zipObj(variables, transformed),
    dataContext,
  };
};

module.exports = {
  transformMass,
};
