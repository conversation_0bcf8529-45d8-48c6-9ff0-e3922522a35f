const { transformStatic } = require('./index');

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  expiry_date: '2122-08-09',
  type: 'ccau_campaign',
  additional_data: [
    { name: 'CCAU_IDV_ID_TYPE', value: '1' },
    { name: 'CCAU_IDV_ID_TYPE_TEXT', value: 'Passport' },
  ],
};

const mockRule = {
  id: 'pAqozbdSmFut',
};

const mockVariables = [
  'SOLUI_CCAU_IDV_ID_TYPE_END',
  'SOLUI_CCAU_IDV_ID_TYPE_TEXT_END',
  'SOLUI_MESSAGEID_END',
  'SOLUI_LANGUAGE_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

describe('Variables > transform CCAU > transformStatic', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be a function', () => {
    expect(transformStatic).toBeInstanceOf(Function);
  });
  test('should successfully transform', async () => {
    const res = await transformStatic({ logger }, 'env', 'token', mockCampaign, mockRule, 'en', mockVariables);
    expect(res.SOLUI_CCAU_IDV_ID_TYPE_END).toEqual('1');
    expect(res.SOLUI_CCAU_IDV_ID_TYPE_TEXT_END).toEqual('Passport');
    expect(res.SOLUI_MESSAGEID_END).toEqual('D1234567');
    expect(res.SOLUI_LANGUAGE_END).toEqual('en');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log error on unknown variable', async () => {
    const res = await transformStatic({ logger }, 'env', 'token', mockCampaign, mockRule, 'en', [ 'SOLUI_UNKNOWN_END' ]);
    expect(res.SOLUI_UNKNOWN_END).toEqual('SOLUI_UNKNOWN_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
