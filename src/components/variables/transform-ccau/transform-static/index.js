const R = require('ramda');
const {
  processAsUnknown,
  processAsIs,
} = require('../../processors');
const { getMessageId } = require('../../helpers');

/**
 * Creates a function that transforms a static CCAU variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {Rule} rule - Rule
 * @param {string} language - Target language
 * @returns {(string) => Promise<string>} - Transformed variable
 */
const processVariable = (deps, campaign, rule, language) => R.cond([
  [ R.equals('SOLUI_CCAU_IDV_ID_TYPE_END'), (x) => processAsIs(deps, campaign, x, 'CCAU_IDV_ID_TYPE') ],
  [ R.equals('SOLUI_CCAU_IDV_ID_TYPE_TEXT_END'), (x) => processAsIs(deps, campaign, x, 'CCAU_IDV_ID_TYPE_TEXT') ],
  [ R.equals('SOLUI_MESSAGEID_END'), () => getMessageId(campaign) ],
  [ R.equals('SOLUI_LANGUAGE_END'), () => language ],
  [ R.T, (x) => processAsUnknown(deps, campaign, x) ],
]);

/**
 * Transforms static variables for a standing or a targeted campaign
 * @param {Object} deps - Dependencies
 * @param {string} env - `Preferred-Environment` header value (not used)
 * @param {string} token - User token (not used)
 * @param {Campaign} campaign - Campaign
 * @param {Rule} rule - Rule
 * @param {string} language - Target language
 * @param {string[]} variables - SOLUI variables to transform
 * @returns {Promise<Object>} - Transformed mappings of variables as a key-value object
 */
const transformStatic = async (deps, env, token, campaign, rule, language, variables) => {
  const transformed = await Promise.all(variables.map((variable) => processVariable(deps, campaign, rule, language)(variable)));
  return R.zipObj(variables, transformed);
};

module.exports = {
  transformStatic,
};
