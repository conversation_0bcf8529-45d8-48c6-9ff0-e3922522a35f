const { transformStatic } = require('./transform-static');

/**
 * Transforms variables for a KT campaign
 * @param {Object} deps - Dependencies
 * @param {string} env - `Preferred-Environment` header value
 * @param {string} token - User token
 * @param {Campaign} campaign - Campaign
 * @param {Rule} rule - Rule
 * @param {string} language - Target language
 * @param {string[]} variables - List of SOLUI variables to transform
 * @returns {Promise<Object>} - Transformed mappings of variables as a key-value object
 */
const transformCCAU = async (deps, env, token, campaign, rule, language, variables) => {
  const variablesStatic = variables;
  // transform static variables
  const transformedStatic = await transformStatic(deps, env, token, campaign, rule, language, variablesStatic);
  return {
    transformed: {
      ...transformedStatic,
    },
  };
};

module.exports = {
  transformCCAU,
};
