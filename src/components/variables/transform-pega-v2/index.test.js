const { transformPEGA2 } = require('./index');

const mockVariableMappings = {
  variable_mappings: [
    {
      'variable_template': 'SOLUI_EXPIRY_DATE_END',
      'variable_campaign': 'expiry_date',
      'variable_type': 'date',
    },
    {
      'variable_template': 'SOLUI_CUST_FULL_NAME_END',
      'variable_campaign': 'cust_full_name',
      'variable_type': 'text',
    },
    {
      'variable_template': 'SOLUI_CUST_FIRST_NAME_END',
      'variable_campaign': 'cust_first_name',
      'variable_type': 'text',
    },
    {
      'variable_template': 'SOLUI_CUST_LAST_NAME_END',
      'variable_campaign': 'cust_last_name',
      'variable_type': 'text',
    },
    {
      'variable_template': 'SOLUI_APPROVED_CREDIT_LIMIT_END',
      'variable_campaign': 'approved_credit_limit',
      'variable_type': 'currency',
    },
    {
      'variable_template': 'SOLUI_CURRENT_ACCOUNT_NUMBER_END',
      'variable_campaign': 'current_account_number',
      'variable_type': 'account-number-mask',
    },
    {
      'variable_template': 'SOLUI_ACCOUNT_NUMBER_END',
      'variable_campaign': 'account_number',
      'variable_type': 'account',
    },
    {
      'variable_template': 'SOLUI_CURRENT_ACCOUNT_MATURITY_DATE_END',
      'variable_campaign': 'current_account_maturity_date',
      'variable_type': 'date',
    },
    {
      'variable_template': 'SOLUI_PRODUCT_CODE_END',
      'variable_campaign': 'product_code',
      'variable_type': 'text',
    },
    {
      'variable_template': 'SOLUI_PROD_NAME_END',
      'variable_campaign': 'prod_name',
      'variable_type': 'text',
    },
    {
      'variable_template': 'SOLUI_OFFER_NAME_END',
      'variable_campaign': 'offer_name',
      'variable_type': 'text',
    },
    {
      'variable_template': 'SOLUI_OFFER_DESCRIPTION_END',
      'variable_campaign': 'offer_description',
      'variable_type': 'text',
    },
    {
      'variable_template': 'SOLUI_OFFER_START_DATE_END',
      'variable_campaign': 'offer_start_date',
      'variable_type': 'date',
    },
    {
      'variable_template': 'SOLUI_CURRENT_CREDIT_LIMIT_END',
      'variable_campaign': 'current_credit_limit',
      'variable_type': 'currency',
    },
    {
      'variable_template': 'SOLUI_OFFER_CONDITION_END',
      'variable_campaign': 'offer_condition',
      'variable_type': 'text',
    },
    {
      'variable_template': 'SOLUI_NBA_NAME_END',
      'variable_campaign': 'nba_name',
      'variable_type': 'text',
    },
    {
      'variable_template': 'SOLUI_TERM_END',
      'variable_campaign': 'term',
      'variable_type': 'number',
    },
    {
      'variable_template': 'SOLUI_SPECIAL_TERM_END',
      'variable_campaign': 'special_term',
      'variable_type': 'gic-special-term',
    },
    {
      'variable_template': 'SOLUI_SPECIAL_RATE_END',
      'variable_campaign': 'special_rate',
      'variable_type': 'gic-special-rate',
    },
    {
      'variable_template': 'SOLUI_LOCAL_RATE_END',
      'variable_campaign': 'locale_rate',
      'variable_type': 'locale-rate',
    },
    {
      'variable_template': 'SOLUI_MESSAGEID_END',
      'variable_type': 'SOLUI_MESSAGEID_END',
    },
  ],
}
  ;

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  expiry_date: '2019-12-31',
  message_source: 'PEGAV2',
  additional_data: [
    { name: 'cust_full_name', value: 'John Doe' },
    { name: 'cust_first_name', value: 'John' },
    { name: 'cust_last_name', value: 'Doe' },
    { name: 'approved_credit_limit', value: '8500' },
    { name: 'current_account_number', value: '*************' },
    { name: 'account_number', value: '*************' },
    { name: 'current_account_maturity_date', value: '2020-01-10' },
    { name: 'product_code', value: 'VLR' },
    { name: 'prod_name', value: 'SCENE VISA card' },
    { name: 'offer_name', value: 'Test' },
    { name: 'offer_description', value: 'This is a test' },
    { name: 'offer_start_date', value: '2020-10-31' },
    { name: 'current_credit_limit', value: '2000' },
    { name: 'offer_condition', value: 'Condition' },
    { name: 'nba_name', value: 'Testing NBA' },
    { name: 'term', value: '60' },
    { name: 'plan_num', value: 'Testing GIC special' },
    { name: 'locale_rate', value: '1.23' },
  ],
};

const mockRule = {
  id: 'pAqozbdSmFut',
};

const mockVariablesDynamic = [
  'SOLUI_EXPIRY_DATE_END',
  'SOLUI_CUST_FULL_NAME_END',
  'SOLUI_APPROVED_CREDIT_LIMIT_END',
  'SOLUI_CURRENT_ACCOUNT_NUMBER_END',
  'SOLUI_ACCOUNT_NUMBER_END',
  'SOLUI_SPECIAL_RATE_END',
  'SOLUI_SPECIAL_TERM_END',
  'SOLUI_TERM_END',
  'SOLUI_MESSAGEID_END',
  'SOLUI_TERM_END',
  'SOLUI_LOCAL_RATE_END',
];

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

const env = {};
const token = '234234';

const mockResponseData = {
  data: {
    request_id: 123,
    gic_rates: [
      {
        code: 'BNSLTNRC',
        type: 'LTNR',
        expiry_date: '2018-03-09-05:00',
        term_rates: [
          {
            term: {
              length: 1,
              unit: 'year',
              details: {
                from: 365,
                to: 545,
                unit: 'Day',
              },
            },
            is_recommended: true,
            step_rates: [
              {
                step_number: 0,
                frequency_rates: [
                  {
                    interest_payment_frequency: 'annually',
                    is_compound: true,
                    offer_rate: 1.1032,
                    target_rate: 1.1525,
                    branch_discretion_limit: 1.2125,
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
};

const investmentService = {
  getGicRates: async () => Promise.resolve(mockResponseData),
};

const variableMappingsCacheService = {
  get: async () => Promise.resolve(mockVariableMappings),
};

describe('Variables > transform Pega v2 > transform dynamic', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    logger.error.mockClear();
  });
  test('should be a function', () => {
    expect(transformPEGA2).toBeInstanceOf(Function);
  });
  test('should successfully transform using dynamic processor', async () => {
    const { transformed } = await transformPEGA2({ logger, variableMappingsCacheService, investmentService }, env, token, mockCampaign, mockRule, 'en', mockVariablesDynamic);
    expect(transformed.SOLUI_CUST_FULL_NAME_END).toEqual('John Doe');
    expect(transformed.SOLUI_APPROVED_CREDIT_LIMIT_END).toEqual('$8,500');
    expect(transformed.SOLUI_CURRENT_ACCOUNT_NUMBER_END).toEqual('\\*\\*\\*\\*\\*\\*\\*\\*\\*0691');
    expect(transformed.SOLUI_ACCOUNT_NUMBER_END).toEqual('\\*\\*\\*\\*\\*\\*\\*\\*\\*0691');
    expect(transformed.SOLUI_EXPIRY_DATE_END).toEqual('December 31, 2019');
    expect(transformed.SOLUI_SPECIAL_RATE_END).toEqual('1.21%');
    expect(transformed.SOLUI_SPECIAL_TERM_END).toEqual('1 year');
    expect(transformed.SOLUI_TERM_END).toEqual('60');
    expect(transformed.SOLUI_MESSAGEID_END).toEqual('D1234567');
    expect(transformed.SOLUI_LOCAL_RATE_END).toEqual('1.23');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should successfully transform non visa/amex account using account mask', async () => {
    const mockCampaignAccount = { ...mockCampaign, additional_data: [ { name: 'current_account_number', value: '00100000000LSCZ' } ] };
    const { transformed } = await transformPEGA2({ logger, variableMappingsCacheService, investmentService }, env, token, mockCampaignAccount, mockRule, 'en', [ 'SOLUI_CURRENT_ACCOUNT_NUMBER_END' ], true);
    expect(transformed.SOLUI_CURRENT_ACCOUNT_NUMBER_END).toEqual('\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*LSCZ');
    expect(logger.warn).toBeCalledTimes(0);
    expect(logger.error).toBeCalledTimes(0);
  });
  test('should log error on unknown variable using dynamic processor', async () => {
    const { transformed } = await transformPEGA2({ logger, variableMappingsCacheService, investmentService }, env, token, mockCampaign, mockRule, 'en', [ 'SOLUI_UNKNOWN_END' ], true);
    expect(transformed.SOLUI_UNKNOWN_END).toEqual('SOLUI_UNKNOWN_END');
    expect(logger.warn).toBeCalledTimes(1);
    expect(logger.error).toBeCalledTimes(0);
  });
});
