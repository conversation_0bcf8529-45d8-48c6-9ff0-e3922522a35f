const { isMortgageRenewalV2 } = require('./campaigns');

describe('Variables > Pega v2 > transform dynamic > targeted campaign > Mortgage Renewal V2', () => {
  test('should be functions', () => {
    expect(isMortgageRenewalV2).toBeInstanceOf(Function);
  });
  test('should identify campaign as Mortgage Last Payment', () => {
    expect(isMortgageRenewalV2({ campaign_code: 'ET04024' })).toEqual(true);
  });
  test('should identify campaign as not Mortgage Last Payment', () => {
    expect(isMortgageRenewalV2({ campaign_code: 'ABC99' })).toEqual(false);
    expect(isMortgageRenewalV2({ campaign_id: 'ET04024' })).toEqual(false);
  });
});
