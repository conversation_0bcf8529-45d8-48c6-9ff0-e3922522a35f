const R = require('ramda');
const {
  processAsUnknown,
  processAsIs,
  processAsCurrency,
  processAsDate,
  processAsGicSpecialRate,
  processAsGicSpecialTerm,
  processAsNumber,
  processAsAccount,
  processAsLocaleRate,
} = require('../processors');

const dataContext = 'pega';

const processVariable = (deps, env, reqHeaders, campaign, rule, language, { variable_template, variable_campaign }) => R.cond([ // eslint-disable-line
  [ R.equals('text'), () => processAsIs(deps, campaign, variable_template, variable_campaign) ],
  [ R.equals('currency'), () => processAsCurrency(deps, campaign, language, variable_template, variable_campaign) ],
  [ R.equals('account-number-mask'), () => processAsAccount(deps, campaign, variable_template, variable_campaign) ],
  [ R.equals('date'), () => processAsDate(deps, campaign, language, variable_template, variable_campaign) ],
  [ R.equals('number'), () => processAsNumber(deps, campaign, variable_template, variable_campaign) ],
  [ R.equals('locale-rate'), () => processAsLocaleRate(deps, campaign, variable_template, variable_campaign, language) ],
  [ R.equals('account'), () => processAsAccount(deps, campaign, variable_template, variable_campaign) ],
  [ R.equals('gic-special-rate'), () => processAsGicSpecialRate(deps, env, reqHeaders, campaign, variable_template, 'plan_num', 'current_account_number') ],
  [ R.equals('gic-special-term'), () => processAsGicSpecialTerm(deps, env, reqHeaders, campaign, language, variable_template, 'plan_num', 'current_account_number') ],
  [ R.equals('SOLUI_MESSAGEID_END'), () => campaign.message_id ],
  [ R.T, () => processAsUnknown(deps, campaign, variable_template) ],
]);

/**
 * Transforms variables for a PEGA 8.4 campaign
 * @param {Object} deps - Dependencies
 * @param {string} env - `Preferred-Environment` header value
 * @param {string} reqHeaders - Request Headers
 * @param {Campaign} campaign - Campaign
 * @param {Rule} rule - Rule
 * @param {string} language - Target language
 * @param {string[]} variables - List of SOLUI variables to transform
 * @returns {Promise<Object>} - Transformed mappings of variables as a key-value object
 */
const transformPEGA2 = async (deps, env, reqHeaders, campaign, rule, language, variables) => {
  const transformed = await Promise.all(variables.map(async (variable) => {
      const { variable_mappings } = await deps.variableMappingsCacheService.get(); // eslint-disable-line
    const variableMapping = variable_mappings.find((item) => item.variable_template === variable) || { variable_template: variable };
    return processVariable(deps, env, reqHeaders, campaign, rule, language, variableMapping)(variableMapping.variable_type || variableMapping.variable_template);
  }));
  return {
    transformed: R.zipObj(variables, transformed),
    dataContext,
  };
};

module.exports = {
  transformPEGA2,
};
