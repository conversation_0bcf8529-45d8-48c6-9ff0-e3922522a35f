const R = require('ramda');
const { transformUniversal } = require('./transform-universal');
const { transformKT } = require('./transform-kt');
const { transformPEGA } = require('./transform-pega');
const { transformPEGA2 } = require('./transform-pega-v2');
const { transformMass } = require('./transform-mass');
const { transformCCAU } = require('./transform-ccau');
const {
  matchVarFormat,
  getMessageSource,
  isString,
  isObject,
  isArray,
  removeFalsyValues,
} = require('./helpers');

const processString = R.curry((transformed, content) =>
  matchVarFormat(content).reduce((acc, name) =>
    acc.replace(name, transformed[String(name)]), content));

const processObject = R.curry((transformed, content) => R.map(processItem(transformed), content)); // NOSONAR

const processArray = R.curry((transformed, content) => R.map(processItem(transformed), content)); // NOSONAR

const processItem = (transformed) => R.cond([
  [ isObject, processObject(transformed) ],
  [ isString, processString(transformed) ],
  [ isArray, processArray(transformed) ],
  [ R.T, R.identity ],
]);

/**
 * Creates and returns a variable replacing function
 * @param {Object} deps - Dependencies
 * @returns {(string, string, object, object, object, string, object) => Object} - Function that returns a content object with replaced variables
 */
const createVariableReplacer = (deps) =>
  async (env, token, content, campaign, rule, language = 'en', reqHeaders = {}) => {
    // get a list of SOLUI variables from content
    const variables = content ? R.uniq(matchVarFormat(JSON.stringify(content))) : [];
    let transformed;
    const campaignSource = getMessageSource(campaign);
    if (campaignSource === 'KT') {
      transformed = await transformKT(deps, env, reqHeaders, campaign, rule, language, variables);
    } else if (campaignSource === 'PEGA') {
      transformed = await transformPEGA(deps, campaign, rule, language, variables);
    } else if (campaignSource === 'PEGAV2') {
      transformed = await transformPEGA2(deps, env, reqHeaders, campaign, rule, language, variables);
    } else if (campaignSource === 'DC_CAMPAIGNS') {
      transformed = await transformCCAU(deps, env, token, campaign, rule, language, variables);
    } else {
      // mass campaign or message
      transformed = await transformMass(deps, env, token, rule, language, variables);
    }

    const transformedVars = await transformUniversal(rule, reqHeaders, variables);
    const universalTransform = removeFalsyValues({ allowEmptyString: true })(transformedVars);
    transformed.transformed = R.mergeRight(transformed.transformed, universalTransform);

    // replace all transformed variables
    const result = processItem(transformed.transformed)(content || '');
    return {
      ...transformed,
      content: result,
    };
  };

module.exports = {
  createVariableReplacer,
};
