const formatterEN = new Intl.NumberFormat('en-CA', {
  minimumFractionDigits: 0,
  style: 'currency',
  currency: 'CAD',
});

const formatterFR = new Intl.NumberFormat('fr-CA', {
  minimumFractionDigits: 0,
  style: 'currency',
  currency: 'CAD',
  currencyDisplay: 'narrowSymbol',
});

const formatters = {
  en: formatterEN,
  fr: formatterFR,
};

/**
 * Transforms a currency amount by language
 * @param {string} language - Target language, available options are: `en` and `fr`
 * @param {string} value - Amount
 * @returns {Promise<string>} - Transformed value
 */
const transformCurrency = async (language, value) => {
  if (typeof value !== 'string' || value.length === 0) {
    return Promise.reject(new Error('not a valid string'));
  }
  const transformed = Number.parseFloat(value);
  if (Number.isNaN(transformed)) {
    return Promise.reject(new Error('invalid number'));
  }

  const transformer = Object.prototype.hasOwnProperty.call(formatters, language)
    ? formatters[String(language)]
    : formatters.en;
  const formatted = transformer.format(transformed);
  return Promise.resolve(`${formatted}`);
};

module.exports = transformCurrency;
