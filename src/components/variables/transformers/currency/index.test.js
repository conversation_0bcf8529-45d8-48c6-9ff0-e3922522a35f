const transformCurrency = require('./index');

describe('Variables > transformers > transformCurrency', () => {
  test('should transform currency using english language', async () => {
    const res = await transformCurrency('en', '010200');
    expect(res).toEqual('$10,200');
  });
  test('should transform currency using french language', async () => {
    const res = await transformCurrency('fr', '001000');
    // NOTE: for french language a No-Break Space is used before `$`
    expect(res).toEqual('1 000 $');
  });
  test('should transform currency using english language if language is invalid', async () => {
    const res = await transformCurrency('zz', '001000');
    expect(res).toEqual('$1,000');
  });
  test('should not transform if not a string', async () => {
    try {
      await transformCurrency('en', 1000);
    } catch (err) {
      expect(err.message).toEqual('not a valid string');
      return Promise.resolve();
    }
    return Promise.reject(new Error('successful call'));
  });
  test('should not transform if trnasformed value is not a number', async () => {
    try {
      await transformCurrency('en', 'abc');
    } catch (err) {
      expect(err.message).toEqual('invalid number');
      return Promise.resolve();
    }
    return Promise.reject(new Error('successful call'));
  });
});
