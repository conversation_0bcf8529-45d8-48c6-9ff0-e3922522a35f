const moment = require('moment');

const validLocales = [ 'en', 'fr' ];
const validFormats = [ 'YYYYMMDD', 'YYYY-MM-DD', 'YYYY/MM/DD' ];

/**
 * Transforms a date by the language
 * @param {string} language - Target language
 * @param {*} value - Date, supported formats are: `YYYYMMDD`, `YYYY-MM-DD`, `YYYY/MM/DD`
 * @returns {Promise<string>} Transformed value
 */
const transformDate = async (language, value) => {
  const parsedDate = moment(value, validFormats);
  if (!parsedDate.isValid()) {
    return Promise.reject(new Error('invalid date format'));
  }
  return Promise.resolve(parsedDate
    .locale(validLocales.indexOf(language) !== -1 ? language : validLocales[0])
    .format('LL'));
};

module.exports = transformDate;
