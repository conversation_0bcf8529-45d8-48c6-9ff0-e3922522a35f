/* eslint-disable sonarjs/no-duplicate-string */
const transformDate = require('./index');

describe('Variables > transformers > transformDate', () => {
  test('should be a function', () => {
    expect(transformDate).toBeInstanceOf(Function);
  });
  test('should transform date as YYYYMMDD to english', async () => {
    const res = await transformDate('en', '20191231');
    expect(res).toEqual('December 31, 2019');
  });
  test('should transform date as YYYY-MM-DD to english', async () => {
    const res = await transformDate('en', '2019-12-31');
    expect(res).toEqual('December 31, 2019');
  });
  test('should transform date as YYYY/MM/DD to english', async () => {
    const res = await transformDate('en', '2019-12-31');
    expect(res).toEqual('December 31, 2019');
  });
  test('should transform date as YYYYMMDD to french', async () => {
    const res = await transformDate('fr', '20191231');
    expect(res).toEqual('31 décembre 2019');
  });
  test('should transform date as YYYY-MM-DD to french', async () => {
    const res = await transformDate('fr', '2019-12-31');
    expect(res).toEqual('31 décembre 2019');
  });
  test('should transform date as YYYY/MM/DD to french', async () => {
    const res = await transformDate('fr', '2019-12-31');
    expect(res).toEqual('31 décembre 2019');
  });
  test('should not transform invalid date', async () => {
    try {
      await transformDate('en', 'abcdef');
    } catch (err) {
      expect(err.message).toEqual('invalid date format');
      return Promise.resolve();
    }
    return Promise.reject(new Error('successful call'));
  });
  test('should use an english as a default language', async () => {
    const res = await transformDate('ca', '20191231');
    expect(res).toEqual('December 31, 2019');
  });
});
