const names = {
  en: 'Long Term Non-Redeemable GIC',
  fr: 'Long Term Non-Redeemable GIC',
};

/**
 * Transforms a GIC plan and certificate number to a product name
 * @param {Object} investmentService - Investment API service
 * @param {string} env - `Preferred-Environment` header value
 * @param {string} token - User token
 * @param {string} plan - GIC plan number
 * @param {string} certificate - GIC certificate number
 * @returns {Promise<string>} - Transformed value
 */
const transformGicSpecialProduct = async (investmentService, env, token, language, plan, certificate) => {
  return language === 'fr' ? names.fr : names.en;
};

module.exports = transformGicSpecialProduct;
