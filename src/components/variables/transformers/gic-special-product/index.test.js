const transformSpecialGicProduct = require('./index');

const mockResponse = {
  data: {
    request_id: 16267,
    gic_rates: [
      {
        code: 'MCCLTNRC',
        type: 'LTNR',
        expiry_date: '2019-04-06',
        term_rates: [
          {
            term: {
              length: 1,
              unit: 'Year',
              details: { from: 365, to: 545, unit: 'Day' },
            },
            is_recommended: true,
            step_rates: [
              {
                step_number: 0,
                frequency_rates: [
                  {
                    interest_payment_frequency: 'Annually',
                    is_compound: true,
                    offer_rate: 2.55,
                    target_rate: 2.55,
                    branch_discretion_limit: 2.55,
                  },
                  {
                    interest_payment_frequency: 'Semi-Annually',
                    is_compound: true,
                    offer_rate: 2.425,
                    target_rate: 2.425,
                    branch_discretion_limit: 2.425,
                  },
                ],
              },
            ],
          },
          {
            term: {
              length: 2,
              unit: 'Year',
              details: { from: 730, to: 911, unit: 'Day' },
            },
            is_recommended: false,
            step_rates: [
              {
                step_number: 0,
                frequency_rates: [
                  {
                    interest_payment_frequency: 'Annually',
                    is_compound: true,
                    offer_rate: 2.71,
                    target_rate: 2.71,
                    branch_discretion_limit: 2.71,
                  },
                  {
                    interest_payment_frequency: 'Semi-Annually',
                    is_compound: true,
                    offer_rate: 2.585,
                    target_rate: 2.585,
                    branch_discretion_limit: 2.585,
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
};

const mockInvestmentService = {
  getGicRates: jest.fn().mockResolvedValue(mockResponse),
};

describe('Variables > transformers > transformSpecialGicProduct', () => {
  beforeEach(() => {
    mockInvestmentService.getGicRates.mockClear();
  });
  test('should be a function', () => {
    expect(transformSpecialGicProduct).toBeInstanceOf(Function);
  });
  test('should successfully call with english', async () => {
    const res = await transformSpecialGicProduct(mockInvestmentService, 'env', 'token', 'en', 'plan#', 'certificate#');
    expect(res).toEqual('Long Term Non-Redeemable GIC');
    expect(mockInvestmentService.getGicRates).toBeCalledTimes(0);
  });
  test('should successfully call with french', async () => {
    const res = await transformSpecialGicProduct(mockInvestmentService, 'env', 'token', 'fr', 'plan#', 'certificate#');
    expect(res).toEqual('Long Term Non-Redeemable GIC');
    expect(mockInvestmentService.getGicRates).toBeCalledTimes(0);
  });
});
