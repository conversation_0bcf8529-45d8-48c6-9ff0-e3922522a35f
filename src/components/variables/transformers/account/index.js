const createMask = (mask, len) => Array(len).fill(mask).join('');

/**
 * Transforms an account number by masking all but last characters of it
 * @param {string} value - Account number
 * @param {string} [mask=*] - Mask character
 * @param {number} [tail=4] - Size of a vizible part
 * @returns {Promise<string>} - Transformed value
 */
const transformAccount = async (value, mask = '*', tail = 4) => {
  if (typeof value !== 'string' || value.length === 0) {
    return '';
  }
  const head = value.length - tail;
  return head > 0 ? `${createMask(mask, head)}${value.slice(head)}` : value;
};

module.exports = transformAccount;
