const transformAccount = require('./index');

describe('Variables > transformers > transformAccount', () => {
  test('should transform account with default values', async () => {
    const res = await transformAccount('**********');
    expect(res).toEqual('******7890');
  });
  test('should transform with custom mask and tail length', async () => {
    const res = await transformAccount('**********', '#', 2);
    expect(res).toEqual('########90');
  });
  test('should return full account if tail is equal or longer than account length', async () => {
    const res = await transformAccount('1234');
    expect(res).toEqual('1234');
  });
  test('should return an empty string if invalid value is passed', async () => {
    const res = await transformAccount(1);
    expect(res).toEqual('');
  });
  test('should return an empty string if an empty string is passed', async () => {
    const res = await transformAccount('');
    expect(res).toEqual('');
  });
});
