const transformNumber = require('./index');

describe('Variables > transformers > transformNumber', () => {
  test('should be a function', () => {
    expect(transformNumber).toBeInstanceOf(Function);
  });
  test('should transform a number', async () => {
    const res = await transformNumber('001000');
    expect(res).toEqual('1000');
  });
  test('should not transform not a number', async () => {
    try {
      await transformNumber('abc');
    } catch (err) {
      expect(err.message).toEqual('invalid number');
      return Promise.resolve();
    }
    return Promise.reject(new Error('successfull call'));
  });
  test('should not transform not a string', async () => {
    try {
      await transformNumber(100);
    } catch (err) {
      expect(err.message).toEqual('not a valid string');
      return Promise.resolve();
    }
    return Promise.reject(new Error('successfull call'));
  });
});
