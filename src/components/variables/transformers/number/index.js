/**
 * Transforms a number to a number, e.g. `001000` to `1000`
 * @param {string} value - Value
 * @returns {Promise<string>} - Transformed value
 */
const transformNumber = async (value) => {
  if (typeof value !== 'string' || value.length === 0) {
    return Promise.reject(new Error('not a valid string'));
  }
  const transformed = Number.parseFloat(value);
  if (Number.isNaN(transformed)) {
    return Promise.reject(new Error('invalid number'));
  }
  return Promise.resolve(`${transformed}`);
};

module.exports = transformNumber;
