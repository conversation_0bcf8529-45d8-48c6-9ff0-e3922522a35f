/* eslint-disable sonarjs/no-duplicate-string */
const transformProduct = require('./index');
const productBook = require('../../../../../data/productbook.json');

const mockResponse = productBook.data.products.find(p => p.product_id === '580040');

const productBookService = {
  getProduct: jest.fn().mockResolvedValue(mockResponse),
};

describe('Variables > transformers > transformProduct', () => {
  beforeEach(() => {
    productBookService.getProduct.mockClear();
  });
  test('should be a function', () => {
    expect(transformProduct).toBeInstanceOf(Function);
  });
  test('should successfully transform to english product name', async () => {
    const res = await transformProduct(productBookService, 'en', 'AFBB1');
    expect(res).toEqual('Future Amex SB B1');
  });
  test('should successfully transform to french product name', async () => {
    const res = await transformProduct(productBookService, 'fr', 'AFBB1');
    expect(res).toEqual('F-Future Amex SB B1');
  });
  test('should successfully transform to english product name when if language is unknown', async () => {
    const res = await transformProduct(productBookService, 'de', 'AFBB1');
    expect(res).toEqual('Future Amex SB B1');
  });
  test('should return original value on invalid product id', async () => {
    const product = await transformProduct(productBookService, 'en', '');
    expect(product).toEqual('');
  });
  test('should successfully transform products with three character subcodes', async () => {
    productBookService.getProduct.mockResolvedValueOnce(productBook.data.products.find(p => p.product_id === '441190'));
    const res = await transformProduct(productBookService, 'en', 'SCLINE');
    expect(res).toEqual('ScotiaLine Line of Credit');
  });
  test('should fail when product not found', async () => {
    productBookService.getProduct.mockResolvedValueOnce(undefined);
    const product = await transformProduct(productBookService, 'en', 'AFBB2');
    expect(product).toEqual('AFBB2');
  });
  test('should fail when product descriptions property is missing', async () => {
    productBookService.getProduct.mockResolvedValueOnce({ ...mockResponse, descriptions: undefined });
    try {
      await transformProduct(productBookService, 'en', 'AFBB2');
    } catch (err) {
      expect(err.message).toEqual('product description is missing');
      return Promise.resolve();
    }
    return Promise.reject(new Error('successful call'));
  });
  test('should fail when product descriptions is empty', async () => {
    productBookService.getProduct.mockResolvedValueOnce({ ...mockResponse, descriptions: [] });
    try {
      await transformProduct(productBookService, 'en', 'AFBB2');
    } catch (err) {
      expect(err.message).toEqual('product description is missing');
      return Promise.resolve();
    }
    return Promise.reject(new Error('successful call'));
  });
});
