const R = require('ramda');
const { escapeMarkdown } = require('../../helpers');

/**
 * Transforms a product code and subcode to a product name
 * @param {Object} productBookService - Product Book service
 * @param {string} language - Target language
 * @param {string} product - Product code and subcode
 * @returns {Promise<string>} - Transformed value
 */
const transformProduct = async (productBookService, language, product) => {
  if (typeof product !== 'string' || !(product.length === 5 || product.length === 6)) {
    return Promise.resolve(product);
  }
  const code = R.toUpper(product.slice(0, 3));
  const subcode = R.toUpper(product.substring(3));
  const productInfo = await productBookService.getProduct(code, subcode);
  if (!productInfo) {
    return Promise.resolve(product);
  }
  const { descriptions } = productInfo;
  if (!Array.isArray(descriptions) || descriptions.length === 0) {
    return Promise.reject(new Error('product description is missing'));
  }
  const targetLanguage = language === 'fr' ? 'fr_CA' : 'en_CA';
  const description = descriptions.find((desc) => desc.locale === targetLanguage);
  const result = R.propOr(descriptions[0].value, 'value', description);
  // return language specific value or the first occurence if language not found
  return Promise.resolve(escapeMarkdown(result));
};

module.exports = transformProduct;
