const transformGicRate = require('./index');

const mockResponse = {
  data: {
    request_id: 16267,
    gic_rates: [
      {
        code: 'MCCLTNRC',
        type: 'LTNR',
        expiry_date: '2019-04-06',
        term_rates: [
          {
            term: { length: 1, unit: 'Year', details: { from: 365, to: 545, unit: 'Day' } },
            is_recommended: true,
            step_rates: [
              {
                step_number: 0,
                frequency_rates: [
                  { interest_payment_frequency: 'Annually', is_compound: true, offer_rate: 2.55, target_rate: 2.55, branch_discretion_limit: 2.55 },
                  { interest_payment_frequency: 'Semi-Annually', is_compound: true, offer_rate: 2.425, target_rate: 2.425, branch_discretion_limit: 2.425 },
                ],
              },
            ],
          },
          {
            term: { length: 2, unit: 'Year', details: { from: 730, to: 911, unit: 'Day' } },
            is_recommended: false,
            step_rates: [
              {
                step_number: 0,
                frequency_rates: [
                  { interest_payment_frequency: 'Annually', is_compound: true, offer_rate: 2.71, target_rate: 2.71, branch_discretion_limit: 2.71 },
                  { interest_payment_frequency: 'Semi-Annually', is_compound: true, offer_rate: 2.585, target_rate: 2.585, branch_discretion_limit: 2.585 },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
};

const mockInvestmentService = {
  getGicRates: jest.fn().mockResolvedValue(mockResponse),
};

describe('Variables > transformers > transformGicSpecialRate', () => {
  beforeEach(() => {
    mockInvestmentService.getGicRates.mockClear();
  });
  test('should be a function', () => {
    expect(transformGicRate).toBeInstanceOf(Function);
  });
  test('should successfully call', async () => {
    const res = await transformGicRate(mockInvestmentService, 'env', 'token', 'plan#', 'certificate#');
    expect(res).toEqual('2.55%');
  });
  test('should not successfully call', async () => {
    mockInvestmentService.getGicRates.mockResolvedValueOnce({ data: { gic_rates: [] } });
    try {
      await transformGicRate(mockInvestmentService, 'env', 'token', 'plan#', 'certificate#');
    } catch (err) {
      expect(err.message).toEqual('special rate not found');
      return Promise.resolve();
    }
    return Promise.reject(new Error('successful call'));
  });
});
