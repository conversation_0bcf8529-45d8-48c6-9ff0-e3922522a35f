const R = require('ramda');
const {
  getGicRate,
  getTermRates,
  getStepRates,
  getFrequencyRates,
  getBranchDiscretionLimit,
} = require('../../helpers');
const { UAT_GIC } = require('../common');

const getOfferRate = R.pipe(
  getGicRate,
  getTermRates,
  R.head,
  getStepRates,
  R.head,
  getFrequencyRates,
  R.head,
  getBranchDiscretionLimit,
);

/**
 * Transforms a GIC plan and certificate number to a rate
 * @param {Object} investmentService - Investment API service
 * @param {string} env - `Preferred-Environment` header value
 * @param {string} reqHeaders - Request Headers
 * @param {string} plan - GIC plan number
 * @param {string} certificate - GIC certificate number
 * @returns {Promise<string>} - Transformed value
 */
const transformGicSpecialRate = async (investmentService, env, reqHeaders, plan, certificate) => {
  // Refer to UAT_GIC constant for more info
  const isUAT = typeof env === 'string' && env.search('uat') === 0;
  const result = await investmentService.getGicRates(env, reqHeaders, isUAT ? UAT_GIC.planNumber : plan, isUAT ? UAT_GIC.certificateNumber : certificate);
  const rate = getOfferRate(result);
  if (rate === undefined) {
    return Promise.reject(new Error('special rate not found'));
  }
  return `${Number(rate).toFixed(2)}%`;
};

module.exports = transformGicSpecialRate;
