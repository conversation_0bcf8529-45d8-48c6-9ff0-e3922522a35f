const transformReason = require('./index');

describe('Variables > transformers > transformReason', () => {
  test('should be a function', () => {
    expect(transformReason).toBeInstanceOf(Function);
  });
  test('should transform an english reason code', async () => {
    const res = await transformReason('en', 'SAM3');
    expect(res).toEqual('home address');
  });
  test('should transform a french reason code', async () => {
    const res = await transformReason('fr', 'SAM3');
    expect(res).toEqual('l\'adresse de votre résidence');
  });
  test('should fallback transform to an english', async () => {
    const res = await transformReason('de', 'SAM3');
    expect(res).toEqual('home address');
  });
  test('should fail when reason code is not found', async () => {
    try {
      await transformReason('en', 'ZZZ1');
    } catch (err) {
      expect(err.message).toEqual('reason code not found');
      return Promise.resolve();
    }
    return Promise.reject(new Error('successful call'));
  });
});
