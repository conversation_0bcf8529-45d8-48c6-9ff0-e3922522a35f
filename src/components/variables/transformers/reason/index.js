const { prop } = require('ramda');
const reasonEN = require('./reason.en.json');
const reasonFR = require('./reason.fr.json');

const languageMapping = {
  en: reasonEN,
  fr: reasonFR,
};

/**
 * Transforms a reason code to a reason message
 * @param {string} language - Target language
 * @param {string} code - Reason code
 * @returns {Promise<string>} - Transformed value
 */
const transformReason = (language, code) => {
  const resource = languageMapping[String(language)] || languageMapping.en;
  const value = prop(code, resource);
  if (!value) {
    return Promise.reject(new Error('reason code not found'));
  }
  return Promise.resolve(value);
};

module.exports = transformReason;
