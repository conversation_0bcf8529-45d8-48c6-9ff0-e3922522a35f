const transformMaskCardNumber = require('./index');

describe('Variables > transformers > transformAccount', () => {
  test('should transform an AMEX card number with 3 last digits removed', async () => {
    const res = await transformMaskCardNumber('***************');
    expect(res).toEqual('3123****8901***');
  });
  test('should transform a VISA card number with 3 last digits removed', async () => {
    const res = await transformMaskCardNumber('*************');
    expect(res).toEqual('4123****8901****');
  });
  test('should transform a VISA card number with 3 last digits removed', async () => {
    const res = await transformMaskCardNumber('*************');
    expect(res).toEqual('5123****8901****');
  });
  test('should transform a card number with 5 last digits removed', async () => {
    const res = await transformMaskCardNumber('***********');
    expect(res).toEqual('5123****890*****');
  });
  test('should return an empty string if invalid value is passed', async () => {
    const res = await transformMaskCardNumber(1);
    expect(res).toEqual('');
  });
  test('should return an empty string if an empty string is passed', async () => {
    const res = await transformMaskCardNumber('');
    expect(res).toEqual('');
  });
  test('should return an all masked if string with 16 number', async () => {
    const res = await transformMaskCardNumber('****************');
    expect(res).toEqual('1234****9012****');
  });
  test('should return the card number as is if it is 4-digit long', async () => {
    const maskedAccountNumber = await transformMaskCardNumber('4123');
    expect(maskedAccountNumber).toEqual('4123');
  });
});
