const R = require('ramda');

const amexMask = '####****####***';
const visaMcMask = '####****####****';

const isMasked = (mask, i) => mask.charAt(i) === '*';

const isAmexCard = (s) => s.charAt(0) === '3' && s.length === 15;
const isVisaMcCard = (s) => [ '4', '5' ].includes(s.charAt(0)) && s.length === 16;

const maskString = R.curry((mask, s) => {
  const masked = s.split('').map((val, i) => isMasked(mask, i) ? '*' : val).join('');
  const toAdd = mask.length - s.length;
  return toAdd > 0 ? `${masked}${Array(toAdd).fill('*').join('')}` : masked;
});

const maskCard = R.cond([
  [ isAmexCard, maskString(amexMask) ],
  [ isVisaMcCard, maskString(visaMcMask) ],
  [ R.T, maskString(visaMcMask) ],
]);

/**
 * Mask a card number by a mask pattern
 * @param {string} value - Account number
 * @returns {Promise<string>} - Transformed value
 */
const transformMaskCardNumber = async (value) => {
  if (typeof value !== 'string' || value.length === 0) {
    return '';
  // Return the last 4 digits as is for ULOC CLI campaigns: PIGEON-5045
  } else if (value.length === 4) {
    return value;
  }
  return maskCard(value);
};

module.exports = transformMaskCardNumber;
module.exports.isAmexCard = isAmexCard;
module.exports.isVisaMcCard = isVisaMcCard;
