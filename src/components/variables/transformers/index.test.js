const transformers = require('./index');

describe('Variables > transformers', () => {
  test('should have all transformers', () => {
    expect(transformers.transformAccount).toBeInstanceOf(Function);
    expect(transformers.transformMaskCardNumber).toBeInstanceOf(Function);
    expect(transformers.transformCurrency).toBeInstanceOf(Function);
    expect(transformers.transformDate).toBeInstanceOf(Function);
    expect(transformers.transformGicSpecialRate).toBeInstanceOf(Function);
    expect(transformers.transformGicSpecialTerm).toBeInstanceOf(Function);
    expect(transformers.transformGicSpecialProduct).toBeInstanceOf(Function);
    expect(transformers.transformNumber).toBeInstanceOf(Function);
    expect(transformers.transformProduct).toBeInstanceOf(Function);
    expect(transformers.transformReason).toBeInstanceOf(Function);
  });
});
