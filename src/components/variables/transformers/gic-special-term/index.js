const R = require('ramda');
const pluralize = require('pluralize');
const {
  getGicRate,
  getTermRates,
} = require('../../helpers');
const { UAT_GIC } = require('../common');

const getOfferTerm = R.pipe(
  getGicRate,
  // get `term_rates`
  getTermRates,
  // pick the first (and only) `term_rate`
  <PERSON>.head,
  <PERSON>.prop('term'),
);

const translations = {
  year: 'an',
  month: 'mois',
  months: 'mois',
};

/**
 * Transforms a GIC plan and certificate number to a term
 * @param {Object} investmentService - Investment API service
 * @param {string} env - `Preferred-Environment` header value
 * @param {string} reqHeaders - Request Headers
 * @param {string} plan - GIC plan number
 * @param {string} certificate - GIC certificate number
 * @returns {Promise<string>} - Transformed value
 */
const transformGicSpecialTerm = async (investmentService, env, reqHeaders, language, plan, certificate) => {
  // Refer to UAT_GIC constant for more info
  const isUAT = typeof env === 'string' && env.search('uat') === 0;
  const result = await investmentService.getGicRates(env, reqHeaders, isUAT ? UAT_GIC.planNumber : plan, isUAT ? UAT_GIC.certificateNumber : certificate);
  const term = getOfferTerm(result);
  if (term === undefined) {
    return Promise.reject(new Error('special term not found'));
  }
  const unit = R.toLower(term.unit) || 'year';
  return `${term.length} ${pluralize(language === 'fr' ? translations[String(unit)] : unit, term.length)}`;
};

module.exports = transformGicSpecialTerm;
