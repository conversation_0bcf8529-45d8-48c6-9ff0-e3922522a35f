const transformSpecialGicTerm = require('./index');

const mockResponse = {
  data: {
    request_id: 16267,
    gic_rates: [
      {
        code: 'MCCLTNRC',
        type: 'LTNR',
        expiry_date: '2019-04-06',
        term_rates: [
          {
            term: {
              length: 1,
              unit: 'Year',
              details: { from: 365, to: 545, unit: 'Day' },
            },
            is_recommended: true,
            step_rates: [
              {
                step_number: 0,
                frequency_rates: [
                  {
                    interest_payment_frequency: 'Annually',
                    is_compound: true,
                    offer_rate: 2.55,
                    target_rate: 2.55,
                    branch_discretion_limit: 2.55,
                  },
                  {
                    interest_payment_frequency: 'Semi-Annually',
                    is_compound: true,
                    offer_rate: 2.425,
                    target_rate: 2.425,
                    branch_discretion_limit: 2.425,
                  },
                ],
              },
            ],
          },
          {
            term: {
              length: 2,
              unit: 'Year',
              details: { from: 730, to: 911, unit: 'Day' },
            },
            is_recommended: false,
            step_rates: [
              {
                step_number: 0,
                frequency_rates: [
                  {
                    interest_payment_frequency: 'Annually',
                    is_compound: true,
                    offer_rate: 2.71,
                    target_rate: 2.71,
                    branch_discretion_limit: 2.71,
                  },
                  {
                    interest_payment_frequency: 'Semi-Annually',
                    is_compound: true,
                    offer_rate: 2.585,
                    target_rate: 2.585,
                    branch_discretion_limit: 2.585,
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
};

const mockInvestmentService = {
  getGicRates: jest.fn().mockResolvedValue(mockResponse),
};

describe('Variables > transformers > transformSpecialGicTerm', () => {
  beforeEach(() => {
    mockInvestmentService.getGicRates.mockClear();
  });
  test('should be a function', () => {
    expect(transformSpecialGicTerm).toBeInstanceOf(Function);
  });
  test('should successfully call', async () => {
    const res = await transformSpecialGicTerm(mockInvestmentService, 'environment', 'token1', 'en', 'plan', 'certificate');
    expect(res).toEqual('1 year');
    expect(mockInvestmentService.getGicRates).toBeCalledTimes(1);
  });

  test('should successfully call - fr ', async () => {
    const response = { ...mockResponse };
    response.data.gic_rates = mockResponse.data.gic_rates.map(e => ({ ...e, term_rates: [ { ...e.term_rates[0], term: { unit: '', length: 1 } } ] }));
    const mockInvestment = {
      getGicRates: jest.fn().mockResolvedValue(response),
    };
    const res = await transformSpecialGicTerm(mockInvestment, 'env', 'token2', 'fr', 'plan#', 'certificate#');
    expect(res).toEqual('1 an');
  });
  test('should not successfully call', async () => {
    mockInvestmentService.getGicRates.mockResolvedValueOnce({ data: { gic_rates: [] } });
    try {
      await transformSpecialGicTerm(mockInvestmentService, 'env', 'token3', 'plan#', 'certificate#');
    } catch (err) {
      expect(err.message).toEqual('special term not found');
      return Promise.resolve();
    }
    return Promise.reject(new Error('successful call'));
  });
});
