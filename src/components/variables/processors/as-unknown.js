const { getCampaignId, getMessageId } = require('../helpers');

/**
 * Process an unknown variable
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} variable - SOLUI variable name
 * @returns {Promise<string>} - Transformed value
 */
const processAsUnknown = ({ logger }, campaign, variable) => {
  // try to guess data variable name and get its value?
  logger.warn({ message: 'unknown SOLUI variable', variable, campaign_id: getCampaignId(campaign), message_id: getMessageId(campaign) });
  return Promise.resolve(variable);
};

module.exports = processAsUnknown;
