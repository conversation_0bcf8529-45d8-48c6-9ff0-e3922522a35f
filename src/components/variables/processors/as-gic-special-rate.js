const { transformGicSpecialRate } = require('../transformers');
const {
  escapeMarkdown,
  getAdditionalDataValue,
  getCampaignId,
  getMessageId,
} = require('../helpers');

/**
 * Process variables as a GIC special rate
 * @param {Object} deps - Dependencies
 * @param {string} env - `Preferred-Environment` header value
 * @param {string} reqHeaders - Request Headers
 * @param {Campaign} campaign - Campaign
 * @param {string} variable - SOLUI variable name
 * @param {string} planName - Plan data variable name
 * @param {string} certName - Certificate data variable name
 * @returns {Promise<String>} - Transformed value
 */
const processAsGicSpecialRate = async (deps, env, reqHeaders, campaign, variable, planName, certName) => {
  const { logger, investmentService } = deps;
  const planValue = getAdditionalDataValue(campaign, planName);
  if (planValue === undefined) {
    logger.warn({ message: 'additional value not found', variable, name: planName, campaign_id: getCampaignId(campaign), message_id: getMessageId(campaign) });
    return Promise.resolve('');
  }
  const certValue = getAdditionalDataValue(campaign, certName);
  if (certValue === undefined) {
    logger.warn({ message: 'additional value not found', variable, name: certName, campaign_id: getCampaignId(campaign), message_id: getMessageId(campaign) });
    return Promise.resolve('');
  }
  try {
    const result = await transformGicSpecialRate(investmentService, env, reqHeaders, planValue, certValue);
    return Promise.resolve(escapeMarkdown(result));
  } catch (err) {
    logger.error({ message: 'failed to get GIC special rate', err, variable, name: certName, campaign_id: getCampaignId(campaign), message_id: getMessageId(campaign) });
    return Promise.resolve('');
  }
};

module.exports = processAsGicSpecialRate;
