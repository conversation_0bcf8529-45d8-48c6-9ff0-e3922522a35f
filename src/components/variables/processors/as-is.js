const {
  escapeMarkdown,
  getAdditionalDataValue,
  getCampaignId,
  getMessageId,
} = require('../helpers');

/**
 * Process variable as is without transformation
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} variable - SOLUI variable name
 * @param {string} name - Data variable name
 * @returns {Promise<String>} - Transformed value
 */
const processAsIs = ({ logger }, campaign, variable, name) => {
  const value = getAdditionalDataValue(campaign, name);
  if (value === undefined) {
    logger.warn({ message: 'additional value not found', variable, name, campaign_id: getCampaignId(campaign), message_id: getMessageId(campaign) });
    return Promise.resolve(variable);
  }
  return Promise.resolve(escapeMarkdown(value));
};

module.exports = processAsIs;
