const processAsLocaleRate = require('./as-locale-rate');
const { formatRateByLanguage } = require('../helpers');
const mockVariable = 'SOLUI_NET_RATE';

const moduleFile = '../helpers';

jest.mock(moduleFile, () => ({
  ...jest.requireActual(moduleFile),
  formatRateByLanguage: jest.fn().mockImplementation(jest.requireActual(moduleFile).formatRateByLanguage),
}));
const logger = {
  warn: jest.fn(),
};

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'net_rate', value: '1.23' },
  ],
};

const name = 'net_rate';
const language = 'en';

describe('Variables > processors > processAsLocaleRate', () => {
  beforeEach(() => {
    logger.warn.mockClear();
  });

  test('format rate with language - correct format for fr', async () => {
    const language = 'fr';
    const formattedValue = await processAsLocaleRate({ logger }, mockCampaign, mockVariable, name, language);
    expect(formattedValue).toBe('1,23');
  });

  test('format rate with language - incorrect format for fr', async () => {
    const language = 'fr';
    const mockCampaign = {
      message_id: 'D1234567',
      campaign_id: 'ABC99',
      additional_data: [
        { name: 'net_rate', value: '1.2' },
      ],
    };
    const formattedValue = await processAsLocaleRate({ logger }, mockCampaign, mockVariable, name, language);
    expect(formattedValue).toBe(mockVariable);
  });

  test('format rate with language - correct format for en', async () => {
    const formattedValue = await processAsLocaleRate({ logger }, mockCampaign, mockVariable, name, language);
    expect(formattedValue).toBe('1.23');
  });

  test('format rate with language - incorrect format for en', async () => {
    const mockCampaign = {
      message_id: 'D1234567',
      campaign_id: 'ABC99',
      additional_data: [
        { name: 'net_rate', value: '1.2' },
      ],
    };
    const formattedValue = await processAsLocaleRate({ logger }, mockCampaign, mockVariable, name, language);
    expect(formattedValue).toBe(mockVariable);
  });

  test('test no additional data ', async () => {
    const formattedValue = await processAsLocaleRate({ logger }, { ...mockCampaign, additional_data: [] }, mockVariable, name, language);
    expect(logger.warn).toBeCalledTimes(1);
    expect(formattedValue).toEqual(mockVariable);
  });

  test('test no additional data ', async () => {
    formatRateByLanguage.mockImplementationOnce(() => { throw new Error(); });
    await processAsLocaleRate({ logger }, mockCampaign, mockVariable, name, 'fr');
    expect(logger.warn).toBeCalledTimes(1);
  });
});
