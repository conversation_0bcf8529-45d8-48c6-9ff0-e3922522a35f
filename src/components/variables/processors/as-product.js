const {
  escapeMarkdown,
  getAdditionalDataValue,
  getCampaignId,
  getMessageId,
} = require('../helpers');
const { transformProduct } = require('../transformers');

/**
 * Process variable as a product name, it takes a product code and subcode and transforms it to a localized product name
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string} variable - SOLUI variable name
 * @param {string} name - Data variable name
 * @returns {Promise<String>} - Transformed value
 */
const processAsProduct = async ({ logger, productBookService }, campaign, language, variable, name) => {
  const value = getAdditionalDataValue(campaign, name);
  if (value === undefined) {
    logger.warn({ message: 'additional value not found', variable, name, campaign_id: getCampaignId(campaign), message_id: getMessageId(campaign) });
    return Promise.resolve(variable);
  }
  try {
    return await transformProduct(productBookService, language, value);
  } catch (err) {
    logger.warn({ message: 'transformation error', err, variable, name, value, campaign_id: getCampaignId(campaign), message_id: getMessageId(campaign) });
    return Promise.resolve(escapeMarkdown(value));
  }
};

module.exports = processAsProduct;
