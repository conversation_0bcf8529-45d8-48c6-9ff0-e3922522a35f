const processAsAccountKey = require('./as-account-key');

const logger = {
  warn: jest.fn(),
  error: jest.fn(),
};

const mockResponse = {
  productSystem: 'VAX',
  currency: 'CAD',
};

const productBookService = {
  getProduct: jest.fn().mockReturnValue(mockResponse),
};

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'OTHER1', value: 'OTHER1' },
    { name: 'ACCT', value: 'ACCT' },
  ],
};

describe('Variables > processors > processAsAccountKey', () => {
  beforeEach(() => {
    logger.warn.mockClear();
  });

  test('test process as account key - success', async () => {
    const formattedValue = await processAsAccountKey({ logger, productBookService }, mockCampaign, 'en', 'x', 'ACCT', 'OTHER1');
    expect(formattedValue).toBe('VQInf3rsSYhz55bMnuOWb/EXZN6efWr05ESH/Cjo1ts=');
  });

  test('test process as account key - warn', async () => {
    const formattedValue = await processAsAccountKey({ logger, productBookService }, {
      ...mockCampaign,
      additional_data: [ { name: 'OTHER1', value: 'OTHER1' },
      ],
    }, 'en', 'x', 'ACCT', 'OTHER1');
    expect(formattedValue).toBe('ACCT');
    expect(logger.warn).toBeCalledTimes(1);
  });

  test('test process as account key', async () => {
    await processAsAccountKey({ logger, productBookService }, {
      ...mockCampaign,
      additional_data: [ { name: 'OTHER1', value: 'OTHER1' },
      ],
    }, 'en', 'x', 'ACCT', 'OTHER1');
  });

  test('test process as account key', async () => {
    productBookService.getProduct.mockImplementation(() => ({ productSystem: 'VAX' }));
    const formattedValue = await processAsAccountKey({ logger, productBookService }, mockCampaign, 'en', 'x', 'ACCT', 'OTHER1');
    expect(formattedValue).toEqual('');
  });

  test('test throw error', async () => {
    productBookService.getProduct.mockImplementation(() => { throw new Error(); });
    await processAsAccountKey({ logger, productBookService }, mockCampaign, 'en', 'x', 'ACCT', 'OTHER1');
    expect(logger.warn).toBeCalledTimes(1);
  });
});
