const processAsAccount = require('./as-account');

const mockVariable = 'SOLUI_ACCT_END';

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'OTHER1', value: '****************' },
    { name: 'OTHER2', value: '****************' },
    { name: 'OTHER3', value: 'ABC' },
  ],
};

const logger = {
  warn: jest.fn(),
};

describe('Variables > processors > processAsAccount', () => {
  beforeEach(() => {
    logger.warn.mockClear();
  });
  test('should successfully process an account', async () => {
    const res = await processAsAccount({ logger }, mockCampaign, mockVariable, 'OTHER1');
    expect(res).toEqual('\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*\\*3333');
    expect(logger.warn).toBeCalledTimes(0);
  });
  test('should successfully process an short account', async () => {
    const res = await processAsAccount({ logger }, mockCampaign, mockVariable, 'OTHER3');
    expect(res).toEqual('ABC');
    expect(logger.warn).toBeCalledTimes(0);
  });
  test('should return SOLUI variable when additional data name not found', async () => {
    const res = await processAsAccount({ logger }, mockCampaign, mockVariable, 'OTHER9');
    expect(res).toEqual(mockVariable);
    expect(logger.warn).toBeCalledTimes(1);
  });
});
