const processAsExpiryDate = require('./as-expiry-date');

const mockVariable = 'SOLUI_EXPIRYDATE_END';

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  expiry_date: '2019-12-31',
  additional_data: [],
};

const logger = {
  warn: jest.fn(),
};

describe('Variables > processors > processAsDate', () => {
  beforeEach(() => {
    logger.warn.mockClear();
  });
  test('should successfully process date', async () => {
    const res = await processAsExpiryDate({ logger }, mockCampaign, 'en', mockVariable);
    expect(res).toEqual('December 31, 2019');
    expect(logger.warn).toBeCalledTimes(0);
  });
  test('should return SOLUI variable when `expiry_date` not found', async () => {
    const res = await processAsExpiryDate({ logger }, { ...mockCampaign, expiry_date: undefined }, 'en', mockVariable);
    expect(res).toEqual(mockVariable);
    expect(logger.warn).toBeCalledTimes(1);
  });
  test('should return `expiry_date` value when value is not a valid number', async () => {
    const res = await processAsExpiryDate({ logger }, { ...mockCampaign, expiry_date: 'abc' }, 'en', mockVariable);
    expect(res).toEqual('abc');
    expect(logger.warn).toBeCalledTimes(1);
  });
});
