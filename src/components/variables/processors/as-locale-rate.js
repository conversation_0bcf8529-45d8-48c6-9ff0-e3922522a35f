const { getAdditionalDataValue, getCampaignId, getMessageId, formatRateByLanguage } = require('../helpers');

/**
 * Process variable as a locale rate
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} variable - SOLUI variable name
 * @param {string} name - Data variable name
 * @param {string} language - language
 * @returns {Promise<String>} - Transformed value
 */
const processAsLocaleRate = async ({ logger }, campaign, variable, name, language) => {
  const value = getAdditionalDataValue(campaign, name);
  const campaignId = getCampaignId(campaign);
  const messageId = getMessageId(campaign);
  if (value === undefined) {
    logger.warn({ message: 'additional value not found', variable, name, campaign_id: campaignId, message_id: messageId });
    return Promise.resolve(variable);
  }
  try {
    return formatRateByLanguage(logger, value, language, variable, name, campaignId, messageId); ;
  } catch (err) {
    logger.warn({
      message: 'transformation error',
      err: { message: err.message, stack: err.stack },
      variable,
      name,
      value,
      campaign_id: campaignId,
      message_id: messageId,
    });
    return Promise.resolve(value);
  }
};

module.exports = processAsLocaleRate;
