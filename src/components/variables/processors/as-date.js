const { getAdditionalDataValue, getCampaignId, getMessageId } = require('../helpers');
const { transformDate } = require('../transformers');
const R = require('ramda');

/**
 * Process variable as a date
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string} variable - SOLUI variable name
 * @param {string} name - Data variable name
 * @returns {Promise<String>} - Transformed value
 */
const processAsDate = async ({ logger }, campaign, language, variable, name) => {
  // grab additoinal data's expiry_date first if not grab it from the top level
  const value = getAdditionalDataValue(campaign, name) || R.prop(name, campaign);
  if (value === undefined) {
    if (R.pathOr([], [ 'additional_data' ])(campaign).some(o => o.name === name)) {
      logger.warn({ message: 'additional value was null', variable, name, campaign_id: getCampaignId(campaign), message_id: getMessageId(campaign) });
      return Promise.resolve('');
    }
    logger.warn({ message: 'additional value not found', variable, name, campaign_id: getCampaignId(campaign), message_id: getMessageId(campaign) });
    return Promise.resolve(variable);
  }
  try {
    return await transformDate(language, value);
  } catch (err) {
    logger.warn({ message: 'transformation error', err, variable, name, value, campaign_id: getCampaignId(campaign), message_id: getMessageId(campaign) });
    return Promise.resolve(value);
  }
};

module.exports = processAsDate;
