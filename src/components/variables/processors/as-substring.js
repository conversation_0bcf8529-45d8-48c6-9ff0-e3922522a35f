const {
  escapeMarkdown,
  getAdditionalDataValue,
  getCampaignId,
  getMessageId,
} = require('../helpers');

/**
 * Process variable as a substring
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} variable - SOLUI variable name
 * @param {string} name - Data variable name
 * @param {number} start - Start offset
 * @param {number} [end] - End offset, optional
 * @returns {Promise<String>} - Transformed value
 */
const processAsSubstring = ({ logger }, campaign, variable, name, start, end) => {
  const value = getAdditionalDataValue(campaign, name);
  if (value === undefined) {
    logger.warn({ message: 'additional value not found', variable, name, campaign_id: getCampaignId(campaign), message_id: getMessageId(campaign) });
    return Promise.resolve(variable);
  }
  return Promise.resolve(escapeMarkdown(value.slice(start, end)));
};

module.exports = processAsSubstring;
