const processAsUnknown = require('./as-unknown');

const mockVariable = 'SOLUI_SOMETHING_END';

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'OTHER1', value: '001000' },
    { name: 'OTHER2', value: '31337' },
    { name: 'OTHER3', value: 'abc' },
  ],
};

const logger = {
  warn: jest.fn(),
};

describe('Variables > processors > processAsUnknown', () => {
  beforeEach(() => {
    logger.warn.mockClear();
  });
  test('should successfully process as unknown', async () => {
    const res = await processAsUnknown({ logger }, mockCampaign, mockVariable);
    expect(res).toEqual(mockVariable);
    expect(logger.warn).toBeCalledTimes(1);
  });
});
