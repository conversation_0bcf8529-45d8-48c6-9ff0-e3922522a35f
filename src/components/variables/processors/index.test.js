const processors = require('./index');

describe('Variables > processors', () => {
  test('should have all functions', () => {
    expect(processors.processAsUnknown).toBeInstanceOf(Function);
    expect(processors.processAsIs).toBeInstanceOf(Function);
    expect(processors.processAsHidden).toBeInstanceOf(Function);
    expect(processors.processAsCurrency).toBeInstanceOf(Function);
    expect(processors.processAsDate).toBeInstanceOf(Function);
    expect(processors.processAsNumber).toBeInstanceOf(Function);
    expect(processors.processAsGicSpecialRate).toBeInstanceOf(Function);
    expect(processors.processAsGicSpecialTerm).toBeInstanceOf(Function);
    expect(processors.processAsGicSpecialProduct).toBeInstanceOf(Function);
    expect(processors.processAsProduct).toBeInstanceOf(Function);
    expect(processors.processAsReason).toBeInstanceOf(Function);
    expect(processors.processAsSubstring).toBeInstanceOf(Function);
    expect(processors.processAsMaskedCardNumber).toBeInstanceOf(Function);
  });
});
