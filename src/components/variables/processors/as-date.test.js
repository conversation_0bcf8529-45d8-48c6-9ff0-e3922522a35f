const processAsDate = require('./as-date');

const mockVariable = 'SOLUI_DATE2_END';

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'OTHER1', value: '' },
    { name: 'OTHER2', value: '20191231' },
    { name: 'OTHER3', value: 'abc' },
  ],
};

const logger = {
  warn: jest.fn(),
};

describe('Variables > processors > processAsDate', () => {
  beforeEach(() => {
    logger.warn.mockClear();
  });
  test('should successfully process date', async () => {
    const res = await processAsDate({ logger }, mockCampaign, 'en', mockVariable, 'OTHER2');
    expect(res).toEqual('December 31, 2019');
    expect(logger.warn).toBeCalledTimes(0);
  });
  test('should return SOLUI variable when additional data name not found', async () => {
    const res = await processAsDate({ logger }, mockCampaign, 'en', mockVariable, 'OTHER9');
    expect(res).toEqual(mockVariable);
    expect(logger.warn).toBeCalledTimes(1);
  });
  test('should return empty string when additional insights variable is empty', async () => {
    const res = await processAsDate({ logger }, mockCampaign, 'en', mockVariable, 'OTHER1');
    expect(res).toEqual('');
    expect(logger.warn).toBeCalledTimes(1);
  });
  test('should return additional value data when value is not a valid number', async () => {
    const res = await processAsDate({ logger }, mockCampaign, 'en', mockVariable, 'OTHER3');
    expect(res).toEqual('abc');
    expect(logger.warn).toBeCalledTimes(1);
  });
});
