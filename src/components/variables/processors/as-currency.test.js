const processAsCurrency = require('./as-currency');

const mockVariable = 'SOLUI_CREDIT_END';

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'OTHER1', value: '001000' },
    { name: 'OTHER2', value: '31337' },
    { name: 'OTHER3', value: 'abc' },
  ],
};

const logger = {
  warn: jest.fn(),
};

describe('Variables > processors > processAsCurrency', () => {
  beforeEach(() => {
    logger.warn.mockClear();
  });
  test('should successfully process currency', async () => {
    const res = await processAsCurrency({ logger }, mockCampaign, 'en', mockVariable, 'OTHER2');
    expect(res).toEqual('$31,337');
    expect(logger.warn).toBeCalledTimes(0);
  });
  test('should return SOLUI variable when additional data name not found', async () => {
    const res = await processAsCurrency({ logger }, mockCampaign, 'en', mockVariable, 'OTHER9');
    expect(res).toEqual(mockVariable);
    expect(logger.warn).toBeCalledTimes(1);
  });
  test('should return additional value data when value is not a valid number', async () => {
    const res = await processAsCurrency({ logger }, mockCampaign, 'en', mockVariable, 'OTHER3');
    expect(res).toEqual('abc');
    expect(logger.warn).toBeCalledTimes(1);
  });
});
