const { getCampaignId, getMessageId } = require('../helpers');
const { transformDate } = require('../transformers');
const R = require('ramda');

/**
 * Process variable as a campaign's expiration date
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string} variable - SOLUI variable name
 * @returns {Promise<String>} - Transformed value
 */
const processAsExpiryDate = async ({ logger }, campaign, language, variable) => {
  const value = R.prop('expiry_date', campaign);
  if (value === undefined) {
    logger.warn({ message: 'expiry_date value not found', variable, campaign_id: getCampaignId(campaign), message_id: getMessageId(campaign) });
    return Promise.resolve(variable);
  }
  try {
    return await transformDate(language, value);
  } catch (err) {
    logger.warn({ message: 'transformation error', err, variable, value, campaign_id: getCampaignId(campaign), message_id: getMessageId(campaign) });
    return Promise.resolve(value);
  }
};

module.exports = processAsExpiryDate;
