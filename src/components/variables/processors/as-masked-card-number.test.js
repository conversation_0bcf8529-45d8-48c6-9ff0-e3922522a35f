/* eslint-disable sonarjs/no-duplicate-string */
const processAsMaskedCardNumber = require('./as-masked-card-number');

const mockVariable = 'SOLUI_ACCT_END';

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'OTHER1', value: '300011112222334' },
    { name: 'OTHER2', value: '*************' },
    { name: 'OTHER3', value: '5000111122223' },
    { name: 'OTHER4', value: 'ABC' },
  ],
};

const logger = {
  warn: jest.fn(),
};

describe('Variables > processors > processAsCardNumber', () => {
  beforeEach(() => {
    logger.warn.mockClear();
  });
  test('should successfully process an AMEX card number', async () => {
    const res = await processAsMaskedCardNumber({ logger }, mockCampaign, mockVariable, 'OTHER1', '####****####****');
    expect(res).toEqual('3000\\*\\*\\*\\*2222\\*\\*\\*');
    expect(logger.warn).toBeCalledTimes(0);
  });
  test('should successfully process a VISA card number', async () => {
    const res = await processAsMaskedCardNumber({ logger }, mockCampaign, mockVariable, 'OTHER2', '####****####****');
    expect(res).toEqual('4000\\*\\*\\*\\*2222\\*\\*\\*\\*');
    expect(logger.warn).toBeCalledTimes(0);
  });
  test('should successfully process a Master Card card number', async () => {
    const res = await processAsMaskedCardNumber({ logger }, mockCampaign, mockVariable, 'OTHER3', '####****####****');
    expect(res).toEqual('5000\\*\\*\\*\\*2222\\*\\*\\*\\*');
    expect(logger.warn).toBeCalledTimes(0);
  });
  test('should return SOLUI variable when additional data name not found', async () => {
    const res = await processAsMaskedCardNumber({ logger }, mockCampaign, mockVariable, 'OTHER9', '####****####****');
    expect(res).toEqual(mockVariable);
    expect(logger.warn).toBeCalledTimes(1);
  });
});
