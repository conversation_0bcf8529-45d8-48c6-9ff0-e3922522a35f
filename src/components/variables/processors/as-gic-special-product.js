const { transformGicSpecialProduct } = require('../transformers');
const {
  escapeMarkdown,
  getAdditionalDataValue,
  getCampaignId,
  getMessageId,
} = require('../helpers');

/**
 * Process variables as a GIC special term
 * @param {Object} deps - Dependencies
 * @param {string} env - `Preferred-Environment` header value
 * @param {string} token - User token
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string} variable - SOLUI variable name
 * @param {string} planName - Plan data variable name
 * @param {string} certName - Certificate data variable name
 * @returns {Promise<String>} - Transformed value
 */
const processAsGicSpecialProduct = async (deps, env, token, campaign, language, variable, planName, certName) => { // NOSONAR
  const { logger, investmentService } = deps;
  const certValue = getAdditionalDataValue(campaign, certName);
  if (certValue === undefined) {
    logger.warn({ message: 'additional value not found', variable, name: certName, campaign_id: getCampaignId(campaign), message_id: getMessageId(campaign) });
    return Promise.resolve(variable);
  }
  const planValue = getAdditionalDataValue(campaign, planName);
  if (planValue === undefined) {
    logger.warn({ message: 'additional value not found', variable, name: planName, campaign_id: getCampaignId(campaign), message_id: getMessageId(campaign) });
    return Promise.resolve(variable);
  }
  const result = await transformGicSpecialProduct(investmentService, env, token, language, planValue, certValue);
  return Promise.resolve(escapeMarkdown(result));
};

module.exports = processAsGicSpecialProduct;
