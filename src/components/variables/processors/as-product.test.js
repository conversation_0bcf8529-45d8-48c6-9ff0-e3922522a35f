const processAsProduct = require('./as-product');

const mockVariable = 'SOLUI_PROD_END';

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'OTHER1', value: 'AFBB1' },
    { name: 'OTHER2', value: '31337' },
    { name: 'OTHER3', value: 'abc' },
  ],
};

const logger = {
  warn: jest.fn(),
};

const mockResponse = {
  productId: '580040',
  productTypes: [
    {
      productDomain: 'default',
      productCode: 'AFB',
      subProductCode: 'B1',
    },
    {
      productDomain: 'KT',
      productCode: 'VAX',
      subProductCode: 'B1',
    },
  ],
  productSystem: 'VAX',
  currency: 'CAD',
  ownership: 'B',
  properties: [
    { type: 'CATEGORY', value: 'CREDITCARDS' },
    { type: 'DEFAULT_CLASSIFICATION', value: 'Amex' },
    { type: 'DETAIL_CLASSIFICATION', value: 'Amex' },
    { type: 'REL_ACTIVITY_CLASSIFICATION', value: 'BusinessAmex' },
    { type: 'CORNERSTONE_ID', value: 'Borrowing' },
    { type: 'ASSET_LIABILITY', value: 'L' },
  ],
  sortWeight: '4000',
  accountNumberLength: 15,
  accountNumberMask: '####*****######',
  descriptions: [
    { locale: 'en_CA', value: 'Future Amex SB B1' },
    { locale: 'fr_CA', value: 'F-Future Amex SB B1' },
  ],
};

const productBookService = {
  getProduct: jest.fn().mockResolvedValue(mockResponse),
};

describe('Variables > processors > processAsProduct', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    productBookService.getProduct.mockClear();
  });
  test('should successfully process product', async () => {
    const res = await processAsProduct({ logger, productBookService }, mockCampaign, 'en', mockVariable, 'OTHER1');
    expect(res).toEqual('Future Amex SB B1');
    expect(logger.warn).toBeCalledTimes(0);
    expect(productBookService.getProduct).toBeCalledTimes(1);
  });
  test('should return SOLUI variable when additional data name not found', async () => {
    const res = await processAsProduct({ logger, productBookService }, mockCampaign, 'en', mockVariable, 'OTHER9');
    expect(res).toEqual(mockVariable);
    expect(logger.warn).toBeCalledTimes(1);
    expect(productBookService.getProduct).toBeCalledTimes(0);
  });
  test('should return additional data value when product not found', async () => {
    productBookService.getProduct.mockResolvedValueOnce(undefined);
    const res = await processAsProduct({ logger, productBookService }, mockCampaign, 'en', mockVariable, 'OTHER1');
    expect(res).toEqual('AFBB1');
    expect(productBookService.getProduct).toBeCalledTimes(1);
  });

  test('should return run logger warn', async () => {
    productBookService.getProduct.mockResolvedValueOnce({ ...mockResponse, descriptions: [] });
    await processAsProduct({ logger, productBookService }, mockCampaign, 'en', mockVariable, 'OTHER1');
    expect(logger.warn).toBeCalledTimes(1);
    expect(productBookService.getProduct).toBeCalledTimes(1);
  });
});
