const processAsReason = require('./as-reason');

const mockVariable = 'SOLUI_REASON_END';

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'OTHER1', value: 'SAM3' },
    { name: 'OTHER2', value: 'ABC1' },
    { name: 'OTHER3', value: 1 },
  ],
};

const logger = {
  warn: jest.fn(),
};

describe('Variables > processors > processAsReason', () => {
  beforeEach(() => {
    logger.warn.mockClear();
  });
  test('should successfully process a reson code', async () => {
    const res = await processAsReason({ logger }, mockCampaign, 'en', mockVariable, 'OTHER1');
    expect(res).toEqual('home address');
    expect(logger.warn).toBeCalledTimes(0);
  });
  test('should return SOLUI variable when additional data name not found', async () => {
    const res = await processAsReason({ logger }, mockCampaign, 'en', mockVariable, 'OTHER9');
    expect(res).toEqual(mockVariable);
    expect(logger.warn).toBeCalledTimes(1);
  });
  test('should return additional data value when reson not found', async () => {
    const res = await processAsReason({ logger }, mockCampaign, 'en', mockVariable, 'OTHER2');
    expect(res).toEqual('ABC1');
    expect(logger.warn).toBeCalledTimes(1);
  });
});
