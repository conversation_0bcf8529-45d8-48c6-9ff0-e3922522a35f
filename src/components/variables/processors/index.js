const processAsUnknown = require('./as-unknown');
const processAsIs = require('./as-is');
const processAsHidden = require('./as-hidden');
const processAsAccount = require('./as-account');
const processAsCurrency = require('./as-currency');
const processAsDate = require('./as-date');
const processAsNumber = require('./as-number');
const processAsGicSpecialRate = require('./as-gic-special-rate');
const processAsGicSpecialTerm = require('./as-gic-special-term');
const processAsGicSpecialProduct = require('./as-gic-special-product');
const processAsProduct = require('./as-product');
const processAsReason = require('./as-reason');
const processAsSubstring = require('./as-substring');
const processAsExpiryDate = require('./as-expiry-date');
const processAsMaskedCardNumber = require('./as-masked-card-number');
const processAsAccountKey = require('./as-account-key');
const processAsLocaleRate = require('./as-locale-rate');

module.exports = {
  processAsUnknown,
  processAsIs,
  processAsHidden,
  processAsAccount,
  processAsCurrency,
  processAsDate,
  processAsNumber,
  processAsGicSpecialRate,
  processAsGicSpecialTerm,
  processAsGicSpecialProduct,
  processAsProduct,
  processAsReason,
  processAsSubstring,
  processAsExpiryDate,
  processAsMaskedCardNumber,
  processAsAccountKey,
  processAsLocaleRate,
};
