const { getAdditionalDataValue, getCampaignId, getMessageId } = require('../helpers');
const { transformNumber } = require('../transformers');

/**
 * Process variable as a number
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} variable - SOLUI variable name
 * @param {string} name - Data variable name
 * @returns {Promise<String>} - Transformed value
 */
const processAsNumber = async ({ logger }, campaign, variable, name) => {
  const value = getAdditionalDataValue(campaign, name);
  if (value === undefined) {
    logger.warn({ message: 'additional value not found', variable, name, campaign_id: getCampaignId(campaign), message_id: getMessageId(campaign) });
    return Promise.resolve(variable);
  }
  try {
    return await transformNumber(value);
  } catch (err) {
    logger.warn({ message: 'transformation error', err, variable, name, value, campaign_id: getCampaignId(campaign), message_id: getMessageId(campaign) });
    return Promise.resolve(value);
  }
};

module.exports = processAsNumber;
