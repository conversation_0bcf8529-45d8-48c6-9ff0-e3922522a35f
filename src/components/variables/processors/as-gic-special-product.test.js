const processAsGicSpecialProduct = require('./as-gic-special-product');

const mockVariable = 'SOLUI_SPECIAL_RATE_END';

const mockInvestmentResponse = {
  data: {
    request_id: 16267,
    gic_rates: [
      {
        code: 'MCCLTNRC',
        type: 'LTNR',
        expiry_date: '2019-04-06',
        term_rates: [
          {
            term: { length: 1, unit: 'Year', details: { from: 365, to: 545, unit: 'Day' } },
            is_recommended: true,
            step_rates: [
              {
                step_number: 0,
                frequency_rates: [
                  { interest_payment_frequency: 'Annually', is_compound: true, offer_rate: 2.55, target_rate: 2.55, branch_discretion_limit: 2.55 },
                  { interest_payment_frequency: 'Semi-Annually', is_compound: true, offer_rate: 2.425, target_rate: 2.425, branch_discretion_limit: 2.425 },
                ],
              },
            ],
          },
          {
            term: { length: 2, unit: 'Year', details: { from: 730, to: 911, unit: 'Day' } },
            is_recommended: false,
            step_rates: [
              {
                step_number: 0,
                frequency_rates: [
                  { interest_payment_frequency: 'Annually', is_compound: true, offer_rate: 2.71, target_rate: 2.71, branch_discretion_limit: 2.71 },
                  { interest_payment_frequency: 'Semi-Annually', is_compound: true, offer_rate: 2.585, target_rate: 2.585, branch_discretion_limit: 2.585 },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
};

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'OTHER1', value: 'something' },
    { name: 'OTHER2', value: 'plan#' },
    { name: 'OTHER3', value: 'certificate#' },
  ],
};

const logger = {
  warn: jest.fn(),
};

const investmentService = {
  getGicRates: jest.fn().mockResolvedValue(mockInvestmentResponse),
};

describe('Variables > processors > processAsGicSpecialProduct', () => {
  beforeEach(() => {
    logger.warn.mockClear();
    investmentService.getGicRates.mockClear();
  });
  test('should successfully process gic special product in english', async () => {
    const res = await processAsGicSpecialProduct({ logger, investmentService }, 'env', 'token', mockCampaign, 'en', mockVariable, 'OTHER2', 'OTHER3');
    expect(res).toEqual('Long Term Non-Redeemable GIC');
    expect(logger.warn).toBeCalledTimes(0);
    expect(investmentService.getGicRates).toBeCalledTimes(0);
  });
  test('should successfully process gic special product in french', async () => {
    const res = await processAsGicSpecialProduct({ logger, investmentService }, 'env', 'token', mockCampaign, 'fr', mockVariable, 'OTHER2', 'OTHER3');
    expect(res).toEqual('Long Term Non-Redeemable GIC');
    expect(logger.warn).toBeCalledTimes(0);
    expect(investmentService.getGicRates).toBeCalledTimes(0);
  });
  test('should return SOLUI variable when first additional data name not found', async () => {
    const res = await processAsGicSpecialProduct({ logger, investmentService }, 'env', 'token', mockCampaign, 'en', mockVariable, 'OTHER2', 'OTHER9');
    expect(res).toEqual(mockVariable);
    expect(logger.warn).toBeCalledTimes(1);
    expect(investmentService.getGicRates).toBeCalledTimes(0);
  });
  test('should return SOLUI variable when first additional data name not found', async () => {
    const res = await processAsGicSpecialProduct({ logger, investmentService }, 'env', 'token', mockCampaign, 'en', mockVariable, 'OTHER9', 'OTHER3');
    expect(res).toEqual(mockVariable);
    expect(logger.warn).toBeCalledTimes(1);
    expect(investmentService.getGicRates).toBeCalledTimes(0);
  });
});
