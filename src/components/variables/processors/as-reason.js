const {
  escapeMarkdown,
  getAdditionalDataValue,
  getCampaignId,
  getMessageId,
} = require('../helpers');
const { transformReason } = require('../transformers');

/**
 * Process variable as a reason code
 * @param {Object} deps - Dependencies
 * @param {Campaign} campaign - Campaign
 * @param {string} language - Target language
 * @param {string} variable - SOLUI variable name
 * @param {string} name - Data variable name
 * @returns {Promise<String>} - Transformed value
 */
const processAsReason = async ({ logger }, campaign, language, variable, name) => {
  const value = getAdditionalDataValue(campaign, name);
  if (value === undefined) {
    logger.warn({ message: 'additional value not found', variable, name, campaign_id: getCampaignId(campaign), message_id: getMessageId(campaign) });
    return Promise.resolve(variable);
  }
  try {
    return await transformReason(language, value);
  } catch (err) {
    logger.warn({ message: 'transformation error', err, variable, name, value, campaign_id: getCampaignId(campaign), message_id: getMessageId(campaign) });
    return Promise.resolve(escapeMarkdown(value));
  }
};

module.exports = processAsReason;
