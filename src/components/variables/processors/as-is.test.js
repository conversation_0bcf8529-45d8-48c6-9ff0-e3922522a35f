const processAsIs = require('./as-is');

const mockVariable = 'SOLUI_CREDIT_END';

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'OTHER1', value: '001000' },
    { name: 'OTHER2', value: '31337' },
    { name: 'OTHER3', value: 'abc' },
  ],
};

const logger = {
  warn: jest.fn(),
};

describe('Variables > processors > processAsIs', () => {
  beforeEach(() => {
    logger.warn.mockClear();
  });
  test('should successfully process currency', async () => {
    const res = await processAsIs({ logger }, mockCampaign, mockVariable, 'OTHER2');
    expect(res).toEqual('31337');
    expect(logger.warn).toBeCalledTimes(0);
  });
  test('should return SOLUI variable when additional data name not found', async () => {
    const res = await processAsIs({ logger }, mockCampaign, mockVariable, 'OTHER9');
    expect(res).toEqual(mockVariable);
    expect(logger.warn).toBeCalledTimes(1);
  });
});
