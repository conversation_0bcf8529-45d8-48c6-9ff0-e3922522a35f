const {
  escapeMarkdown,
  getAdditionalDataValue,
  getCampaignId,
  getMessageId,
} = require('../helpers');
const { getKeyHash } = require('../../../components/cache/crypto');

/**
   * Process variable as a account key, it takes a product code and subcode and generates a account key
   * @param {Object} deps - Dependencies
   * @param {Campaign} campaign - Campaign
   * @param {string} language - Target language
   * @param {string} variable - SOLUI variable name
   * @param {string} variable1 - Data variable name
   *  * @param {string} variable2 - Data variable name
   * @returns {Promise<String>} - Transformed value
   */
const processAsAccountKey = async ({ logger, productBookService }, campaign, language, x, variable1, variable2) => {
  const accountNumber = getAdditionalDataValue(campaign, variable1);
  const productCode = getAdditionalDataValue(campaign, variable2).slice(0, 3);
  const subProductCode = getAdditionalDataValue(campaign, variable2).substr(3);

  if (!accountNumber || !productCode || !subProductCode) {
    logger.warn({ message: 'additional value not found', campaign_id: getCampaignId(campaign), message_id: getMessageId(campaign) });
    return Promise.resolve(variable1);
  }
  try {
    // get productSystem & currency
    const { productSystem, currency } = productBookService.getProduct(productCode, subProductCode);
    if (productSystem && accountNumber && currency) {
      return getKeyHash(productSystem + accountNumber + currency);
    }
    return '';
  } catch (err) {
    logger.warn({ message: 'transformation error', err, campaign_id: getCampaignId(campaign), message_id: getMessageId(campaign) });
    return Promise.resolve(escapeMarkdown(accountNumber));
  }
};

module.exports = processAsAccountKey;
