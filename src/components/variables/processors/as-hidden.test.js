const processAsHidden = require('./as-hidden');

const mockVariable = 'SOLUI_CREDIT_END';

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'OTHER1', value: '001000' },
    { name: 'OTHER2', value: '31337' },
    { name: 'OTHER3', value: 'abc' },
  ],
};

const logger = {
  warn: jest.fn(),
};

describe('Variables > processors > processAsIs', () => {
  beforeEach(() => {
    logger.warn.mockClear();
  });
  test('should successfully replace with an empty string', async () => {
    const res = await processAsHidden({ logger }, mockCampaign, mockVariable, 'OTHER2');
    expect(res).toEqual('');
  });
});
