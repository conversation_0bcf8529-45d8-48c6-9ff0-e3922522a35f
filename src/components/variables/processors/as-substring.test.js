const processAsSubstring = require('./as-substring');

const mockVariable = 'SOLUI_OTHER1_END';

const mockCampaign = {
  message_id: 'D1234567',
  campaign_id: 'ABC99',
  additional_data: [
    { name: 'OTHER1', value: 'F5YC-3.67%' },
    { name: 'OTHER2', value: '' },
    { name: 'OTHER3', value: 'abc' },
  ],
};

const logger = {
  warn: jest.fn(),
};

describe('Variables > processors > processAsSubstring', () => {
  beforeEach(() => {
    logger.warn.mockClear();
  });
  test('should successfully process substring', async () => {
    const res = await processAsSubstring({ logger }, mockCampaign, mockVariable, 'OTHER1', 1, 2);
    expect(res).toEqual('5');
    expect(logger.warn).toBeCalledTimes(0);
  });
  test('should successfully process substring with only start', async () => {
    const res = await processAsSubstring({ logger }, mockCampaign, mockVariable, 'OTHER1', -5);
    expect(res).toEqual('3.67%');
    expect(logger.warn).toBeCalledTimes(0);
  });
  test('should return SOLUI variable when additional data name not found', async () => {
    const res = await processAsSubstring({ logger }, mockCampaign, mockVariable, 'OTHER9', 1, 2);
    expect(res).toEqual(mockVariable);
    expect(logger.warn).toBeCalledTimes(1);
  });
});
