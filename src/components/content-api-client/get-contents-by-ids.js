const qs = require('querystring');
const { getResponse } = require('../common');

const getContentsByIds = async ({ fetch }, basePath, space, ids = [], opts = {}) => {
  // TODO: ids.unique
  const query = {
    ...opts,
    content_ids: [ ...new Set(ids) ].join(','), // Set only gets distinct values
  };
  const result = await fetch(`${basePath}/contents/spaces/${space}/contents${Object.keys(query).length > 0 ? '?' + qs.stringify(query) : ''}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json; charset=utf-8',
      Accept: 'application/json',
    },
  });
  return getResponse(result);
};

module.exports = getContentsByIds;
