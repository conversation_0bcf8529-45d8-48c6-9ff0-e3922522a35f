const qs = require('querystring');
const { getResponse } = require('../common');

const getContentByTypeAndId = async ({ fetch }, basePath, space, type, id, opts = {}) => {
  const query = {
    ...opts,
    language: opts.language === 'en' ? 'en-US' : opts.language,
  };
  const result = await fetch(`${basePath}/contents/spaces/${space}/types/${type}/contents/${id}${Object.keys(query).length > 0 ? '?' + qs.stringify(query) : ''}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json; charset=utf-8',
      Accept: 'application/json',
    },
  });
  return getResponse(result);
};

module.exports = getContentByTypeAndId;
