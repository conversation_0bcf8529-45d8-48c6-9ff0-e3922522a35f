const getContentByTypeAndId = require('./get-content-by-type-id');
const getContentsByIds = require('./get-contents-by-ids');
const getVignetteUpdatedFragments = require('./get-vignette-updated-fragments');
const getVignetteContentById = require('./get-vignette-content-by-id');

const init = ({ fetch }, basePath) => {
  return {
    getContentByTypeAndId: (space, type, id, opts) => getContentByTypeAndId({ fetch }, basePath, space, type, id, opts),
    getContentsByIds: (space, ids, opts) => getContentsByIds({ fetch }, basePath, space, ids, opts),
    getVignetteUpdatedFragments: (query) => getVignetteUpdatedFragments({ fetch }, basePath, query),
    getVignetteContentById: (id) => getVignetteContentById({ fetch }, basePath, id),
  };
};

module.exports = init;
