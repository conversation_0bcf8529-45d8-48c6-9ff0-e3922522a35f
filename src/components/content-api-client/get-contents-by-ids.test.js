const getContentsByIds = require('./get-contents-by-ids');
const { getResponse } = require('../common');

jest.mock('../common', () => ({
  getResponse: jest.fn(),
}));

describe('getContentsByIds', () => {
  let mockFetch;
  const basePath = 'https://content-api-test.nonp.atlas.bns';
  const space = 'test-space';
  const requestOpts = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json; charset=utf-8',
      Accept: 'application/json',
    },
  };

  beforeEach(() => {
    mockFetch = jest.fn();
    getResponse.mockClear();
  });

  describe('successful requests', () => {
    test('should fetch contents with minimal parameters', async () => {
      const ids = [ 'content-1', 'content-2' ];
      const mockResponse = { status: 200, data: [ { id: 'content-1' }, { id: 'content-2' } ] };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      const result = await getContentsByIds({ fetch: mockFetch }, basePath, space, ids);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/contents?content_ids=content-1%2Ccontent-2`,
        requestOpts,
      );
      expect(getResponse).toHaveBeenCalledWith(mockResult);
      expect(result).toEqual(mockResponse);
    });

    test('should fetch contents with options', async () => {
      const ids = [ 'content-1', 'content-2', 'content-3' ];
      const mockResponse = { status: 200, data: [ { id: 'content-1' }, { id: 'content-2' } ] };
      const mockResult = { status: 200 };
      const opts = {
        select: 'title,description',
        include: 'author',
        language: 'en-US',
      };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      const result = await getContentsByIds({ fetch: mockFetch }, basePath, space, ids, opts);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/contents?select=title%2Cdescription&include=author&language=en-US&content_ids=content-1%2Ccontent-2%2Ccontent-3`,
        requestOpts,
      );
      expect(result).toEqual(mockResponse);
    });

    test('should handle empty options object', async () => {
      const ids = [ 'content-1' ];
      const mockResponse = { status: 200, data: [ { id: 'content-1' } ] };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      await getContentsByIds({ fetch: mockFetch }, basePath, space, ids, {});

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/contents?content_ids=content-1`,
        expect.any(Object),
      );
    });

    test('should handle single content ID', async () => {
      const ids = [ 'single-content' ];
      const mockResponse = { status: 200, data: [ { id: 'single-content' } ] };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      await getContentsByIds({ fetch: mockFetch }, basePath, space, ids);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/contents?content_ids=single-content`,
        requestOpts,
      );
    });
  });

  describe('content IDs handling', () => {
    test('should handle empty IDs array', async () => {
      const ids = [];
      const mockResponse = { status: 200, data: [] };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      await getContentsByIds({ fetch: mockFetch }, basePath, space, ids);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/contents?content_ids=`,
        requestOpts,
      );
    });

    test('should handle default empty IDs array when not provided', async () => {
      const mockResponse = { status: 200, data: [] };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      await getContentsByIds({ fetch: mockFetch }, basePath, space);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/contents?content_ids=`,
        requestOpts,
      );
    });

    test('should remove duplicate IDs using Set', async () => {
      const ids = [ 'content-1', 'content-2', 'content-1', 'content-3', 'content-2' ];
      const mockResponse = { status: 200, data: [] };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      await getContentsByIds({ fetch: mockFetch }, basePath, space, ids);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/contents?content_ids=content-1%2Ccontent-2%2Ccontent-3`,
        requestOpts,
      );
    });

    test('should handle IDs with special characters', async () => {
      const ids = [ 'content-with-dashes', 'content_with_underscores', 'content123' ];
      const mockResponse = { status: 200, data: [] };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      await getContentsByIds({ fetch: mockFetch }, basePath, space, ids);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/contents?content_ids=content-with-dashes%2Ccontent_with_underscores%2Ccontent123`,
        requestOpts,
      );
    });
  });

  describe('query parameter encoding', () => {
    test('should properly encode special characters in query parameters', async () => {
      const ids = [ 'content-1', 'content-2' ];
      const mockResponse = { status: 200, data: [] };
      const mockResult = { status: 200 };
      const opts = {
        filter: 'category=news&status=published',
        sort: 'created_at+desc',
      };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      await getContentsByIds({ fetch: mockFetch }, basePath, space, ids, opts);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/contents?filter=category%3Dnews%26status%3Dpublished&sort=created_at%2Bdesc&content_ids=content-1%2Ccontent-2`,
        expect.any(Object),
      );
    });

    test('should handle boolean and numeric values', async () => {
      const ids = [ 'content-1' ];
      const mockResponse = { status: 200, data: [] };
      const mockResult = { status: 200 };
      const opts = {
        draft: true,
        limit: 10,
        archived: false,
      };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      await getContentsByIds({ fetch: mockFetch }, basePath, space, ids, opts);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/contents?draft=true&limit=10&archived=false&content_ids=content-1`,
        expect.any(Object),
      );
    });

    test('should handle language parameter', async () => {
      const ids = [ 'content-1', 'content-2' ];
      const mockResponse = { status: 200, data: [] };
      const mockResult = { status: 200 };
      const opts = {
        language: 'fr',
        select: 'title',
      };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      await getContentsByIds({ fetch: mockFetch }, basePath, space, ids, opts);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/contents?language=fr&select=title&content_ids=content-1%2Ccontent-2`,
        expect.any(Object),
      );
    });
  });

  describe('URL construction', () => {
    test('should construct URL without query string when no IDs and no options', async () => {
      const mockResponse = { status: 200, data: [] };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      await getContentsByIds({ fetch: mockFetch }, basePath, space, [], {});

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/contents?content_ids=`,
        expect.any(Object),
      );
    });

    test('should handle special characters in space parameter', async () => {
      const ids = [ 'content-1' ];
      const mockResponse = { status: 200, data: [] };
      const mockResult = { status: 200 };
      const specialSpace = 'space-with-dashes';

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      await getContentsByIds({ fetch: mockFetch }, basePath, specialSpace, ids);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${specialSpace}/contents?content_ids=content-1`,
        expect.any(Object),
      );
    });

    test('should handle large number of content IDs', async () => {
      const ids = Array.from({ length: 50 }, (_, i) => `content-${i}`);
      const mockResponse = { status: 200, data: [] };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      await getContentsByIds({ fetch: mockFetch }, basePath, space, ids);

      const expectedIds = ids.join('%2C');
      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/contents?content_ids=${expectedIds}`,
        expect.any(Object),
      );
    });
  });

  describe('error handling', () => {
    test('should handle fetch errors', async () => {
      const ids = [ 'content-1' ];
      const fetchError = new Error('Network error');
      mockFetch.mockRejectedValue(fetchError);

      await expect(
        getContentsByIds({ fetch: mockFetch }, basePath, space, ids),
      ).rejects.toThrow('Network error');
    });

    test('should handle getResponse errors', async () => {
      const ids = [ 'content-1' ];
      const mockResult = { status: 500 };
      const responseError = new Error('Response processing error');

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockImplementation(() => {
        throw responseError;
      });

      await expect(
        getContentsByIds({ fetch: mockFetch }, basePath, space, ids),
      ).rejects.toThrow('Response processing error');
    });

    test('should handle undefined fetch function', async () => {
      const ids = [ 'content-1' ];

      await expect(
        getContentsByIds({}, basePath, space, ids),
      ).rejects.toThrow();
    });
  });

  describe('request headers', () => {
    test('should use correct headers for all requests', async () => {
      const ids = [ 'content-1' ];
      const mockResponse = { status: 200, data: [] };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      await getContentsByIds({ fetch: mockFetch }, basePath, space, ids);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        requestOpts,
      );
    });
  });

  describe('edge cases', () => {
    test('should handle null basePath', async () => {
      const ids = [ 'content-1' ];
      const mockResult = { status: 200 };
      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue({ data: [] });

      await getContentsByIds({ fetch: mockFetch }, null, space, ids);

      expect(mockFetch).toHaveBeenCalledWith(
        `null/contents/spaces/${space}/contents?content_ids=content-1`,
        expect.any(Object),
      );
    });

    test('should handle empty string space parameter', async () => {
      const ids = [ 'content-1' ];
      const mockResult = { status: 200 };
      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue({ data: [] });

      await getContentsByIds({ fetch: mockFetch }, basePath, '', ids);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces//contents?content_ids=content-1`,
        expect.any(Object),
      );
    });

    test('should handle null or undefined IDs array', async () => {
      const mockResult = { status: 200 };
      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue({ data: [] });

      await getContentsByIds({ fetch: mockFetch }, basePath, space, null);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/contents?content_ids=`,
        expect.any(Object),
      );
    });

    test('should handle IDs array with empty strings', async () => {
      const ids = [ 'content-1', '', 'content-2', '' ];
      const mockResult = { status: 200 };
      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue({ data: [] });

      await getContentsByIds({ fetch: mockFetch }, basePath, space, ids);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/contents?content_ids=content-1%2C%2Ccontent-2`,
        requestOpts,
      );
    });
  });
});
