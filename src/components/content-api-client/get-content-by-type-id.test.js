const getContentByTypeAndId = require('./get-content-by-type-id');
const { getResponse } = require('../common');

jest.mock('../common', () => ({
  getResponse: jest.fn(),
}));

describe('getContentByTypeAndId', () => {
  let mockFetch;
  const basePath = 'https://content-api-test.nonp.atlas.bns';
  const space = 'test-space';
  const type = 'article';
  const id = 'content-123';
  const requestOpts = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json; charset=utf-8',
      Accept: 'application/json',
    },
  };

  beforeEach(() => {
    mockFetch = jest.fn();
    getResponse.mockClear();
  });

  describe('successful requests', () => {
    test('should fetch content with minimal parameters', async () => {
      const mockResponse = { status: 200, data: { id, title: 'Test Content' } };
      const mockResult = { status: 200 };
      const opts = { language: 'en' };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      const result = await getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id, opts);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/types/${type}/contents/${id}?language=en-US`,
        requestOpts,
      );
      expect(getResponse).toHaveBeenCalledWith(mockResult);
      expect(result).toEqual(mockResponse);
    });

    test('should fetch content with options', async () => {
      const mockResponse = { status: 200, data: { id, title: 'Test Content' } };
      const mockResult = { status: 200 };
      const opts = {
        select: 'title,description',
        include: 'author',
        version: '2',
        language: 'en',
      };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      const result = await getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id, opts);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/types/${type}/contents/${id}?select=title%2Cdescription&include=author&version=2&language=en-US`,
        requestOpts,
      );
      expect(result).toEqual(mockResponse);
    });

    test('should handle empty options object', async () => {
      const mockResponse = { status: 200, data: { id } };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      await getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id, {});

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/types/${type}/contents/${id}?language=`,
        expect.any(Object),
      );
    });
  });

  describe('language handling', () => {
    test('should transform "en" to "en-US"', async () => {
      const mockResponse = { status: 200, data: { id } };
      const mockResult = { status: 200 };
      const opts = { language: 'en' };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      await getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id, opts);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/types/${type}/contents/${id}?language=en-US`,
        expect.any(Object),
      );
    });

    test('should preserve other language codes unchanged', async () => {
      const mockResponse = { status: 200, data: { id } };
      const mockResult = { status: 200 };
      const testCases = [ 'fr', 'de', 'es-ES', 'zh-CN', 'en-GB' ];

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      for (const language of testCases) {
        mockFetch.mockClear();

        await getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id, { language });

        expect(mockFetch).toHaveBeenCalledWith(
          `${basePath}/contents/spaces/${space}/types/${type}/contents/${id}?language=${encodeURIComponent(language)}`,
          expect.any(Object),
        );
      }
    });

    test('should handle undefined language', async () => {
      const mockResponse = { status: 200, data: { id } };
      const mockResult = { status: 200 };
      const opts = { select: 'title' };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      await getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id, opts);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/types/${type}/contents/${id}?select=title&language=`,
        expect.any(Object),
      );
    });
  });

  describe('query parameter encoding', () => {
    test('should properly encode special characters in query parameters', async () => {
      const mockResponse = { status: 200, data: { id } };
      const mockResult = { status: 200 };
      const opts = {
        filter: 'category=news&status=published',
        sort: 'created_at+desc',
      };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      await getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id, opts);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/types/${type}/contents/${id}?filter=category%3Dnews%26status%3Dpublished&sort=created_at%2Bdesc&language=`,
        expect.any(Object),
      );
    });

    test('should handle boolean and numeric values', async () => {
      const mockResponse = { status: 200, data: { id } };
      const mockResult = { status: 200 };
      const opts = {
        draft: true,
        limit: 10,
        archived: false,
      };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      await getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id, opts);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/types/${type}/contents/${id}?draft=true&limit=10&archived=false&language=`,
        expect.any(Object),
      );
    });
  });

  describe('URL construction', () => {
    test('should construct URL without query string when no options', async () => {
      const mockResponse = { status: 200, data: { id } };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      await getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/types/${type}/contents/${id}?language=`,
        expect.any(Object),
      );
    });

    test('should handle special characters in path parameters', async () => {
      const mockResponse = { status: 200, data: { id } };
      const mockResult = { status: 200 };
      const specialSpace = 'space-with-dashes';
      const specialType = 'blog_post';
      const specialId = 'content-123-abc';

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      await getContentByTypeAndId({ fetch: mockFetch }, basePath, specialSpace, specialType, specialId);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${specialSpace}/types/${specialType}/contents/${specialId}?language=`,
        expect.any(Object),
      );
    });
  });

  describe('error handling', () => {
    test('should handle fetch errors', async () => {
      const fetchError = new Error('Network error');
      mockFetch.mockRejectedValue(fetchError);

      await expect(
        getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id),
      ).rejects.toThrow('Network error');
    });

    test('should handle getResponse errors', async () => {
      const mockResult = { status: 500 };
      const responseError = new Error('Response processing error');

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockImplementation(() => {
        throw responseError;
      });

      await expect(
        getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id),
      ).rejects.toThrow('Response processing error');
    });

    test('should handle undefined fetch function', async () => {
      await expect(
        getContentByTypeAndId({}, basePath, space, type, id),
      ).rejects.toThrow();
    });
  });

  describe('request headers', () => {
    test('should use correct headers for all requests', async () => {
      const mockResponse = { status: 200, data: { id } };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      await getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id);

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        requestOpts,
      );
    });
  });

  describe('edge cases', () => {
    test('should handle null basePath', async () => {
      const mockResult = { status: 200 };
      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue({ data: {} });

      await getContentByTypeAndId({ fetch: mockFetch }, null, space, type, id);

      expect(mockFetch).toHaveBeenCalledWith(
        `null/contents/spaces/${space}/types/${type}/contents/${id}?language=`,
        expect.any(Object),
      );
    });

    test('should handle empty string parameters', async () => {
      const mockResult = { status: 200 };
      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue({ data: {} });

      await getContentByTypeAndId({ fetch: mockFetch }, basePath, '', '', '');

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces//types//contents/?language=`,
        expect.any(Object),
      );
    });
  });
  describe('query parameter construction branch coverage', () => {
    test('should construct URL without query string when opts is undefined', async () => {
      const mockResponse = { status: 200, data: { id } };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      // Call without opts parameter to test default parameter
      await getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/types/${type}/contents/${id}?language=`,
        requestOpts,
      );
      expect(getResponse).toHaveBeenCalledWith(mockResult);
    });

    test('should construct URL without query string when opts is empty and no query keys', async () => {
      const mockResponse = { status: 200, data: { id } };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      // Test with empty opts object that results in empty query after language transformation
      const emptyOpts = {};
      await getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id, emptyOpts);

      // When opts is empty, the query object will only have language: undefined
      // After filtering, if no keys have values, the URL should have no query string
      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/types/${type}/contents/${id}?language=`,
        requestOpts,
      );
    });

    test('should construct URL with query string when opts has values', async () => {
      const mockResponse = { status: 200, data: { id } };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      const opts = { select: 'title', language: 'fr' };
      await getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id, opts);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/types/${type}/contents/${id}?select=title&language=fr`,
        requestOpts,
      );
    });

    test('should handle opts with only language undefined', async () => {
      const mockResponse = { status: 200, data: { id } };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      const opts = { language: undefined };
      await getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id, opts);

      // When language is undefined, it becomes empty string, but still gets added to query
      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/types/${type}/contents/${id}?language=`,
        requestOpts,
      );
    });

    test('should handle opts with falsy values that should be included', async () => {
      const mockResponse = { status: 200, data: { id } };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      const opts = {
        draft: false,
        limit: 0,
        archived: false,
        language: 'en',
      };
      await getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id, opts);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/types/${type}/contents/${id}?draft=false&limit=0&archived=false&language=en-US`,
        requestOpts,
      );
    });

    test('should handle null and undefined values in opts', async () => {
      const mockResponse = { status: 200, data: { id } };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      const opts = {
        select: 'title',
        include: null,
        version: undefined,
        language: 'en',
      };
      await getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id, opts);

      // null and undefined values should still be stringified by qs.stringify
      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/types/${type}/contents/${id}?select=title&include=&version=&language=en-US`,
        requestOpts,
      );
    });
  });

  describe('language transformation edge cases', () => {
    test('should handle language as null', async () => {
      const mockResponse = { status: 200, data: { id } };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      const opts = { language: null, select: 'title' };
      await getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id, opts);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/types/${type}/contents/${id}?language=&select=title`,
        requestOpts,
      );
    });

    test('should handle language as empty string', async () => {
      const mockResponse = { status: 200, data: { id } };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      const opts = { language: '', select: 'title' };
      await getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id, opts);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/types/${type}/contents/${id}?language=&select=title`,
        requestOpts,
      );
    });

    test('should handle exact "en" string match case sensitivity', async () => {
      const mockResponse = { status: 200, data: { id } };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      // Test that "EN" (uppercase) doesn't get transformed
      const opts = { language: 'EN' };
      await getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id, opts);

      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/types/${type}/contents/${id}?language=EN`,
        requestOpts,
      );
    });
  });

  describe('Object.keys query construction branch', () => {
    test('should handle query object with no enumerable properties', async () => {
      const mockResponse = { status: 200, data: { id } };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      // Create an object with no enumerable properties
      const opts = {};
      Object.defineProperty(opts, 'language', {
        value: 'en',
        enumerable: false, // This won't be picked up by Object.keys()
      });

      await getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id, opts);

      // Should result in no query string since Object.keys() returns empty array
      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/types/${type}/contents/${id}?language=en-US`,
        requestOpts,
      );
    });

    test('should handle mixed enumerable and non-enumerable properties', async () => {
      const mockResponse = { status: 200, data: { id } };
      const mockResult = { status: 200 };

      mockFetch.mockResolvedValue(mockResult);
      getResponse.mockReturnValue(mockResponse);

      const opts = { select: 'title' };
      Object.defineProperty(opts, 'language', {
        value: 'en',
        enumerable: false,
      });

      await getContentByTypeAndId({ fetch: mockFetch }, basePath, space, type, id, opts);

      // Only enumerable properties should be in query string
      expect(mockFetch).toHaveBeenCalledWith(
        `${basePath}/contents/spaces/${space}/types/${type}/contents/${id}?select=title&language=en-US`,
        requestOpts,
      );
    });
  });

  describe('fetch function dependency', () => {
    test('should handle fetch function that returns non-promise', async () => {
      const mockResult = { status: 200 };
      const syncFetch = jest.fn().mockReturnValue(mockResult);
      getResponse.mockReturnValue({ data: { id } });

      await getContentByTypeAndId({ fetch: syncFetch }, basePath, space, type, id);

      expect(syncFetch).toHaveBeenCalled();
      expect(getResponse).toHaveBeenCalledWith(mockResult);
    });

    test('should handle missing fetch property in first parameter', async () => {
      await expect(
        getContentByTypeAndId({ notFetch: jest.fn() }, basePath, space, type, id),
      ).rejects.toThrow();
    });
  });
});
