const ContentApiClient = require('./index');

const mockFetch = jest.fn();

describe('Alert API Client', () => {
  beforeEach(() => {
    mockFetch.mockClear();
  });
  test('should init', () => {
    const contentClient = ContentApiClient({ fetch: mockFetch });
    expect(contentClient).toHaveProperty('getContentByTypeAndId');
    expect(typeof contentClient.getContentByTypeAndId).toEqual('function');
    expect(typeof contentClient.getContentsByIds).toEqual('function');
  });
  test('getContentByTypeAndId: should throw an error on status >= 400', async () => {
    const mockResponse = {
      status: 404,
      ok: false,
      statusText: 'not found',
      text: jest.fn().mockResolvedValueOnce(JSON.stringify({})),
    };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const contentClient = ContentApiClient({ fetch: mockFetch });
    try {
      await contentClient.getContentByTypeAndId('space', 'type', 'id');
      throw new Error('successfull call');
    } catch (err) {
      expect(err.message).toEqual(mockResponse.statusText);
      expect(mockFetch).toBeCalled();
    }
  });
  test('getContentByTypeAndId: should successfully get response', async () => {
    const mockPayload = {
      data: { id: '12345abcd' },
      notifications: [],
    };
    const mockResponse = {
      status: 200,
      ok: true,
      text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockPayload)),
    };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const contentClient = ContentApiClient({ fetch: mockFetch });
    const result = await contentClient.getContentByTypeAndId('space', 'type', 'id');
    expect(result).toEqual(mockPayload);
  });
  test('getContentsByIds: should successfully get response', async () => {
    const mockPayload = {
      data: [
        { id: '12345abcd1' },
        { id: '12345abcd2' },
        { id: '12345abcd3' },
      ],
      notifications: [],
    };
    const mockResponse = {
      status: 200,
      ok: true,
      text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockPayload)),
    };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const contentClient = ContentApiClient({ fetch: mockFetch });
    const result = await contentClient.getContentsByIds('space', 'type', 'id');
    expect(result).toEqual(mockPayload);
  });
});
