const qs = require('querystring');
const { getResponse } = require('../common');

const opts = {
  method: 'GET',
  headers: { 'Content-Type': 'application/json; charset=utf-8', Accept: 'application/json' },
};

// updated_at_start: datetime,
// updated_at_end: datetime,
const defaultQuery = {
  type: 'webfragment',
  sort: 'web_fragment_id',
};

const getVignetteUpdatedFragments = async ({ fetch }, basePath, query = defaultQuery) => {
  const result = await fetch(`${basePath}/vignette/contents?${qs.stringify(query)}`, opts);
  return getResponse(result);
};

module.exports = getVignetteUpdatedFragments;
