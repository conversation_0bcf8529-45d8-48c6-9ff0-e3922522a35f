const qs = require('querystring');
const { getResponse } = require('../common');

const opts = {
  method: 'GET',
  headers: { 'Content-Type': 'application/json; charset=utf-8', Accept: 'application/json' },
};

// updated_at_start: datetime,
// updated_at_end: datetime,
const defaultQuery = {
  deleted: false,
  disabled: false,
  sort: 'id',
};

const getUpdatedV2 = async ({ fetch }, basePath, query = defaultQuery) => {
  const result = await fetch(`${basePath}/v2/rules?${qs.stringify(query)}`, opts);
  return getResponse(result);
};

module.exports = getUpdatedV2;
