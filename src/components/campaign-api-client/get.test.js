const get = require('./get');

const mockFetch = jest.fn();
const mockText = jest.fn();
const mockUri = 'https://cloud.bns';

describe('Campaign API Client', () => {
  beforeEach(() => {
    mockFetch.mockClear();
    mockText.mockClear();
  });
  test('should throw an error on reponse status >= 400', async () => {
    const mockRuleId = 'abcdef1234';
    const mockResponse = { status: 400, ok: false, statusText: 'bad request', text: jest.fn().mockResolvedValueOnce(JSON.stringify({})) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    try {
      const res = await get({ fetch: mockFetch }, mockUri, mockRuleId);
      throw new Error(`successful response: ${JSON.stringify(res)}`);
    } catch (err) {
      expect(err).toBeDefined();
      expect(err.message).toEqual(mockResponse.statusText);
    }
  });
  test('should get campaign', async () => {
    const mockRuleId = 'abcdef1234';
    const mockPayload = { id: mockRuleId, title: 'some title' };
    mockText.mockResolvedValueOnce(JSON.stringify(mockPayload));
    const mockResponse = { status: 200, ok: true, text: mockText };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const res = await get({ fetch: mockFetch }, mockUri, mockRuleId);
    expect(res).toEqual(mockPayload);
  });
});
