const qs = require('querystring');
const { getResponse } = require('../common');

const opts = {
  method: 'GET',
  headers: { 'Content-Type': 'application/json; charset=utf-8', Accept: 'application/json' },
};

const get = async ({ fetch, tokenService }, basePath, id, query = {}) => {
  const result = await fetch(`${basePath}/v1/campaign-rules/${id}?${Object.keys(query).length > 0 ? '?' + qs.stringify(query) : ''}`, opts);
  return getResponse(result);
};

module.exports = get;
