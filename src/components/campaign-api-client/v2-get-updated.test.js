const getUpdatedV2 = require('./v2-get-updated');

const mockFetch = jest.fn();
const mockText = jest.fn();
const mockUri = 'https://get.cloud.bns';

const opts = {
  method: 'GET',
  headers: { 'Content-Type': 'application/json; charset=utf-8', Accept: 'application/json' },
};

describe('Campaign API Client - Get Updated V2', () => {
  beforeEach(() => {
    mockFetch.mockClear();
    mockText.mockClear();
  });

  test('should get updated campaigns with default query', async () => {
    const mockResponse = { status: 200, ok: true, text: mockText };
    const mockPayload = [
      { id: 'rule1', title: 'Campaign 1', updated_at: '2023-01-01T10:00:00Z' },
      { id: 'rule2', title: 'Campaign 2', updated_at: '2023-01-02T10:00:00Z' },
    ];
    mockText.mockResolvedValueOnce(JSON.stringify(mockPayload));
    mockFetch.mockResolvedValueOnce(mockResponse);

    const res = await getUpdatedV2({ fetch: mockFetch }, mockUri);

    expect(mockFetch).toHaveBeenCalledWith(
      `${mockUri}/v2/rules?deleted=false&disabled=false&sort=id`,
      opts,
    );
    expect(res).toEqual(mockPayload);
  });

  test('should get updated campaigns with custom query', async () => {
    const mockResponse = { status: 200, ok: true, text: mockText };
    const mockQuery = {
      deleted: true,
      disabled: true,
      sort: 'updated_at',
      updated_at_start: '2023-01-01T00:00:00Z',
      updated_at_end: '2023-12-31T23:59:59Z',
    };
    const mockPayload = [
      { id: 'rule3', title: 'Deleted Campaign', updated_at: '2023-06-01T10:00:00Z' },
    ];
    mockText.mockResolvedValueOnce(JSON.stringify(mockPayload));
    mockFetch.mockResolvedValueOnce(mockResponse);

    const res = await getUpdatedV2({ fetch: mockFetch }, mockUri, mockQuery);

    expect(mockFetch).toHaveBeenCalledWith(
      `${mockUri}/v2/rules?deleted=true&disabled=true&sort=updated_at&updated_at_start=2023-01-01T00%3A00%3A00Z&updated_at_end=2023-12-31T23%3A59%3A59Z`,
      opts,
    );
    expect(res).toEqual(mockPayload);
  });

  test('should get updated campaigns with empty query', async () => {
    const mockResponse = { status: 200, ok: true, text: mockText };
    const mockPayload = [];
    mockText.mockResolvedValueOnce(JSON.stringify(mockPayload));
    mockFetch.mockResolvedValueOnce(mockResponse);

    const res = await getUpdatedV2({ fetch: mockFetch }, mockUri, {});

    expect(mockFetch).toHaveBeenCalledWith(
      `${mockUri}/v2/rules?`,
      opts,
    );
    expect(res).toEqual(mockPayload);
  });

  test('should handle fetch error', async () => {
    const fetchError = new Error('Network error');
    mockFetch.mockRejectedValueOnce(fetchError);

    await expect(getUpdatedV2({ fetch: mockFetch }, mockUri))
      .rejects
      .toThrow('Network error');
  });

  test('should handle non-200 response', async () => {
    const mockResponse = { status: 404, ok: false, text: mockText };
    mockText.mockResolvedValueOnce('Not Found');
    mockFetch.mockResolvedValueOnce(mockResponse);

    await expect(getUpdatedV2({ fetch: mockFetch }, mockUri))
      .rejects
      .toThrow();
  });

  test('should properly encode special characters in query parameters', async () => {
    const mockResponse = { status: 200, ok: true, text: mockText };
    const mockQuery = {
      sort: 'name+desc',
      filter: 'type=campaign&status=active',
    };
    const mockPayload = [];
    mockText.mockResolvedValueOnce(JSON.stringify(mockPayload));
    mockFetch.mockResolvedValueOnce(mockResponse);

    await getUpdatedV2({ fetch: mockFetch }, mockUri, mockQuery);

    expect(mockFetch).toHaveBeenCalledWith(
      `${mockUri}/v2/rules?sort=name%2Bdesc&filter=type%3Dcampaign%26status%3Dactive`,
      opts,
    );
  });
});
