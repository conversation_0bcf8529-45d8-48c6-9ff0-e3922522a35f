const getV2 = require('./v2-get');

const mockFetch = jest.fn();
const mockText = jest.fn();
const mockUri = 'https://get.cloud.bns';

const requestOpts = {
  method: 'GET',
  headers: { 'Content-Type': 'application/json; charset=utf-8', Accept: 'application/json' },
};

describe('Campaign API Client', () => {
  beforeEach(() => {
    mockFetch.mockClear();
    mockText.mockClear();
  });
  test('should get campaign', async () => {
    const mockResponse = { status: 200, ok: true, text: mockText };
    const mockRuleId = 'abcdef1234';
    const mockQuery = {
      deleted: false,
      disabled: false,
      sort: 'rules_api_id',
    };
    const mockPayload = { id: mockRuleId, title: 'some title' };
    mockText.mockResolvedValueOnce(JSON.stringify(mockPayload));
    mockFetch.mockResolvedValueOnce(mockResponse);
    const res = await getV2({ fetch: mockFetch }, mockUri, mockRuleId, mockQuery);
    expect(mockFetch).toHaveBeenCalled();
    expect(res).toEqual(mockPayload);
  });
});
test('should get campaign with empty query', async () => {
  const mockResponse = { status: 200, ok: true, text: mockText };
  const mockRuleId = 'xyz789';
  const mockPayload = { id: mockRuleId, title: 'another title' };
  mockText.mockResolvedValueOnce(JSON.stringify(mockPayload));
  mockFetch.mockResolvedValueOnce(mockResponse);

  const res = await getV2({ fetch: mockFetch }, mockUri, mockRuleId);

  expect(mockFetch).toHaveBeenCalledWith(
    `${mockUri}/v2/rules/${mockRuleId}?`,
    requestOpts,
  );
  expect(res).toEqual(mockPayload);
});

test('should get campaign with custom query parameters', async () => {
  const mockResponse = { status: 200, ok: true, text: mockText };
  const mockRuleId = 'custom123';
  const mockQuery = {
    include: 'content',
    fields: 'id,title,description',
    version: '2.1',
  };
  const mockPayload = { id: mockRuleId, title: 'custom campaign', content: {} };
  mockText.mockResolvedValueOnce(JSON.stringify(mockPayload));
  mockFetch.mockResolvedValueOnce(mockResponse);

  const res = await getV2({ fetch: mockFetch }, mockUri, mockRuleId, mockQuery);

  expect(mockFetch).toHaveBeenCalledWith(
    `${mockUri}/v2/rules/${mockRuleId}?include=content&fields=id%2Ctitle%2Cdescription&version=2.1`,
    requestOpts,
  );
  expect(res).toEqual(mockPayload);
});

test('should handle fetch error', async () => {
  const mockRuleId = 'error123';
  const fetchError = new Error('Network error');
  mockFetch.mockRejectedValueOnce(fetchError);

  await expect(getV2({ fetch: mockFetch }, mockUri, mockRuleId))
    .rejects
    .toThrow('Network error');
});

test('should handle undefined query parameter', async () => {
  const mockResponse = { status: 200, ok: true, text: mockText };
  const mockRuleId = 'undefined123';
  const mockPayload = { id: mockRuleId, title: 'undefined query campaign' };
  mockText.mockResolvedValueOnce(JSON.stringify(mockPayload));
  mockFetch.mockResolvedValueOnce(mockResponse);

  const res = await getV2({ fetch: mockFetch }, mockUri, mockRuleId, undefined);

  expect(mockFetch).toHaveBeenCalledWith(
    `${mockUri}/v2/rules/${mockRuleId}?`,
    requestOpts,
  );
  expect(res).toEqual(mockPayload);
});
