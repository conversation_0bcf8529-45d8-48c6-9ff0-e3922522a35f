const getAll = require('./get-all');
const get = require('./get');
const getUpdatedV2 = require('./v2-get-updated');
const getV2 = require('./v2-get');

const init = ({ logger, fetch }, basePath) => {
  return {
    getAll: (query) => getAll({ logger, fetch }, basePath, query),
    get: (id, query) => get({ logger, fetch }, basePath, id, query),
    getUpdatedV2: (query) => getUpdatedV2({ logger, fetch }, basePath, query),
    getV2: (id, query) => getV2({ logger, fetch }, basePath, id, query),
  };
};

module.exports = init;
