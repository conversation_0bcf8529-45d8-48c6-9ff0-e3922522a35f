const getAll = require('./get-all');

const mockFetch = jest.fn();
const mockTokenService = {
  get: jest.fn(),
};
const mockUri = 'https://cloud.bns';

describe('Campaign API Client', () => {
  beforeEach(() => {
    mockFetch.mockClear();
    mockTokenService.get.mockClear();
  });
  test('should throw an error on reponse status >= 400', async () => {
    const mockResponse = { status: 400, ok: false, statusText: 'bad request', text: jest.fn().mockResolvedValueOnce(JSON.stringify({})) };
    mockTokenService.get.mockResolvedValueOnce('sample-token');
    mockFetch.mockResolvedValueOnce(mockResponse);
    try {
      const res = await getAll({ fetch: mockFetch, tokenService: mockTokenService }, mockUri);
      throw new Error(`successful response: ${JSON.stringify(res)}`);
    } catch (err) {
      expect(err).toBeDefined();
      expect(err.message).toEqual(mockResponse.statusText);
    }
  });
});
