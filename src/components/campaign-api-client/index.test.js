const CampaignApiClient = require('./index');

const mockFetch = jest.fn();
const mockUri = 'https://cloud.bns';

describe('Campaign API Client', () => {
  beforeEach(() => {
    mockFetch.mockClear();
  });
  test('should init', () => {
    const client = CampaignApiClient({ fetch: mockFetch }, mockUri);
    expect(client).toHaveProperty('getAll');
    expect(typeof client.getAll).toEqual('function');
  });
  test('should call getAll()', async () => {
    const mockPayload = { id: '123456abcde' };
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockPayload)) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const client = CampaignApiClient({ fetch: mockFetch }, mockUri);
    const res = await client.getAll({});
    expect(res).toBeDefined();
    expect(res).toEqual(mockPayload);
  });
  test('should call get()', async () => {
    const mockPayload = { id: '123456abcde' };
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockPayload)) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const client = CampaignApiClient({ fetch: mockFetch }, mockUri);
    const res = await client.get(mockPayload.id, {});
    expect(res).toBeDefined();
    expect(res).toEqual(mockPayload);
  });
});
