const filterCampaign = (campaign) => ({
  message_source: campaign.message_source,
  message_id: campaign.message_id,
  message_response: campaign.message_response,
  campaign_id: campaign.campaign_id,
  campaign_type: campaign.campaign_type,
  priority: campaign.priority,
  language: campaign.language,
  start_date: campaign.start_date,
  expiry_date: campaign.expiry_date,
  message_status: campaign.message_status,
});

const getCampaignMeta = (campaign) => ({
  data: filterCampaign(campaign.data),
  notifications: campaign.notifications,
});

const getCampaignsMeta = (campaigns) => ({
  data: campaigns.data.map(filterCampaign),
  notifications: campaigns.notifications,
});

module.exports = {
  getCampaignMeta,
  getCampaignsMeta,
};
