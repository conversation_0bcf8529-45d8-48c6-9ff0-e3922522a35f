/* eslint-disable sonarjs/no-duplicate-string */
const getCampaign = require('./get-campaign');

const mockFetch = jest.fn();
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};
const mockMessageId = 'a1b2c3d4';
const mockLanguage = 'en';
const mockPreferredEnv = 'istgreen';
const mockUserIdentifier = '****************'; // card number for CA
const mockChannelId = 'Mobile';
const mockXApplication = 'N1';
const mockSpanId = '75b5758032ef96e0';
const mockTraceId = 'b0c2f8966d348e895db842c86500b15d';
const mockXOriginatingApplCode = 'BFB6';
const mockFeatureFlagUid = '667e1c19-cdc7-4062-a334-67d5539f0ad9';

describe('Insights API Client', () => {
  beforeEach(() => {
    mockFetch.mockClear();
  });
  test('should successfully call Insights service', async () => {
    const mockPath = '';
    const basePath = 'https://insights.apps.cloud.bns';
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify({ data: {} })) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const res = await getCampaign({ fetch: mockFetch,
      logger: mockLogger,
      basePath,
      mockPath,
      mockMessageId,
      channelId: mockChannelId,
      language: mockLanguage,
      id: mockMessageId,
      preferredEnv: mockPreferredEnv,
      spanId: mockSpanId,
      traceId: mockTraceId,
      userIdentifier: mockUserIdentifier,
      xApplication: mockXApplication,
      xOriginatingApplCode: mockXOriginatingApplCode,
      xFeatureFlagUid: mockFeatureFlagUid,
    });

    expect(mockFetch).toHaveBeenCalledTimes(1);
    expect(mockFetch).toHaveBeenCalledWith(
      expect.stringContaining(`/insights/campaigns/${mockMessageId}`),
      expect.objectContaining({
        method: 'GET',
        headers: expect.objectContaining({
          'x-feature-flag-uid': mockFeatureFlagUid,
        }),
      }),
    );

    expect(mockResponse.text).toBeCalled();
    expect(res).toHaveProperty('data');
  });
  test('should successfully call mocked Insights service', async () => {
    const mockPath = 'https://insights-mock.apps.cloud.bns';
    const basePath = '';
    const mockResponse = { status: 200, ok: true, text: jest.fn().mockResolvedValueOnce(JSON.stringify({ data: {} })) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const res = await getCampaign({ fetch: mockFetch,
      logger: mockLogger,
      basePath,
      mockPath,
      id: mockMessageId,
      channelId: mockChannelId,
      language: mockLanguage,
      preferredEnv: mockPreferredEnv,
      spanId: mockSpanId,
      traceId: mockTraceId,
      userIdentifier: mockUserIdentifier,
      xApplication: mockXApplication,
      xOriginatingApplCode: mockXOriginatingApplCode,
      xFeatureFlagUid: mockFeatureFlagUid,
    });

    expect(mockFetch).toHaveBeenCalledTimes(1);
    expect(mockFetch).toHaveBeenCalledWith(
      expect.stringContaining(`/insights/campaigns/${mockMessageId}`),
      expect.objectContaining({
        method: 'GET',
        headers: expect.objectContaining({
          'x-feature-flag-uid': mockFeatureFlagUid,
        }),
      }),
    );

    expect(mockResponse.text).toBeCalled();
    expect(res).toHaveProperty('data');
  });
  test('should throw an error on reponse status >= 400', async () => {
    const mockPath = 'https://insights-mock.apps.cloud.bns';
    const mockResponse = { status: 400, ok: false, statusText: 'bad request', text: jest.fn().mockResolvedValueOnce(JSON.stringify({})) };
    mockFetch.mockResolvedValueOnce(mockResponse);
    try {
      const res = await getCampaign({ fetch: mockFetch,
        logger: mockLogger,
        basePath: '',
        mockPath,
        id: mockMessageId,
        channelId: mockChannelId,
        language: mockLanguage,
        preferredEnv: mockPreferredEnv,
        spanId: mockSpanId,
        traceId: mockTraceId,
        userIdentifier: mockUserIdentifier,
        xApplication: mockXApplication,
        xOriginatingApplCode: mockXOriginatingApplCode,
        xFeatureFlagUid: undefined,
      });
      throw new Error(`successful response: ${JSON.stringify(res)}`);
    } catch (err) {
      expect(err).toBeDefined();
      expect(err.message).toEqual(mockResponse.statusText);
      expect(err).toHaveProperty('response');
    }
  });
  test('should throw an error on reponse status 500', async () => {
    const mockPath = 'https://insights-mock.apps.cloud.bns';
    const mockResponse = {
      status: 500,
      statusText: 'bad request',
      text: jest.fn().mockResolvedValueOnce(JSON.stringify({
        data: null,
        notifications: [ {
          code: '9999-E',
          message: 'Datapower service error occurred: The backend system to DataPower responded with an application level error. Backend: IMS_IMU1-2. Transaction error: [RLI-ERR: 12] [KTMGD-ACCT-NOT-FOUND]',
          uuid: 'f6050c3d-7e12-4bc9-b123-7a853e42e4a0',
          timestamp: '2018-11-18T16:11:07.211-0500',
          metadata: null,
        } ],
      })),
    };
    mockFetch.mockResolvedValueOnce(mockResponse);
    try {
      const res = await getCampaign({ fetch: mockFetch, logger: mockLogger, basePath: '', mockPath }, mockMessageId, {
        channelId: mockChannelId,
        language: mockLanguage,
        preferredEnv: mockPreferredEnv,
        spanId: mockSpanId,
        traceId: mockTraceId,
        userIdentifier: mockUserIdentifier,
        xApplication: mockXApplication,
        xOriginatingApplCode: mockXOriginatingApplCode,
        xFeatureFlagUid: undefined,
      });
      throw new Error(`successful response: ${JSON.stringify(res)}`);
    } catch (err) {
      expect(err).toBeDefined();
      expect(mockResponse.text).toBeCalled();
    }
  });
});
