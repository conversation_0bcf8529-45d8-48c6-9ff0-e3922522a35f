const { getResponse } = require('../common');
const { pickTruthy } = require('../helpers');
const { getCampaignsMeta } = require('./common');

const getCampaigns = async (
  // dependencies
  { fetch, logger, basePath, mockPath },
  // header data
  {
    userIdentifier,
    channelId,
    xApplication,
    language,
    country,
    preferredEnv,
    spanId,
    traceId,
    xOriginatingApplCode,
    xFeatureFlagUid,
  },
) => {
  const url = `${mockPath || `${basePath}/v1`}/insights/campaigns`;
  const headers = {
    'Content-Type': 'application/json; charset=utf-8',
    Accept: 'application/json',
    'x-channel-id': channelId,
    'x-application': xApplication,
    'x-customer-scotiacard': userIdentifier,
    'x-language': language,
    'preferred-environment': preferredEnv,
    'x-b3-traceid': traceId,
    'x-b3-spanid': spanId,
    'x-originating-appl-code': xOriginatingApplCode,
    'x-country-code': country,
    'x-feature-flag-uid': xFeatureFlagUid,
  };
  const opts = {
    method: 'GET',
    headers: pickTruthy(headers),
  };
  const now = new Date();
  try {
    const result = await fetch(url, opts);
    const loggableReq = pickTruthy({
      request: {
        url,
        method: opts.method,
        headers: {
          ...opts.headers,
          'x-customer-scotiacard': '***',
        },
      },
    });
    const response = await getResponse(result);
    logger.info({
      message: 'insight request: get campaigns',
      ...loggableReq,
      response: getCampaignsMeta(response),
      response_time: new Date() - now,
    });
    return response;
  } catch (err) {
    logger.error({ message: 'Error: getting campaigns ', err });
    return Promise.reject(err);
  }
};

module.exports = getCampaigns;
