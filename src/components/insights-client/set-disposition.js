const { getResponse } = require('../common');
const { pickTruthy } = require('../helpers');

/**
 * Set disposition on marvel insight
 *
 * @param {string} basePath - insight base url
 * @param {object} fetch - fetch library
 * @param {object} logger - logger
 * @param {string} mockPath - mock insight base url
 * @param {string} channelId - x-channel-id header
 * @param {string} customerId - optional x-customer-id header to differentiate canadian scoticards with both retail and business profiles
 * @param {string} disposition - new disposition value
 * @param {string} id - message_id on insight, unique identifier for insight record
 * @param {string} language
 * @param {string} messageCategory - if message_id is not provided, this can target all message_id's belonging to the category provided
 * @param {string} preferredEnv
 * @param {string} spanId
 * @param {string} traceId
 * @param {string} userIdentifier - scotiacard number
 * @param {string} xApplication - x-application header
 * @param {string} xOriginatingApplCode - originating application's EPM code

 * @returns
 */
const setDisposition = async ({
  // dependencies
  basePath,
  fetch,
  logger,
  mockPath,
  // data
  channelId,
  customerId,
  country,
  disposition,
  id,
  language,
  messageCategory,
  preferredEnv,
  spanId,
  traceId,
  userIdentifier,
  xApplication,
  xOriginatingApplCode,
  xFeatureFlagUid,
}) => {
  const url = `${mockPath || `${basePath}/v3`}/insights/campaigns/${id || ''}`;
  const headers = {
    'Content-Type': 'application/json; charset=utf-8',
    Accept: 'application/json',
    'x-channel-id': channelId,
    'x-application': xApplication,
    'x-customer-scotiacard': userIdentifier,
    'x-language': language,
    'preferred-environment': preferredEnv,
    'x-b3-traceid': traceId,
    'x-b3-spanid': spanId,
    'x-originating-appl-code': xOriginatingApplCode,
    'x-customer-id': customerId,
    'x-country-code': country,
    'x-feature-flag-uid': xFeatureFlagUid,
  };

  const body = {
    message_response: disposition,
    message_category: !id && messageCategory,
  };

  const opts = {
    method: 'PUT',
    headers: pickTruthy(headers),
    body: JSON.stringify(pickTruthy(body)),
  };

  const loggableReq = {
    url,
    method: opts.method,
    request: {
      headers: {
        ...opts.headers,
        'x-customer-scotiacard': '***',
        'x-customer-id': customerId ? '***' : undefined,
      },
      body: opts.body,
    },
  };

  try {
    const tStart = new Date();
    const result = await fetch(url, opts);
    const response = await getResponse(result);
    logger.info({
      message: 'Successfully set disposition on insight',
      ...loggableReq,
      response_time: new Date() - tStart,
    });
    return response;
  } catch (err) {
    logger.error({
      message: 'Failed to set disposition on insight',
      ...loggableReq,
      error: JSON.parse(JSON.stringify(err)),
    });
    return Promise.reject(err);
  }
};

module.exports = setDisposition;
