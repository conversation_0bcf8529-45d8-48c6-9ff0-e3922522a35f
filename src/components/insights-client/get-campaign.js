const { getResponse } = require('../common');
const { getCampaignMeta } = require('./common');
const { pickTruthy } = require('../helpers');

const getCampaign = async ({
  // dependencies
  fetch,
  basePath,
  logger,
  mockPath,
  // header data
  channelId,
  country,
  id,
  language,
  preferredEnv,
  spanId,
  traceId,
  userIdentifier,
  xApplication,
  xOriginatingApplCode,
  xFeatureFlagUid,
}) => {
  const url = `${mockPath || `${basePath}/v3`}/insights/campaigns/${id}`;
  const headers = {
    'Content-Type': 'application/json; charset=utf-8',
    Accept: 'application/json',
    'x-channel-id': channelId,
    'x-application': xApplication,
    'x-customer-scotiacard': userIdentifier,
    'x-language': language,
    'preferred-environment': preferredEnv,
    'x-b3-traceid': traceId,
    'x-b3-spanid': spanId,
    'x-originating-appl-code': xOriginatingApplCode,
    'x-country-code': country,
    'x-feature-flag-uid': xFeatureFlagUid,
  };
  const opts = {
    method: 'GET',
    headers: pickTruthy(headers),
  };
  const now = new Date();
  try {
    const result = await fetch(url, opts);
    const loggableReq = pickTruthy({
      request: {
        url,
        method: opts.method,
        headers: {
          ...opts.headers,
          'x-customer-scotiacard': '****',
        },
      },
    });
    const response = await getResponse(result);
    logger.info({
      message: 'insight request: get campaign',
      ...loggableReq,
      response: getCampaignMeta(response),
      response_time: new Date() - now,
    });
    return response;
  } catch (err) {
    return Promise.reject(err);
  }
};

module.exports = getCampaign;
