const knex = require('knex');
const mockKnex = require('mock-knex');
const clearDispositions = require('./clear-disposition');

const db = knex({ client: 'mssql', debug: false });
const tracker = mockKnex.getTracker();
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};
const mockSecret = 'ABA21ZE1X2Y3ZWNhNDcyNKlkZmYxZOLkMTAqNGFkOpP=';
const mockLaunchDarklyService = {
  init: jest.fn(),
  isFeatureEnabled: jest.fn(),
  close: jest.fn(),
};
const svcDependencies = {
  db,
  logger: mockLogger,
  timeout: 1000,
  schema: 'disposition',
  secret: mockSecret,
  launchDarklyService: mockLaunchDarklyService,
};
const cardNumber = 'ZARwhwHWxNDeKbqIdhGUsaP2MBgKM8ECWddE+1GYpCI=                    ';
const createdTimeStamp = '2021-03-05T21:21:32.208Z';
const mockContainer = 'offers-and-programs-it';
const dbResponse = [
  {
    rule_id: 'Vp4YWkCvFaw5',
    message_id: 'Vp4YWkCvFaw5',
    card_number: cardNumber,
    container: mockContainer,
    page: 'accounts-it',
    platform: 'ios',
    application: 'nova',
    disposition_status_id: 1,
    created_by: 'sa',
    created_ts: createdTimeStamp,
    updated_by: null,
    updated_ts: createdTimeStamp,
    expired_ts: '2021-12-31T05:00:00.000Z',
  },
  {
    rule_id: 'XroFuQYEW1wY',
    message_id: 'XroFuQYEW1wY',
    card_number: cardNumber,
    container: 'offers-and-programs',
    page: 'accounts',
    platform: 'ios',
    application: 'nova',
    disposition_status_id: 2,
  },
];
describe('Get Dispositions', () => {
  beforeAll(() => {
    mockKnex.mock(db);
  });
  afterAll(() => {
    mockKnex.unmock(db);
  });
  beforeEach(() => {
    tracker.install();
    mockLogger.info.mockReset();
    mockLogger.error.mockReset();
    mockLaunchDarklyService.isFeatureEnabled.mockReset();
  });
  afterEach(() => {
    tracker.uninstall();
  });

  test('should delete the record from database', async () => {
    mockLaunchDarklyService.isFeatureEnabled.mockReturnValueOnce(true);
    tracker.on('query', (query, step) => {
      query.response(dbResponse);
    });
    const result = await clearDispositions(svcDependencies, '****************', 'XroFuQYEW1wY', mockContainer, 'CA');
    expect(result).toEqual(undefined);
  });

  test('should delete the record from database - no container', async () => {
    mockLaunchDarklyService.isFeatureEnabled.mockReturnValueOnce(true);
    tracker.on('query', (query, step) => {
      query.response(dbResponse);
    });
    const result = await clearDispositions(svcDependencies, '****************', 'XroFuQYEW1wY', undefined, 'CA');
    expect(result).toEqual(undefined);
  });

  test('should not read from dispositions db and return empty response if feature flag is disabled', async () => {
    mockLaunchDarklyService.isFeatureEnabled.mockReturnValueOnce(false);
    const result = await clearDispositions(svcDependencies, '****************', 'CA', 'nova');
    expect(result).toEqual(undefined);
  });

  test('should read from dispositions db if call to launch darkly fails', async () => {
    mockLaunchDarklyService.isFeatureEnabled.mockRejectedValueOnce(new Error('timeout'));
    tracker.on('query', (query) => {
      query.response([
        {
          disposition_id: '1',
          rule_id: 'Vp4YWkCvFaw5',
          message_id: 'Vp4YWkCvFaw5',
          card_number: cardNumber,
          container: mockContainer,
          page: 'accounts-it',
          platform: 'ios',
          application: 'nova',
          disposition_status_id: 1,
          created_by: 'sa',
          created_ts: createdTimeStamp,
          updated_by: null,
          updated_ts: createdTimeStamp,
          expired_ts: '2021-12-31T05:00:00.000Z',
        },
      ]);
    });
    const result = await clearDispositions(svcDependencies, '****************', 'XroFuQYEW1wY', mockContainer, 'CA');
    expect(result).toEqual(undefined);
  });
  test('should read from dispositions db if call to launch darkly fails', async () => {
    mockLaunchDarklyService.isFeatureEnabled.mockRejectedValueOnce(new Error('timeout'));
    tracker.on('query', (query) => {
      query.response([
      ]);
    });
    try {
      await clearDispositions(svcDependencies, '****************', 'XroFuQYEW1wY', mockContainer, 'CA');
    } catch (err) {
      expect(err.message).toEqual('Clearing Disposition - This is rule id :XroFuQYEW1wY is not found');
      expect(err.statusCode).toEqual(400);
    }
  });
});
