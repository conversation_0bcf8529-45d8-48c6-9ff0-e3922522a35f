const R = require('ramda');
const momentTz = require('moment-timezone');

const { isCCAU: checkForCCAURegion, decodeUserContext } = require('../helpers');
const { getKeyHmac } = require('../cache/crypto');
const { dispositionValues } = require('../common');

const NEXT_PURGE_KEY = 'dispositions:next_purge';
const LOCKED_UNTIL_KEY = 'dispositions:locked_until';
const PURGE_LOCK_KEY = 'dispositions:disp_lock';

/**
 * @typedef PurgeResult
 * @property {string} idCountToPurge - count of DB records to purge in current interval
 * @property {string} recordsPurged - count of DB records to purge in current interval
 * @property {string} tDbSelect - duration of DB select query to identify records to purge
 * @property {string} tDbDelete - duration of DB delete query to purge records
 * @property {string} nextPurgeTime - date and time of next earliest purge time
 * @property {string} tRedisSetNext - duration of redis set next purge time command
 * @property {string} tRedisUnlock - duration of redis unlock command
 * @property {string} tRedisLockHeld - duration of redis lock hold
 */
/**
 * Perform disposition table purge operation
 * @param {object} redisClient - Redis instance
 * @param {object} db - database connection
 * @param {string} schema - target database schema
 * @param {number} purgeInterval - Time between purge operations (in minutes)
 * @param {number} maxDeleteCount - Maximum number of rows to delete in one purge
 * @param {number} deleteCheck - Number used to check when to perform delete
 * @param {PurgeCheckResult} purgeCheckResult - result of purge check,
 *   includes reference to lock, and lock start time
 * @return {PurgeResult} purge result
 */
const purgeDispositions = async (
  redisClient,
  db,
  schema,
  purgeInterval,
  maxDeleteCount,
  purgeCheckResult,
  deleteCheck,
) => {
  const result = {};
  const now = `${momentTz.utc().format()}`;
  try {
    // obtain ids to deletion
    const tStartSel = new Date();
    const idsToPurge = await db('disposition')
      .withSchema(schema)
      .count('disposition_id as count')
      .where('expired_ts', '<', now)
      .first();
    result.tDbSelect = `${new Date() - tStartSel} ms`;
    result.idCountToPurge = idsToPurge && idsToPurge.count;
    // delete dispositions from db
    if (idsToPurge.count >= deleteCheck) {
      const tStartDel = new Date();
      result.recordsPurged = await db('disposition')
        .withSchema(schema)
        .delete()
        .whereIn('disposition_id', function() {
        // use callback to get over ms sql paramter limit of 2100
          this.select('disposition_id')
            .from('disposition')
            .withSchema(schema)
            .where('expired_ts', '<', now)
            .limit(maxDeleteCount);
        });
      result.tDbDelete = `${new Date() - tStartDel} ms`;
    }
    // in redis, set next purge time and release purge lock
    result.nextPurgeTime = momentTz.utc().add(purgeInterval, 'minutes').utc();
    const tStartNext = new Date();
    await redisClient.setEx(NEXT_PURGE_KEY, result.nextPurgeTime, purgeInterval * 60);
    result.tRedisSetNext = `${new Date() - tStartNext} ms`;
    await redisClient.del(LOCKED_UNTIL_KEY); // test with this disabled to verify TTL unit and ability to carry on with lock acquire failures
    const tStartUnlock = new Date();
    if (!purgeCheckResult.lock) {
      result.warning = 'lock not in place at time of purge';
    }
    // purgeCheckResult.lock && await redisClient.unlock(purgeCheckResult.lock);
    result.tRedisUnlock = `${new Date() - tStartUnlock} ms`;
    result.tRedisLockHeld = `${new Date() - purgeCheckResult.tStartLockHold} ms`;
  } catch (e) {
    result.error = { message: 'Disposition purge - error while purging disposition', error: e.message };
  }
  return result;
};

/**
 * @typedef PurgeCheckResult
 * @property {string} lockedUntil - lock expiry date time
 * @property {string} nextPurgeTime - Next purge date time
 * @property {object} lock - Redis lock on the purge process
 * @property {string} tLockAcquire - time taken to acquire lock
 * @property {Date} tStartLockHold - date time when lock was acquired
 * @property {string} tSetLockTil - time taken to set lock until time in redis
 * @property {boolean} shouldPurgeData - check whether should proceed with disposition purge
 */
/**
 * Determines whether or not a purge operation should be performed on the disposition table
 * @param {object} redisClient - Redis instance
 * @param {number} purgeTTL - Amount of time (in seconds) a purge operation can remain un-interrupted
 * @return {PurgeCheckResult} purge check result
 */
const purgeCheck = async (redisClient, purgeTTL) => {
  const result = { shouldPurgeData: false };

  try {
    result.lockedUntil = await redisClient.get(LOCKED_UNTIL_KEY);
    result.nextPurgeTime = await redisClient.get(NEXT_PURGE_KEY);
    const currTime = momentTz.utc();
    const notInLockPeriod = !result.lockedUntil || (currTime > momentTz(result.lockedUntil));
    const pastPurgeTime = !result.nextPurgeTime || currTime > momentTz(result.nextPurgeTime);

    if (notInLockPeriod && pastPurgeTime) {
      // acquire lock to prevent parallel access to DB
      const tStartLock = new Date();
      result.lock = await redisClient.lock(PURGE_LOCK_KEY, purgeTTL);
      result.tLockAcquire = `${new Date() - tStartLock} ms`;
      result.tStartLockHold = new Date();

      const tStartSetLockTil = new Date();
      await redisClient.setEx(
        LOCKED_UNTIL_KEY,
        currTime.add(purgeTTL, 'seconds').utc().format(),
        purgeTTL, // TODO test auto expire, to verify TTL units is in sec
      );
      result.tSetLockTil = `${new Date() - tStartSetLockTil} ms`;
      result.shouldPurgeData = true;
    }
  } catch (e) {
    result.error = { message: 'Disposition purge - error caught doing purge check', error: e.message };
  }
  return result;
};

/**
 * Schedules a timer responsible for purging disposition records at the appropriate time
 * @param {object} deps Dependencies
 * @param {object} deps.redisClient Redis instance
 * @param {object} deps.logger Logger instnace
 * @param {object} deps.db Database connection instance
 * @param {object} config - disposition purge job configurations
 * @param {number} config.purgeCheckInterval - purge check interval in minutes
 * @param {number} config.purgeInterval - purge action interval in minutes
 * @param {number} config.purgeTTL - Purge lock ttl - amount of time purge lock is in effect (in seconds)
 * @param {number} config.maxDeleteCount - max number of records to delete per run
 * @param {number} config.deleteCheck - number used to check when to perform delete
 */
const scheduleDispositionPurge = (deps, config) => {
  const {
    purgeCheckInterval,
    purgeInterval,
    maxDeleteCount,
    purgeTTL,
    deleteCheck,
  } = config;

  const { logger, redisClient, db, schema } = deps;
  logger.info('Disposition purge - scheduling new disposition purge job');

  return setInterval(async () => {
    const checkResult = await purgeCheck(redisClient, purgeTTL);
    let purgeResult;
    if (checkResult.shouldPurgeData) {
      purgeResult = await purgeDispositions(
        redisClient,
        db,
        schema,
        purgeInterval,
        maxDeleteCount,
        checkResult,
        deleteCheck,
      );
    }
    logger.info({
      message: 'Disposition purge - new interval',
      checkResult: { ...checkResult, lock: !!checkResult.lock },
      purgeResult,
    });
  }, purgeCheckInterval);
};

/**
 * Resolve value for sql db disposition.disposition.card_number.
 * For Canada, hash the card number.
 * For CCAU countries, hash cif, which is found inside decoded user context.
 *
 * @param {object} logger
 * @param {string} secret - to generate unforgeable hash
 * @param {*} uid - card number or user context
 * @param {string} country - two letter country code
 * @returns
 */
const getDbUidHash = (logger, secret, uid, country) => {
  const isCCAU = checkForCCAURegion({ country });
  const userIdDecoded = isCCAU ? (decodeUserContext(logger, uid) || {}).cid : uid;
  return getKeyHmac(secret, userIdDecoded);
};

/**
 * Resolve value for sql db disposition.disposition.application.
 * For Canada, application is as is.
 * For CCAU countries, application is expected to be suffixed with
 * dash country. E.g. wave-JM
 *
 * @param {*} application
 * @param {*} country
 * @returns
 */
const getDbAppVal = (application, country) => {
  const isCCAU = checkForCCAURegion({ country });
  const applicationCountrySuffix = isCCAU ? `-${country.toUpperCase()}` : '';
  return `${application}${applicationCountrySuffix}`;
};

const getDispositionStatus = R.cond([
  [ R.equals(dispositionValues.dismissed), R.always(1) ],
  [ R.equals(dispositionValues.viewed), R.always(2) ],
  [ R.equals(dispositionValues.seen), R.always(3) ],
  [ R.equals(dispositionValues.deleted), R.always(4) ],
]);

const getDispositionValue = R.cond([
  [ R.equals(1), R.always(dispositionValues.dismissed) ],
  [ R.equals(2), R.always(dispositionValues.viewed) ],
  [ R.equals(3), R.always(dispositionValues.seen) ],
  [ R.equals(4), R.always(dispositionValues.deleted) ],
]);

module.exports = {
  getDbAppVal,
  getDbUidHash,
  scheduleDispositionPurge,
  purgeCheck,
  purgeDispositions,
  getDispositionStatus,
  getDispositionValue,
};
