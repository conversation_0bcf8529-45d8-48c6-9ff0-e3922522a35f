const { getApplicationPlatform } = require('../helpers');
const { dispositionValues } = require('../common');
const { HttpError } = require('../errors');
const { getDbAppVal, getDbUidHash, getDispositionStatus } = require('./utils');

/**
 * Set disposition in disposition db
 * @param {Object} dependencies
 * @param {Object} dependencies.db - db client
 * @param {Object} dependencies.logger - logger
 * @param {Object} dependencies.timeout - db query timeout limit in ms
 * @param {Object} dependencies.schema - disposition db schema
 * @param {Object} dependencies.launchDarklyService - ld client
 * @param {Object} dependencies.secret - key used to create unforgeable hash
 * @param {string} userId - user identifier, can be card number or user context for CCAU countries
 * @param {string} country - required to globally identify customers
 * @param {string} ruleId - pigeon rule id
 * @param {string} expiry - timestamp when disposition is open to be purged
 *   It's usually the earliest between campaign service and pigeon rule db
 * @param {string} disposition - value of new disposition
 * @param {Object} body - additional details for set disposition request
 * @param {string} body.message_id - campaign service message id
 * @param {string} body.container - pigeon rule container placement
 * @param {string} body.page - pigeon rule page placement
 * @param {string} body.application - pigeon rule application placement.
 *   If CCAU request, append application with "-<two letter country code". e.g. "wave-JM"
 * @param {string} body.platform - pigeon rule platform targeting condition
 * @returns
 */
const setDisposition = async (
  { db, logger, timeout, schema, launchDarklyService, secret }, // dependencies
  userId, country, ruleId, expiry, disposition, body, // data
) => {
  let isDispositionsDbEnabled;
  try {
    isDispositionsDbEnabled = await launchDarklyService.isFeatureEnabled('pigeon-api.features.dispositions.db', true);
  } catch (err) {
    logger.error({ message: 'unable to call Launch Darkly for dispositions db from get-campaigns', err });
    isDispositionsDbEnabled = true;
  }

  if (isDispositionsDbEnabled === false) {
    return;
  }
  const { message_id: messageId, container, page, platform: queryPlatform, application: queryApplication } = body;
  const { application, platform } = getApplicationPlatform(queryPlatform, queryApplication);
  const hashedCardNumber = getDbUidHash(logger, secret, userId, country);
  const applicationColumnValue = getDbAppVal(application, country);
  try {
    const findDispositionQuery = db.withSchema(schema)
      .where({
        rule_id: ruleId,
        card_number: hashedCardNumber,
        application: applicationColumnValue,
      })
      .timeout(timeout);
    if (messageId && messageId !== ruleId) {
      if (body.old_message_id) {
        findDispositionQuery.andWhere((builder) => {
          builder.where({ message_id: messageId }).orWhere({ message_id: body.old_message_id });
        });
      } else {
        findDispositionQuery.andWhere({ message_id: messageId });
      }
    }

    if (disposition === dispositionValues.seen) {
      findDispositionQuery.andWhere((builder) => {
        builder.where({ disposition_status_id: getDispositionStatus(dispositionValues.viewed) })
          .orWhere({ disposition_status_id: getDispositionStatus(disposition) });
      });
    } else {
      findDispositionQuery.andWhere({ disposition_status_id: getDispositionStatus(disposition) });
    }

    const dispositionFindResult = await findDispositionQuery.from('disposition')
      .limit(1)
      .select([ 'rule_id', 'message_id', 'disposition_status_id' ]);
    logger.info({ message: `Set Disposition - ${dispositionFindResult.length ? 'existing db entry found' : 'no existing db entries'}`, ruleId, messageId, disposition, container, page });

    if (dispositionFindResult.length === 0) {
      if (!Array.isArray(page)) {
        await db.withSchema(schema).into('disposition')
          .insert({
            rule_id: ruleId,
            message_id: messageId,
            card_number: hashedCardNumber,
            container,
            page,
            platform,
            application: applicationColumnValue,
            disposition_status_id: getDispositionStatus(disposition),
            expired_ts: expiry,
          })
          .timeout(timeout);
      } else {
        const insertQuery = page.map(p => ({
          rule_id: ruleId,
          message_id: messageId,
          card_number: hashedCardNumber,
          container,
          page: p,
          platform,
          application: applicationColumnValue,
          disposition_status_id: getDispositionStatus(disposition),
          expired_ts: expiry,
        }));
        await db.withSchema(schema).into('disposition')
          .insert(insertQuery)
          .timeout(timeout);
      }
      logger.info({ message: 'Set Disposition - new disposition successfully added to db', ruleId, messageId, disposition, container, page });
    }
    return;
  } catch (err) {
    logger.error({ message: 'Set Disposition - failed to insert the disposition to the db', err, ruleId, messageId, disposition, container, page });
    throw HttpError.internalServer('Set Disposition - failed to insert the disposition to the db', [ { error: err.message, ruleId, messageId, disposition, container, page } ]);
  }
};

module.exports = setDisposition;
