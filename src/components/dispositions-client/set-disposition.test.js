/* eslint-disable sonarjs/no-identical-functions */
const knex = require('knex');
const mockKnex = require('mock-knex');

const setDisposition = require('./set-disposition');
const { getKeyHmac } = require('../cache/crypto');
const { decodeUserContext } = require('../helpers');

const db = knex({ client: 'mssql', debug: false });
const tracker = mockKnex.getTracker();
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};
const cardNumberEncrypted = 'ZARwhwHWxNDeKbqIdhGUsaP2MBgKM8ECWddE+1GYpCI=                    ';
const mockSecret = 'ABA21ZE1X2Y3ZWNhNDcyNKlkZmYxZOLkMTAqNGFkOpP=';
const mockLaunchDarklyService = {
  init: jest.fn(),
  isFeatureEnabled: jest.fn(),
  close: jest.fn(),
};
mockLaunchDarklyService.init.mockReturnValue(true);
mockLaunchDarklyService.isFeatureEnabled.mockReturnValue(true);
const svcDependencies = {
  db,
  logger: mockLogger,
  timeout: 1000,
  schema: 'disposition',
  secret: mockSecret,
  launchDarklyService: mockLaunchDarklyService,
};

const container = 'offers-and-programs';
const cardNumber = '****************';
const country = 'CA';
const xUserContext = '************************************************************************************************************************************************************************************************************************************************************';
const ruleId = 'XroFuQYEW1wY';
const messageId = 'XroFuQYEW1wY';
const expiry = '2021-03-31T04:00:00.000Z';
const disposition = 'D';
const page = 'accounts';
const modifiedTs = '2021-03-05T21:21:37.243Z';
const body = {
  message_id: 'XroFuQYEW1wY',
  container: 'offers-and-programs',
  page: 'accounts',
  platform: 'ios',
  application: 'nova',
};
const opts = {
  message_id: messageId,
  container,
  page,
  platform: 'ios',
  application: 'nova',
};

const noEntryExist = {
  message: 'Set Disposition - no existing db entries',
  ruleId,
  messageId: body.message_id,
  disposition,
  container,
  page,
};
const newEntrySet = {
  message: 'Set Disposition - new disposition successfully added to db',
  ruleId,
  messageId: body.message_id,
  disposition,
  container,
  page,
};

describe('Set Disposition', () => {
  beforeAll(() => {
    mockKnex.mock(db);
  });
  afterAll(() => {
    mockKnex.unmock(db);
  });
  beforeEach(() => {
    tracker.install();
    mockLogger.info.mockClear();
    mockLogger.error.mockClear();
    mockLaunchDarklyService.isFeatureEnabled.mockReturnValue(true);
  });
  afterEach(() => {
    tracker.uninstall();
  });

  test('should insert a new disposition if no record exists', async () => {
    tracker.on('query', (query, step) => {
      if (query.method === 'select') {
        query.response([]);
      } else if (query.method === 'insert') {
        const hashedCardNumber = getKeyHmac(svcDependencies.secret, cardNumber);
        // expect user id to be hashed from card number
        expect(query.bindings[1]).toBe(hashedCardNumber);
        // expect application to be unaltered
        expect(query.bindings[0]).toBe('nova');
        query.response();
      }
    });

    try {
      await setDisposition(svcDependencies, cardNumber, country, ruleId, expiry, disposition, body);
      expect(mockLogger.info).toHaveBeenCalledTimes(2);
      expect(mockLogger.info).toHaveBeenCalledWith(noEntryExist);
      expect(mockLogger.info).toHaveBeenCalledWith(newEntrySet);
    } catch (err) {
      expect(err).not.toBeDefined();
      expect(mockLogger.error).not.toBeCalled();
    }
  });

  test('should insert a new disposition if no record exists - ccau implementation', async () => {
    const queryBody = { ...body, application: 'wave' };
    tracker.on('query', (query, step) => {
      if (query.method === 'select') {
        query.response([]);
      } else if (query.method === 'insert') {
        const userIdDecoded = decodeUserContext(mockLogger, xUserContext).cid;
        const hashedCardNumber = getKeyHmac(svcDependencies.secret, userIdDecoded);
        // expect user id to be hashed from cid inside decoded user context instead of raw input
        expect(query.bindings[1]).toBe(hashedCardNumber);
        // expect dash country to be appended to application
        expect(query.bindings[0]).toBe('wave-DO');
        query.response();
      }
    });

    try {
      await setDisposition(svcDependencies, xUserContext, 'DO', ruleId, expiry, disposition, queryBody);
      expect(mockLogger.info).toHaveBeenCalledTimes(2);
      expect(mockLogger.info).toHaveBeenCalledWith(noEntryExist);
      expect(mockLogger.info).toHaveBeenCalledWith(newEntrySet);
    } catch (err) {
      expect(err).not.toBeDefined();
      expect(mockLogger.error).not.toBeCalled();
    }
  });

  test('should not insert a new disposition if a record exists', async () => {
    tracker.on('query', (query, step) => {
      if (query.method === 'select') {
        query.response([
          {
            disposition_id: '1',
            rule_id: ruleId,
            message_id: messageId,
            card_number: cardNumberEncrypted,
            container,
            page,
            platform: 'ios',
            application: 'nova',
            disposition_status_id: 1,
            created_by: 'sa',
            created_ts: modifiedTs,
            updated_by: null,
            updated_ts: modifiedTs,
            expired_ts: expiry,
          },
        ]);
      }
    });

    try {
      await setDisposition(svcDependencies, cardNumber, country, ruleId, expiry, disposition, body);
      expect(mockLogger.info).toHaveBeenCalledTimes(1);
      expect(mockLogger.info).toHaveBeenCalledWith({ message: 'Set Disposition - existing db entry found', ruleId, messageId: body.message_id, disposition, container, page });
    } catch (err) {
      expect(err).not.toBeDefined();
      expect(mockLogger.error).not.toBeCalled();
    }
  });

  test('should log an error on failed db upsert', async () => {
    tracker.on('query', (query, step) => {
      query.reject(new Error());
    });
    try {
      await setDisposition(svcDependencies, cardNumber, country, ruleId, expiry, disposition, opts);
    } catch (err) {
      expect(err.message).toEqual('Set Disposition - failed to insert the disposition to the db');
      expect(err.statusCode).toEqual(500);
      expect(mockLogger.error).toHaveBeenCalledTimes(1);
    }
  });

  test('should not insert a disposition if feature flag disabled', async () => {
    let queryCount = 0;
    mockLaunchDarklyService.isFeatureEnabled.mockReturnValue(false);
    tracker.on('query', (query, step) => {
      if (query.method === 'select') {
        query.response([]);
      } else if (query.method === 'insert') {
        queryCount++;
        query.response();
      }
    });

    try {
      await setDisposition(svcDependencies, cardNumber, country, ruleId, expiry, disposition, opts);
    } catch (err) {
      expect(err).not.toBeDefined();
      expect(mockLogger.error).not.toBeCalled();
      expect(queryCount).toEqual(0);
    }
  });
  test('should not update an existing disposition if feature flag disabled', async () => {
    let queryCount = 0;
    mockLaunchDarklyService.isFeatureEnabled.mockReturnValue(false);
    tracker.on('query', (query, step) => {
      if (query.method === 'select') {
        query.response([
          {
            disposition_id: '1',
            rule_id: ruleId,
            message_id: messageId,
            card_number: cardNumberEncrypted,
            container,
            page,
            platform: 'ios',
            application: 'nova',
            disposition_status_id: 1,
            created_by: 'sa',
            created_ts: modifiedTs,
            updated_by: null,
            updated_ts: modifiedTs,
            expired_ts: expiry,
          },
        ]);
      } else if (query.method === 'update') {
        queryCount++;
        query.response();
      }
    });

    try {
      await setDisposition({ db, logger: mockLogger, timeout: 1000, schema: 'disposition', launchDarklyService: mockLaunchDarklyService }, cardNumber, ruleId, expiry, disposition, opts);
    } catch (err) {
      expect(err).not.toBeDefined();
      expect(mockLogger.error).not.toBeCalled();
      expect(queryCount).toEqual(0);
    }
  });

  test('should insert a new disposition for array of pages', async () => {
    tracker.on('query', (query, step) => {
      if (query.method === 'select') {
        query.response([]);
      } else if (query.method === 'insert') {
        const hashedCardNumber = getKeyHmac(svcDependencies.secret, cardNumber);
        // expect user id to be hashed from card number
        expect(query.bindings[1]).toBe(hashedCardNumber);
        // expect application to be unaltered
        expect(query.bindings[0]).toBe('orion');
        query.response();
      }
    });
    const newBody = {
      ...body,
      application: 'orion',
      page: [
        'accounts',
        'activities',
      ],
    };
    try {
      await setDisposition(svcDependencies, cardNumber, country, ruleId, expiry, disposition, newBody);
      expect(mockLogger.info).toHaveBeenCalledTimes(2);
      expect(mockLogger.info).toHaveBeenCalledWith({ ...noEntryExist, page: newBody.page });
      expect(mockLogger.info).toHaveBeenCalledWith({ ...newEntrySet, page: newBody.page });
    } catch (err) {
      expect(err).not.toBeDefined();
      expect(mockLogger.error).not.toBeCalled();
    }
  });

  test('Seen disposition is passed', async () => {
    tracker.on('query', (query, step) => {
      if (query.method === 'select') {
        query.response([
          {
            disposition_id: '1',
            rule_id: ruleId,
            message_id: messageId,
            card_number: cardNumberEncrypted,
            container: 'orion-campaign',
            application: 'orion',
            disposition_status_id: 3,
          },
        ]);
      } else if (query.method === 'insert') {
        const hashedCardNumber = getKeyHmac(svcDependencies.secret, cardNumber);
        // expect user id to be hashed from card number
        expect(query.bindings[1]).toBe(hashedCardNumber);
        // expect application to be unaltered
        expect(query.bindings[0]).toBe('orion');
        query.response();
      }
    });
    const newBody = {
      ...body,
      container: 'orion-campaign',
      application: 'orion',
      page: [
        'accounts',
      ],
    };
    try {
      await setDisposition(svcDependencies, cardNumber, country, ruleId, expiry, 'W', newBody);
      expect(mockLogger.info).toHaveBeenCalledTimes(1);
    } catch (err) {
      expect(err).not.toBeDefined();
      expect(mockLogger.error).not.toBeCalled();
    }
  });

  test('Old messageId is passed', async () => {
    tracker.on('query', (query, step) => {
      if (query.method === 'select') {
        query.response([]);
      } else if (query.method === 'insert') {
        const hashedCardNumber = getKeyHmac(svcDependencies.secret, cardNumber);
        // expect user id to be hashed from card number
        expect(query.bindings[1]).toBe(hashedCardNumber);
        // expect application to be unaltered
        expect(query.bindings[0]).toBe('nova');
        query.response();
      }
    });
    const newBody = {
      ...body,
      old_message_id: 'abc',
    };
    try {
      await setDisposition(svcDependencies, cardNumber, country, 'abc123', expiry, disposition, newBody);
      expect(mockLogger.info).toHaveBeenCalledTimes(2);
    } catch (err) {
      expect(err).not.toBeDefined();
      expect(mockLogger.error).not.toBeCalled();
    }
  });

  test('Targeted campaign - MessageId not the same as ruleId', async () => {
    tracker.on('query', (query, step) => {
      if (query.method === 'select') {
        query.response([]);
      } else if (query.method === 'insert') {
        const hashedCardNumber = getKeyHmac(svcDependencies.secret, cardNumber);
        // expect user id to be hashed from card number
        expect(query.bindings[1]).toBe(hashedCardNumber);
        // expect application to be unaltered
        expect(query.bindings[0]).toBe('nova');
        query.response();
      }
    });
    try {
      await setDisposition(svcDependencies, cardNumber, country, 'abc123', expiry, disposition, body);
      expect(mockLogger.info).toHaveBeenCalledTimes(2);
    } catch (err) {
      expect(err).not.toBeDefined();
      expect(mockLogger.error).not.toBeCalled();
    }
  });
});
