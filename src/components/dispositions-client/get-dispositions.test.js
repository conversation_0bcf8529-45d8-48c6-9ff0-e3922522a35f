const knex = require('knex');
const mockKnex = require('mock-knex');
const getDispositions = require('./get-dispositions');

const db = knex({ client: 'mssql', debug: false });
const tracker = mockKnex.getTracker();
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};
const mockSecret = 'ABA21ZE1X2Y3ZWNhNDcyNKlkZmYxZOLkMTAqNGFkOpP=';
const mockLaunchDarklyService = {
  init: jest.fn(),
  isFeatureEnabled: jest.fn(),
  close: jest.fn(),
};
const svcDependencies = {
  db,
  logger: mockLogger,
  timeout: 1000,
  schema: 'disposition',
  secret: mockSecret,
  launchDarklyService: mockLaunchDarklyService,
};
const cardNumber = 'ZARwhwHWxNDeKbqIdhGUsaP2MBgKM8ECWddE+1GYpCI=                    ';
const createdTimeStamp = '2021-03-05T21:21:32.208Z';

const containerOfferIt = 'offers-and-programs-it';

const ruleResponse = [
  {
    rule_id: 'Vp4YWkCvFaw5',
    message_id: 'Vp4YWkCvFaw5',
    card_number: cardNumber,
    container: containerOfferIt,
    page: 'accounts-it',
    platform: 'ios',
    application: 'nova',
    disposition_status_id: 1,
    created_by: 'sa',
    created_ts: createdTimeStamp,
    updated_by: null,
    updated_ts: createdTimeStamp,
    expired_ts: '2021-12-31T05:00:00.000Z',
  },
  {
    rule_id: 'XroFuQYEW1wY',
    message_id: 'XroFuQYEW1wY',
    card_number: cardNumber,
    container: 'offers-and-programs',
    page: 'accounts',
    platform: 'ios',
    application: 'nova',
    disposition_status_id: 2,
  },
];

const dispositionResponse = [
  {
    rule_id: 'Vp4YWkCvFaw5',
    message_id: 'Vp4YWkCvFaw5',
    disposition: 'D',
    container: containerOfferIt,
  },
  {
    rule_id: 'XroFuQYEW1wY',
    message_id: 'XroFuQYEW1wY',
    disposition: 'V',
    container: 'offers-and-programs',
  },
];

describe('Get Dispositions', () => {
  beforeAll(() => {
    mockKnex.mock(db);
  });
  afterAll(() => {
    mockKnex.unmock(db);
  });
  beforeEach(() => {
    tracker.install();
    mockLogger.info.mockReset();
    mockLogger.error.mockReset();
    mockLaunchDarklyService.isFeatureEnabled.mockReset();
  });
  afterEach(() => {
    tracker.uninstall();
  });

  test('should return list of dispositions from the dispositions db', async () => {
    tracker.on('query', (query, step) => {
      query.response(ruleResponse);
    });
    const result = await getDispositions(svcDependencies, '****************', 'CA', 'nova');
    expect(result).toEqual(dispositionResponse);
    expect(mockLogger.info).toBeCalledTimes(1);
    expect(mockLogger.error).not.toBeCalled();
  });

  test('should not read from dispositions db and return empty response if feature flag is disabled', async () => {
    mockLaunchDarklyService.isFeatureEnabled.mockReturnValueOnce(false);
    const result = await getDispositions(svcDependencies, '****************', 'CA', 'nova');
    expect(result).toEqual([]);
    expect(mockLogger.info).not.toBeCalled();
    expect(mockLogger.error).not.toBeCalled();
  });

  test('should log an error on failed db query', async () => {
    mockLaunchDarklyService.isFeatureEnabled.mockReturnValueOnce(true);
    tracker.on('query', (query, step) => {
      query.reject(new Error());
    });
    try {
      await getDispositions(svcDependencies, '****************', 'CA', 'nova');
    } catch (err) {
      expect(err.message).toEqual('Get Dispositions - failed to fetch dispositions from the database');
      expect(err.statusCode).toEqual(500);
      expect(mockLogger.error).toBeCalledTimes(1);
    }
  });

  test('should read from dispositions db if call to launch darkly fails', async () => {
    mockLaunchDarklyService.isFeatureEnabled.mockRejectedValueOnce(new Error('timeout'));
    tracker.on('query', (query) => {
      query.response([
        {
          disposition_id: '1',
          rule_id: 'Vp4YWkCvFaw5',
          message_id: 'Vp4YWkCvFaw5',
          card_number: cardNumber,
          container: containerOfferIt,
          page: 'accounts-it',
          platform: 'ios',
          application: 'nova',
          disposition_status_id: 1,
          created_by: 'sa',
          created_ts: createdTimeStamp,
          updated_by: null,
          updated_ts: createdTimeStamp,
          expired_ts: '2021-12-31T05:00:00.000Z',
        },
      ]);
    });
    const result = await getDispositions(svcDependencies, '****************', 'CA', 'nova');
    expect(result).toEqual([
      dispositionResponse[0],
    ]);
    expect(mockLogger.error).toBeCalledTimes(1); // ld failure
    expect(mockLogger.info).toBeCalledTimes(1);
  });
});
