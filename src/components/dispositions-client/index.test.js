const knex = require('knex');
const mockKnex = require('mock-knex');
const dispositionsClient = require('./index');

const db = knex({ client: 'mssql', debug: false });
const tracker = mockKnex.getTracker();
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};
const mockRedisClient = {
  lock: jest.fn().mockResolvedValue(1),
  unlock: jest.fn().mockResolvedValue({}),
  get: jest.fn().mockImplementation(args => {
    switch (args) {
      case 'dispositions:next_purge': return '1999';
      case 'dispositions:locked_until': return '2000';
      default: return {};
    }
  }),
  set: jest.fn().mockResolvedValue({}),
  setEx: jest.fn().mockResolvedValue({}),
  del: jest.fn().mockResolvedValue({}),
};
const mockSecret = 'ABA21ZE1X2Y3ZWNhNDcyNKlkZmYxZOLkMTAqNGFkOpP=';
const config = {
  purgeCheckInterval: 1000,
  purgeInterval: 5,
  maxDeleteCount: 10,
  purgeTTL: 20,
  secretCustomerPepper: mockSecret,
};
const mockLaunchDarklyService = {
  init: jest.fn(),
  isFeatureEnabled: jest.fn(),
  close: jest.fn(),
};
mockLaunchDarklyService.init.mockReturnValue(true);
mockLaunchDarklyService.isFeatureEnabled.mockReturnValue(true);

describe('Campaign API Client', () => {
  beforeAll(() => {
    mockKnex.mock(db);
  });
  afterAll(() => {
    mockKnex.unmock(db);
  });

  let defaultClient;

  beforeEach(() => {
    tracker.install();
    mockLogger.info.mockClear();
    mockLogger.error.mockClear();
    mockRedisClient.lock.mockClear();
    mockRedisClient.unlock.mockClear();
    mockRedisClient.get.mockClear();
    mockRedisClient.set.mockClear();
    mockRedisClient.del.mockClear();

    defaultClient = dispositionsClient({
      db,
      logger: mockLogger,
      timeout: 3000,
      schema: 'disposition',
      config,
      redisClient: mockRedisClient,
      launchDarklyService: mockLaunchDarklyService,
    });
  });

  afterEach(() => {
    tracker.uninstall();
    clearInterval(defaultClient.purgeJob);
    clearInterval(defaultClient.purgeJobAlternate);
    defaultClient = undefined;
  });

  test('should init', () => {
    expect(defaultClient).toHaveProperty('getDispositions');
    expect(typeof defaultClient.getDispositions).toEqual('function');
    expect(defaultClient).toHaveProperty('setDisposition');
    expect(typeof defaultClient.setDisposition).toEqual('function');
    expect(defaultClient).toHaveProperty('purgeJob');
    expect(typeof defaultClient.purgeJob).toEqual('object');
  });

  test('should call getDispositions', async () => {
    tracker.on('query', (query, step) => {
      query.response([]);
    });
    const result = await defaultClient.getDispositions('****************', 'CA', 'nova');
    expect(mockLogger.error).not.toBeCalled();
    expect(result).toEqual([]);
  });

  test('should call setDisposition', async () => {
    tracker.on('query', (query, step) => {
      query.response([]);
    });
    const body = {
      message_id: 'XroFuQYEW1wY',
      container: 'offers-and-programs',
      page: 'accounts',
      platform: 'ios',
      application: 'nova',
    };
    await defaultClient.setDisposition('****************', 'CA', 'XroFuQYEW1wY', '2021-03-31T04:00:00.000Z', 'D', body);
    expect(mockLogger.error).not.toBeCalled();
  });

  test('should purge expired disposition', () => {
    jest.useFakeTimers();
    tracker.on('query', (query, step) => {
      if (query.sql === 'select top (@p0) [disposition_id] from [disposition].[disposition] where [expired_ts] < @p1') {
        query.response([ { disposition_id: 1 } ]);
      } else if (query.sql === 'delete from [disposition].[disposition] where [disposition_id] in (@p0);select @@rowcount') {
        query.response(1);
      }
    });
    try {
      jest.advanceTimersByTime(1000);
      setTimeout(() => {
        // if expected db query is not detected by tracker,
        // done() won't be called to terminate the test.
        // Set timeout is expected to be frozen due to fake timer,
        // jest will throw timeout error, which ends up failing the test.
        // This approach is chosen as tracker functionality is affected by jest fake timer
        // preventing any jest assertion callback from being attached to the tracker.
      }, 0);
    } finally {
      jest.useRealTimers();
    }
  });
});
