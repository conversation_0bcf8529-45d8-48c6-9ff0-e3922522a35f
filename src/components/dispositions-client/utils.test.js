const momentTz = require('moment-timezone');
const knex = require('knex');
const mockKnex = require('mock-knex');

const dispositionUtils = require('./utils');

const db = knex({ client: 'mssql', debug: false });
const tracker = mockKnex.getTracker();
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

describe('Purge dispositions', () => {
  let mockRedisClient = {};

  beforeAll(() => {
    mockKnex.mock(db);
  });
  afterAll(() => {
    mockKnex.unmock(db);
  });
  beforeEach(() => {
    tracker.install();
    mockLogger.info.mockClear();
    mockLogger.error.mockClear();
    mockRedisClient = {
      lock: jest.fn().mockResolvedValue({}),
      unlock: jest.fn().mockResolvedValue({}),
      get: jest.fn().mockResolvedValue({}),
      set: jest.fn().mockResolvedValue({}),
      setEx: jest.fn().mockResolvedValue({}),
      del: jest.fn().mockResolvedValue({}),
    };
  });
  afterEach(() => {
    tracker.uninstall();
  });

  test('should invoke timer', () => {
    jest.useFakeTimers('legacy');
    mockRedisClient.get.mockResolvedValue(undefined);

    const config = {
      purgeCheckInterval: 1000,
      purgeInterval: 1,
      deleteCheck: 5,
      maxDeleteCount: 10,
      purgeTTL: 5,
    };
    const deps = {
      logger: mockLogger,
      redisClient: mockRedisClient,
      db: { raw: jest.fn() },
      schema: 'disposition',
    };
    dispositionUtils.scheduleDispositionPurge(deps, config);
    expect(setInterval).toHaveBeenCalledTimes(1);
    expect(setInterval).toHaveBeenLastCalledWith(expect.any(Function), 1000);
    jest.advanceTimersByTime(2000);
  });

  test('should purge if next purge date not set and locked until not set', async () => {
    // setup mockRedisClient to return undefined for nextPruge and lockedUntil keys
    mockRedisClient.get
      .mockResolvedValueOnce(undefined)
      .mockResolvedValueOnce(undefined);

    const config = {
      purgeCheckInterval: 1000,
      purgeInterval: 1,
      maxDeleteCount: 10,
      deleteCheck: 5,
      purgeTTL: 5,
    };
    const result = await dispositionUtils.purgeCheck(mockRedisClient, config.purgeTTL);
    expect(mockLogger.error).not.toBeCalled();
    expect(mockRedisClient.lock).toBeCalledTimes(1);
    expect(mockRedisClient.unlock).toBeCalledTimes(1);
    expect(mockRedisClient.setEx).toBeCalled();
    expect(result.shouldPurgeData).toBe(true);
  });
  test('should not purge if next purge date has not passed', async () => {
    // setup mockRedisClient to return undefined for nextPruge and lockedUntil keys
    const nextPurgeTime = momentTz.utc().subtract(3, 'minutes').format();
    const lockEndTime = momentTz.utc().add(10, 'minutes').format();
    mockRedisClient.get
      .mockResolvedValueOnce(lockEndTime)
      .mockResolvedValueOnce(nextPurgeTime);
    const result = await dispositionUtils.purgeCheck(mockRedisClient, 5);
    expect(mockLogger.error).not.toBeCalled();
    expect(mockRedisClient.lock).toBeCalledTimes(1);
    expect(mockRedisClient.unlock).toBeCalledTimes(1);
    expect(mockRedisClient.setEx).not.toBeCalled();
    expect(result).toBe(false);
  });
  test.only('should not purge if next purge date has passed and purge operation is in progress', async () => {
    // setup mockRedisClient to return undefined for nextPruge and lockedUntil keys
    const nextPurgeTime = momentTz.utc().subtract(3, 'minutes').format();
    const lockEndTime = momentTz.utc().add(10, 'minutes').format();
    mockRedisClient.get
      .mockResolvedValueOnce(lockEndTime)
      .mockResolvedValueOnce(nextPurgeTime);
    const result = await dispositionUtils.purgeCheck(mockRedisClient, 5);
    expect(mockLogger.error).not.toBeCalled();
    expect(mockRedisClient.setEx).not.toBeCalled();
    expect(result.shouldPurgeData).toBe(false);
  });
});
