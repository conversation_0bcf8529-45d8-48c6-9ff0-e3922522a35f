const { scheduleDispositionPurge } = require('./utils');
const getDispositions = require('./get-dispositions');
const setDisposition = require('./set-disposition');
const clearDisposition = require('./clear-disposition');

const init = ({ db, logger, timeout, schema, config, redisClient, launchDarklyService }) => {
  const purgeJob = scheduleDispositionPurge({ logger, redisClient, db, schema }, config);
  const svcDependencies = { db, logger, timeout, schema, secret: config.secretCustomerPepper, launchDarklyService, config };
  return {
    getDispositions: (userId, country, application) =>
      getDispositions(svcDependencies, userId, country, application),
    setDisposition: (userId, country, ruleId, expiry, disposition, opts) =>
      setDisposition(svcDependencies, userId, country, ruleId, expiry, disposition, opts),
    clearDisposition: (...props) =>
      clearDisposition(svcDependencies, ...props),
    purgeJob,
  };
};

module.exports = init;
