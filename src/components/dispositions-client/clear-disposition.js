const { HttpError } = require('../errors');
const { getDbUidHash } = require('./utils');

/**
 * Clear disposition in disposition db
 * @param {Object} dependencies
 * @param {Object} dependencies.db - db client
 * @param {Object} dependencies.logger - logger
 * @param {Object} dependencies.timeout - db query timeout limit in ms
 * @param {Object} dependencies.schema - disposition db schema
 * @param {Object} dependencies.launchDarklyService - ld client
 * @param {Object} dependencies.secret - key used to create unforgeable hash
 * @param {string} uid - user identifier, can be card number or user context for CCAU countries
 * @param {Object} ruleId - rule id
 * @param {string} container - pigeon rule container placement
 * @param {string} country - the user's country
 * @returns
 */
const clearDisposition = async (
  { db, logger, timeout, schema, launchDarklyService, secret }, // dependencies
  uid,
  ruleId,
  container, // data
  country,
) => {
  // adding container conditionally
  const addingContainerConditionally = (queryBuilder) => {
    if (container) {
      queryBuilder.where('container', container);
    }
    return queryBuilder;
  };
  // check is LD flag is enabled
  let isDispositionsDbEnabled = true;
  try {
    isDispositionsDbEnabled = await launchDarklyService.isFeatureEnabled('pigeon-api.features.dispositions.db', true);
  } catch (err) {
    logger.error({ message: 'unable to call Launch Darkly for dispositions db from clear-campaigns', err });
  }
  if (!isDispositionsDbEnabled) return;

  // get user infromation like country and uid , ..etc
  const cardNumber = getDbUidHash(logger, secret, uid, country);
  // check if the rule is exist
  const findDispositionQuery = await db.withSchema(schema)
    .where({
      rule_id: ruleId,
      card_number: cardNumber,
    })
    .modify(addingContainerConditionally)
    .select([ 'rule_id', 'message_id', 'disposition_status_id' ])
    .from('disposition')
    .limit(1)
    .timeout(timeout);
  if (!findDispositionQuery.length) {
    throw HttpError.badRequest(`Clearing Disposition - This is rule id :${ruleId} is not found`);
  }

  // delete the dispostion record
  await db.withSchema(schema)
    .where({
      rule_id: ruleId,
      card_number: cardNumber,
    })
    .modify(addingContainerConditionally)
    .from('disposition').del();
};

module.exports = clearDisposition;
