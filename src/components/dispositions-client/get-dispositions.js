
const { HttpError } = require('../errors');
const { getDbAppVal, getDbUidHash, getDispositionValue } = require('./utils');

/**
 * Get dispositions from disposition db.
 * For CCAU countries, to ensure global uniqueness in terms of user identifier,
 * card_number column will be stored with hash of cif instead of card number,
 * and application column will be appended with -<country>. e.g. "wave-JM".
 * For backwards compatibility until migration to mongo db, Canadian data
 * will continue to use card number and application under existing format.
 *
 * @param {Object} dependencies
 * @param {Object} dependencies.db - db client
 * @param {Object} dependencies.logger - logger
 * @param {Object} dependencies.timeout - db query timeout limit in ms
 * @param {Object} dependencies.schema - disposition db schema
 * @param {Object} dependencies.launchDarklyService - ld client
 * @param {Object} dependencies.secret - key used to create unforgeable hash
 * @param {string} userId - user identifier, can be card number or user context for CCAU countries
 * @param {string} country - required to globally identify customers
 * @param {string} application - pigeon rule application placement
 * @returns dispositions found for user identifier and application provided
 */
const getDispositions = async (
  { db, logger, timeout, schema, secret, launchDarklyService },
  userId,
  country,
  application,
) => {
  if (!userId || !application) {
    throw HttpError.badRequest('Get Dispositions - Card number and application required to query dispositions db');
  }

  // FEATURE FLAG HERE
  let isDispositionsDbEnabled;
  try {
    isDispositionsDbEnabled = await launchDarklyService.isFeatureEnabled('pigeon-api.features.dispositions.db', true);
  } catch (err) {
    logger.error({ message: 'unable to call Launch Darkly for dispositions db from get-campaigns', err });
    isDispositionsDbEnabled = true;
  }

  if (isDispositionsDbEnabled === false) {
    return [];
  }

  try {
    const result = await db
      .withSchema(schema)
      .timeout(timeout)
      .where({
        card_number: getDbUidHash(logger, secret, userId, country),
        application: getDbAppVal(application, country),
      })
      .from('disposition')
      .select([ 'rule_id', 'message_id', 'disposition_status_id', 'container' ]);

    logger.info({ message: 'Get Dispositions - dispositions successfully fetched from the database', result });

    return result.map(disposition => ({
      rule_id: disposition.rule_id,
      message_id: disposition.message_id,
      disposition: getDispositionValue(disposition.disposition_status_id),
      container: disposition.container,
    }));
  } catch (err) {
    logger.error({ message: 'Get Dispositions - failed to fetch dispositions from the database', err });
    throw HttpError.internalServer('Get Dispositions - failed to fetch dispositions from the database', [ { error: err.message } ]);
  }
};

module.exports = getDispositions;
