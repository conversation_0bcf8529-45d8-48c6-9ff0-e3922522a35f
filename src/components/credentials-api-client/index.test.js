const CredentialsClient = require('./index');
const { launchDarklyService, logger } = require('../../../acceptance-tests/mock');
describe('Credentials client', () => {
  const mockSamlToken = 'mock_saml_token';
  const fetch = jest.fn().mockResolvedValue({
    ok: true,
    text: jest.fn().mockResolvedValue(`{
      "saml_token": "${mockSamlToken}"
    }`),
  });

  const mockSamlData = {
    mockProp: 1,
  };

  it('service should init correctly', () => {
    const credentialsClient = CredentialsClient({
      fetch,
    }, 'Bearer: mock', 'en', 'testred', mockSamlData);
    expect(typeof credentialsClient.createSamlToken).toEqual('function');
  });

  it('should create saml token correctly', async () => {
    const credentialsClient = CredentialsClient({
      fetch,
      launchDarklyService: await launchDarklyService(),
      logger,
    }, 'Bearer: mock', 'en', 'testred', mockSamlData);
    const token = await credentialsClient.createSamlToken('token', 'en', 'testred', mockSamlData);
    expect(token).toEqual(mockSamlToken);
  });
});
