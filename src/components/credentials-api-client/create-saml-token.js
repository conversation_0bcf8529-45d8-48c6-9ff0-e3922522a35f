const R = require('ramda');
const { getResponse } = require('../common');
const { handleLDError } = require('../campaign/routes/common');

const validEnvs = [ 'istgreen', 'istred', 'istblack', 'uatgreen', 'uatred', 'uatblack', 'uatgold' ];

const ATLAS_CREDENTIALS_FLAG = 'pigeon-api.downstreams.credentials-atlas';

const createSamlToken = async ({ logger, fetch, launchDarklyService }, credentialsAPI, language, env, data, cardNumber) => {
  const headers = {
    'Content-Type': 'application/json; charset=utf-8',
    Accept: 'application/json',
    'X-Language': language,
    'x-customer-scotiacard': cardNumber,
  };
  let credentialsUrl = credentialsAPI.uri;
  try {
    credentialsUrl = await launchDarklyService.isFeatureEnabled(ATLAS_CREDENTIALS_FLAG, false) ? credentialsAPI.AtlasUri : credentialsAPI.uri;
  } catch (err) {
    handleLDError({ err, flag: ATLAS_CREDENTIALS_FLAG, caller: 'getRewardPoints method', logger });
  }

  if (typeof env === 'string' && validEnvs.includes(env.toLowerCase())) {
    headers['Preferred-Environment'] = env;
  }
  const result = await fetch(`${credentialsUrl}/v3/security/authenticators/saml-token`, {
    headers,
    method: 'POST',
    body: JSON.stringify(data),
  });
  const response = await getResponse(result);
  return R.propOr(null, 'saml_token', response);
};

module.exports = createSamlToken;
