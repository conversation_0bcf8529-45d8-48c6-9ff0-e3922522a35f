const { Router } = require('express');
const routes = require('./index');

const mockNext = jest.fn();
const mockJson = jest.fn();
const mockStatus = jest.fn();

const mockLogger = {
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};
const mockAuthorize = () => (req, res, next) => next();

const router = routes({ logger: mockLogger, authorize: mockAuthorize }, { space: 'IST', features: { application: true, disposition: true } });

describe('Campaign routes', () => {
  beforeEach(() => {
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
  });
  test('should be an express.Router object', () => {
    expect(router.prototype).toEqual(Router.prototype);
    expect(typeof router.use).toEqual('function');
    expect(typeof router.handle).toEqual('function');
  });
  test('should have POST /dispositions', () => {
    const found = router.stack.some((item) => item.route.path === '/' && item.route.methods.post);
    expect(found).toEqual(true);
  });
});
