/* eslint-disable sonarjs/no-duplicate-string */
const httpMocks = require('node-mocks-http');
const setDisposition = require('./set-disposition');

const defaultToken = 'opaque-token';
const config = { tokenPath: 'auth.token', tokenClaimsPath: 'auth.claims', anonymousClientIds: [ 'test' ] };
const channelId = 'Mobile';
const xApplication = 'N1';
const messageId = 'message-id';
const messageCategory = 'KYC';
const ruleId = '1adzsnGZ3QWe';
const cardNumber = '4000********3333';
const userIdentifier = '4000********3333';
const country = 'CA';
const spanId = '********';
const traceId = '****************';
const customerId = '***************';
const xOriginatingApplCode = 'BFB6';

const mockTargetedCampaignData = {
  message_id: messageId,
  message_status: ' ',
  start_date: '2021-01-01',
  expiry_date: '2021-12-31',
  application_data: [],
};

let mockRule = {
  id: ruleId,
  external_ref: 'ORCDL',
  content_space: 'samplespace',
  content_type: 'creditCardOffer',
  content_id: 'aaaa1111',
  container: 'priority-box',
  pages: [ 'accounts', 'activities' ],
  status: 'published',
  start_at: '2021-01-01T00:00:00.000Z',
  end_at: '2021-01-20T00:00:00.000Z',
  disabled: false,
  dismissable_flag: true,
  platforms: [ 'ios', 'Android', 'web' ],
  type: 'campaign',
};

let mockCcauRule = {
  id: '1adzsnGZ3QWeCcau',
  external_ref: 'ORCDL',
  content_space: 'samplespace',
  content_type: 'creditCardOffer',
  content_id: 'aaaa1111',
  container: 'offers-and-programs',
  pages: [ 'accounts', 'activities' ],
  status: 'published',
  start_at: '2021-01-01T00:00:00.000Z',
  end_at: '2021-01-20T00:00:00.000Z',
  disabled: false,
  dismissable_flag: true,
  platforms: [ 'ios', 'Android', 'web' ],
  type: 'ccau_campaign',
};

const defaultHeaders = {
  'Preferred-Environment': 'istgreen',
  Authorization: `Bearer: ${defaultToken}`,
  'x-customer-scotiacard': cardNumber,
  'x-channel-id': channelId,
  'x-application': xApplication,
  'x-b3-traceid': traceId,
  'x-b3-spanid': spanId,
};

const campaignCacheService = {
  get: jest.fn().mockReturnValue({
    campaigns: {
      [mockRule.id]: mockRule,
      [mockCcauRule.id]: mockCcauRule },
  }),
};

const targetedCampaignService = {
  getInstance: jest.fn(),
};
targetedCampaignService.getInstance.mockReturnValue({
  getCampaign: jest.fn().mockResolvedValue({ data: mockTargetedCampaignData, notifications: [] }),
  setDisposition: jest.fn().mockResolvedValue(true),
});
const dispositionsService = {
  setDisposition: jest.fn().mockResolvedValue(true),
};
const status = jest.fn();
const json = jest.fn();
const next = jest.fn();
const logger = {
  error: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
};

describe('Set Disposition Campaign', () => {
  beforeEach(() => {
    campaignCacheService.get.mockClear();
    targetedCampaignService.getInstance().getCampaign.mockClear();
    targetedCampaignService.getInstance().setDisposition.mockClear();
    dispositionsService.setDisposition.mockClear();
    next.mockClear();
    json.mockClear();
    status.mockClear();
    logger.error.mockClear();
    logger.info.mockClear();
  });
  test('should initialize', () => {
    expect(setDisposition).toBeDefined();
    expect(typeof setDisposition).toEqual('function');
  });
  test('should not fail on valid targeted campaign (PW flow - rule_id + message_id)', async () => {
    mockRule.container = 'offers-and-programs';
    const { req, res } = httpMocks.createMocks({
      headers: defaultHeaders,
      query: {},
      body: {
        message_id: messageId,
        rule_id: ruleId,
        disposition: 'V',
        application: 'nova',
        platform: 'ios',
        page: 'accounts',
        container: 'offers-and-programs',
      },
    }, {
      locals: { auth: { token: defaultToken }, language: 'en', country: 'CA' },
    });
    const endpoint = setDisposition({
      logger,
      campaignCacheService,
      targetedCampaignService,
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(0);
    // get campaign data
    expect(targetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
    const getCampaignCall = targetedCampaignService.getInstance().getCampaign.mock.calls[0];
    expect(getCampaignCall[0]).toEqual({
      id: messageId,
      customerToken: undefined,
      userIdentifier,
      language: 'en',
      preferredEnv: 'istgreen',
      channelId,
      xApplication,
      useMock: false,
      traceId,
      spanId,
      type: 'campaign',
      country: 'CA',
      xOriginatingApplCode,
    });
    // set disposition to Insight
    expect(targetedCampaignService.getInstance().setDisposition).toBeCalledTimes(1);
    const setDispositionCall = targetedCampaignService.getInstance().setDisposition.mock.calls[0];
    expect(setDispositionCall[0]).toEqual({
      id: messageId,
      disposition: 'V',
      customerToken: undefined,
      userIdentifier,
      language: 'en',
      preferredEnv: 'istgreen',
      channelId,
      xApplication,
      useMock: false,
      traceId,
      spanId,
      messageCategory: undefined,
      country: 'CA',
      type: 'campaign',
      'xOriginatingApplCode': 'BFB6',
      'customerId': undefined,
    });
    // save disposition to DB
    expect(dispositionsService.setDisposition).toBeCalledTimes(1);
    const saveDispositionCall = dispositionsService.setDisposition.mock.calls[0];
    expect(saveDispositionCall[0]).toEqual(cardNumber);
    expect(saveDispositionCall[1]).toEqual(country);
    expect(saveDispositionCall[2]).toEqual(ruleId);
    expect(saveDispositionCall[3]).toEqual(mockRule.end_at);
    expect(saveDispositionCall[4]).toEqual('V');
    expect(saveDispositionCall[5]).toEqual({
      message_id: messageId,
      container: 'offers-and-programs',
      page: 'accounts',
      application: 'nova',
      platform: 'ios',
    });
  });
  test('should not fail on valid targeted campaign (Constellation flow - message_id only)', async () => {
    mockRule.container = 'offers-and-programs';
    const { req, res } = httpMocks.createMocks({
      headers: {
        ...defaultHeaders,
        'x-customer-id': customerId,
        'x-originating-appl-code': 'BFYL',
        'x-language': 'en',
        'x-country-code': 'CA',
      },
      query: {},
      body: {
        message_id: messageId,
        disposition: 'A',
        application: 'lpob',
        platform: 'web',
      },
    }, {
      locals: { auth: { token: defaultToken }, country: 'CA' },
    });
    const endpoint = setDisposition({
      logger,
      campaignCacheService,
      targetedCampaignService,
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(0);
    // get campaign data
    expect(targetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
    const getCampaignCall = targetedCampaignService.getInstance().getCampaign.mock.calls[0];
    expect(getCampaignCall[0]).toEqual({
      id: messageId,
      userIdentifier,
      language: 'en',
      preferredEnv: 'istgreen',
      channelId,
      xApplication,
      useMock: false,
      traceId,
      spanId,
      country: 'CA',
      xOriginatingApplCode: 'BFYL',
      type: undefined,
      customerToken: undefined,
    });
    // set disposition to Insight
    expect(targetedCampaignService.getInstance().setDisposition).toBeCalledTimes(1);
    const setDispositionCall = targetedCampaignService.getInstance().setDisposition.mock.calls[0];
    expect(setDispositionCall[0]).toEqual({
      id: messageId,
      disposition: 'A',
      userIdentifier,
      language: 'en',
      preferredEnv: 'istgreen',
      channelId,
      xApplication,
      useMock: false,
      traceId,
      spanId,
      country: 'CA',
      customerId,
      'xOriginatingApplCode': 'BFYL',
      'type': undefined,
      'messageCategory': undefined,
      'customerToken': undefined,
    });
    // should not store to disposition DB (D/V dispositions only)
    expect(dispositionsService.setDisposition).not.toHaveBeenCalled();
  });
  test('should not fail on valid targeted softstop campaign (AKYC flow - rule_id + message_category)', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: defaultHeaders,
      query: {},
      body: {
        message_category: messageCategory,
        disposition: 'A',
        application: 'cob',
        platform: 'web',
        rule_id: ruleId,
      },
    }, {
      locals: { auth: { token: defaultToken }, language: 'en', country: 'CA' },
    });
    const endpoint = setDisposition({
      logger,
      campaignCacheService,
      targetedCampaignService,
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(0);
    // get campaign data
    expect(targetedCampaignService.getInstance().getCampaign).toBeCalledTimes(0);
    // set disposition to Insight
    expect(targetedCampaignService.getInstance().setDisposition).toBeCalledTimes(1);
    const setDispositionCall = targetedCampaignService.getInstance().setDisposition.mock.calls[0];
    expect(setDispositionCall[0]).toEqual({
      customerToken: undefined,
      userIdentifier,
      language: 'en',
      preferredEnv: 'istgreen',
      channelId,
      xApplication,
      useMock: false,
      traceId,
      spanId,
      messageId: undefined,
      messageCategory,
      disposition: 'A',
      country: 'CA',
      id: undefined,
      type: 'campaign',
      customerId: undefined,
      'xOriginatingApplCode': 'BFB6',
    });
    // save disposition to DB
    expect(dispositionsService.setDisposition).toBeCalledTimes(0);
  });
  test('should not fail on valid mass campaign', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: defaultHeaders,
      query: {},
      body: {
        rule_id: ruleId,
        disposition: 'V',
        application: 'nova',
        platform: 'ios',
        page: 'accounts',
        container: 'offers-and-programs',
      },
    }, {
      locals: { auth: { token: defaultToken }, country: 'CA' },
    });
    const endpoint = setDisposition({
      logger,
      campaignCacheService,
      targetedCampaignService,
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(0);
    // get campaign data
    expect(targetedCampaignService.getInstance().getCampaign).toBeCalledTimes(0);
    // set disposition to Insight
    expect(targetedCampaignService.getInstance().setDisposition).toBeCalledTimes(0);
    // save disposition to DB
    expect(dispositionsService.setDisposition).toBeCalledTimes(1);
    const saveDispositionCall = dispositionsService.setDisposition.mock.calls[0];
    expect(saveDispositionCall[0]).toEqual(cardNumber);
    expect(saveDispositionCall[1]).toEqual(country);
    expect(saveDispositionCall[2]).toEqual(ruleId);
    expect(saveDispositionCall[3]).toEqual(mockRule.end_at);
    expect(saveDispositionCall[4]).toEqual('V');
    expect(saveDispositionCall[5]).toEqual({
      container: 'offers-and-programs',
      page: 'accounts',
      application: 'nova',
      platform: 'ios',
    });
  });
  test('should fail on body validation - rule_id, message_id or message_category is missing when required', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: defaultHeaders,
      query: {},
      body: {
        disposition: 'V',
        application: 'nova',
        platform: 'ios',
        page: 'accounts',
        container: 'offers-and-programs',
      },
    }, {
      locals: { auth: { token: defaultToken } },
    });
    const endpoint = setDisposition({
      logger,
      campaignCacheService,
      targetedCampaignService,
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err.message).toEqual('Validation error');
    expect(err.metadata[0].message).toEqual('rule_id, message_id or message_category is required');
  });
  test('should fail on body validation - rule_id is missing when required', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: defaultHeaders,
      query: {},
      body: {
        message_id: messageId,
        disposition: 'V',
        application: 'nova',
        platform: 'ios',
        page: 'accounts',
        container: 'offers-and-programs',
      },
    }, {
      locals: { auth: { token: defaultToken } },
    });
    const endpoint = setDisposition({
      logger,
      campaignCacheService,
      targetedCampaignService,
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err.message).toEqual('Validation error');
    expect(err.metadata[0].message).toEqual('rule_id is required');
  });
  test('should fail on body validation - page is missing when required', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: defaultHeaders,
      query: {},
      body: {
        message_id: messageId,
        rule_id: ruleId,
        disposition: 'V',
        application: 'nova',
        platform: 'ios',
        container: 'offers-and-programs',
      },
    }, {
      locals: { auth: { token: defaultToken } },
    });
    const endpoint = setDisposition({
      logger,
      campaignCacheService,
      targetedCampaignService,
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err.message).toEqual('Validation error');
    expect(err.metadata[0].message).toEqual('page is required');
  });
  test('should fail on body validation - container is missing when required', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: defaultHeaders,
      query: {},
      body: {
        message_id: messageId,
        rule_id: ruleId,
        disposition: 'V',
        application: 'nova',
        platform: 'ios',
        page: 'accounts',
      },
    }, {
      locals: { auth: { token: defaultToken } },
    });
    const endpoint = setDisposition({
      logger,
      campaignCacheService,
      targetedCampaignService,
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(logger.warn).toBeCalledTimes(1);
  });
  test('should fail when card number not set', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: {
        ...defaultHeaders,
        'x-customer-scotiacard': null,
      },
      query: {},
      body: {
        message_id: messageId,
        rule_id: ruleId,
        disposition: 'V',
        application: 'nova',
        platform: 'ios',
        page: 'accounts',
        container: 'offers-and-programs',
      },
    }, {
      locals: { auth: { token: defaultToken } },
    });
    const endpoint = setDisposition({
      logger,
      campaignCacheService,
      targetedCampaignService,
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err.message).toEqual('card number or user context is required');
  });
  test('should fail when rule not found', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: defaultHeaders,
      query: {},
      body: {
        message_id: messageId,
        rule_id: ruleId,
        disposition: 'V',
        application: 'nova',
        platform: 'ios',
        page: 'accounts',
        container: 'offers-and-programs',
      },
    }, {
      locals: { auth: { token: defaultToken } },
    });
    campaignCacheService.get.mockReturnValueOnce({ campaigns: {} });
    const endpoint = setDisposition({
      logger,
      campaignCacheService,
      targetedCampaignService,
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err.message).toEqual('Campaign not found');
    expect(err.metadata[0].message).toEqual('campaign rule not found');
    expect(campaignCacheService.get).toBeCalledTimes(1);
  });
  test('should fail when get campaign from Insight fails', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: defaultHeaders,
      query: {},
      body: {
        message_id: messageId,
        rule_id: ruleId,
        disposition: 'V',
        application: 'nova',
        platform: 'ios',
        page: 'accounts',
        container: 'offers-and-programs',
      },
    }, {
      locals: { auth: { token: defaultToken } },
    });
    targetedCampaignService.getInstance().getCampaign.mockRejectedValueOnce(new Error('error text'));
    const endpoint = setDisposition({
      logger,
      campaignCacheService,
      targetedCampaignService,
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err.message).toEqual('Internal Server Error');
    expect(err.metadata[0].message).toEqual('Failed to get downstream targeted campaign');
    expect(targetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
  });
  test('should fail when set campaign disposition to Insight fails', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: defaultHeaders,
      query: {},
      body: {
        message_id: messageId,
        rule_id: ruleId,
        disposition: 'V',
        application: 'nova',
        platform: 'ios',
        page: 'accounts',
        container: 'offers-and-programs',
      },
    }, {
      locals: { auth: { token: defaultToken } },
    });
    targetedCampaignService.getInstance().setDisposition.mockRejectedValueOnce(new Error('error text'));
    const endpoint = setDisposition({
      logger,
      campaignCacheService,
      targetedCampaignService,
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err.message).toEqual('Server error');
    expect(err.metadata[0].message).toEqual('failed to set insight disposition');
    expect(targetedCampaignService.getInstance().setDisposition).toBeCalledTimes(1);
  });
  test('should fail when save disposition to database fails', async () => {
    const { req, res } = httpMocks.createMocks({
      headers: defaultHeaders,
      query: {},
      body: {
        message_id: messageId,
        rule_id: ruleId,
        disposition: 'V',
        application: 'nova',
        platform: 'ios',
        page: 'accounts',
        container: 'offers-and-programs',
      },
    }, {
      locals: { auth: { token: defaultToken } },
    });
    dispositionsService.setDisposition.mockRejectedValueOnce(new Error('error text'));
    const endpoint = setDisposition({
      logger,
      campaignCacheService,
      targetedCampaignService,
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err.metadata[0].message).toEqual('failed to save disposition to database');
    expect(dispositionsService.setDisposition).toBeCalledTimes(1);
  });
  test('should fail when rule type country and country code header do not match ', async () => {
    mockRule.container = 'offers-and-programs';
    const { req, res } = httpMocks.createMocks({
      headers: {
        ...defaultHeaders,
        'x-country-code': 'DO',
        'x-user-context': 'b64encodedusercontext',
      },
      query: {},
      body: {
        message_id: messageId,
        rule_id: ruleId,
        disposition: 'V',
        application: 'nova',
        platform: 'ios',
        page: 'accounts',
        container: 'offers-and-programs',
      },
    }, {
      locals: { auth: { token: defaultToken }, country: 'DO', language: 'en' },
    });
    const endpoint = setDisposition({
      logger,
      campaignCacheService,
      targetedCampaignService,
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalled();
    const err = next.mock.calls[0][0];
    expect(err.message).toEqual('Given country has insufficient access to rule types available');
    expect(err.statusCode).toEqual(400);
  });
  test('should not fail on valid CCAU targeted campaign', async () => {
    mockRule.container = 'offers-and-programs';
    const { req, res } = httpMocks.createMocks({
      headers: {
        ...defaultHeaders,
        'x-user-context': userIdentifier,
        'x-country-code': 'DO',
      },
      query: {},
      body: {
        message_id: messageId,
        rule_id: mockCcauRule.id,
        disposition: 'V',
        application: 'wave',
        platform: 'ios',
        page: 'accounts',
        container: 'offers-and-programs',
      },
    }, {
      locals: { auth: { token: defaultToken }, country: 'DO', language: 'en' },
    });
    const endpoint = setDisposition({
      logger,
      campaignCacheService,
      targetedCampaignService,
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(0);
    // get campaign data
    expect(targetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
    const getCampaignCall = targetedCampaignService.getInstance().getCampaign.mock.calls[0];
    expect(getCampaignCall[0]).toEqual({
      id: messageId,
      customerToken: undefined,
      userIdentifier,
      language: 'en',
      preferredEnv: 'istgreen',
      channelId,
      xApplication,
      useMock: false,
      traceId,
      spanId,
      type: 'ccau_campaign',
      country: 'DO',
      xOriginatingApplCode,
    });
    // set disposition to dc-campaign
    expect(targetedCampaignService.getInstance().setDisposition).toBeCalledTimes(1);
    const setDispositionCall = targetedCampaignService.getInstance().setDisposition.mock.calls[0];
    expect(setDispositionCall[0]).toEqual({
      id: messageId,
      disposition: 'V',
      customerToken: undefined,
      userIdentifier,
      language: 'en',
      preferredEnv: 'istgreen',
      channelId,
      xApplication,
      useMock: false,
      traceId,
      spanId,
      messageCategory: undefined,
      country: 'DO',
      customerId: undefined,
      type: 'ccau_campaign',
      'xOriginatingApplCode': 'BFB6',
    });
    // save disposition to DB
    expect(dispositionsService.setDisposition).toBeCalledTimes(1);
    const saveDispositionCall = dispositionsService.setDisposition.mock.calls[0];
    expect(saveDispositionCall[0]).toEqual(cardNumber);
    expect(saveDispositionCall[1]).toEqual('DO');
    expect(saveDispositionCall[2]).toEqual(mockCcauRule.id);
    expect(saveDispositionCall[3]).toEqual(mockRule.end_at);
    expect(saveDispositionCall[4]).toEqual('V');
    expect(saveDispositionCall[5]).toEqual({
      message_id: messageId,
      container: 'offers-and-programs',
      page: 'accounts',
      application: 'wave',
      platform: 'ios',
    });
  });

  test('should accept array of pages', async () => {
    mockRule.container = 'offers-and-programs';
    const { req, res } = httpMocks.createMocks({
      headers: defaultHeaders,
      query: {},
      body: {
        message_id: messageId,
        rule_id: ruleId,
        disposition: 'V',
        application: 'nova',
        platform: 'ios',
        page: 'accounts',
        container: 'offers-and-programs',
      },
    }, {
      locals: { auth: { token: defaultToken }, language: 'en', country: 'CA' },
    });
    const endpoint = setDisposition({
      logger,
      campaignCacheService,
      targetedCampaignService,
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(0);
    // get campaign data
    expect(targetedCampaignService.getInstance().getCampaign).toBeCalledTimes(1);
    const getCampaignCall = targetedCampaignService.getInstance().getCampaign.mock.calls[0];
    expect(getCampaignCall[0]).toEqual({
      id: messageId,
      customerToken: undefined,
      userIdentifier,
      language: 'en',
      preferredEnv: 'istgreen',
      channelId,
      xApplication,
      useMock: false,
      traceId,
      spanId,
      type: 'campaign',
      country: 'CA',
      xOriginatingApplCode,
    });
    // set disposition to Insight
    expect(targetedCampaignService.getInstance().setDisposition).toBeCalledTimes(1);
    const setDispositionCall = targetedCampaignService.getInstance().setDisposition.mock.calls[0];
    expect(setDispositionCall[0]).toEqual({
      id: messageId,
      disposition: 'V',
      customerToken: undefined,
      userIdentifier,
      language: 'en',
      preferredEnv: 'istgreen',
      channelId,
      xApplication,
      useMock: false,
      traceId,
      spanId,
      messageCategory: undefined,
      country: 'CA',
      type: 'campaign',
      'customerId': undefined,
      'xOriginatingApplCode': 'BFB6',
    });
    // save disposition to DB
    expect(dispositionsService.setDisposition).toBeCalledTimes(1);
    const saveDispositionCall = dispositionsService.setDisposition.mock.calls[0];
    expect(saveDispositionCall[0]).toEqual(cardNumber);
    expect(saveDispositionCall[1]).toEqual(country);
    expect(saveDispositionCall[2]).toEqual(ruleId);
    expect(saveDispositionCall[3]).toEqual(mockRule.end_at);
    expect(saveDispositionCall[4]).toEqual('V');
    expect(saveDispositionCall[5]).toEqual({
      message_id: messageId,
      container: 'offers-and-programs',
      page: 'accounts',
      application: 'nova',
      platform: 'ios',
    });
  });
});
