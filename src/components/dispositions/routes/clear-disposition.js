const R = require('ramda');
const Joi = require('@hapi/joi');
const { HttpError } = require('../../errors');
const { clearDispositionBodySchema } = require('./validation');
const {
  getClientInfo,
} = require('../../helpers');
const {
  validateCampaignRuleType,
} = require('../../common');

const msgValidationError = 'Validation error';
const msgCampaignNotFound = 'Campaign not found';

const clearDisposition = (deps, config) => async (req, res, next) => {
  const {
    campaignCacheService,
    dispositionsService,
  } = deps;
    // validate input parameters
  const { error, value: opts } = Joi.validate(req.body, clearDispositionBodySchema, { stripUnknown: true });
  if (error) {
    next(HttpError.badRequest(msgValidationError, error.details.map((err) => ({ path: err.path.join('.'), message: err.message }))));
    return;
  }
  // get header values
  const {
    country,
    uid,
  } = getClientInfo({ req, res, config });

  if (!uid) {
    next(HttpError.badRequest('card number or user context is required'));
    return;
  }

  // get and verify rule if specified
  const rule = R.clone(R.pathOr({}, [ 'campaigns', opts.rule_id ], campaignCacheService.get()));
  if (rule && !Object.keys(rule).length) {
    next(HttpError.notFound(msgCampaignNotFound, [ { message: 'campaign rule not found' } ]));
    return;
  }

  const { error: ruleTypeError } = validateCampaignRuleType(country, rule.type);
  if (ruleTypeError) {
    return next(HttpError.badRequest(ruleTypeError));
  }

  if (rule && opts.container && rule.container !== opts.container) {
    next(HttpError.badRequest('Error clearing Dispositions validation error: container Name not matching the rule'));
    return;
  }

  // clear to Pigeon DB if required

  try {
    await dispositionsService.clearDisposition(
      uid,
      opts.rule_id,
      opts.container,
      country,
    );
  } catch (err) {
    next(HttpError.internalServer('Server error', [ { message: 'failed to clear disposition to database', error: err.message } ]));
    return;
  }

  // send response back
  res.status(200).json({ data: {}, notifications: [] });
};

module.exports = clearDisposition;
