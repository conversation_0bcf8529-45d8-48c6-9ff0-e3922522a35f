const { Router } = require('express');
const wrapAsync = require('../../server/middleware/wrap-async');
const setDisposition = require('./set-disposition');
const { scopes } = require('../../campaign/routes/common');

const tempScope = 'ca:baas:campaign-rules:read';

const init = ({
  logger,
  targetedCampaignService,
  campaignCacheService,
  dispositionsService,
  authorize,
}, config) => {
  const router = Router();
  // set disposition
  router.post(
    '/',
    authorize([ tempScope, scopes.campaignsDispositionsWrite, scopes.standard, scopes.legacy.campaignsDispositionsWrite ]),
    wrapAsync(setDisposition({ logger, targetedCampaignService, campaignCacheService, dispositionsService }, config)));
  return router;
};

module.exports = init;
