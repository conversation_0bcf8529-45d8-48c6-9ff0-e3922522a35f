/* eslint-disable sonarjs/no-duplicate-string */
/* eslint-disable sonarjs/cognitive-complexity */
/* eslint-disable quotes */
const R = require('ramda');
const Joi = require('@hapi/joi');
const crypto = require('crypto');
const { HttpError } = require('../../errors');
const { dispositionBodySchema } = require('./validation');
const {
  calcTargetedCampaignExpiry,
  generateMessageIdsPega,
  getClientInfo,
} = require('../../helpers');
const {
  dispositionUpdateRequired,
  shouldStoreDispositionToDb,
  messageSourceArr,
  dispositionValues,
  dispositionDatabase,
  validateCampaignRuleType,
  ORION_INBOX_CONTAINER_ID,
  ORION_INBOX_PAGE_ID,
} = require('../../common');

const msgValidationError = 'Validation error';
const msgCampaignNotFound = 'Campaign not found';

/**
 * Get targeted campaign data from Insight or DC service
 * @param {object} targetedCampaignService Targeted campaign service's instance
 * @param {string} messageId Campaign's message id
 * @param {object} headers Header values to pass to targeted campaign service
 * @returns {Promise} Campaign data
 */
const getTargetedCampaignData = async (targetedCampaignService, messageId, data) => {
  try {
    const response = await targetedCampaignService.getInstance({ ruleType: data.type }).getCampaign({ ...data, id: messageId });
    const campaignData = response.data;
    if (!campaignData) {
      return Promise.reject(HttpError.notFound('Not found', [ { message: 'campaign with specified message_id is not found' } ]));
    }
    return campaignData;
  } catch (err) {
    if (err instanceof HttpError && err.statusCode < 500) {
      // known error
      return Promise.reject(err);
    }
    const originalError = R.pipe(R.pathOr([], [ 'response', 'notifications' ]), R.head)(err);
    if (originalError && originalError.code === 'INSIGHT-001-E') {
      return Promise.reject(HttpError.notFound('Not found', [ {
        message: `campaign with specified message_id is not found`,
        notifications: R.pathOr([], [ 'response', 'notifications' ], err),
      } ]));
    }
    return Promise.reject(HttpError.internalServer('Internal Server Error', [ {
      message: `Failed to get downstream targeted campaign`,
      notifications: R.pathOr([], [ 'response', 'notifications' ], err),
    } ]));
  }
};

/**
 * Set disposition for a campaign using Insight or DC service
 * @param {object} targetedCampaignService Targeted campaign service's instance
 * @param {string|undefined} messageId Campaign's message id
 * @param {string|undefined} messageCategory Campaign's message category
 * @param {string} disposition Disposition value
 * @param {*} headers Header values to pass to targeted campaign service
 * @returns {Promise} Status
 */
const setTargetedCampaignDisposition = async (targetedCampaignService, messageId, messageCategory, disposition, headers, logger) => {
  try {
    await targetedCampaignService
      .getInstance({ ruleType: headers.type })
      .setDisposition({ id: messageId, messageCategory, disposition, ...headers });
  } catch (err) {
    logger.error({ message: `Error setting Dispositions : ${JSON.stringify(err)}` });
    const originalError = R.pipe(R.pathOr([], [ 'response', 'notifications' ]), R.head)(err);
    if (originalError && (originalError.code === 'INSIGHT-003-E' || originalError.code === 'INSIGHT-007-E' || originalError.code === 'INSIGHT-008-E')) {
      return Promise.reject(HttpError.badRequest(msgValidationError, [ { message: err.message, notifications: R.pathOr([], [ 'response', 'notifications' ], err) } ]));
    }
    return Promise.reject(HttpError.internalServer('Server error', [ { message: 'failed to set insight disposition', error: err.message, notifications: R.pathOr([], [ 'response', 'notifications' ], err) } ]));
  }
};

const setDisposition = (deps, config) => async (req, res, next) => {
  const {
    logger,
    targetedCampaignService,
    campaignCacheService,
    dispositionsService,
  } = deps;

  // validate input parameters
  const { error, value: opts } = Joi.validate(req.body, dispositionBodySchema, { stripUnknown: true });
  if (error) {
    logger.error({ message: `Error setting Dispositions validation error: ${JSON.stringify(error.details.map((err) => ({ path: err.path.join('.'), message: err.message })))}` });
    next(HttpError.badRequest(msgValidationError, error.details.map((err) => ({ path: err.path.join('.'), message: err.message }))));
    return;
  }
  if (!opts.rule_id && !opts.message_id && !opts.message_category) {
    logger.error({ message: `Error setting Dispositions validation error: rule_id, message_id or message_category is required`, ...opts });
    next(HttpError.badRequest(msgValidationError, [ { message: 'rule_id, message_id or message_category is required' } ]));
    return;
  }
  if (dispositionDatabase.includes(opts.disposition)) {
    let msg;
    if (!opts.rule_id) {
      msg = 'rule_id is required';
    } else if (!opts.page) {
      msg = 'page is required';
    }
    if (!opts.container) {
      logger.warn({ message: `container is undefined will infer values for container from rule object` });
    }
    if (msg) {
      logger.error({ message: `Error setting Dispositions validation error: ${msg}`, ...opts });
      next(HttpError.badRequest(msgValidationError, [ { message: msg } ]));
      return;
    }
  }

  // get header values
  const {
    country,
    customerToken,
    uid,
  } = getClientInfo({ req, res, config });
  // TODO: Implement a header processing middleware to store headers and pass down to other requests
  // get request's language
  const language = R.pathOr('en', [ 'language' ], res.locals);
  // get preferred environment header value
  const preferredEnv = req.get('preferred-environment');
  // get mocked Insight flag
  const isMockedInsight = req.get('x-mock-insight') !== undefined;
  // get channel id
  const channelId = req.get('x-channel-id');
  // get application id
  const applicationId = req.get('x-application');
  // get spand id
  const spanId = req.get('x-b3-spanid') || crypto.randomBytes(8).toString('hex');
  // get customer id
  const customerId = req.get('x-customer-id');
  // get trace id
  const traceId = req.get('x-b3-traceid') || crypto.randomBytes(16).toString('hex');
  // get OriginatingAppl Code
  const xOriginatingApplCode = req.get('x-originating-appl-code') || 'BFB6';
  // get fid flag for Insights pilot testing - PIGEON-5412
  const xFeatureFlagUid = req.get('x-feature-flag-uid');

  if (!uid) {
    logger.error({
      message: 'Error setting Dispositions error: card number or x-user-context is required',
      traceId,
      spanId,
      ...opts,
    });
    next(HttpError.badRequest('card number or user context is required'));
    return;
  }

  // get and verify rule if specified
  let rule;
  if (opts.rule_id) {
    try {
      rule = R.clone(R.pathOr({}, [ 'campaigns', opts.rule_id ], campaignCacheService.get()));
      if (rule && !Object.keys(rule).length) {
        next(HttpError.notFound(msgCampaignNotFound, [ { message: 'campaign rule not found' } ]));
        return;
      }

      const { error: ruleTypeError } = validateCampaignRuleType(country, rule.type);
      if (ruleTypeError) {
        return next(HttpError.badRequest(ruleTypeError));
      }
    } catch (err) {
      logger.error({ message: `Error setting Dispositions error: ${JSON.stringify(err)}`, ...opts });
      next(err);
      return;
    }
  }

  // Container passed in the body should match the container of the rule expect for Orion Inbox container
  if (rule && opts.container && rule.container !== opts.container && opts.container !== ORION_INBOX_CONTAINER_ID) {
    logger.error({ message: `Error setting Dispositions validation error: container Name not matching the rule`, ...opts });
    next(HttpError.badRequest('container Name not matching the rule'));
    return;
  }

  if (rule && !rule.platforms.includes(opts.platform)) {
    logger.error({ message: `Error setting Dispositions validation error: platform not matching the rule`, ...opts });
    next(HttpError.badRequest('platform not matching the rule'));
    return;
  }

  const isValid = opts.disposition === dispositionValues.dismissed || opts.disposition === dispositionValues.deleted ? rule.dismissable_flag : Object.values(dispositionValues).includes(opts.disposition);
  if (rule && opts.container !== ORION_INBOX_CONTAINER_ID && !isValid) {
    logger.error({ message: `Error setting Dispositions validation error: This rule is not dismissable`, ...opts });
    next(HttpError.badRequest('This rule is not dismissable'));
    return;
  }

  // identify campaign type
  const isSoftStopCampaign = !!opts.message_category;
  const isTargetedCampaign = !!opts.message_id && opts.message_id !== opts.rule_id;

  // get targeted campaign data
  let targetedCampaignData = {};
  if (isTargetedCampaign) {
    try {
      targetedCampaignData = await getTargetedCampaignData(targetedCampaignService, opts.message_id, {
        customerToken,
        userIdentifier: uid,
        preferredEnv,
        language,
        channelId,
        xApplication: applicationId,
        useMock: isMockedInsight,
        spanId,
        traceId,
        type: rule && rule.type,
        country,
        xOriginatingApplCode,
        xFeatureFlagUid,
      });
    } catch (err) {
      logger.error({ message: `Error setting Dispositions getTargetedCampaignData error: ${JSON.stringify(err)}`, ...opts });
      next(err);
      return;
    }
  }

  // send targeted campaign disposition if required
  const dispositionValue = opts.disposition === dispositionValues.dismissed || opts.disposition === dispositionValues.deleted ? dispositionValues.viewed : opts.disposition;
  if ((isTargetedCampaign || isSoftStopCampaign) && dispositionUpdateRequired(targetedCampaignData.message_status, dispositionValue)) {
    try {
      await setTargetedCampaignDisposition(targetedCampaignService, opts.message_id, opts.message_category, dispositionValue, {
        customerToken,
        userIdentifier: uid,
        preferredEnv,
        language,
        channelId,
        xApplication: applicationId,
        useMock: isMockedInsight,
        spanId,
        traceId,
        country,
        type: rule && rule.type,
        customerId,
        xOriginatingApplCode,
        xFeatureFlagUid,
      }, logger);
    } catch (err) {
      logger.error({ message: `Error setting Dispositions setTargetedCampaignDisposition error: ${JSON.stringify(err)}`, ...opts });
      next(err);
      return;
    }
  }

  const { page } = opts;
  const pageIsArray = Array.isArray(page);
  let checkPage = false;
  if (rule) checkPage = pageIsArray ? page.find(p => !rule.pages.includes(p)) : !rule.pages.includes(page);
  // Container passed in the body should match the container of the rule expect for Orion Inbox container
  if (rule && page && checkPage && (!pageIsArray && page !== ORION_INBOX_PAGE_ID)) {
    logger.error({ message: `Error setting Dispositions validation error: page is not matching the rule`, ...opts });
    next(HttpError.badRequest('page is not matching the rule'));
    return;
  }

  // save to Pigeon DB if required
  const isStoreToDatabase = rule && shouldStoreDispositionToDb(rule.dismissable_flag, opts.disposition);
  if (isStoreToDatabase) {
    const expiryDate = calcTargetedCampaignExpiry(rule.end_at, targetedCampaignData.expiry_date);
    try {
      const messageId = opts.message_id && (opts.message_id !== opts.rule_id ? opts.message_id : null);
      const container = opts.container || rule.container;
      const page = opts.page;
      const body = {
        message_id: messageId,
        container,
        page,
        platform: opts.platform,
        application: opts.application,
      };
      if (messageSourceArr.includes(targetedCampaignData.message_source)) {
        const oldMessageId = generateMessageIdsPega(targetedCampaignData.campaign_id, targetedCampaignData.language, targetedCampaignData.message_source);
        if (oldMessageId !== targetedCampaignData.message_id) {
          body.old_message_id = oldMessageId;
        }
      }
      await dispositionsService.setDisposition(
        uid,
        country,
        opts.rule_id,
        expiryDate,
        opts.disposition,
        body,
      );
    } catch (err) {
      logger.error({ message: `Error setting Dispositions failed to save disposition to database error: ${err.message}`, ...opts });
      next(HttpError.internalServer('Server error', [ { message: 'failed to save disposition to database', error: err.message } ]));
      return;
    }
  }

  // send response back
  res.status(200).json({ data: {}, notifications: [] });
};

module.exports = setDisposition;
