const Joi = require('@hapi/joi');

const dispositionBodySchema = Joi.object()
  .keys({
    message_id: Joi.string().min(1).max(50).regex(/^[a-zA-Z0-9-]+$/),
    disposition: Joi.when('application', {
      is: 'orion',
      then: Joi.string().valid('X', 'V', 'D', 'P', 'A', 'C', 'Y', 'N', 'S', ' '),
      otherwise: Joi.string().valid('V', 'D', 'P', 'A', 'C', 'Y', 'N', 'S', ' '),
    }),
    page: Joi.string().min(1).max(64).regex(/^[a-zA-Z0-9_-]+$/),
    container: Joi.string().min(1).max(64).regex(/^[a-zA-Z0-9_-]+$/),
    platform: Joi.string().min(1).max(20).regex(/^[a-zA-Z]+$/),
    application: Joi.string().min(1).max(20).regex(/^[a-zA-Z]+$/).lowercase(),
    message_category: Joi.string().optional().allow('').max(20).regex(/^[a-zA-Z0-9_-]+$/),
    rule_id: Joi.string().min(1).max(20).regex(/^[a-zA-Z0-9]+$/),
  });

const clearDispositionBodySchema = Joi.object()
  .keys({
    container: Joi.string().min(1).max(64).regex(/^[a-zA-Z0-9_-]+$/),
    rule_id: Joi.string().min(1).max(20).regex(/^[a-zA-Z0-9]+$/).required(),
  });

module.exports = {
  dispositionBodySchema,
  clearDispositionBodySchema,
};
