/* eslint-disable sonarjs/no-duplicate-string */
const httpMocks = require('node-mocks-http');
const clearDisposition = require('./clear-disposition');

const defaultToken = 'opaque-token';
const config = { tokenPath: 'auth.token', tokenClaimsPath: 'auth.claims', anonymousClientIds: [ 'test' ] };
const channelId = 'Mobile';
const xApplication = 'N1';
const messageId = 'message-id';
const ruleId = '1adzsnGZ3QWe';
const cardNumber = '4000********3333';
const country = 'CA';
const spanId = '********';
const traceId = '********33334444';

const mockTargetedCampaignData = {
  message_id: messageId,
  message_status: ' ',
  start_date: '2021-01-01',
  expiry_date: '2021-12-31',
  application_data: [],
};

let mockRule = {
  id: ruleId,
  external_ref: 'ORCDL',
  content_space: 'samplespace',
  content_type: 'creditCardOffer',
  content_id: 'aaaa1111',
  container: 'priority-box',
  pages: [ 'accounts', 'activities' ],
  status: 'published',
  start_at: '2021-01-01T00:00:00.000Z',
  end_at: '2021-01-20T00:00:00.000Z',
  disabled: false,
  dismissable_flag: true,
  platforms: [ 'ios', 'Android', 'web' ],
  type: 'campaign',
};

let mockCcauRule = {
  id: '1adzsnGZ3QWeCcau',
  external_ref: 'ORCDL',
  content_space: 'samplespace',
  content_type: 'creditCardOffer',
  content_id: 'aaaa1111',
  container: 'offers-and-programs',
  pages: [ 'accounts', 'activities' ],
  status: 'published',
  start_at: '2021-01-01T00:00:00.000Z',
  end_at: '2021-01-20T00:00:00.000Z',
  disabled: false,
  dismissable_flag: true,
  platforms: [ 'ios', 'Android', 'web' ],
  type: 'ccau_campaign',
};

const defaultHeaders = {
  'Preferred-Environment': 'istgreen',
  Authorization: `Bearer: ${defaultToken}`,
  'x-customer-scotiacard': cardNumber,
  'x-channel-id': channelId,
  'x-application': xApplication,
  'x-b3-traceid': traceId,
  'x-b3-spanid': spanId,
};

const campaignCacheService = {
  get: jest.fn().mockReturnValue({
    campaigns: {
      [mockRule.id]: mockRule,
      [mockCcauRule.id]: mockCcauRule },
  }),
};

const targetedCampaignService = {
  getInstance: jest.fn(),
};
targetedCampaignService.getInstance.mockReturnValue({
  getCampaign: jest.fn().mockResolvedValue({ data: mockTargetedCampaignData, notifications: [] }),
  setDisposition: jest.fn().mockResolvedValue(true),
});
const dispositionsService = {
  clearDisposition: jest.fn().mockResolvedValue(true),
};
const status = jest.fn();
const json = jest.fn();
const next = jest.fn();
const logger = {
  error: jest.fn(),
  info: jest.fn(),
};

describe('Clear Disposition Campaign', () => {
  beforeEach(() => {
    campaignCacheService.get.mockClear();
    dispositionsService.clearDisposition.mockClear();
    next.mockClear();
    json.mockClear();
    status.mockClear();
    logger.error.mockClear();
    logger.info.mockClear();
  });
  test('should initialize', () => {
    expect(clearDisposition).toBeDefined();
    expect(typeof clearDisposition).toEqual('function');
  });
  test('should not fail on valid campaign (PW flow - rule_id + container )', async () => {
    mockRule.container = 'offers-and-programs';
    const { req, res } = httpMocks.createMocks({
      headers: defaultHeaders,
      query: {},
      body: {
        rule_id: ruleId,
        container: 'offers-and-programs',
      },
    }, {
      locals: { auth: { token: defaultToken }, language: 'en', country: 'CA' },
    });
    const endpoint = clearDisposition({
      campaignCacheService,
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(0);
    // save disposition to DB
    expect(dispositionsService.clearDisposition).toBeCalledTimes(1);
    const saveDispositionCall = dispositionsService.clearDisposition.mock.calls[0];
    expect(saveDispositionCall[0]).toEqual(cardNumber);
    expect(saveDispositionCall[1]).toEqual(ruleId);
    expect(saveDispositionCall[2]).toEqual(mockRule.container);
    expect(saveDispositionCall[3]).toEqual(country);
  });

  test('should fail on sending wrong data', async () => {
    mockRule.container = 'offers-and-programs';
    const { req, res } = httpMocks.createMocks({
      headers: defaultHeaders,
      query: {},
      body: {
        container: 'offers-and-programs',
      },
    }, {
      locals: { auth: { token: defaultToken }, language: 'en', country: 'CA' },
    });
    const endpoint = clearDisposition({
      campaignCacheService,
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    expect(dispositionsService.clearDisposition).toBeCalledTimes(0);
  });

  test('should fail when getting error from updating the database', async () => {
    mockRule.container = 'offers-and-programs';
    const { req, res } = httpMocks.createMocks({
      headers: defaultHeaders,
      query: {},
      body: {
        rule_id: ruleId,
        container: 'offers-and-programs',
      },
    }, {
      locals: { auth: { token: defaultToken }, language: 'en', country: 'CA' },
    });
    dispositionsService.clearDisposition.mockRejectedValueOnce(new Error('error text'));
    const endpoint = clearDisposition({
      campaignCacheService,
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err.message).toEqual('Server error');
    expect(err.metadata[0].message).toEqual('failed to clear disposition to database');
  });

  test('should fail when sending wrong container', async () => {
    mockRule.container = 'offers-and-programs-qa';
    const { req, res } = httpMocks.createMocks({
      headers: defaultHeaders,
      query: {},
      body: {
        rule_id: ruleId,
        container: 'offers-and-programs',
      },
    }, {
      locals: { auth: { token: defaultToken }, language: 'en', country: 'CA' },
    });
    const endpoint = clearDisposition({
      campaignCacheService,
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err.message).toEqual('Error clearing Dispositions validation error: container Name not matching the rule');
  });

  test('should fail when sending wrong country ', async () => {
    mockRule.container = 'offers-and-programs-qa';
    const { req, res } = httpMocks.createMocks({
      headers: defaultHeaders,
      query: {},
      body: {
        rule_id: ruleId,
        container: 'offers-and-programs',
      },
    }, {
      locals: { auth: { token: defaultToken }, language: 'en', country: 'DO' },
    });
    const endpoint = clearDisposition({
      campaignCacheService,
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err.message).toEqual('card number or user context is required');
  });

  test('should fail when sending wrong rule type ', async () => {
    mockRule.container = 'offers-and-programs-qa';
    mockRule.type = 'ccau_campaign';
    const { req, res } = httpMocks.createMocks({
      headers: defaultHeaders,
      query: {},
      body: {
        rule_id: ruleId,
        container: 'offers-and-programs',
      },
    }, {
      locals: { auth: { token: defaultToken }, language: 'en', country: 'CA' },
    });
    const endpoint = clearDisposition({
      campaignCacheService,
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err.message).toEqual('Given country has insufficient access to rule types available');
  });

  test('should fail when sending the cache service is down', async () => {
    mockRule.container = 'offers-and-programs-qa';
    mockRule.type = 'ccau_campaign';
    const { req, res } = httpMocks.createMocks({
      headers: defaultHeaders,
      query: {},
      body: {
        rule_id: ruleId,
        container: 'offers-and-programs',
      },
    }, {
      locals: { auth: { token: defaultToken }, language: 'en', country: 'CA' },
    });
    const endpoint = clearDisposition({
      campaignCacheService: { get: jest.fn().mockReturnValue(() => new Error('error text')) },
      dispositionsService,
    }, config);
    await endpoint(req, res, next);
    expect(next).toBeCalledTimes(1);
    const err = next.mock.calls[0][0];
    expect(err.message).toEqual('Campaign not found');
  });
});
