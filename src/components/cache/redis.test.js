/* eslint-disable sonarjs/no-identical-functions */
/* eslint-disable sonarjs/no-duplicate-string */
const Redlock = require('redlock');
const { createRedisClient } = require('./redis');

const logger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

const redisReady = jest.fn().mockReturnValue(true);

const redisInstance = {
  setex: jest.fn().mockImplementation((key, ttl, data, cb) => {
    return cb(null, 'OK');
  }),
  get: jest.fn().mockImplementation((key, cb) => {
    if (cb) {
      return cb(null, JSON.stringify('test-data'));
    }
  }),
  expire: jest.fn().mockImplementation((key, ttl, cb) => {
    if (cb) {
      return cb(null, 1);
    }
  }),
  exec: jest.fn().mockImplementation((cb) => {
    return cb(null, [ 'OK' ]);
  }),
  del: jest.fn().mockImplementation((key, cb) => {
    return cb(null, 'OK');
  }),
  eval: jest.fn().mockImplementation((arr, cb) => {
    return cb(null, 1);
  }),
  flushall: jest.fn().mockImplementation((cb) => {
    if (cb) {
      return cb(null, 'OK');
    }
  }),
  once: jest.fn().mockImplementation((event, cb) => {
    if (event === 'ready') return cb();
  }),
  on: jest.fn(),
};
redisInstance.multi = jest.fn().mockReturnValue(redisInstance);
Object.defineProperty(redisInstance, 'ready', { get: redisReady });
const redis = { createClient: jest.fn().mockReturnValue(redisInstance) };

const ERR_REDIS_NOT_CONNECTED = 'redis not connected';

const configConnection = {
  // host: '127.0.0.1',
  // sslPort: 6380,
  // database: 0,
  // connect_timeout: Number.MAX_SAFE_INTEGER,
  url: 'redis://localhost:6379',
};
const configOptions = {
  namespace: 'pigeon',
  useKeyHash: true,
  encryptionKey: '12345678901234567890123456789012',
  refreshLockRetryTimes: 3,
  refreshLockRetryWaitSecs: 500,
  retryTimeLimit: 1000 * 60 * 60,
  retryMaxAttempts: 100,
};

describe('cache', () => {
  describe('redis', () => {
    beforeEach(() => {
      logger.info.mockClear();
      logger.warn.mockClear();
      logger.error.mockClear();
      redisReady.mockClear();
      redisInstance.get.mockClear();
      redisInstance.setex.mockClear();
      redisInstance.multi.mockClear();
      redisInstance.exec.mockClear();
      redisInstance.del.mockClear();
      redisInstance.expire.mockClear();
      redisInstance.eval.mockClear();
      redisInstance.flushall.mockClear();
      redisInstance.on.mockClear();
      redisInstance.once.mockClear();
    });

    test('should create a redis instance', async () => {
      const client = await createRedisClient({ redis, Redlock, logger }, { ...configConnection, ...configOptions });
      expect(client).toBeDefined();
    });

    test('should fail to create a redis instance', async () => {
      redisReady.mockReturnValueOnce(false);
      const err = new Error('failed to connect');
      redisInstance.once.mockImplementation((event, cb) => {
        if (event === 'error') return cb(err);
      });
      const client = createRedisClient({ redis, Redlock, logger }, { ...configConnection, ...configOptions });
      await expect(client).rejects.toEqual(err);
    });

    test('should log error on redis error', async () => {
      const err = new Error('unexpected error');
      redisInstance.on
        .mockImplementationOnce((event, cb) => {
          if (event === 'error') return cb(err);
        })
        .mockImplementationOnce((event, cb) => {
          if (event === 'error') return cb(err);
        });
      await createRedisClient({ redis, Redlock, logger }, { ...configConnection, ...configOptions });
      expect(logger.error).toBeCalledTimes(1);
      expect(logger.error).toBeCalledWith({ message: `redis cache error: ${err.message}`, err });
    });

    test('should log warning on redis warning', async () => {
      const err = new Error('minor error');
      redisInstance.on
        .mockImplementationOnce((event, cb) => {
          if (event === 'warning') return cb(err);
        })
        .mockImplementationOnce((event, cb) => {
          if (event === 'warning') return cb(err);
        });
      await createRedisClient({ redis, Redlock, logger }, { ...configConnection, ...configOptions });
      expect(logger.warn).toBeCalledTimes(1);
      expect(logger.warn).toBeCalledWith({ message: `redis cache warning: ${err.message}`, err });
    });

    describe('setEx', () => {
      test('should set without useKeyHash and without encryptionKey', async () => {
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
          namespace: undefined,
          useKeyHash: false,
          encryptionKey: undefined,
        });
        await client.setEx('test', 'value', 1000);
        const args = redisInstance.setex.mock.calls[0];
        expect(args[0]).toEqual('test');
        expect(args[1]).toEqual(1000);
        expect(args[2]).toEqual('"value"');
      });

      test('should set with useKeyHash and with encryptionKey', async () => {
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
          namespace: undefined,
        });
        await client.setEx('test', 'value', 1000);
        const args = redisInstance.setex.mock.calls[0];
        expect(args[0]).toEqual('n4bQgYhMfWWaL+qgxVrQFaO/TxsrC4Is0V1sFbDwCgg=');
        expect(args[1]).toEqual(1000);
        expect(args[2]).not.toEqual('"value"');
      });

      test('should setfail', async () => {
        const error = new Error('setex response failure');
        redisInstance.setex.mockImplementationOnce((key, ttl, data, cb) => {
          return cb(error);
        });
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
        });
        const res = client.setEx('key', 'value', 2000);
        await expect(res).rejects.toEqual(error);
      });

      test('should set return a connection error', async () => {
        redisReady.mockReturnValueOnce(true).mockReturnValueOnce(false);
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
        });
        const res = client.setEx('key', 'value', 2000);
        await expect(res).rejects.toEqual(new Error(ERR_REDIS_NOT_CONNECTED));
      });
    });

    describe('get', () => {
      test('should get without useKeyHash and without encryptionKey', async () => {
        const key = 'get key';
        const data = 'get result';
        redisInstance.get.mockImplementationOnce((key, cb) => {
          return cb(null, JSON.stringify(data));
        });
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
          namespace: undefined,
          useKeyHash: false,
          encryptionKey: undefined,
        });
        const response = await client.get(key);
        expect(response).toEqual(data);
        expect(redisInstance.get).toBeCalledTimes(1);
        const args = redisInstance.get.mock.calls[0];
        expect(args[0]).toEqual(key);
      });

      test('should get with useKeyHash and with encryptionKey', async () => {
        const key = 'test';
        const data = 'eyJpdiI6IjVNV3BWa2htVDNoQUxLcnQ3YXBjQXc9PSIsInRhZyI6IjBiUFQ0VWpvL2xOOXNqWVExL0oxQ0E9PSIsImNyeXB0b2dyYW0iOiIvVmIwWGU1SWV3PT0ifQ==';
        redisInstance.get.mockImplementationOnce((key, cb) => {
          return cb(null, JSON.stringify(data));
        });
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
          namespace: undefined,
        });
        const response = await client.get(key);
        expect(response).toEqual('value');
        const args = redisInstance.get.mock.calls[0];
        expect(args[0]).toEqual('n4bQgYhMfWWaL+qgxVrQFaO/TxsrC4Is0V1sFbDwCgg=');
      });

      test('should fail', async () => {
        const error = new Error('get response failure');
        redisInstance.get.mockImplementationOnce((key, cb) => {
          return cb(error);
        });
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
        });
        const res = client.get('key');
        await expect(res).rejects.toEqual(error);
      });

      test('should return a connection error', async () => {
        redisReady.mockReturnValueOnce(true).mockReturnValueOnce(false);
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
        });
        const res = client.get('key');
        await expect(res).rejects.toEqual(new Error('redis not connected'));
      });
    });

    describe('getAndTouch', () => {
      test('should getAndTouch without useKeyHash and without encryptionKey', async () => {
        const key = 'get key';
        const data = 'get result';
        redisInstance.exec.mockImplementationOnce((cb) => {
          return cb(null, [ JSON.stringify(data) ]);
        });
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
          namespace: undefined,
          useKeyHash: false,
          encryptionKey: undefined,
        });
        const response = await client.getAndTouch(key, 500);
        expect(response).toEqual(data);
        expect(redisInstance.multi).toBeCalledTimes(1);
        expect(redisInstance.get).toBeCalledTimes(1);
        expect(redisInstance.get).toBeCalledWith(key);
        expect(redisInstance.expire).toBeCalledTimes(1);
        expect(redisInstance.expire).toBeCalledWith(key, 500);
        expect(redisInstance.exec).toBeCalledTimes(1);
      });

      test('should getAndTouch with useKeyHash and with encryptionKey', async () => {
        const key = 'test';
        const data = 'eyJpdiI6IjVNV3BWa2htVDNoQUxLcnQ3YXBjQXc9PSIsInRhZyI6IjBiUFQ0VWpvL2xOOXNqWVExL0oxQ0E9PSIsImNyeXB0b2dyYW0iOiIvVmIwWGU1SWV3PT0ifQ==';
        redisInstance.exec.mockImplementationOnce((cb) => {
          return cb(null, [ JSON.stringify(data) ]);
        });
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
          namespace: undefined,
        });
        const response = await client.getAndTouch(key, 500);
        expect(response).toEqual('value');
        expect(redisInstance.multi).toBeCalledTimes(1);
        expect(redisInstance.get).toBeCalledTimes(1);
        expect(redisInstance.get).toBeCalledWith('n4bQgYhMfWWaL+qgxVrQFaO/TxsrC4Is0V1sFbDwCgg=');
        expect(redisInstance.expire).toBeCalledTimes(1);
        expect(redisInstance.expire).toBeCalledWith('n4bQgYhMfWWaL+qgxVrQFaO/TxsrC4Is0V1sFbDwCgg=', 500);
        expect(redisInstance.exec).toBeCalledTimes(1);
      });

      test('should fail', async () => {
        const error = new Error('get response failure');
        redisInstance.exec.mockImplementationOnce((cb) => {
          return cb(error);
        });
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
        });
        const res = client.getAndTouch('key', 500);
        await expect(res).rejects.toEqual(error);
      });

      test('should return a connection error', async () => {
        redisReady.mockReturnValueOnce(true).mockReturnValueOnce(false);
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
        });
        const res = client.getAndTouch('key', 600);
        await expect(res).rejects.toEqual(new Error('redis not connected'));
      });
    });

    describe('del', () => {
      test('should del without useKeyHash', async () => {
        const key = 'get key';
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
          namespace: undefined,
          useKeyHash: false,
          encryptionKey: undefined,
        });
        await client.del(key);
        expect(redisInstance.del).toBeCalledTimes(1);
        const args = redisInstance.del.mock.calls[0];
        expect(args[0]).toEqual(key);
      });

      test('should del with useKeyHash', async () => {
        const key = 'test';
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
          namespace: undefined,
        });
        await client.del(key);
        const args = redisInstance.del.mock.calls[0];
        expect(args[0]).toEqual('n4bQgYhMfWWaL+qgxVrQFaO/TxsrC4Is0V1sFbDwCgg=');
      });

      test('should del fail', async () => {
        const error = new Error('del response failure');
        redisInstance.del.mockImplementationOnce((key, cb) => {
          return cb(error);
        });
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
        });
        const res = client.del('key');
        await expect(res).rejects.toEqual(error);
      });

      test('should del return a connection error', async () => {
        redisReady.mockReturnValueOnce(true).mockReturnValueOnce(false);
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
        });
        const res = client.del('key');
        await expect(res).rejects.toEqual(new Error(ERR_REDIS_NOT_CONNECTED));
      });
    });

    describe('lock', () => {
      test('should create a lock without useKeyHash', async () => {
        const key = 'lock-key';
        const ttl = 500;
        redisInstance.eval.mockImplementationOnce((arr, cb) => {
          expect(arr[2]).toEqual(`lock:${key}`);
          expect(arr[4]).toEqual(ttl * 1000);
          return cb(null, 1);
        });
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
          namespace: undefined,
          useKeyHash: false,
          encryptionKey: undefined,
        });
        const lock = await client.lock(key, ttl);
        expect(lock).toBeDefined();
        expect(redisInstance.eval).toBeCalledTimes(1);
      });

      test('should create a lock with useKeyHash', async () => {
        const key = 'test';
        redisInstance.eval.mockImplementationOnce((arr, cb) => {
          expect(arr[2]).toEqual(`lock:n4bQgYhMfWWaL+qgxVrQFaO/TxsrC4Is0V1sFbDwCgg=`);
          expect(arr[4]).toEqual(600 * 1000);
          return cb(null, 1);
        });
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
          namespace: undefined,
        });
        const lock = await client.lock(key);
        expect(lock).toBeDefined();
        expect(redisInstance.eval).toBeCalledTimes(1);
      });

      test('should lock return a connection error', async () => {
        redisReady.mockReturnValueOnce(true).mockReturnValueOnce(false);
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
        });
        const res = client.lock('key');
        await expect(res).rejects.toEqual(new Error(ERR_REDIS_NOT_CONNECTED));
      });
    });

    describe('unlock', () => {
      test('should unlock without useKeyHash', async () => {
        const key = 'lock-key';
        const ttl = 500;
        redisInstance.eval
          .mockImplementationOnce((arr, cb) => {
            expect(arr[2]).toEqual(`lock:${key}`);
            expect(arr[4]).toEqual(ttl * 1000);
            return cb(null, 1);
          })
          .mockImplementationOnce((arr, cb) => {
            expect(arr[2]).toEqual(`lock:${key}`);
            return cb(null, 1);
          });
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
          namespace: undefined,
          useKeyHash: false,
          encryptionKey: undefined,
        });
        const lock = await client.lock(key, ttl);
        await client.unlock(lock);
        expect(lock).toBeDefined();
        expect(redisInstance.eval).toBeCalledTimes(2);
      });

      test('should create a lock with useKeyHash', async () => {
        const key = 'test';
        redisInstance.eval.mockImplementationOnce((arr, cb) => {
          expect(arr[2]).toEqual(`lock:n4bQgYhMfWWaL+qgxVrQFaO/TxsrC4Is0V1sFbDwCgg=`);
          expect(arr[4]).toEqual(600 * 1000);
          return cb(null, 1);
        });
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
          namespace: undefined,
        });
        const lock = await client.lock(key);
        expect(lock).toBeDefined();
        expect(redisInstance.eval).toBeCalledTimes(1);
      });

      test('should unlock return a connection error', async () => {
        redisReady.mockReturnValueOnce(true).mockReturnValueOnce(true).mockReturnValueOnce(false);
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
        });
        const lock = await client.lock('key');
        const res = client.unlock(lock);
        await expect(res).rejects.toEqual(new Error(ERR_REDIS_NOT_CONNECTED));
      });
    });
    describe('flushall', () => {
      test('should flushall', async () => {
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
          namespace: undefined,
          useKeyHash: false,
          encryptionKey: undefined,
        });
        await client.flushall();
        expect(redisInstance.flushall).toBeCalledTimes(1);
      });

      test('should del fail', async () => {
        const error = new Error('del response failure');
        redisInstance.flushall.mockImplementationOnce((cb) => {
          return cb(error);
        });
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
        });
        const res = client.flushall();
        await expect(res).rejects.toEqual(error);
      });

      test('should flushall return a connection error', async () => {
        redisReady.mockReturnValueOnce(true).mockReturnValueOnce(false);
        const client = await createRedisClient({ redis, Redlock, logger }, {
          ...configConnection,
          ...configOptions,
        });
        const res = client.flushall();
        await expect(res).rejects.toEqual(new Error(ERR_REDIS_NOT_CONNECTED));
      });
    });
  });
});
