const { createCipheriv, createDecipheriv, createHash, createHmac, randomBytes } = require('crypto');
const { pipe } = require('ramda');

const HASH_ALGORITHM = 'sha256';
const CIPHER_ALGORITHM = 'aes-256-gcm';
const STRING_ENCODING = 'base64';
const IV_SIZE = 16;

const generateIV = (size = IV_SIZE) => new Promise((resolve, reject) => {
  randomBytes(size, (err, buf) => {
    if (err) {
      return reject(err);
    }
    return resolve(buf);
  });
});

const encryptFromBuffer = async (key, data) => {
  const iv = await generateIV();
  const cipher = createCipheriv(CIPHER_ALGORITHM, key, iv);
  const cryptogram = Buffer.concat([
    cipher.update(data),
    cipher.final(),
  ]);
  const tag = cipher.getAuthTag();
  return { iv, tag, cryptogram };
};

const decryptToBuffer = async (key, value) => {
  const decipher = createDecipheriv(CIPHER_ALGORITHM, key, value.iv);
  decipher.setAuthTag(value.tag);
  return Buffer.concat([
    decipher.update(value.cryptogram),
    decipher.final(),
  ]);
};

const bufferToString = (buf) => buf.toString(STRING_ENCODING);

const stringToBuffer = (str) => Buffer.from(str, STRING_ENCODING);

const bufferToObject = (buf) => JSON.parse(buf.toString('utf8'));

const objectToBuffer = (data) => Buffer.from(JSON.stringify(data), 'utf8');

const stringToObject = (str) => pipe(stringToBuffer, bufferToObject)(str);

const objectToString = (data) => pipe(objectToBuffer, bufferToString)(data);

const toBlob = (value) => ({
  iv: bufferToString(value.iv),
  tag: bufferToString(value.tag),
  cryptogram: bufferToString(value.cryptogram),
});

const fromBlob = (blob) => ({
  iv: stringToBuffer(blob.iv),
  tag: stringToBuffer(blob.tag),
  cryptogram: stringToBuffer(blob.cryptogram),
});

const encryptToString = (key, data) => encryptFromBuffer(key, objectToBuffer(data))
  .then(pipe(toBlob, objectToString));

const decryptFromString = (key, str) => Promise.resolve(str)
  .then(pipe(stringToObject, fromBlob))
  .then((value) => decryptToBuffer(key, value))
  .then(bufferToObject);

const getKeyHash = (key) => {
  const hash = createHash(HASH_ALGORITHM);
  hash.update(key);
  return hash.digest().toString(STRING_ENCODING);
};

const getKeyHmac = (secret, key) => {
  const hmac = createHmac(HASH_ALGORITHM, secret);
  hmac.update(key);
  return hmac.digest('hex');
};

module.exports = {
  encryptFromBuffer,
  decryptToBuffer,
  encryptToString,
  decryptFromString,
  getKeyHash,
  getKeyHmac,
};
