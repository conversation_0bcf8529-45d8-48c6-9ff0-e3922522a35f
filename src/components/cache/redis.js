const R = require('ramda');
const { getKeyHash, encryptToString, decryptFromString } = require('./crypto');

const ERR_REDIS_NOT_CONNECTED = 'redis not connected';

// const isEmpty = R.either(<PERSON>.isNil, R.isEmpty);

const removeNullPropertiesFromObjectDeep = R.when(
  R.either(R.is(Array), R.is(Object)),
  R.pipe(
    R.reject(R.isNil),
    R.map((value) => removeNullPropertiesFromObjectDeep(value)),
  ),
);

const withNamespace = (key, namespace) => namespace ? `${namespace}:${key}` : key;

const buildKey = ({ useKeyHash = false }, key, namespace) => {
  const k = useKeyHash ? getKeyHash(key) : key;
  return withNamespace(k, namespace);
};

const buildBlobValue = ({ encryptionKey = undefined }, value) =>
  encryptionKey ? encryptToString(encryptionKey, value) : Promise.resolve(JSON.stringify(value));

const getEncrypted = ({ redisInstance, namespace, encryptionKey, useKeyHash }, key) => {
  return new Promise((resolve, reject) => {
    const keyHash = buildKey({ useKeyHash }, key, namespace);
    redisInstance.get(keyHash, (err, reply) => {
      if (err) return reject(err);
      if (!reply) return resolve(undefined);
      return decryptFromString(encryptionKey, reply)
        .then(resolve)
        .catch(reject);
    });
  });
};

const getAndTouchEncrypted = ({ redisInstance, namespace, encryptionKey, useKeyHash }, key, ttl) => {
  return new Promise((resolve, reject) => {
    const keyHash = buildKey({ useKeyHash }, key, namespace);
    const multi = redisInstance.multi();
    multi.get(keyHash);
    multi.expire(keyHash, ttl);
    multi.exec((err, reply) => {
      if (err) return reject(err);
      if (!reply || !reply[0]) return resolve(undefined);
      return decryptFromString(encryptionKey, reply[0])
        .then(resolve)
        .catch(reject);
    });
  });
};

const getPlain = ({ redisInstance, namespace }, key) => {
  return new Promise((resolve, reject) => {
    redisInstance.get(withNamespace(key, namespace), (err, reply) => {
      if (err) return reject(err);
      try {
        return resolve(JSON.parse(reply));
      } catch (e) {
        return reject(e);
      }
    });
  });
};

const getAndTouchPlain = ({ redisInstance, namespace }, key, ttl) => {
  return new Promise((resolve, reject) => {
    const k = withNamespace(key, namespace);
    const multi = redisInstance.multi();
    multi.get(k);
    multi.expire(k, ttl);
    multi.exec((err, reply) => {
      if (err) return reject(err);
      try {
        return resolve(JSON.parse(reply[0]));
      } catch (e) {
        return reject(e);
      }
    });
  });
};

/**
 * Set a record with a TTL
 * @param {object} deps Dependencies
 * @property {object} deps.redisInstance Redis instance
 * @property {string} deps.namespace Namespace (optional)
 * @property {string} deps.encryptionKey Encryption key to decrypt record's value (optional)
 * @property {boolean} deps.useKeyHash Replace key with its its hash flag
 * @param {string} key Key
 * @param {number} ttl TTL in seconds, default to 10 minutes
 * @returns {Promise}
 */
const setEx = async ({ redisInstance, namespace, encryptionKey, useKeyHash }, key, value, ttl = 600) => {
  if (!redisInstance.ready) {
    return Promise.reject(new Error(ERR_REDIS_NOT_CONNECTED));
  }
  const blob = await buildBlobValue({ encryptionKey }, value);
  return new Promise((resolve, reject) => {
    redisInstance.setex(buildKey({ useKeyHash }, key, namespace), ttl, blob, (err) => {
      if (err) return reject(err);
      return resolve();
    });
  });
};

/**
 * Get a record by its key
 * @param {object} deps Dependencies
 * @property {object} deps.redisInstance Redis instance
 * @property {string} deps.namespace Namespace (optional)
 * @property {string} deps.encryptionKey Encryption key to decrypt record's value (optional)
 * @property {boolean} deps.useKeyHash Replace key with its its hash flag
 * @param {string} key Key
 * @returns {Promise<any>} Value
 */
const get = async ({ redisInstance, namespace, encryptionKey, useKeyHash }, key) => {
  if (!redisInstance.ready) {
    return Promise.reject(new Error(ERR_REDIS_NOT_CONNECTED));
  }
  if (!encryptionKey) {
    return getPlain({ redisInstance, namespace }, key);
  }
  return getEncrypted({ redisInstance, namespace, encryptionKey, useKeyHash }, key);
};

/**
 * Get a record by its key and update its TTL
 * @param {object} deps Dependencies
 * @property {object} deps.redisInstance Redis instance
 * @property {string} deps.namespace Namespace (optional)
 * @property {string} deps.encryptionKey Encryption key to decrypt record's value (optional)
 * @property {boolean} deps.useKeyHash Replace key with its its hash flag
 * @param {string} key Key
 * @param {number} ttl TTL
 * @returns {Promise<any>} Value
 */
const getAndTouch = async ({ redisInstance, namespace, encryptionKey, useKeyHash }, key, ttl = 600) => {
  if (!redisInstance.ready) {
    return Promise.reject(new Error(ERR_REDIS_NOT_CONNECTED));
  }
  if (!encryptionKey) {
    return getAndTouchPlain({ redisInstance, namespace }, key, ttl);
  }
  return getAndTouchEncrypted({ redisInstance, namespace, encryptionKey, useKeyHash }, key, ttl);
};

/**
 * Delete a record by its key
 * @param {object} deps Dependencies
 * @property {object} deps.redisInstance Redis instance
 * @property {string} deps.namespace Namespace (optional)
 * @property {boolean} deps.useKeyHash Replace key with its its hash flag
 * @param {string} key Key
 * @returns {Promise}
 */
const del = async ({ redisInstance, namespace, useKeyHash }, key) => {
  if (!redisInstance.ready) {
    return Promise.reject(new Error(ERR_REDIS_NOT_CONNECTED));
  }
  const keyHash = buildKey({ useKeyHash }, key, namespace);
  return new Promise((resolve, reject) => {
    redisInstance.del(keyHash, (err) => {
      if (err) return reject(err);
      return resolve();
    });
  });
};

/**
 * Create a lock
 * @param {object} deps Dependencies
 * @param {string} key Key to lock
 * @param {number} ttl Lock's TTL
 * @returns {object} An active lock
 */
const lock = async ({ redisInstance, redlockInstance, namespace, useKeyHash }, key, ttl = 600) => {
  if (!redisInstance.ready) {
    return Promise.reject(new Error(ERR_REDIS_NOT_CONNECTED));
  }
  const keyHash = buildKey({ useKeyHash }, key, namespace);
  return redlockInstance.lock(withNamespace(`lock:${keyHash}`, namespace), ttl * 1000);
};

/**
 * Unlock an active lock
 * @param {object} deps Dependencies
 * @property {object} deps.redlockInstance Instance of a Redlock
 * @param {object} activeLock Active lock
 */
const unlock = async ({ redisInstance, redlockInstance }, activeLock) => {
  if (!redisInstance.ready) {
    return Promise.reject(new Error(ERR_REDIS_NOT_CONNECTED));
  }
  return redlockInstance.unlock(activeLock);
};

/**
 * Delete all keys of all existing databases
 * @param {object} deps Dependencies
 * @property {object} deps.redisInstance Redis instance
 * @returns {Promise}
 */
const flushall = async ({ redisInstance }) => {
  if (!redisInstance.ready) {
    return Promise.reject(new Error(ERR_REDIS_NOT_CONNECTED));
  }
  return new Promise((resolve, reject) => {
    redisInstance.flushall((err) => {
      if (err) return reject(err);
      return resolve();
    });
  });
};

/**
 * Wait for redis to initialize
 * @param {object} redisInstance Redis isntance
 * @returns {Promise}
 */
const awaitRedis = (redisInstance) => {
  if (redisInstance.ready) {
    return Promise.resolve();
  }
  return new Promise((resolve, reject) => {
    redisInstance.once('ready', resolve);
    redisInstance.once('error', reject);
  });
};

/**
 * Setup and return a retry strategy function for redis
 * @returns {(object) => void} retry strategy function
 */
const retryStrategy = () => (opts) => {
  return Math.min(opts.attempt * 100, 3000);
};

/**
 * Create and initialize redis cache service
 * @param {Object} deps Dependencies
 * @property {object} deps.redis `redis`-compatible module
 * @property {object} deps.Redlock `Redlock`-compatible module
 * @property {object} deps.logger logger instance
 * @param {object} config Config
 * @property {string} config.host Hostname
 * @property {number} config.port Port
 * @property {number} config.sslPort SSL port
 * @property {string} config.password Password
 * @property {number} config.database Database number
 * @property {string} config.namespace Namespace that will be used as part of a key: {namespace}:{key}, optional
 * @property {string} encryptionKey Encryption key with a length of 32 characters
 * @property {boolean} useKeyHash Use hash of keys instead of keys
 * @property {number} config.refreshLockRetryTimes Number of retries to set a lock
 * @property {number} config.refreshLockRetryWaitSecs Wait time to retry to set a lock in seconds
 */
const createRedisClient = async ({ redis, Redlock, logger }, config) => {
  // initialize redis client
  const redisInstance = redis.createClient({
    ...config.connection,
    retry_strategy: retryStrategy(),
  });
  // log errors
  redisInstance.on('error', (err) => {
    logger.error({ message: `redis cache error: ${err.message}`, err });
  });
  // log warnings
  redisInstance.on('warning', (err) => {
    logger.warn({ message: `redis cache warning: ${err.message}`, err });
  });
  // initialize redlock instance
  const redlockInstance = new Redlock([ redisInstance ], {
    driftFactor: 0.01,
    retryJitter: 50,
    retryCount: config.refreshLockRetryTimes,
    retryDelay: config.refreshLockRetryWaitSecs,
  });
  // get misc options
  const namespace = R.pathOr(undefined, [ 'namespace' ], config);
  const useKeyHash = R.pathOr(true, [ 'useKeyHash' ], config);
  const encryptionKey = R.pathOr(undefined, [ 'encryptionKey' ], config);

  return awaitRedis(redisInstance).then(() => ({
    setEx: (key, value, ttl) => setEx({ redisInstance, namespace, encryptionKey, useKeyHash }, key, value, ttl),
    get: (key) => get({ redisInstance, namespace, encryptionKey, useKeyHash }, key),
    getAndTouch: (key, ttl) => getAndTouch({ redisInstance, namespace, encryptionKey, useKeyHash }, key, ttl),
    del: (key) => del({ redisInstance, namespace, useKeyHash }, key),
    lock: (key, ttl) => lock({ redisInstance, redlockInstance, namespace, useKeyHash }, key, ttl),
    unlock: (activeLock) => unlock({ redisInstance, redlockInstance }, activeLock),
    flushall: () => flushall({ redisInstance }),
  }));
};

module.exports = {
  createRedisClient,
};
