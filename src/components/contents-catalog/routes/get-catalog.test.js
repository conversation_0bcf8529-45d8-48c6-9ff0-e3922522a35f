const getCatalog = require('./get-catalog');

const mockJson = {
  json: jest.fn(),
};

const logger = {
  error: jest.fn(),
};

const mockReq = { query: { from_date: '2019-05-01T00:00:00.000Z', to_date: '2019-05-03T23:59:59.000Z' } };
const mockRes = {
  status: jest.fn().mockReturnValue(mockJson),
};
const mockNext = jest.fn();

const ruleAPIClient = {
  getUpdatedV2: jest.fn(),
};

const contentService = {
  getVignetteUpdatedFragments: jest.fn(),
};

describe('Contents Catalog', () => {
  describe('Get Contents Catalog', () => {
    beforeEach(() => {
      logger.error.mockClear();
      mockRes.status.mockClear();
      mockJson.json.mockClear();
      mockNext.mockClear();
      ruleAPIClient.getUpdatedV2.mockClear();
      contentService.getVignetteUpdatedFragments.mockClear();
    });
    test('should return 403 on invalid client id', async () => {
      const res = { locals: { auth: { claims: { client_id: 'd25053e8-3a5e-42cc-bc57-8389df4401ff' } } } };
      const handler = getCatalog({ logger }, { space: 'IST' });
      await handler(mockReq, { ...mockRes, ...res });
      expect(mockRes.status).toBeCalledWith(403);
      expect(mockJson.json).toBeCalled();
      expect(mockNext).not.toBeCalled();
    });
    test('should return 200 on valid request', async () => {
      const res = { locals: { auth: { claims: { client_id: 'd25053e8-3a5e-42cc-bc57-8389df4401f1' } } } };
      ruleAPIClient.getUpdatedV2.mockResolvedValueOnce({ total: 0, items: [] });
      contentService.getVignetteUpdatedFragments.mockResolvedValueOnce({ total: 0, items: [] });
      const handler = getCatalog({ logger, ruleAPIClient, contentService }, { space: 'IST' });
      await handler(mockReq, { ...mockRes, ...res });
      expect(ruleAPIClient.getUpdatedV2).toBeCalledTimes(1);
      expect(contentService.getVignetteUpdatedFragments).toBeCalledTimes(1);
      expect(mockRes.status).toBeCalledWith(200);
      expect(mockJson.json).toBeCalled();
      expect(mockNext).not.toBeCalled();
    });
  });
});
