const R = require('ramda');

const allowedClientIds = [
  // pigeon test
  'd25053e8-3a5e-42cc-bc57-8389df4401f1',
  // sol
  'd99957a9-4e9a-4b9d-ac9c-6197115a4b0b',
  // pigeon bff - for atlas proxy
  '107eb6a6-da19-41bd-9e7f-01598f83689d',
];

const validateClientId = (claims) => {
  const clientId = R.prop([ 'client_id' ], claims);
  return allowedClientIds.includes(clientId);
};

module.exports = {
  validateClientId,
};
