const R = require('ramda');
const { validateClientId } = require('./common');

const getWebfragment = ({ logger, contentService }, config) => async (req, res) => {
  res.set('Content-Type', 'text/plain');
  // validate client id
  if (!validateClientId(R.path([ 'auth', 'claims' ], res.locals))) {
    res.status(403).send('Forbidden Error');
    return;
  }
  // process request
  const webfragmentId = req.params.id;
  try {
    const webfragment = await contentService.getVignetteContentById(webfragmentId);
    // validate jwt's client id
    res.type('html').status(200).send(webfragment.content);
    return;
  } catch (err) {
    res.status(404).send('Not Found Error');
  }
};

module.exports = getWebfragment;
