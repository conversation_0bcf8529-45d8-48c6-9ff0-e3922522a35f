const { Router } = require('express');

const wrapAsync = require('../../server/middleware/wrap-async');
const getCatalog = require('./get-catalog');
const getRule = require('./get-rule');
const getWebFragment = require('./get-webfragment');
const { getResponse } = require('../../common');

const pigeonApiProxyMiddleware = ({ logger, launchDarklyService, pigeonBffAtlasClient, pigeonBffPCFClient, config }) => async (req, res, next) => {
  try {
    const { hostingEnv } = config;
    if (hostingEnv === 'PCF') {
      // Check if proxy-pcf is enabled in LaunchDarkly
      const isPcfProxyEnabled = await launchDarklyService.isFeatureEnabled(
        'pigeon-api.features.proxy-pcf',
        false,
      );
      if (isPcfProxyEnabled) {
        const response = await pigeonBffAtlasClient.fetch(req.originalUrl);
        if (response.headers.get('content-type').startsWith('text/html')) {
          return res.type('html').status(200).send(await response.text());
        }
        const result = await getResponse(response);
        return res.status(200).json(result);
      }
    } else if (hostingEnv === 'GCP') {
      // Check if proxy-atlas is enabled in LaunchDarkly
      const isGcpAtlasProxyEnabled = await launchDarklyService.isFeatureEnabled(
        'pigeon-api.features.proxy-atlas',
        false,
      );
      if (isGcpAtlasProxyEnabled) {
        const response = await pigeonBffPCFClient.fetch(req.originalUrl);
        if (response.headers.get('content-type').startsWith('text/html')) {
          return res.type('html').status(200).send(await response.text());
        }
        const result = await getResponse(response);
        return res.status(200).json(result);
      }
    }
    next();
  } catch (error) {
    logger.error({ message: 'Error in proxying pigeon api call', err: { message: error.message, stack: error.stack } });
    return res.status(500).json({ error: 'Error in proxying pigeon api call' });
  }
};

const init = ({
  logger,
  ruleAPIClient,
  contentService,
  rateLimitingMiddleware,
  contentsCatalogTotalRateLimiter,
  launchDarklyService,
  pigeonBffAtlasClient,
  pigeonBffPCFClient,
}, config) => {
  const router = Router();
  router.use(rateLimitingMiddleware, contentsCatalogTotalRateLimiter, pigeonApiProxyMiddleware({ logger, launchDarklyService, config, pigeonBffAtlasClient, pigeonBffPCFClient }));
  router.get('/', wrapAsync(getCatalog({ logger, ruleAPIClient, contentService }, config)));
  router.get('/rules/:id', wrapAsync(getRule({ logger, ruleAPIClient }, config)));
  router.get('/webfragments/:id', wrapAsync(getWebFragment({ logger, ruleAPIClient, contentService }, config)));
  return router;
};

module.exports = init;
