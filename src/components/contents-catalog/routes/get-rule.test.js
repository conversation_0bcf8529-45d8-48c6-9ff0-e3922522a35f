/* eslint-disable sonarjs/no-duplicate-string */
const getRule = require('./get-rule');

const mockRule = {
  rules_api_id: 17485,
  rules_uniq_url: 'vgntt17485',
  rule_name: 'ITRADE-BROADCAST-TEST',
  rule_type: 'vignette',
  rule_status: 'terminated',
  start_date: '2018-04-12T00:00:00.000Z',
  end_date: '2018-04-13T00:00:00.000Z',
  urgent_flag: false,
  disabled_flag: false,
  deleted_flag: false,
  campaign_name: null,
  created_by_sid: 's1105426',
  updated_by_sid: 's1105426',
  created_ts: '2018-05-03T11:47:54.000Z',
  updated_ts: '2018-05-03T11:47:54.000Z',
  message_type: 'N',
  system_message: null,
  language_code: 'en',
  country_code: 'CA',
  contents: [
    {
      container_name: 'CommunicationCentre_Normal',
      page_name: 'CommunicationCentre',
      content_space: 'db',
      content_type: 'webfragment',
      content_id: '6979',
      subject: 'ITRADE-BROADCAST-TEST',
      content_disabled: false,
    },
    {
      container_name: 'CommunicationCentre_Normal',
      page_name: 'CommunicationCentre_Broadcast',
      content_space: 'db',
      content_type: 'webfragment',
      content_id: '6979',
      subject: 'ITRADE-BROADCAST-TEST',
      content_disabled: false,
    },
  ],
  platforms: [],
  products: [
    {
      targeting_mode: 'or',
      targeting_type: 'BU',
      attribute_values: [
        'HW:',
        'R:',
        'SPCGIIA:',
        'CASL:',
        'SDBI:SL',
        'SDBI:SLP',
        'SMI:',
        'SMI:PLUS',
        'SMI:PINN',
        'SMI:AW',
        'SMI:SUMM',
        'SMI:FMB',
        'TRST:',
        'TRST:EB',
        'B:',
      ],
    },
  ],
};

const mockJson = jest.fn();
const mockStatus = jest.fn().mockReturnValue({ json: mockJson });
const mockSet = jest.fn();
const logger = {
  error: jest.fn(),
};

const mockReq = { params: { id: '17485' } };
const mockRes = {
  set: mockSet,
  status: mockStatus,
};
const mockNext = jest.fn();

const ruleAPIClient = {
  getV2: jest.fn(),
};

describe('Contents Catalog', () => {
  describe('Get Rule', () => {
    beforeEach(() => {
      logger.error.mockClear();
      mockStatus.mockClear();
      mockSet.mockClear();
      mockJson.mockClear();
      mockNext.mockClear();
      ruleAPIClient.getV2.mockClear();
    });
    test('should return 403 on invalid client id', () => {
      const res = { locals: { auth: { claims: { client_id: 'd25053e8-3a5e-42cc-bc57-8389df4401ff' } } } };
      const handler = getRule({ logger }, { space: 'IST' });
      handler(mockReq, { ...mockRes, ...res }, mockNext);
      expect(mockStatus).toBeCalledWith(403);
      expect(mockJson).toBeCalled();
      expect(mockNext).not.toBeCalled();
    });
    test('should return 404 on invalid rule id', async () => {
      const req = { params: { id: '1111111' } };
      const res = {
        set: mockSet,
        status: mockStatus,
        locals: { auth: { claims: { client_id: 'd25053e8-3a5e-42cc-bc57-8389df4401f1' } } },
      };
      const handler = getRule({ logger }, { space: 'IST' });
      await handler(req, res, mockNext);
      expect(mockStatus).not.toBeCalled();
      expect(mockJson).not.toBeCalled();
      expect(mockNext).toBeCalled();
    });
    test('should return 200 on valid request', async () => {
      const req = { params: { id: '19504' } };
      const res = { locals: { auth: { claims: { client_id: 'd25053e8-3a5e-42cc-bc57-8389df4401f1' } } } };
      ruleAPIClient.getV2.mockResolvedValueOnce(mockRule);
      const handler = getRule({ logger, ruleAPIClient }, { space: 'IST' });
      await handler(req, { ...mockRes, ...res }, mockNext);
      expect(ruleAPIClient.getV2).toBeCalledTimes(1);
      expect(mockStatus).toBeCalledWith(200);
      expect(mockJson).toBeCalled();
      expect(mockNext).not.toBeCalled();
    });
  });
});
