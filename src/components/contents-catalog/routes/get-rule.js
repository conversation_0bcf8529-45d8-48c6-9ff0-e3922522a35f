const R = require('ramda');
const uuid = require('uuid/v4');
const { validateClientId } = require('./common');
const { HttpError } = require('../../errors');

const getWebFragmentId = (contents) => {
  if (!Array.isArray(contents) || contents.length === 0) return undefined;
  return contents[0].content_id;
};

const getContainerId = (contents) => {
  if (!Array.isArray(contents) || contents.length === 0) return undefined;
  return contents[0].container;
};

const getPageIds = (contents) => {
  if (!Array.isArray(contents) || contents.length === 0) return [];
  return contents.map((c) => c.page);
};

const getSubject = (contents) => {
  if (!Array.isArray(contents) || contents.length === 0) return undefined;
  return contents[0].subject;
};

const targetModeMappings = {
  and: 'STRICT',
  or: 'LENIENT',
  not: 'EXCLUSIVE',
};

const toTargetMode = (m) => targetModeMappings[String(m)];

const getContentRules = R.cond([
  [ R.is(Array), (ps) => ps.map((p) => ({
    target_mode: toTargetMode(p.attribute_mode),
    rule_type: p.attribute_type,
    rule_attributes: p.attribute_values,
  })) ],
  [ R.T, () => [] ],
]);

const getTargeting = R.propOr({}, 'targeting');

const getTargetingProperty = R.curry((r, prop) => R.pipe(getTargeting, R.prop(prop))(r));

const transformRule = (r) => {
  return {
    rule_id: String(r.id),
    rule_name: r.name,
    content_type: r.message_type,
    start_date: r.start_date,
    expiry_date: r.end_date,
    language: getTargetingProperty(r, 'language_code'),
    country: getTargetingProperty(r, 'country_code'),
    webfragment_id: getWebFragmentId(r.contents),
    container_id: getContainerId(r.contents),
    page_ids: getPageIds(r.contents),
    subject: getSubject(r.contents),
    campaign_id: getTargetingProperty(r, 'campaign_name'),
    content_rules: getContentRules(getTargetingProperty(r, 'products')),
  };
};

const getRule = ({ logger, ruleAPIClient }, config) => async (req, res, next) => {
  // validate client id
  if (!validateClientId(R.path([ 'auth', 'claims' ], res.locals))) {
    res.status(403).json({
      code: 'PIGEON_ERR_FORBIDDEN',
      message: 'client id has no access',
      uuid: uuid(),
      timestamp: (new Date()).toISOString(),
      metadata: [],
    });
    return;
  }
  // process request
  const ruleId = req.params.id;
  try {
    const rule = await ruleAPIClient.getV2(ruleId, {});
    res.status(200).json(transformRule(rule));
  } catch (err) {
    if (err.message === 'Not Found') {
      next(HttpError.notFound('Not Found'));
      return;
    }
    next(err);
  }
};

module.exports = getRule;
