const { validateClientId } = require('./common');

describe('Contents Catalog', () => {
  test('should return false on non-listed client id', () => {
    const claims = { client_id: 'd25053e8-3a5e-42cc-bc57-8389df4401ff' };
    expect(validateClientId(claims)).toEqual(false);
  });
  test('should return false on missing client id', () => {
    const claims = {};
    expect(validateClientId(claims)).toEqual(false);
  });
  test('should return true on listed client id', () => {
    const claims = { client_id: 'd25053e8-3a5e-42cc-bc57-8389df4401f1' };
    expect(validateClientId(claims)).toEqual(true);
  });
});
