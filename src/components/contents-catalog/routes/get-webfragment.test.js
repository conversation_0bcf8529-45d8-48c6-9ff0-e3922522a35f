const getWebFragment = require('./get-webfragment');

const mockSend = jest.fn();
const mockStatus = jest.fn().mockReturnValue({ send: mockSend });
const mockType = jest.fn().mockReturnValue({ status: mockStatus });
const mockSet = jest.fn();
const logger = {
  error: jest.fn(),
};
const contentService = {
  getVignetteContentById: jest.fn(),
};

const mockReq = { params: { id: '7873' } };
const mockRes = {
  set: mockSet,
  type: mockType,
  status: mockStatus,
};
const mockNext = jest.fn();

describe('Contents Catalog', () => {
  describe('Get Web Fragment', () => {
    beforeEach(() => {
      logger.error.mockClear();
      mockStatus.mockClear();
      mockSet.mockClear();
      mockSend.mockClear();
      mockType.mockClear();
      mockNext.mockClear();
      contentService.getVignetteContentById.mockClear();
    });
    test('should return 403 on invalid client id', () => {
      const res = { locals: { auth: { claims: { client_id: 'd25053e8-3a5e-42cc-bc57-8389df4401ff' } } } };
      const handler = getWebFragment({ logger });
      handler(mockReq, { ...mockRes, ...res });
      expect(mockStatus).toBeCalledWith(403);
      expect(mockSend).toBeCalled();
      expect(mockNext).not.toBeCalled();
    });
    test('should return 404 on invalid web fragment id', async () => {
      const req = { params: { id: '1111111' } };
      const res = {
        set: mockSet,
        status: mockStatus,
        locals: { auth: { claims: { client_id: 'd25053e8-3a5e-42cc-bc57-8389df4401f1' } } },
      };
      contentService.getVignetteContentById.mockResolvedValueOnce(null);
      const handler = getWebFragment({ logger, contentService }, { space: 'IST' });
      await handler(req, res);
      expect(contentService.getVignetteContentById).toBeCalledTimes(1);
      expect(mockStatus).toBeCalledWith(404);
      expect(mockSend).toBeCalled();
      expect(mockNext).not.toBeCalled();
    });
    test('should return 200 on valid request', async () => {
      const res = { locals: { auth: { claims: { client_id: 'd25053e8-3a5e-42cc-bc57-8389df4401f1' } } } };
      contentService.getVignetteContentById.mockResolvedValueOnce({ content: '<div></div>' });
      const handler = getWebFragment({ logger, contentService }, { space: 'IST' });
      await handler(mockReq, { ...mockRes, ...res });
      expect(contentService.getVignetteContentById).toBeCalledTimes(1);
      expect(mockType).toBeCalledWith('html');
      expect(mockStatus).toBeCalledWith(200);
      expect(mockSend).toBeCalled();
      expect(mockSend.mock.calls[0][0]).toEqual('<div></div>');
      expect(mockNext).not.toBeCalled();
    });
  });
});
