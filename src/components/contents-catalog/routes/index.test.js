const { Router } = require('express');
const routes = require('./index');

const mockNext = jest.fn();
const mockJson = jest.fn();
const mockStatus = jest.fn();

const mockLogger = {
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};
const mockAuthorize = () => (req, res, next) => next();

const router = routes({
  logger: mockLogger,
  authorize: mockAuthorize,
  rateLimitingMiddleware: jest.fn(),
  contentsCatalogTotalRateLimiter: jest.fn(),
}, { space: 'IST' });

describe('Contents Catalog routes', () => {
  beforeEach(() => {
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
  });
  test('should be an express.Router object', () => {
    expect(router.prototype).toEqual(Router.prototype);
    expect(typeof router.get).toEqual('function');
    expect(typeof router.use).toEqual('function');
    expect(typeof router.handle).toEqual('function');
  });
  test('should have GET /rules/:id', () => {
    const found = router.stack.some((item) => item.route && item.route.path === '/rules/:id' && item.route.methods.get);
    expect(found).toEqual(true);
  });
  test('should have GET /webfragments/:id', () => {
    const found = router.stack.some((item) => item.route && item.route.path === '/webfragments/:id' && item.route.methods.get);
    expect(found).toEqual(true);
  });
});
