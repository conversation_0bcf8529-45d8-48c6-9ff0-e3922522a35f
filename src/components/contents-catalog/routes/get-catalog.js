const uuid = require('uuid/v4');
const Joi = require('@hapi/joi');
const R = require('ramda');
const { validateClientId } = require('./common');
const { getCatalogSchema } = require('../../campaign/routes/validation');
const { HttpError } = require('../../errors');

const publishingStatesMapping = {
  published: 'C',
  terminated: 'E',
};

const toPublishingState = (s) => publishingStatesMapping[String(s)];

const getCatalog = ({ logger, ruleAPIClient, contentService }, config) => async (req, res, next) => {
  // validate client id
  if (!validateClientId(R.path([ 'auth', 'claims' ], res.locals))) {
    res.status(403).json({
      code: 'PIGEON_ERR_FORBIDDEN',
      message: 'client id has no access',
      uuid: uuid(),
      timestamp: (new Date()).toISOString(),
      metadata: [],
    });
    return;
  }

  const {
    error,
  } = Joi.validate(req.query, getCatalogSchema, {
    stripUnknown: true,
  });
  if (error) {
    next(HttpError.badRequest('Validation error', error.details.map((err) => {
      return {
        path: err.path.join('.'),
        message: err.message,
      };
    })));
    return;
  }

  // process request
  const fromDate = req.query.from_date;
  const toDate = req.query.to_date;
  // get rules updates
  const rules = ruleAPIClient.getUpdatedV2({
    deleted: false,
    disabled: false,
    updated_at_gt: fromDate,
    updated_at_lt: toDate,
    type: 'vignette',
    status: 'published,terminated',
    offset: 0,
    limit: 5000,
  });
  // get webfragments updates
  const webfragments = contentService.getVignetteUpdatedFragments({
    updated_at_start: fromDate,
    updated_at_end: toDate,
    offset: 0,
    limit: 5000,
  });
  // send result
  const result = await Promise.all([ webfragments, rules ]);
  res.status(200).json({
    webfragments: result[0].items.map((f) => ({
      webfragment_id: String(f.web_fragment_id),
      last_updated_time: f.updated_ts,
    })),
    rules: result[1].items.map((r) => ({
      rule_id: String(r.id),
      last_updated_time: r.updated_at,
      publishing_state: toPublishingState(r.status),
    })),
  });
};

module.exports = getCatalog;
