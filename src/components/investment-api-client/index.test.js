const InvestmentApiClient = require('./index');
const mockLogger = {
  error: jest.fn(),
};
const mockFetch = jest.fn();
const mockUri = 'https://cdb-int-investments-ist.apps.stg.azr-cc-pcf.cloud.bns';

describe('Investment API Client', () => {
  beforeEach(() => {
    mockFetch.mockClear();
    mockLogger.error.mockClear();
  });

  test('should init', () => {
    const client = InvestmentApiClient({ logger: mockLogger, fetch: mockFetch }, mockUri);
    expect(client).toHaveProperty('getGicRates');
    expect(client.getGicRates).toBeInstanceOf(Function);
  });

  test('should call getGicRates', async () => {
    const mockResponseData = {
      'data': {
        'request_id': 15396,
        'gic_rates': [
          {
            'code': 'BNSLTNRC',
            'type': 'LTNR',
            'expiry_date': '2013-09-30',
            'term_rates': [
              {
                'term': {
                  'term': 1,
                  'unit': 'Year',
                  'details': {
                    'from': 365,
                    'to': 545,
                    'unit': 'Day',
                  },
                },
                'is_recommended': false,
                'frequency_rates': [
                  {
                    'interest_payment_frequency': 'Annually',
                    'is_compound': true,
                    'offer_rate': 2.54,
                    'target_rate': 2.54,
                    'branch_discretion_limit': 2.54,
                  },
                  {
                    'interest_payment_frequency': 'Semi-Annually',
                    'is_compound': true,
                    'offer_rate': 2.415,
                    'target_rate': 2.415,
                    'branch_discretion_limit': 2.415,
                  },
                ],
              },
            ],
          },
        ],
      },
      'notifications': null,
    };

    const mockResponse = {
      status: 200,
      ok: true,
      text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockResponseData)),
    };

    mockFetch.mockResolvedValueOnce(mockResponse);

    const mockPlanNumber = '123abc';
    const mockCertificateNumber = '123abcd';
    const mockRequestHeaders = {
      'xCustomerScotiacard': '1234567890',
    };
    const mockPreferredEnvironment = 'uatgreen';

    const client = InvestmentApiClient({ logger: mockLogger, fetch: mockFetch }, mockUri);
    const res = await client.getGicRates(mockPreferredEnvironment, mockRequestHeaders, mockPlanNumber, mockCertificateNumber);

    expect(res).toBeDefined();
    expect(res).toEqual(mockResponseData);
  });
});
