const getGicRates = require('./get-gic-rates');

const mockFetch = jest.fn();
const mockLogger = {
  error: jest.fn(),
};

const mockResponseData = {
  'data': {
    'request_id': 15396,
    'gic_rates': [
      {
        'code': 'BNSLTNRC',
        'type': 'LTNR',
        'expiry_date': '2013-09-30',
        'term_rates': [
          {
            'term': {
              'term': 1,
              'unit': 'Year',
              'details': {
                'from': 365,
                'to': 545,
                'unit': 'Day',
              },
            },
            'is_recommended': false,
            'frequency_rates': [
              {
                'interest_payment_frequency': 'Annually',
                'is_compound': true,
                'offer_rate': 2.54,
                'target_rate': 2.54,
                'branch_discretion_limit': 2.54,
              },
              {
                'interest_payment_frequency': 'Semi-Annually',
                'is_compound': true,
                'offer_rate': 2.415,
                'target_rate': 2.415,
                'branch_discretion_limit': 2.415,
              },
            ],
          },
        ],
      },
    ],
  },
  'notifications': null,
};

const mockRequestHeaders = {
  'xCustomerScotiacard': '1234567890',
};
describe('Investments API Client', () => {
  beforeEach(() => {
    mockFetch.mockClear();
    mockLogger.error.mockClear();
  });

  test('should throw error on response status >= 400', async () => {
    const mockResponse = {
      status: 400,
      statusText: 'bad request',
      ok: false,
      text: jest.fn().mockResolvedValueOnce(JSON.stringify({})),
    };

    mockFetch.mockResolvedValueOnce(mockResponse);

    try {
      const res = await getGicRates({ logger: mockLogger, fetch: mockFetch }, 'uatgreen', mockRequestHeaders, 'plan#1', 'certificate#1');
      throw new Error(`success response: ${JSON.stringify(res)}`); // this line should not run.
    } catch (err) {
      expect(err).toBeDefined();
      expect(err.message).toEqual(mockResponse.statusText);
    }
  });

  test('should successfully call', async () => {
    const mockResponse = {
      status: 200,
      ok: true,
      text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockResponseData)),
    };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const res = await getGicRates({ logger: mockLogger, fetch: mockFetch }, 'uatgreen', mockRequestHeaders, 'plan#2', 'certificate#2');
    expect(mockResponse.text).toBeCalled();
    expect(res).toHaveProperty('data');
    expect(res).toEqual(mockResponseData);
  });

  test('should successfully get from cache', async () => {
    const mockResponse = {
      status: 200,
      ok: true,
      text: jest.fn().mockResolvedValue(JSON.stringify(mockResponseData)),
    };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const res = await Promise.all([
      getGicRates({ logger: mockLogger, fetch: mockFetch }, 'uatgreen', mockRequestHeaders, 'plan#3', 'certificate#3'),
      getGicRates({ logger: mockLogger, fetch: mockFetch }, 'uatgreen', mockRequestHeaders, 'plan#3', 'certificate#3'),
    ]);
    expect(mockResponse.text).toBeCalled();
    expect(mockFetch).toBeCalledTimes(1);
    expect(res.length).toEqual(2);
    expect(res[0]).toEqual(res[1]);
  });

  test('should throw error for no card is present in the headers', async () => {
    const mockErrResponse = {
      status: 200,
      ok: true,
      text: jest.fn().mockResolvedValueOnce(JSON.stringify({})),
    };

    mockFetch.mockResolvedValueOnce(mockErrResponse);

    try {
      const res = await getGicRates({ logger: mockLogger, fetch: mockFetch }, 'uatgreen', {}, 'plan#', 'certificate#');
      throw new Error(`success response: ${JSON.stringify(res)}`); // this line should not run.
    } catch (err) {
      expect(err).toBeDefined();
      expect(err.message).toEqual('Required field missing from headers: x-customer-scotiacard');
    }
  });
});
