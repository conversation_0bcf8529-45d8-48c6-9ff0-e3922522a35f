const getGicRates = require('./get-gic-rates');

const withBasePath = (fetch, basePath) => (url, opts) => fetch(`${basePath}${url}`, opts);

const init = ({ logger, fetch }, basePath) => {
  const fetchWithBase = withBasePath(fetch, basePath);
  return {
    getGicRates: (env, reqHeaders, plan, certificate) =>
      getGicRates({ logger, fetch: fetchWithBase }, env, reqHeaders, plan, certificate),
  };
};

module.exports = init;
