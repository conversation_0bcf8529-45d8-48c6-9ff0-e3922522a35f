const LRU = require('lru-cache');
const AsyncLock = require('async-lock');
const { createHash, randomBytes } = require('crypto');
const { getResponse } = require('../common');

const matchLeadingZeros = new RegExp(/^0+(?!$)/);

const validEnvs = [ 'istgreen', 'istred', 'istblack', 'uatgreen', 'uatred', 'uatblack', 'uatgold' ];

const cache = new LRU({
  maxAge: 600000,
  max: 1000,
  length: () => 1,
});

const lock = new AsyncLock();

const getGicRates = async ({ logger, fetch }, env, reqHeaders, plan, certificate) => {
  const xCustomerScotiacard = reqHeaders['xCustomerScotiacard'];

  if (!xCustomerScotiacard) {
    logger.error({ message: 'Required field missing from headers: x-customer-scotiacard' });
    throw new Error('Required field missing from headers: x-customer-scotiacard');
  }

  const hash = createHash('sha256');
  hash.update(`${xCustomerScotiacard}_${certificate}_${plan}`);
  const cacheKey = hash.digest('base64');
  const cachedGicResponse = cache.get(cacheKey);
  if (cachedGicResponse) {
    return cachedGicResponse;
  }
  return lock.acquire(cacheKey, async () => {
    const threadSafeGicEntry = cache.get(cacheKey);
    if (threadSafeGicEntry) {
      return threadSafeGicEntry;
    }
    const body = {
      plan: { number: plan },
      // remove leading zeroes
      gic: { certificate_number: certificate.replace(certificate.match(matchLeadingZeros), '') },
    };
    const headers = {
      'Content-Type': 'application/json; charset=utf-8',
      Accept: 'application/json',
      'x-customer-scotiacard': xCustomerScotiacard,
      'x-country-code': reqHeaders['x-country-code'] || 'CA',
      'x-b3-traceid': reqHeaders['traceId'] || randomBytes(8).toString('hex'),
      'x-channel-id': reqHeaders['channelId'],
      'x-b3-spanid': reqHeaders['spanId'] || randomBytes(8).toString('hex'),
      'x-originating-appl-code': reqHeaders['xOriginatingApplCode'] || 'BFB6',
      'preferred-environment': reqHeaders['preferredEnv'],
      'x-application': reqHeaders['xApplication'],
    };
    if (typeof env === 'string' && validEnvs.includes(env.toLowerCase())) {
      headers['Preferred-Environment'] = env;
    }
    const response = await fetch('/v3/investment-plans/gic/rates', {
      headers,
      method: 'POST',
      body: JSON.stringify(body),
    });
    const result = await getResponse(response);
    cache.set(cacheKey, result);
    return result;
  });
};

module.exports = getGicRates;
