const { CerberusErrorCodes } = require('pigeon-cerberus');
const HttpError = require('./http-error');

module.exports = ({ logger = null }) => (err, req, res, next) => {
  if (res.headerSent) {
    next(err);
    return;
  }
  if (err instanceof HttpError && err.statusCode < 500) {
    // known error
    res.status(err.statusCode).json(err);
    return;
  }
  if (err.name === 'CerberusError') {
    // auth error
    const authError = [ CerberusErrorCodes.InvalidScope ].includes(err.code)
      ? HttpError.forbidden('Forbidden')
      : HttpError.unauthorized('Unauthorized');
    logger.info({ message: 'auth error', err, timestamp: authError.timestamp, uuid: authError.uuid });
    res.status(authError.statusCode).json(authError);
    return;
  }

  // bad request
  // mostly thrown by body-parser's out-of-box error handler
  // http://expressjs.com/en/resources/middleware/body-parser.html#errors
  if (parseInt(Number(err.statusCode) / 100) === 4) {
    const badRequestError = HttpError.badRequest(err.message);
    const loggableErr = { ...err, url: req.originalUrl };
    delete loggableErr.body; // too verbose, can open up logging in the future as needed
    logger.warn({
      message: 'Rejected bad request',
      error: { ...badRequestError, metadata: [ loggableErr ] },
    });
    return res
      .status(err.statusCode)
      .json({ data: {}, notifications: [ badRequestError ] });
  }

  const internalError = HttpError.internalServer('Internal Server Error', err.metadata);
  if (Object.prototype.hasOwnProperty.call(err, 'uuid')) {
    internalError.uuid = err.uuid;
  }
  if (Object.prototype.hasOwnProperty.call(err, 'metadata')) {
    internalError.metadata = err.metadata;
  }
  if (Object.prototype.hasOwnProperty.call(err, 'timestamp')) {
    internalError.timestamp = err.timestamp;
  }
  if (logger && typeof logger.error === 'function') {
    logger.error({ message: 'unknown error', err, uuid: internalError.uuid });
  }
  res.status(internalError.statusCode).json(internalError);
};
