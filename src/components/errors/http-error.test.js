const HttpError = require('./http-error');
const codes = require('./codes');

describe('HttpError', () => {
  test('should create proper HttpError object', () => {
    const message = 'Validation error';
    const metadata = [
      {
        message: 'name must not be empty',
        path: 'name',
      },
    ];
    const err = HttpError.badRequest(message, metadata);
    expect(err.isHttpError).toEqual(true);
    expect(err.message).toEqual(message);
    expect(err.metadata).toEqual(metadata);
    expect(err.statusCode).toEqual(400);
    expect(err.code).toEqual(codes['400']);
    expect(err).toHaveProperty('uuid');
    expect(err).toHaveProperty('timestamp');
    expect(err instanceof HttpError).toBe(true);
  });
  test('should create proper HttpError object using Error', () => {
    const message = 'Validation error';
    const metadata = [
      {
        message: 'name must not be empty',
        path: 'name',
      },
    ];
    const err = new HttpError(new Error(message), 500, metadata);
    expect(err.isHttpError).toEqual(true);
    expect(err.message).toEqual(message);
    expect(err.metadata).toEqual(metadata);
    expect(err.statusCode).toEqual(500);
    expect(err.code).toEqual(codes['500']);
    expect(err).toHaveProperty('uuid');
    expect(err).toHaveProperty('timestamp');
    expect(err instanceof HttpError).toBe(true);
  });
  test('should use toJSON()', () => {
    const err = HttpError.badRequest();
    const json = JSON.parse(JSON.stringify(err));
    expect(json).not.toHaveProperty('isHttpError');
    expect(json).not.toHaveProperty('stack');
    expect(Object.keys(json).length).toEqual(5);
    expect(json).toHaveProperty('code');
    expect(json).toHaveProperty('message');
    expect(json).toHaveProperty('uuid');
    expect(json).toHaveProperty('timestamp');
    expect(json).toHaveProperty('metadata');
  });
  test('badRequest', () => {
    const err = HttpError.badRequest();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(400);
    expect(err.code).toEqual(codes['400']);
  });
  test('unauthorized', () => {
    const err = HttpError.unauthorized();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(401);
    expect(err.code).toEqual(codes['401']);
  });
  test('paymentRequired', () => {
    const err = HttpError.paymentRequired();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(402);
    expect(err.code).toEqual(codes['402']);
  });
  test('forbidden', () => {
    const err = HttpError.forbidden();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(403);
    expect(err.code).toEqual(codes['403']);
  });
  test('notFound', () => {
    const err = HttpError.notFound();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(404);
    expect(err.code).toEqual(codes['404']);
  });
  test('methodNotAllowed', () => {
    const err = HttpError.methodNotAllowed();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(405);
    expect(err.code).toEqual(codes['405']);
  });
  test('notAcceptable', () => {
    const err = HttpError.notAcceptable();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(406);
    expect(err.code).toEqual(codes['406']);
  });
  test('proxyAuthRequired', () => {
    const err = HttpError.proxyAuthRequired();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(407);
    expect(err.code).toEqual(codes['407']);
  });
  test('requestTimeout', () => {
    const err = HttpError.requestTimeout();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(408);
    expect(err.code).toEqual(codes['408']);
  });
  test('conflict', () => {
    const err = HttpError.conflict();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(409);
    expect(err.code).toEqual(codes['409']);
  });
  test('resourceGone', () => {
    const err = HttpError.resourceGone();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(410);
    expect(err.code).toEqual(codes['410']);
  });
  test('lengthRequired', () => {
    const err = HttpError.lengthRequired();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(411);
    expect(err.code).toEqual(codes['411']);
  });
  test('preconditionFailed', () => {
    const err = HttpError.preconditionFailed();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(412);
    expect(err.code).toEqual(codes['412']);
  });
  test('payloadTooLarge', () => {
    const err = HttpError.payloadTooLarge();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(413);
    expect(err.code).toEqual(codes['413']);
  });
  test('uriTooLong', () => {
    const err = HttpError.uriTooLong();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(414);
    expect(err.code).toEqual(codes['414']);
  });
  test('unsupportedMediaType', () => {
    const err = HttpError.unsupportedMediaType();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(415);
    expect(err.code).toEqual(codes['415']);
  });
  test('rangeNotSatisfiable', () => {
    const err = HttpError.rangeNotSatisfiable();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(416);
    expect(err.code).toEqual(codes['416']);
  });
  test('expectationFailed', () => {
    const err = HttpError.expectationFailed();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(417);
    expect(err.code).toEqual(codes['417']);
  });
  test('teapot', () => {
    const err = HttpError.teapot();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(418);
    expect(err.code).toEqual(codes['418']);
  });
  test('misdirectedRequest', () => {
    const err = HttpError.misdirectedRequest();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(421);
    expect(err.code).toEqual(codes['421']);
  });
  test('unprocessableEntity', () => {
    const err = HttpError.unprocessableEntity();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(422);
    expect(err.code).toEqual(codes['422']);
  });
  test('locked', () => {
    const err = HttpError.locked();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(423);
    expect(err.code).toEqual(codes['423']);
  });
  test('failedDependency', () => {
    const err = HttpError.failedDependency();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(424);
    expect(err.code).toEqual(codes['424']);
  });
  test('upgradeRequired', () => {
    const err = HttpError.upgradeRequired();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(426);
    expect(err.code).toEqual(codes['426']);
  });
  test('preconditionRequired', () => {
    const err = HttpError.preconditionRequired();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(428);
    expect(err.code).toEqual(codes['428']);
  });
  test('tooManyRequests', () => {
    const err = HttpError.tooManyRequests();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(429);
    expect(err.code).toEqual(codes['429']);
  });
  test('requestHeaderFieldsTooLarge', () => {
    const err = HttpError.requestHeaderFieldsTooLarge();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(431);
    expect(err.code).toEqual(codes['431']);
  });
  test('legalReasons', () => {
    const err = HttpError.legalReasons();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(451);
    expect(err.code).toEqual(codes['451']);
  });
  test('internalServer', () => {
    const err = HttpError.internalServer();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(500);
    expect(err.code).toEqual(codes['500']);
  });
  test('notImplemented', () => {
    const err = HttpError.notImplemented();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(501);
    expect(err.code).toEqual(codes['501']);
  });
  test('badGateway', () => {
    const err = HttpError.badGateway();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(502);
    expect(err.code).toEqual(codes['502']);
  });
  test('serviceUnavailable', () => {
    const err = HttpError.serviceUnavailable();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(503);
    expect(err.code).toEqual(codes['503']);
  });
  test('gatewayTimeout', () => {
    const err = HttpError.gatewayTimeout();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(504);
    expect(err.code).toEqual(codes['504']);
  });
  test('httpVersionNotSupported', () => {
    const err = HttpError.httpVersionNotSupported();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(505);
    expect(err.code).toEqual(codes['505']);
  });
  test('variantAlsoNegotiates', () => {
    const err = HttpError.variantAlsoNegotiates();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(506);
    expect(err.code).toEqual(codes['506']);
  });
  test('insufficientStorage', () => {
    const err = HttpError.insufficientStorage();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(507);
    expect(err.code).toEqual(codes['507']);
  });
  test('loopDetected', () => {
    const err = HttpError.loopDetected();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(508);
    expect(err.code).toEqual(codes['508']);
  });
  test('notExtended', () => {
    const err = HttpError.notExtended();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(510);
    expect(err.code).toEqual(codes['510']);
  });
  test('networkAuthRequired', () => {
    const err = HttpError.networkAuthRequired();
    expect(err instanceof HttpError).toEqual(true);
    expect(err.statusCode).toEqual(511);
    expect(err.code).toEqual(codes['511']);
  });
});
