const uuid = require('uuid/v4');
const clone = require('./clone');
const codes = require('./codes');

class HttpError extends Error {
  static [Symbol.hasInstance](instance) {
    return instance instanceof Error && !!instance.isHttpError;
  }
  constructor(message = 'Error', statusCode = 500, metadata = [], ctor = HttpError) {
    super();
    if (message instanceof Error) {
      return HttpError._initialize(message, statusCode, metadata, ctor);
    }
    const err = new Error(message);
    Error.captureStackTrace(err, ctor);
    return HttpError._initialize(err, statusCode, metadata, ctor);
  }
  static _initialize(err, statusCode = 500, metadata = [], ctor = HttpError) {
    return Object.assign(clone(err), {
      statusCode,
      metadata,
      typeof: ctor,
      code: codes[String(statusCode)],
      timestamp: (new Date()).toISOString(),
      uuid: uuid(),
      isHttpError: true,
      toJSON: function() {
        return {
          code: this.code,
          message: this.message,
          uuid: this.uuid,
          timestamp: this.timestamp,
          metadata: this.metadata,
        };
      },
    });
  }
  static badRequest(message, metadata = []) {
    return new HttpError(message, 400, metadata, HttpError.badRequest);
  }
  static unauthorized(message, metadata = []) {
    return new HttpError(message, 401, metadata, HttpError.unauthorized);
  }
  static paymentRequired(message, metadata = []) {
    return new HttpError(message, 402, metadata, HttpError.paymentRequired);
  }
  static forbidden(message, metadata = []) {
    return new HttpError(message, 403, metadata, HttpError.forbidden);
  }
  static notFound(message, metadata = []) {
    return new HttpError(message, 404, metadata, HttpError.notFound);
  }
  static methodNotAllowed(message, metadata = []) {
    return new HttpError(message, 405, metadata, HttpError.methodNotAllowed);
  }
  static notAcceptable(message, metadata = []) {
    return new HttpError(message, 406, metadata, HttpError.notAcceptable);
  }
  static proxyAuthRequired(message, metadata = []) {
    return new HttpError(message, 407, metadata, HttpError.proxyAuthRequired);
  }
  static requestTimeout(message, metadata = []) {
    return new HttpError(message, 408, metadata, HttpError.requestTimeout);
  }
  static conflict(message, metadata = []) {
    return new HttpError(message, 409, metadata, HttpError.conflict);
  }
  static resourceGone(message, metadata = []) {
    return new HttpError(message, 410, metadata, HttpError.resourceGone);
  }
  static lengthRequired(message, metadata = []) {
    return new HttpError(message, 411, metadata, HttpError.lengthRequired);
  }
  static preconditionFailed(message, metadata = []) {
    return new HttpError(message, 412, metadata, HttpError.preconditionFailed);
  }
  static payloadTooLarge(message, metadata = []) {
    return new HttpError(message, 413, metadata, HttpError.payloadTooLarge);
  }
  static uriTooLong(message, metadata = []) {
    return new HttpError(message, 414, metadata, HttpError.uriTooLong);
  }
  static unsupportedMediaType(message, metadata = []) {
    return new HttpError(message, 415, metadata, HttpError.unsupportedMediaType);
  }
  static rangeNotSatisfiable(message, metadata = []) {
    return new HttpError(message, 416, metadata, HttpError.rangeNotSatisfiable);
  }
  static expectationFailed(message, metadata = []) {
    return new HttpError(message, 417, metadata, HttpError.expectationFailed);
  }
  static teapot(message, metadata = []) {
    return new HttpError(message, 418, metadata, HttpError.teapot);
  }
  static misdirectedRequest(message, metadata = []) {
    return new HttpError(message, 421, metadata, HttpError.misdirectedRequest);
  }
  static unprocessableEntity(message, metadata = []) {
    return new HttpError(message, 422, metadata, HttpError.unprocessableEntity);
  }
  static locked(message, metadata = []) {
    return new HttpError(message, 423, metadata, HttpError.locked);
  }
  static failedDependency(message, metadata = []) {
    return new HttpError(message, 424, metadata, HttpError.failedDependency);
  }
  static upgradeRequired(message, metadata = []) {
    return new HttpError(message, 426, metadata, HttpError.upgradeRequired);
  }
  static preconditionRequired(message, metadata = []) {
    return new HttpError(message, 428, metadata, HttpError.preconditionRequired);
  }
  static tooManyRequests(message, metadata = []) {
    return new HttpError(message, 429, metadata, HttpError.tooManyRequests);
  }
  static requestHeaderFieldsTooLarge(message, metadata = []) {
    return new HttpError(message, 431, metadata, HttpError.requestHeaderFieldsTooLarge);
  }
  static legalReasons(message, metadata = []) {
    return new HttpError(message, 451, metadata, HttpError.legalReasons);
  }
  static internalServer(message, metadata = []) {
    return new HttpError(message, 500, metadata, HttpError.internalServer);
  }
  static notImplemented(message, metadata = []) {
    return new HttpError(message, 501, metadata, HttpError.notImplemented);
  }
  static badGateway(message, metadata = []) {
    return new HttpError(message, 502, metadata, HttpError.badGateway);
  }
  static serviceUnavailable(message, metadata = []) {
    return new HttpError(message, 503, metadata, HttpError.serviceUnavailable);
  }
  static gatewayTimeout(message, metadata = []) {
    return new HttpError(message, 504, metadata, HttpError.gatewayTimeout);
  }
  static httpVersionNotSupported(message, metadata = []) {
    return new HttpError(message, 505, metadata, HttpError.httpVersionNotSupported);
  }
  static variantAlsoNegotiates(message, metadata = []) {
    return new HttpError(message, 506, metadata, HttpError.variantAlsoNegotiates);
  }
  static insufficientStorage(message, metadata = []) {
    return new HttpError(message, 507, metadata, HttpError.insufficientStorage);
  }
  static loopDetected(message, metadata = []) {
    return new HttpError(message, 508, metadata, HttpError.loopDetected);
  }
  static notExtended(message, metadata = []) {
    return new HttpError(message, 510, metadata, HttpError.notExtended);
  }
  static networkAuthRequired(message, metadata = []) {
    return new HttpError(message, 511, metadata, HttpError.networkAuthRequired);
  }
}

module.exports = HttpError;
