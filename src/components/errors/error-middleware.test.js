const { CerberusError, CerberusErrorCodes } = require('pigeon-cerberus');
const errorMiddleware = require('./error-middleware');
const HttpError = require('./http-error');

const mockNext = jest.fn();
const mockJson = jest.fn();
const mockStatus = jest.fn();
const mockRes = { status: mockStatus };
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
};

describe('Error middleware', () => {
  const errorMessage = 'Unexpected number in JSON at position 7';
  beforeEach(() => {
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
    mockStatus.mockReturnValue({ json: mockJson });
    mockLogger.info.mockClear();
    mockLogger.error.mockClear();
  });
  test('should create a middleware', () => {
    const mw = errorMiddleware({});
    expect(typeof mw).toEqual('function');
  });
  test('pass error to next if header was already sent', () => {
    const mw = errorMiddleware({});
    const error = new Error('some error');
    mw(error, {}, { headerSent: true }, mockNext);
    expect(mockNext.mock.calls.length).toEqual(1);
    expect(mockNext.mock.calls[0][0]).toEqual(error);
  });
  test('should transform unknown error to Internal Server Error', () => {
    const message = 'some error';
    const mw = errorMiddleware({});
    const error = new Error(message);
    mw(error, {}, mockRes);
    expect(mockStatus).toBeCalledWith(500);
    expect(mockJson.mock.calls.length).toEqual(1);
    expect(typeof mockJson.mock.calls[0][0]).toEqual('object');
    expect(mockJson.mock.calls[0][0].message.indexOf('Internal Server Error')).not.toBe(-1);
    expect(mockJson.mock.calls[0][0].message.indexOf(message)).toBe(-1);
  });
  test('should send known arror as is', () => {
    const message = 'entity not found';
    const mw = errorMiddleware({});
    const notFound = HttpError.notFound(message);
    mw(notFound, {}, mockRes);
    expect(mockStatus).toBeCalledWith(notFound.statusCode);
    expect(mockJson.mock.calls.length).toEqual(1);
    expect(typeof mockJson.mock.calls[0][0]).toEqual('object');
    expect(mockJson.mock.calls[0][0].message.indexOf(message)).not.toBe(-1);
  });
  test('should send any 500+ as an Internal Server Error', () => {
    const mockMessage = 'bad gateway';
    const err = HttpError.badGateway(mockMessage);
    const mw = errorMiddleware({ logger: mockLogger });
    mw(err, {}, mockRes);
    expect(mockStatus).toBeCalledWith(500);
    expect(mockJson.mock.calls[0][0].message).not.toEqual(mockMessage);
    expect(mockLogger.error).toBeCalled();
  });
  test('should send 401 if not authenticated', () => {
    const err = new CerberusError(CerberusErrorCodes.OpaqueTokenExpired);
    const mw = errorMiddleware({ logger: mockLogger });
    mw(err, {}, mockRes);
    expect(mockStatus).toBeCalledWith(401);
    expect(mockJson.mock.calls[0][0].message).toEqual('Unauthorized');
    expect(mockLogger.info).toBeCalled();
  });
  test('400 - should return validation failure details', () => {
    const err = { // sample error thrown by body-parser library for invalid json
      expose: true,
      statusCode: 400,
      status: 400,
      body: '{\n  "a"1:1,\n}\n', // extra 1 after field name 'a'
      type: 'entity.parse.failed',
      message: errorMessage,
      stack: 'SyntaxError: Unexpected number in JSON at position 7\n    at JSON.parse (<anonymous>...',
    };

    const originalUrl = '/v1/some';
    const mw = errorMiddleware({ logger: mockLogger });
    mw(err, { originalUrl }, mockRes);

    // assert response
    expect(mockStatus).toHaveBeenCalledWith(400);
    const mockResBody = JSON.parse(JSON.stringify(mockJson.mock.calls[0][0]));
    expect(mockResBody.notifications[0].code).toBe('HTTP_BAD_REQUEST');
    expect(mockResBody.notifications[0].message).toBe(errorMessage);

    // assert logger warning content
    const mockLoggerCall = mockLogger.warn.mock.calls[0][0];
    expect(mockLoggerCall.message).toBe('Rejected bad request');

    const actualErr = mockLoggerCall.error;
    expect(actualErr.code).toBe('HTTP_BAD_REQUEST');
    expect(actualErr.message).toBe(errorMessage);

    const actualMetadata = actualErr.metadata[0];
    // skipping date and uuid assertion due to challenges in mocking these
    expect(actualMetadata.status).toBe(400);
    expect(actualMetadata.statusCode).toBe(400);
    expect(actualMetadata.type).toBe(err.type);
    expect(actualMetadata.url).toBe(originalUrl);
    expect(mockStatus).toBeCalledWith(400);
  });
});
