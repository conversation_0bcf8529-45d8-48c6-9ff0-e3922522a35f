const Joi = require('@hapi/joi');

const getAlertsSchema = Joi.object()
  .keys({
    container: Joi.string().min(1).max(40).regex(/^[a-zA-Z0-9_-]+$/),
    platform: Joi.string().min(1).max(20).regex(/^[a-z]+$/),
    app_version: Joi.string().min(1).max(50),
    os_version: Joi.string().min(1).max(100),
    device_model: Joi.string().min(1).max(100),
    limit: Joi.number().min(1).default(1),
    application: Joi.string().min(1).max(20).regex(/^[a-zA-Z0-9_-]+$/).lowercase(),
    page: Joi.string().min(1).max(64).regex(/^[a-zA-Z0-9_\-.]+$/),
  })
  .requiredKeys([ 'platform' ]);

module.exports = {
  getAlertsSchema,
};
