const Joi = require('@hapi/joi');
const escapeHtml = require('escape-html');
const R = require('ramda');
const { cloneDeep } = require('lodash');
const { getAlertsSchema } = require('./validation');
const { HttpError } = require('../../errors');
const {
  filterRulesByVersion,
  trim,
  filterByApplication,
  filterByContainerAndPage,
  getApplicationPlatform,
} = require('../../helpers');

const getAlerts = ({ logger, alertService }, config) => async (req, res, next) => {
  // checking if `os_version` & `app_version` are passed with null values and then delete it from req.query before Joi to avoid 400 validation error
  // splitting into 2 if statements to make it more readable
  if (!req.query.os_version) {
    delete req.query.os_version;
  }
  if (!req.query.app_version) {
    delete req.query.app_version;
  }

  // validate input parameters
  const { error, value: query } = Joi.validate(req.query, getAlertsSchema, { stripUnknown: true });
  if (error) {
    next(HttpError.badRequest('Validation error', error.details.map((err) => {
      return {
        path: err.path.join('.'),
        message: escapeHtml(err.message),
      };
    })));
    return;
  }
  const language = R.pathOr('en', [ 'languageContentful' ], res.locals);
  const { application, platform } = getApplicationPlatform(query.platform, query.application);
  let alerts = Object.values(cloneDeep(R.pathOr([], [ 'alerts', language ], alertService.get())));
  alerts = filterByContainerAndPage(alerts, query.page, query.container);
  alerts = filterRulesByVersion(alerts, {
    platform: trim(platform),
    appVersion: trim(query.app_version),
    osVersion: trim(query.os_version),
    deviceModel: trim(query.device_model),
  });
  if (R.pathOr(false, [ 'features', 'application' ], config)) {
    alerts = filterByApplication(alerts, application)
      .map((alert) => ({
        id: alert.id,
        name: alert.name,
        container: alert.container,
        type: alert.type,
        content: alert.content,
        application: alert.application,
      }));
  } else {
    alerts = alerts.map((alert) => ({
      id: alert.id,
      name: alert.name,
      container: alert.container,
      type: alert.type,
      content: alert.content,
    }));
  }
  if (alerts.length === 0) {
    res.status(200).json({
      data: { total: 0, items: [] },
      notifications: [],
    });
    return;
  }
  // send response
  res.status(200).json({
    data: {
      total: alerts.length,
      items: alerts.filter((item) => item !== null).slice(0, query.limit),
    },
    notifications: [],
  });
};

module.exports = getAlerts;
