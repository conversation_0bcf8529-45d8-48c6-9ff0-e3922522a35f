const { Router } = require('express');
const wrapAsync = require('../../server/middleware/wrap-async');
const getAlerts = require('./get-alerts');
const maintenanceMode = require('../../server/middleware/maintenance-mode');

const init = ({
  logger,
  alertService,
  rateLimitingMiddleware,
  alertsTotalRateLimiter,
  alertsClientRateLimiter,
  launchDarklyService,
}, config) => {
  const router = Router();

  router.use(rateLimitingMiddleware, alertsTotalRateLimiter, alertsClientRateLimiter);

  router.get('/',
    [ maintenanceMode({ launchDarklyService, hostingEnv: config.hostingEnv }, { code: 200, type: 'getAlerts' }) ],
    wrapAsync(getAlerts({ logger, alertService }, config)));
  return router;
};

module.exports = init;
