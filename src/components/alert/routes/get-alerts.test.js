const getAlerts = require('./get-alerts');
const { HttpError } = require('../../errors');

const config = {
  features: { application: true, disposition: false },
  hostingEnv: 'PCF',
};

const mockNext = jest.fn();
const mockJson = jest.fn();
const mockStatus = jest.fn();
const mockRes = { status: mockStatus, locals: { language: 'fr' } };

const mockLogger = {
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};
const mockLaunchDarklyService = {
  init: jest.fn(),
  isFeatureEnabled: jest.fn(),
  close: jest.fn(),
};
const mockAlertService = {
  get: jest.fn(),
};
const mockContentService = {
  getContentByTypeAndId: jest.fn(),
};
const testId1 = 'test-id-1';
const testContainer1 = 'test-container-1';
const testContainer2 = 'test-container-2';
const testPage1 = 'test-page-1';
const testName1 = 'test-name-1';

describe('Alert Rule routes list', () => {
  beforeEach(() => {
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
    mockStatus.mockReturnValue({ json: mockJson });
    mockAlertService.get.mockClear();
    mockContentService.getContentByTypeAndId.mockClear();
    mockLaunchDarklyService.isFeatureEnabled.mockReturnValue({ 'pcf': false, 'gcp': false });
    Object.keys(mockLogger).forEach((k) => mockLogger[String(k)].mockClear());
  });

  test('should call next with Bad Request error on invalid request', async () => {
    const endpoint = getAlerts({ logger: mockLogger, alertService: mockAlertService, contentService: mockContentService, launchDarklyService: mockLaunchDarklyService }, config);
    const mockReq = { query: { platform: 'ios', container: '#' } };
    await endpoint(mockReq, mockRes, mockNext);
    expect(mockNext).toBeCalled();
    expect(mockNext.mock.calls.length).toEqual(1);
    expect(mockNext.mock.calls[0][0] instanceof HttpError).toBe(true);
    expect(mockNext.mock.calls[0][0].statusCode).toBe(400);
  });

  test('should return an empty array if Alert Service returns an empty array', async () => {
    const endpoint = getAlerts({ logger: mockLogger, alertService: mockAlertService, contentService: mockContentService, launchDarklyService: mockLaunchDarklyService }, config);
    const mockReq = { query: { platform: 'ios' } };
    mockAlertService.get.mockReturnValue({
      alerts: {
        en: {},
        fr: {},
      },
    });
    await endpoint(mockReq, mockRes, mockNext);
    expect(mockLogger.error).not.toBeCalled();
    expect(mockNext).not.toBeCalled();
    expect(mockStatus).toBeCalled();
    expect(mockStatus).toBeCalledWith(200);
    expect(mockJson).toBeCalled();
    const result = mockJson.mock.calls[0][0];
    expect(result).toBeDefined();
    expect(result).toHaveProperty('data');
    expect(result.data).toHaveProperty('items');
    expect(Array.isArray(result.data.items)).toEqual(true);
    expect(result.data.items.length).toEqual(0);
    expect(result).toHaveProperty('notifications');
  });

  test('should return alerts with `application` disabled', async () => {
    const endpoint = getAlerts({ logger: mockLogger, alertService: mockAlertService, contentService: mockContentService, launchDarklyService: mockLaunchDarklyService }, { features: { application: false } });
    const mockReq = { query: { platform: 'ios' } };
    mockAlertService.get.mockReturnValue({
      alerts: {
        en: {
          [testId1]: { id: testId1, container: testContainer1, platforms: [ 'ios', 'android' ], content: {}, name: testName1 },
        },
        fr: {
          [testId1]: { id: testId1, container: testContainer1, platforms: [ 'ios', 'android' ], content: {}, name: testName1 },
        },
      },
    });
    await endpoint(mockReq, mockRes, mockNext);
    expect(mockLogger.error).not.toBeCalled();
    expect(mockNext).not.toBeCalled();
    expect(mockStatus).toBeCalled();
    expect(mockStatus).toBeCalledWith(200);
    expect(mockJson).toBeCalled();
    const result = mockJson.mock.calls[0][0];
    expect(result).toBeDefined();
    expect(result).toHaveProperty('data');
    expect(result.data).toHaveProperty('items');
    expect(Array.isArray(result.data.items)).toEqual(true);
    expect(result.data.items.length).toEqual(1);
    expect(result.data.items[0].name).toEqual(testName1);
    expect(result).toHaveProperty('notifications');
  });

  test('should return alerts when passing null values `os_version` and `app_version` query parameters', async () => {
    const endpoint = getAlerts({ logger: mockLogger, alertService: mockAlertService, contentService: mockContentService, launchDarklyService: mockLaunchDarklyService }, config);
    const mockReq = { query: { os_version: '', app_version: '', platform: 'ios' } };
    mockAlertService.get.mockReturnValue({
      alerts: {
        en: {
          [testId1]: { id: testId1, container: testContainer1, platforms: [ 'ios', 'android' ], content: {} },
        },
        fr: {
          [testId1]: { id: testId1, container: testContainer1, platforms: [ 'ios', 'android' ], content: {} },
        },
      },
    });
    await endpoint(mockReq, mockRes, mockNext);
    expect(mockLogger.error).not.toBeCalled();
    expect(mockNext).not.toBeCalled();
    expect(mockStatus).toBeCalledWith(200);
    expect(mockJson).toBeCalled();
    const result = mockJson.mock.calls[0][0];
    expect(result).toBeDefined();
    expect(result).toHaveProperty('data');
    expect(result.data).toHaveProperty('items');
    expect(Array.isArray(result.data.items)).toEqual(true);
    expect(result.data.items.length).toEqual(1);
    expect(result).toHaveProperty('notifications');
  });

  test('should query by page and container', async () => {
    const endpoint = getAlerts({ logger: mockLogger, alertService: mockAlertService, contentService: mockContentService, launchDarklyService: mockLaunchDarklyService }, config);
    const mockReq = { query: { platform: 'ios', container: testContainer2, app_version: 'x', os_version: 'x', page: testPage1, limit: 10 } };
    mockAlertService.get.mockReturnValue({
      alerts: {
        en: {
          [testId1]: { id: testId1, container: testContainer1, pages: [ testPage1 ], platforms: [ 'ios', 'android' ], content: {} },
          'test-id-2': { id: 'test-id-2', container: testContainer2, pages: [ testPage1 ], platforms: [ 'ios', 'android' ], content: {} },
          'test-id-3': { id: 'test-id-3', container: testContainer2, pages: [ 'test-page-2' ], platforms: [ 'ios', 'android' ], content: {} },
        },
        fr: {
          [testId1]: { id: testId1, container: testContainer1, pages: [ testPage1 ], platforms: [ 'ios', 'android' ], content: {} },
          'test-id-2': { id: 'test-id-2', container: testContainer2, pages: [ testPage1 ], platforms: [ 'ios', 'android' ], content: {} },
          'test-id-3': { id: 'test-id-3', container: testContainer2, pages: [ 'test-page-2' ], platforms: [ 'ios', 'android' ], content: {} },
        },
      },
    });
    await endpoint(mockReq, mockRes, mockNext);
    expect(mockLogger.error).not.toBeCalled();
    expect(mockNext).not.toBeCalled();
    expect(mockStatus).toBeCalledWith(200);
    expect(mockJson).toBeCalled();
    const result = mockJson.mock.calls[0][0];
    expect(result).toBeDefined();
    expect(result).toHaveProperty('data');
    expect(result.data).toHaveProperty('items');
    expect(Array.isArray(result.data.items)).toEqual(true);
    expect(result.data.items.length).toEqual(1); // test-id-2
    expect(result).toHaveProperty('notifications');
  });
});
