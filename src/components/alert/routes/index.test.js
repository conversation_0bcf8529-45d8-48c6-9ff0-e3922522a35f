const { Router } = require('express');
const routes = require('./index');

const mockNext = jest.fn();
const mockJson = jest.fn();
const mockStatus = jest.fn();

const mockLogger = {
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};
const mockLaunchDarklyService = {
  init: jest.fn(),
  isFeatureEnabled: jest.fn(),
  close: jest.fn(),
};
const router = routes({
  logger: mockLogger,
  rateLimitingMiddleware: jest.fn(),
  alertsTotalRateLimiter: jest.fn(),
  alertsClientRateLimiter: jest.fn(),
  launchDarklyService: mockLaunchDarklyService,
}, { hostingEnv: 'PCF' });

describe('Alert routes', () => {
  beforeEach(() => {
    mockNext.mockClear();
    mockJson.mockClear();
    mockStatus.mockClear();
  });
  test('should be an express.Router object', () => {
    expect(router.prototype).toEqual(Router.prototype);
    expect(typeof router.get).toEqual('function');
    expect(typeof router.use).toEqual('function');
    expect(typeof router.handle).toEqual('function');
  });
  test('should have GET /alerts', () => {
    const found = router.stack.some((item) => item.route && item.route.path === '/' && item.route.methods.get);
    expect(found).toEqual(true);
  });
});
