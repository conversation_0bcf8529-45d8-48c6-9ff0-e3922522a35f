const PigeonBffAtlasClient = require('./index');

const mockLogger = {
  info: jest.fn(),
};
const mockFetch = jest.fn();
const mockUri = 'https://pigeon-ist.nonp.atlas.bns';

describe('Pigeon API Atlas Client', () => {
  beforeEach(() => {
    mockFetch.mockClear();
    mockLogger.info.mockClear();
  });

  test('should init', () => {
    const client = PigeonBffAtlasClient({ logger: mockLogger, fetch: mockFetch }, mockUri);
    expect(client).toHaveProperty('fetch');
    expect(client.fetch).toBeInstanceOf(Function);
  });

  test('should call pigeon atlas', async () => {
    const mockResponseData = {
      'webfragments': [],
      'rules': [
        {
          'rule_id': '100654',
          'last_updated_time': '2023-02-01T15:49:06.273Z',
          'publishing_state': 'C',
        },
      ],
    };
    const mockResponse = {
      status: 200,
      ok: true,
      text: jest.fn().mockResolvedValueOnce(JSON.stringify(mockResponseData)),
    };
    mockFetch.mockResolvedValueOnce(mockResponse);

    const client = PigeonBffAtlasClient({ logger: mockLogger, fetch: mockFetch }, mockUri);
    const res = await client.fetch('/v1/contents-catalog');
    expect(mockFetch).toHaveBeenCalledWith(`${mockUri}/v1/contents-catalog`, { 'headers': { 'Accept': 'application/json', 'Content-Type': 'application/json; charset=utf-8' }, 'method': 'GET' });
    expect(res).toBeDefined();
    expect(res).toEqual(mockResponse);
  });
});
