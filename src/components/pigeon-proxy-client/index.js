const fetchFromAtlas = async ({ fetch, logger }, basePath, url) => {
  const opts = {
    method: 'GET',
    headers: { 'Content-Type': 'application/json; charset=utf-8', Accept: 'application/json' },
  };

  logger.info({ message: 'Calling pigeon atlas.' });
  return fetch(`${basePath}${url}`, opts);
};

const init = ({ fetch, logger }, basePath) => {
  return {
    fetch: (url) => fetchFromAtlas({ fetch, logger }, basePath, url),
  };
};

module.exports = init;
