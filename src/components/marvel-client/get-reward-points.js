const { getResponse } = require('../common');
const { randomBytes } = require('crypto');
const { pickTruthy } = require('../helpers');
const { handleLDError } = require('../campaign/routes/common');

const ATLAS_REWARDS_FLAG = 'pigeon-api.downstreams.rewards-atlas';

const getRewardPoints = async ({ fetch, logger, launchDarklyService }, config, cardNumber, language, preferredEnv, channelId, traceId, spanId, xOriginatingApplCode) => {
  const headers = {
    'Content-Type': 'application/json',
    Accept: 'application/json',
    'x-language': language,
    'x-customer-scotiacard': cardNumber,
    'x-b3-traceid': traceId || randomBytes(8).toString('hex'),
    'x-b3-spanid': spanId || randomBytes(8).toString('hex'),
    'x-channel-id': channelId || 'Mobile',
    'x-originating-appl-code': xOriginatingApplCode,
    'x-country-code': 'CA',
    'Preferred-Environment': preferredEnv,
  };
  let rewardsUrl = config.rewardsUri;
  try {
    rewardsUrl = await launchDarklyService.isFeatureEnabled(ATLAS_REWARDS_FLAG, false) ? config.rewardsAtlasUri : config.rewardsUri;
  } catch (err) {
    handleLDError({ err, flag: ATLAS_REWARDS_FLAG, caller: 'getRewardPoints method', logger });
  }
  try {
    const result = await fetch(rewardsUrl, {
      method: 'GET',
      headers: pickTruthy(headers),
    });
    const response = await getResponse(result);
    logger.info({ message: 'Get Reward Points - reward points successfully fetched from Marvel', response });
    return response.data.portfolio_balance;
  } catch (err) {
    logger.error({ message: 'Get Reward Points - failed to fetch reward points from Marvel', err });
    return Promise.reject(err);
  }
};

module.exports = getRewardPoints;
