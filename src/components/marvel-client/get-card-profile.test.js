const getCardProfile = require('./get-card-profile');

const mockFetch = jest.fn();
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};
const mockUri = 'https://cdb-int-credential-cardprofile-uat.apps.stg.azr-cc-pcf.cloud.bns/v1/profile';
const cardNumber = '****************';
const preferredEnv = 'uatred';
const channelId = 'Mobile';
const traceId = 'bd7a977555f6b982';
const spanId = '71200de3d3f82a83';

describe('Marvel CArd Profile API', () => {
  beforeEach(() => {
    mockFetch.mockClear();
  });

  it('should throw an error on reponse status >= 400', async () => {
    const mockResponse = {
      status: 400,
      ok: false,
      statusText: 'bad request',
      text: jest.fn().mockResolvedValueOnce(JSON.stringify({
        data: '',
        notifications: [ {
          'category': '400',
          'code': 'E400_MRVL_CARDPROFILE_INCORRECT_PCSVC_KT_SETUP',
          'message': 'There is a downstream error for Marvel Credential Card Profile Service from TDS.',
          'source': 'TDS',
          'metadata': {
            'E_CBTTDSCSS_CUSTOMER_AUTHENTICATION_SERVICE_2012': 'CustomerId/scotiacard not found.',
          },
        } ],
      })),
    };
    mockFetch.mockResolvedValueOnce(mockResponse);
    try {
      const res = await getCardProfile({ fetch: mockFetch, logger: mockLogger }, { uri: mockUri, cardNumber, preferredEnv, channelId, traceId, spanId });
      expect(res).toBeDefined();
    } catch (err) {
      expect(err).toBeDefined();
      expect(err.message).toEqual(mockResponse.statusText);
      expect(err).toHaveProperty('response');
    }
  });

  test('should successfully call', async () => {
    const mockResponse = {
      status: 200,
      ok: true,
      text: jest.fn().mockResolvedValueOnce(JSON.stringify({
        'data': {
          'loginProfile': {
            'loginId': '****************',
            'loginIdType': null,
            'featureFlagUID': '130ac19e-c79a-3f1e-94db-f21934b4dbe0',
            'lastLoginTimestamp': '2022-03-03T21:26:54.475Z',
            'userAgent': null,
            'trueClientIp': null,
            'authenticators': null,
            'twoStepVerificationStatus': 'REGISTERED',
          },
          'cardSysProfile': [
            {
              'sysId': 'CI',
              'infoId': [
                {
                  'type': 'CID',
                  'id': '***************',
                },
              ],
            },
            {
              'sysId': 'BANKING',
              'infoId': [],
            },
            {
              'sysId': 'IP',
              'infoId': [
                {
                  'type': 'CID',
                  'id': '***************',
                },
              ],
            },
            {
              'sysId': 'AC',
              'infoId': [
                {
                  'type': 'CARDTYPE',
                  'id': 'P',
                },
              ],
            },
          ],
          'cardInfo': {
            'expireDate': '2027-08-31T00:00:00Z',
            'cardLevel': '0 ',
            'cardType': 'P',
            'businessCardSubType': '',
            'cardStatus': null,
            'chipInd': true,
            'disabInd': null,
            'cvv2Ind': null,
            'featureInd': null,
            'financialEntlInd': false,
            'categoryCd': '   ',
          },
          'brokerageProfile': {
            'isActiveClient': false,
            'isSetupRequired': false,
            'isOverLimit': false,
            'isChangeRequired': false,
            'customerId': null,
            'status': null,
            'firstName': null,
            'lastName': null,
            'emailAddress': null,
            'dateOfBirth': null,
            'phoneNumber': null,
            'wealthAttributes': null,
            'sysList': null,
            'businessLine': null,
          },
          'customerProfile': {
            'firstName': 'LOAN116',
            'lastName': 'DAILY',
            'fullName': 'MR LOAN116 PMT DAILY',
            'isEmployee': true,
          },
          'cardEntitlements': [
            'UNRESTRICTED_CARD_TYPE',
            'PERSONAL_PROFILE',
            'CHANGE_RECORDKEEPING_BANKING',
            'PENDING_TRANSACTIONS',
            'ALERTS',
            'BILL_PAYMENTS',
            'TRANSFERS',
            'INTERAC_E-TRANSFER',
            'WESTERN_UNION',
            'CHANGE_RECORDKEEPING_BORROWING',
            'CREDIT_STATEMENT',
            'REMOTE_DATA_CAPTURE',
            'SCOTIA_INVESTMENTS',
            'PRODAPP_INV',
            'HISA',
            'TRANSFER_WITHIN_INV',
            'PRODAPP_GIC_TARGETED',
            'SUPP_CARD_SA',
            'SUPP_CARD_PA',
            'SUPP_CARD_NPA',
            'TRANS_UNION_DB',
            'INTERNATIONAL_TRANSFER',
            'MORTGAGE_PREPAYMENT',
            'STOP_PAYMENT',
            'AUTO_PAY',
            'CREDITCARD_LOCK_UNLOCK',
            'RETAIL_COMBO_ELIG',
            'CREDIT_CARD_ACTIVATION',
          ],
        },
        'notifications': null,
      })),
    };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const res = await getCardProfile({ fetch: mockFetch, logger: mockLogger }, { uri: mockUri, cardNumber, preferredEnv, channelId, traceId, spanId });
    expect(mockResponse.text).toBeCalled();
    expect(res).toEqual('REGISTERED');
  });
});
