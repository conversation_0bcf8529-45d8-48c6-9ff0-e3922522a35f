const { getResponse } = require('../common');
const { randomBytes } = require('crypto');

const getProducts = async ({ fetch, logger }, uri) => {
  const headers = {
    'Content-Type': 'application/json',
    Accept: 'application/json',
    'x-b3-traceid': randomBytes(16).toString('hex'),
    'x-b3-spanid': randomBytes(16).toString('hex'),
    'x-channel-id': 'Online',
    'x-originating-appl-code': 'BFB6',
    'x-country-code': 'CA',
  };
  try {
    const result = await fetch(uri, { method: 'GET', headers });
    const response = await getResponse(result);
    logger.info({ message: 'Product book - successfully fetched products from marvel.' });
    if (!response.data && !response.data.products) {
      throw new Error('Product book - response object is invalid');
    }

    return response.data.products;
  } catch (err) {
    logger.error({
      message: 'Product book - failed to fetch products from marvel',
      err: { error: err.message, stack: err.stack },
    });
    return Promise.reject(err);
  }
};

module.exports = getProducts;
