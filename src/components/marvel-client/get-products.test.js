const getProducts = require('./get-products');

const mockFetch = jest.fn();
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};
const mockUri = 'http://localhost/v2/product';

describe('Marvel Product API', () => {
  beforeEach(() => {
    mockFetch.mockClear();
  });

  it('should throw an error on reponse status >= 400', async () => {
    const mockResponse = {
      status: 400,
      ok: false,
      statusText: 'bad request',
      text: jest.fn().mockResolvedValueOnce(JSON.stringify({
        data: '',
        notifications: [ {
          code: 'MRVL_RW_REJECTED_ERR',
          source: 'MRVL_RW',
          message: 'Error connecting to products service.',
          uuid: '1a3f2a5e-5f4a-4696-9485-5f0811f82a7a',
          timestamp: '2021-09-14T09:41:26.831-0400',
          metadata: {
            SOURCE_ERR_CODE_1: 'MRVL_RW_REJECTED',
          },
        } ],
      })),
    };
    mockFetch.mockResolvedValueOnce(mockResponse);
    try {
      const res = await getProducts({ fetch: mockFetch, logger: mockLogger }, mockUri);
      expect(res).toBeDefined();
    } catch (err) {
      expect(err).toBeDefined();
      expect(err.message).toEqual(mockResponse.statusText);
      expect(err).toHaveProperty('response');
    }
  });

  test('should successfully call', async () => {
    const product = { products: [ {
      product_id: '580040',
      product_types: [
        { product_domain: 'default', product_code: 'AFB', sub_product_code: 'B1' },
        { product_domain: 'KT', product_code: 'VAX', sub_product_code: 'B1' },
      ],
      product_system: 'VAX',
      currency: 'CAD',
      ownership: 'B',
      properties: [
        { type: 'CATEGORY', value: 'CREDITCARDS' },
        { type: 'DEFAULT_CLASSIFICATION', value: 'Amex' },
        { type: 'DETAIL_CLASSIFICATION', value: 'Amex' },
        { type: 'REL_ACTIVITY_CLASSIFICATION', value: 'BusinessAmex' },
        { type: 'CORNERSTONE_ID', value: 'Borrowing' },
        { type: 'ASSET_LIABILITY', value: 'L' },
      ],
      sort_weight: '4000',
      account_number_length: 15,
      account_number_mask: '####*****######',
      descriptions: [
        { locale: 'en_CA', value: 'Future Amex SB B1' },
        { locale: 'fr_CA', value: 'F-Future Amex SB B1' },
      ],
    } ] };
    const mockResponse = {
      status: 200,
      ok: true,
      text: jest.fn().mockResolvedValueOnce(JSON.stringify({ data: product })),
    };
    mockFetch.mockResolvedValueOnce(mockResponse);
    const res = await getProducts({ fetch: mockFetch, logger: mockLogger }, mockUri);
    expect(mockResponse.text).toBeCalled();
    expect(res).toEqual(product.products);
  });
});
