const { getResponse } = require('../common');

const fetchAccounts = async ({ fetch, logger }, uri, cardNumber, language, preferredEnv) => {
  if (!cardNumber) {
    logger.info({ message: 'Redis: Card Number does not exist in fetch-accounts.js marvel' });
  }
  const headers = {
    'Content-Type': 'application/json',
    Accept: 'application/json',
    'Preferred-Environment': preferredEnv,
    'x-language': language,
    'x-customer-scotiacard': cardNumber,
    'x-originating-appl-code': 'BFB6', // Pigeon BFF application EPM code
  };
  const result = await fetch(uri, {
    method: 'GET',
    headers,
  });
  return getResponse(result);
};

module.exports = fetchAccounts;
