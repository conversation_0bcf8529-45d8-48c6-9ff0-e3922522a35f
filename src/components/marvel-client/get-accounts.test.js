const getAccounts = require('./get-accounts');

const uri = 'https://marvel-account-cache.cloud.bns';
const cardNumber = '****************';
const language = 'en';
const preferredEnv = 'uatgreen';
const ttl = 600;
const mockSplunkErrorCodes = {
  1002: 'Getting customer info fails',
};
const xOriginatingApplCode = 'BFB6';
const products = {
  accountList: [
    {
      accountUniqueId: 'AAAAAAAAAAAAAAAAAAAA=',
      type: 'Saving',
      ciProductCode: 'AAA',
      ciProductSubCode: 'A1',
      productSystem: 'AAA',
      ownership: 'R',
      dateOpened: '2018-01-08T00:00:00-0500',
      dateClosed: '',
    },
    {
      accountUniqueId: 'AAAAAAAAAAAAAAAAAAAB=',
      type: 'Borrowing',
      ciProductCode: 'BBB',
      ciProductSubCode: 'B1',
      productSystem: 'BBB',
      ownership: 'B',
      dateOpened: '2018-01-09T00:00:00-0500',
      dateClosed: '',
    },
  ],
};
const productsRedis = {
  accountList: [
    {
      i: 'AAAAAAAAAAAAAAAAAAAA=',
      t: 'Saving',
      c: 'AAA',
      s: 'A1',
      p: 'AAA',
      o: 'R',
      do: '2018-01-08T00:00:00-0500',
      dc: '',
    },
    {
      i: 'AAAAAAAAAAAAAAAAAAAB=',
      t: 'Borrowing',
      c: 'BBB',
      s: 'B1',
      p: 'BBB',
      o: 'B',
      do: '2018-01-09T00:00:00-0500',
      dc: '',
    },
  ],
};

const fetch = jest.fn();
const logger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
};
const redisClient = {
  setEx: jest.fn(), // (key, value, ttl) => setEx({ redisInstance, namespace, encryptionKey, useKeyHash }, key, value, ttl),
  get: jest.fn(), // (key) => get({ redisInstance, namespace, encryptionKey, useKeyHash }, key),
  getAndTouch: jest.fn(), // (key, ttl) => getAndTouch({ redisInstance, namespace, encryptionKey, useKeyHash }, key, ttl),
  del: jest.fn(), // (key) => del({ redisInstance, namespace, useKeyHash }, key),
  lock: jest.fn(), // (key, ttl) => lock({ redisInstance, redlockInstance, namespace, useKeyHash }, key, ttl),
  unlock: jest.fn(), // (activeLock) => unlock({ redisInstance, redlockInstance }, activeLock),
};
const circuitBreaker = {
  fire: jest.fn(),
};

describe('Marvel Account Cache', () => {
  beforeEach(() => {
    logger.info.mockClear();
    logger.warn.mockClear();
    logger.error.mockClear();
    redisClient.setEx.mockClear();
    redisClient.get.mockClear();
    redisClient.getAndTouch.mockClear();
    redisClient.del.mockClear();
    redisClient.lock.mockClear();
    redisClient.unlock.mockClear();
    circuitBreaker.fire.mockClear();
    fetch.mockClear();
  });
  // ({ fetch, logger, redisClient }, circuitBreaker, uri, cardNumber, token, language, preferredEnv)
  describe('getAccounts', () => {
    test('should get account details and save them to cache', async () => {
      redisClient.get.mockResolvedValueOnce();
      circuitBreaker.fire.mockResolvedValueOnce(products);
      const result = await getAccounts({ fetch, logger, redisClient, circuitBreaker }, { uri, ttl }, cardNumber, language, preferredEnv, xOriginatingApplCode);
      expect(redisClient.get).toBeCalledTimes(1);
      expect(redisClient.get).toBeCalledWith(cardNumber);
      expect(circuitBreaker.fire).toBeCalledTimes(1);
      expect(circuitBreaker.fire).toBeCalledWith({ fetch, logger }, uri, cardNumber, language, preferredEnv, xOriginatingApplCode);
      expect(redisClient.setEx).toBeCalledTimes(1);
      expect(redisClient.setEx).toBeCalledWith(cardNumber, productsRedis, ttl);
      expect(logger.error).toBeCalledTimes(0);
      expect(result).toEqual(products);
    });
    test.only('should get account details from cache', async () => {
      redisClient.get.mockResolvedValueOnce(productsRedis);
      const result = await getAccounts({ fetch, logger, redisClient, circuitBreaker }, { uri, ttl }, cardNumber, language, preferredEnv, xOriginatingApplCode);
      expect(redisClient.get).toBeCalledTimes(1);
      expect(redisClient.get).toBeCalledWith(cardNumber);
      expect(circuitBreaker.fire).toBeCalledTimes(0);
      expect(redisClient.setEx).toBeCalledTimes(0);
      expect(result).toEqual(products);
    });
    test('should fail call to redis get', async () => {
      const err = new Error('redis get error');
      redisClient.get.mockRejectedValueOnce(err);
      circuitBreaker.fire.mockResolvedValueOnce(products);
      const result = await getAccounts({ fetch, logger, redisClient, circuitBreaker }, { uri, ttl }, cardNumber, language, preferredEnv, xOriginatingApplCode);
      expect(redisClient.get).toBeCalledTimes(1);
      expect(redisClient.get).toBeCalledWith(cardNumber);
      expect(logger.error).toBeCalledTimes(1);
      expect(circuitBreaker.fire).toBeCalledTimes(1);
      expect(circuitBreaker.fire).toBeCalledWith({ fetch, logger }, uri, cardNumber, language, preferredEnv, xOriginatingApplCode);
      expect(redisClient.setEx).toBeCalledTimes(1);
      expect(redisClient.setEx).toBeCalledWith(cardNumber, productsRedis, ttl);
      expect(result).toEqual(products);
    });
    test('should fail call to marvel', async () => {
      const err = new Error('marvel error');
      redisClient.get.mockResolvedValueOnce(undefined);
      circuitBreaker.fire.mockRejectedValueOnce(err);
      const result = getAccounts({ fetch, logger, redisClient, circuitBreaker }, { uri, ttl }, cardNumber, language, preferredEnv, xOriginatingApplCode);
      expect(result).rejects.toEqual(err);
      try {
        await result;
      } catch (e) {}
      expect(redisClient.get).toBeCalledTimes(1);
      expect(redisClient.get).toBeCalledWith(cardNumber);
      expect(circuitBreaker.fire).toBeCalledTimes(1);
      expect(circuitBreaker.fire).toBeCalledWith({ fetch, logger }, uri, cardNumber, language, preferredEnv, xOriginatingApplCode);
      expect(logger.error).toBeCalledTimes(1);
      expect(logger.error).toBeCalledWith({ message: 'Circuit breaker: unable to call Marvel', err, code: 1002, description: mockSplunkErrorCodes[1002] });
      expect(redisClient.setEx).toBeCalledTimes(0);
    });
    test('should fail call to redis setEx', async () => {
      const err = new Error('redis setEx error');
      redisClient.get.mockResolvedValueOnce(undefined);
      circuitBreaker.fire.mockResolvedValueOnce(products);
      redisClient.setEx.mockRejectedValueOnce(err);
      const result = await getAccounts({ fetch, logger, redisClient, circuitBreaker }, { uri, ttl }, cardNumber, language, preferredEnv, xOriginatingApplCode);
      expect(redisClient.get).toBeCalledTimes(1);
      expect(redisClient.get).toBeCalledWith(cardNumber);
      expect(circuitBreaker.fire).toBeCalledTimes(1);
      expect(circuitBreaker.fire).toBeCalledWith({ fetch, logger }, uri, cardNumber, language, preferredEnv, xOriginatingApplCode);
      expect(redisClient.setEx).toBeCalledTimes(1);
      expect(redisClient.setEx).toBeCalledWith(cardNumber, productsRedis, ttl);
      expect(logger.error).toBeCalledTimes(1);
      expect(result).toEqual(products);
    });
  });
});
