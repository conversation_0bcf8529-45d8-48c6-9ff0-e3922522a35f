const { getResponse } = require('../common');
const { pickTruthy } = require('../helpers');

/**
 * @param {object} common_functions Fecthing and logging functions.
 * @param {function} common_functions.fetch fetch function to get the card profile.
 * @param {function} common_functions.logger logging function to log info / errors.
 * @param {object} config configration to get the card profile.
 * @param {string} config.uri Marvel url.
 * @param {string} config.cardNumber user card number.
 * @param {string} config.preferredEnv Preferred Environment.
 * @param {string} config.channelId Channel information such as Mobile, SOL.
 * @param {string} config.traceId Correlation ID, unique identifier value that allow reference to event chain across multiple integration tiers..
 * @param {string} config.xOriginatingApplCode Source application EPM code which is making this request
 * @returns {('NEW'|null|'ENROLL_REQUIRED'|'REGISTERED')} enrolment status - The allowed enrolment status
 */

const getCardProfile = async (
  { fetch, logger },
  { uri, cardNumber, preferredEnv, channelId, traceId, xOriginatingApplCode }) => {
  const headers = pickTruthy({
    Accept: 'application/json',
    'Preferred-Environment': preferredEnv,
    'x-b3-traceid': traceId,
    'x-channel-id': channelId,
    'x-originating-appl-code': xOriginatingApplCode,
    'login-id': cardNumber,
    'login-id-type': 'SCN',
    'Content-Type': 'application/json',
  });

  try {
    const result = await fetch(uri, {
      method: 'GET',
      headers: pickTruthy(headers),
    });
    const { data } = await getResponse(result);
    logger.info({
      message:
        'Get Card Profile - card profile successfully fetched from Marvel',
      data,
    });
    return data.loginProfile.twoStepVerificationStatus;
  } catch (err) {
    logger.error({
      message: `Get Card Profile - failed to fetch card profile from Marvel`,
      err,
    });
    return Promise.reject(err);
  }
};

module.exports = getCardProfile;
