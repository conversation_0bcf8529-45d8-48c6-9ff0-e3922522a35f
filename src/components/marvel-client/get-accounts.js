const R = require('ramda');
const { handleLDError } = require('../campaign/routes/common');
const splunkErrorCodes = require('../campaign/routes/common').splunkErrorCodes;

const compactAccountDetail = (o) => ({
  i: o.accountUniqueId,
  t: o.type,
  c: o.ciProductCode,
  s: o.ciProductSubCode,
  p: o.productSystem,
  o: o.ownership,
  do: o.dateOpened,
  dc: o.dateClosed,
});

const expandAccountDetail = (o) => ({
  accountUniqueId: o.i,
  type: o.t,
  ciProductCode: o.c,
  ciProductSubCode: o.s,
  productSystem: o.p,
  ownership: o.o,
  dateOpened: o.do,
  dateClosed: o.dc,
});

const compactAccountDetails = R.map(compactAccountDetail);

const expandAccountDetails = R.map(expandAccountDetail);

const compact = R.evolve({ accountList: compactAccountDetails });

const expand = R.evolve({ accountList: expandAccountDetails });

const ATLAS_ACCOUNTS_FLAG = 'pigeon-api.downstreams.accounts-atlas';

const getAccounts = async ({ fetch, logger, redisClient, circuitBreaker, launchDarklyService }, { config }, cardNumber, language, preferredEnv, xOriginatingApplCode) => {
  // try to get from cache
  try {
    const cacheResp = await redisClient.get(cardNumber);
    if (cacheResp) {
      logger.info({ message: 'Redis: Trying to get from the cache' });
      return expand(cacheResp);
    }
  } catch (err) {
    logger.error({ message: 'Redis: Unable to get cached response for card number', error: { message: err.message, stack: err.stack } });
  }

  let products;
  let accountsUrl = config.uri;
  try {
    accountsUrl = await launchDarklyService.isFeatureEnabled(ATLAS_ACCOUNTS_FLAG, false) ? config.AtlasUri : config.uri;
  } catch (err) {
    handleLDError({ err, flag: ATLAS_ACCOUNTS_FLAG, caller: 'getAccounts method', logger });
  }
  try {
    logger.info({ message: 'Redis: Circuit breaker fire ' });
    products = await circuitBreaker.fire({ fetch, logger }, accountsUrl, cardNumber, language, preferredEnv, xOriginatingApplCode);
    logger.info({ message: 'Circuit breaker: Products were fetched' });
  } catch (err) {
    logger.error({ message: 'Circuit breaker: unable to call Marvel', err, code: 1002, description: splunkErrorCodes[1002] });
    return Promise.reject(err);
  }

  if (products) {
    // try to write to cache
    try {
      if (products.accountList && products.accountList.length > 0) {
        await redisClient.setEx(cardNumber, compact(products), config.ttl);
        logger.info({ message: 'Redis:  Cache is populated' });
      }
    } catch (err) {
      logger.error({ message: 'Redis: Unable to save marvel response to Redis for card number', error: { message: err.message, stack: err.stack } });
    }
  }

  return products;
};

module.exports = getAccounts;
