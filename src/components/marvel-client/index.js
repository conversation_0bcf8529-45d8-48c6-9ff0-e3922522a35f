const fetchAccounts = require('./fetch-accounts');
const getAccounts = require('./get-accounts');
const getRewardPoints = require('./get-reward-points');
const getCardProfile = require('./get-card-profile');
const getProducts = require('./get-products');
const CircuitBreaker = require('opossum');

const init = ({ logger, fetch, redisClient, launchDarklyService }, config) => {
  const circuitBreaker = new CircuitBreaker(fetchAccounts, {
    timeout: config.circuitBreaker.timeout,
    errorThresholdPercentage: config.circuitBreaker.errorThreshold,
    resetTimeout: config.circuitBreaker.breakerResetTimeout,
  });

  circuitBreaker.on('open', () => {
    logger.info({ message: '************************CircuitBreaker open' });
  });
  circuitBreaker.on('halfOpen', () => {
    logger.info({ message: '************************CircuitBreaker is half open' });
  });
  circuitBreaker.on('close', () => {
    logger.info({ message: '************************CircuitBreak is closed' });
  });
  circuitBreaker.on('rejected', () => {
    logger.info({ message: '************************CircuitBreaker rejection detected. Failing fast' });
  });
  circuitBreaker.on('semaphoreLocked', () => {
    logger.info({ message: '************************Circuit Break at capacity, cannot execute the request' });
  });

  return {
    getAccounts: ({ cardNumber, language, xOriginatingApplCode, preferredEnv }) =>
      getAccounts({ fetch, logger, redisClient, circuitBreaker, launchDarklyService }, { config }, cardNumber, language, preferredEnv, xOriginatingApplCode),
    getRewardPoints: (cardNumber, language, preferredEnv, channelId, traceId, spanId, xOriginatingApplCode) =>
      getRewardPoints({ fetch, logger, launchDarklyService }, config, cardNumber, language, preferredEnv, channelId, traceId, spanId, xOriginatingApplCode),
    getCardProfile: (args) => getCardProfile({ fetch, logger }, { ...args, uri: config.cardProfileUri }),
    getProducts: () => getProducts({ fetch, logger }, config.productBook.productsUri),
  };
};

module.exports = init;
