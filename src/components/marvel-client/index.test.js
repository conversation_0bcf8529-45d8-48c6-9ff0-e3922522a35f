const init = require('./index');

const basePath = 'https://marvel-account-cache.cloud.bns';
const cardNumber = '****************';
const language = 'en';
const preferredEnv = 'uatgreen';
const products = [
  { accountUniqueId: 'AAAAAAAAAAAAAAAAAAAA=', ownership: 'R', code: 'AAA', sub_code: 'A1' },
  { accountUniqueId: 'AAAAAAAAAAAAAAAAAAAB=', ownership: 'R', code: 'BBB', sub_code: 'B1' },
];
const { launchDarklyService } = require('../../../acceptance-tests/mock');

const fetch = jest.fn().mockResolvedValue({
  ok: true,
  text: jest.fn().mockResolvedValue(JSON.stringify(products)),
});
const logger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};
const redisClient = {
  setEx: jest.fn(), // (key, value, ttl) => setEx({ redisInstance, namespace, encryptionKey, useKeyHash }, key, value, ttl),
  get: jest.fn(), // (key) => get({ redisInstance, namespace, encryptionKey, useKeyHash }, key),
  getAndTouch: jest.fn(), // (key, ttl) => getAndTouch({ redisInstance, namespace, encryptionKey, useKeyHash }, key, ttl),
  del: jest.fn(), // (key) => del({ redisInstance, namespace, useKeyHash }, key),
  lock: jest.fn(), // (key, ttl) => lock({ redisInstance, redlockInstance, namespace, useKeyHash }, key, ttl),
  unlock: jest.fn(), // (activeLock) => unlock({ redisInstance, redlockInstance }, activeLock),
};

const config = {
  uri: basePath,
  circuitBreaker: {
    timeout: 100,
    errorThreshold: 1,
    breakerResetTimeout: 100,
  },
  launchDarklyService,
};

describe('Marvel Account Cache', () => {
  beforeEach(() => {
    logger.info.mockClear();
    logger.warn.mockClear();
    logger.error.mockClear();
    redisClient.setEx.mockClear();
    redisClient.get.mockClear();
    redisClient.getAndTouch.mockClear();
    redisClient.del.mockClear();
    redisClient.lock.mockClear();
    redisClient.unlock.mockClear();
    fetch.mockClear();
  });
  describe('init', () => {
    test('should create a client', () => {
      const client = init({ logger, fetch, redisClient }, config);
      expect(client).toBeDefined();
    });
    test('should get products', async () => {
      const client = init({ logger, fetch, redisClient, launchDarklyService: await launchDarklyService() }, config);
      expect(client).toBeDefined();
      redisClient.get.mockResolvedValueOnce(undefined);
      const result = await client.getAccounts(cardNumber, language, preferredEnv);
      expect(result).toEqual(products);
    });
  });
});
