const getRewardPoints = require('./get-reward-points');

jest.mock('../common', () => ({
  getResponse: jest.fn(),
}));

jest.mock('../helpers', () => ({
  pickTruthy: jest.fn(),
}));

jest.mock('../campaign/routes/common', () => ({
  handleLDError: jest.fn(),
}));

jest.mock('crypto', () => ({
  randomBytes: jest.fn(),
}));

const { getResponse } = require('../common');
const { pickTruthy } = require('../helpers');
const { handleLDError } = require('../campaign/routes/common');
const { randomBytes } = require('crypto');

const mockFetch = jest.fn();
const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
};

const mockLaunchDarklyService = {
  isFeatureEnabled: jest.fn(),
};

const config = {
  rewardsUri: 'https://cdb-int-rewards-ist.apps.stg.azr-cc-pcf.cloud.bns/v2/rewards',
  rewardsAtlasUri: 'https://cdb-int-rewards-ist.nonp.atlas.bns/v2/rewards',
};

const cardNumber = '****************';
const language = 'en';
const preferredEnv = 'istred';
const channelId = 'Mobile';
const traceId = 'bd7a977555f6b982';
const spanId = '71200de3d3f82a83';
const xOriginatingApplCode = 'TEST_APP';

const badRequestError = 'bad request';

describe('Marvel Rewards API', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Setup default mocks
    pickTruthy.mockImplementation(obj => obj);
    randomBytes.mockReturnValue(Buffer.from('abcd1234', 'hex'));
  });

  // Existing tests...
  it('should throw an error on reponse status >= 400', async () => {
    const mockResponse = {
      status: 400,
      ok: false,
      statusText: badRequestError,
      text: jest.fn().mockResolvedValueOnce(JSON.stringify({
        data: '',
        notifications: [ {
          code: 'MRVL_RW_REJECTED_ERR',
          source: 'MRVL_RW',
          message: 'Error connecting to Account Cache service.',
          uuid: '1a3f2a5e-5f4a-4696-9485-5f0811f82a7a',
          timestamp: '2021-09-14T09:41:26.831-0400',
          metadata: {
            SOURCE_ERR_CODE_1: 'MRVL_RW_ACCTCH_REJECTED',
          },
        } ],
      })),
    };

    const mockError = new Error(badRequestError);
    mockError.response = mockResponse;

    mockFetch.mockResolvedValueOnce(mockResponse);
    getResponse.mockRejectedValueOnce(mockError);
    mockLaunchDarklyService.isFeatureEnabled.mockResolvedValue(false);

    try {
      const res = await getRewardPoints(
        { fetch: mockFetch, logger: mockLogger, launchDarklyService: mockLaunchDarklyService },
        config,
        cardNumber,
        language,
        preferredEnv,
        channelId,
        traceId,
        spanId,
        xOriginatingApplCode,
      );
      expect(res).toBeDefined();
    } catch (err) {
      expect(err).toBeDefined();
      expect(err.message).toEqual(badRequestError);
      expect(err).toHaveProperty('response');
    }
  });

  test('should successfully call', async () => {
    const mockResponseData = {
      data: {
        reward_type: 'ScenePlus',
        cid: '021256084701990',
        program_name: 'SCENE',
        scene_number: '6046469690672647',
        portfolio_balance: 600,
      },
    };

    mockFetch.mockResolvedValueOnce({ status: 200 });
    getResponse.mockResolvedValueOnce(mockResponseData);
    mockLaunchDarklyService.isFeatureEnabled.mockResolvedValue(false);

    const res = await getRewardPoints(
      { fetch: mockFetch, logger: mockLogger, launchDarklyService: mockLaunchDarklyService },
      config,
      cardNumber,
      language,
      preferredEnv,
      channelId,
      traceId,
      spanId,
      xOriginatingApplCode,
    );

    expect(res).toEqual(600);
    expect(mockLogger.info).toHaveBeenCalledWith({
      message: 'Get Reward Points - reward points successfully fetched from Marvel',
      response: mockResponseData,
    });
  });

  // NEW TEST CASES FOR 100% BRANCH COVERAGE

  describe('LaunchDarkly Feature Flag Branch Coverage', () => {
    test('should use Atlas URI when feature flag is enabled', async () => {
      const mockResponseData = {
        data: {
          portfolio_balance: 500,
        },
      };

      mockFetch.mockResolvedValueOnce({ status: 200 });
      getResponse.mockResolvedValueOnce(mockResponseData);
      mockLaunchDarklyService.isFeatureEnabled.mockResolvedValue(true);

      await getRewardPoints(
        { fetch: mockFetch, logger: mockLogger, launchDarklyService: mockLaunchDarklyService },
        config,
        cardNumber,
        language,
        preferredEnv,
        channelId,
        traceId,
        spanId,
        xOriginatingApplCode,
      );

      expect(mockLaunchDarklyService.isFeatureEnabled).toHaveBeenCalledWith('pigeon-api.downstreams.rewards-atlas', false);
      expect(mockFetch).toHaveBeenCalledWith(
        config.rewardsAtlasUri,
        expect.objectContaining({
          method: 'GET',
        }),
      );
    });

    test('should use PCF URI when feature flag is disabled', async () => {
      const mockResponseData = {
        data: {
          portfolio_balance: 500,
        },
      };

      mockFetch.mockResolvedValueOnce({ status: 200 });
      getResponse.mockResolvedValueOnce(mockResponseData);
      mockLaunchDarklyService.isFeatureEnabled.mockResolvedValue(false);

      await getRewardPoints(
        { fetch: mockFetch, logger: mockLogger, launchDarklyService: mockLaunchDarklyService },
        config,
        cardNumber,
        language,
        preferredEnv,
        channelId,
        traceId,
        spanId,
        xOriginatingApplCode,
      );

      expect(mockFetch).toHaveBeenCalledWith(
        config.rewardsUri,
        expect.objectContaining({
          method: 'GET',
        }),
      );
    });

    test('should handle LaunchDarkly service error and use default PCF URI', async () => {
      const ldError = new Error('LaunchDarkly service error');
      const mockResponseData = {
        data: {
          portfolio_balance: 500,
        },
      };

      mockFetch.mockResolvedValueOnce({ status: 200 });
      getResponse.mockResolvedValueOnce(mockResponseData);
      mockLaunchDarklyService.isFeatureEnabled.mockRejectedValue(ldError);

      await getRewardPoints(
        { fetch: mockFetch, logger: mockLogger, launchDarklyService: mockLaunchDarklyService },
        config,
        cardNumber,
        language,
        preferredEnv,
        channelId,
        traceId,
        spanId,
        xOriginatingApplCode,
      );

      expect(handleLDError).toHaveBeenCalledWith({
        err: ldError,
        flag: 'pigeon-api.downstreams.rewards-atlas',
        caller: 'getRewardPoints method',
        logger: mockLogger,
      });

      // Should fall back to PCF URI
      expect(mockFetch).toHaveBeenCalledWith(
        config.rewardsUri,
        expect.objectContaining({
          method: 'GET',
        }),
      );
    });
  });

  describe('Header Generation', () => {
    test('should generate random traceId and spanId when not provided', async () => {
      const mockResponseData = {
        data: {
          portfolio_balance: 500,
        },
      };

      randomBytes.mockReturnValueOnce(Buffer.from('trace123', 'hex'));
      randomBytes.mockReturnValueOnce(Buffer.from('span456', 'hex'));

      mockFetch.mockResolvedValueOnce({ status: 200 });
      getResponse.mockResolvedValueOnce(mockResponseData);
      mockLaunchDarklyService.isFeatureEnabled.mockResolvedValue(false);

      // Call without traceId and spanId
      await getRewardPoints(
        { fetch: mockFetch, logger: mockLogger, launchDarklyService: mockLaunchDarklyService },
        config,
        cardNumber,
        language,
        preferredEnv,
        channelId,
        null, // traceId
        null, // spanId
        xOriginatingApplCode,
      );

      expect(randomBytes).toHaveBeenCalledWith(8);
      expect(randomBytes).toHaveBeenCalledTimes(2);
      expect(pickTruthy).toHaveBeenCalledWith(
        expect.objectContaining({
          'x-b3-traceid': Buffer.from('trace123', 'hex').toString('hex'),
          'x-b3-spanid': Buffer.from('span456', 'hex').toString('hex'),
        }),
      );
    });

    test('should use provided traceId and spanId when available', async () => {
      const mockResponseData = {
        data: {
          portfolio_balance: 500,
        },
      };

      mockFetch.mockResolvedValueOnce({ status: 200 });
      getResponse.mockResolvedValueOnce(mockResponseData);
      mockLaunchDarklyService.isFeatureEnabled.mockResolvedValue(false);

      await getRewardPoints(
        { fetch: mockFetch, logger: mockLogger, launchDarklyService: mockLaunchDarklyService },
        config,
        cardNumber,
        language,
        preferredEnv,
        channelId,
        traceId,
        spanId,
        xOriginatingApplCode,
      );

      // randomBytes should not be called when traceId and spanId are provided
      expect(randomBytes).not.toHaveBeenCalled();
      expect(pickTruthy).toHaveBeenCalledWith(
        expect.objectContaining({
          'x-b3-traceid': traceId,
          'x-b3-spanid': spanId,
        }),
      );
    });

    test('should use default channelId when not provided', async () => {
      const mockResponseData = {
        data: {
          portfolio_balance: 500,
        },
      };

      mockFetch.mockResolvedValueOnce({ status: 200 });
      getResponse.mockResolvedValueOnce(mockResponseData);
      mockLaunchDarklyService.isFeatureEnabled.mockResolvedValue(false);

      await getRewardPoints(
        { fetch: mockFetch, logger: mockLogger, launchDarklyService: mockLaunchDarklyService },
        config,
        cardNumber,
        language,
        preferredEnv,
        null, // channelId
        traceId,
        spanId,
        xOriginatingApplCode,
      );

      expect(pickTruthy).toHaveBeenCalledWith(
        expect.objectContaining({
          'x-channel-id': 'Mobile',
        }),
      );
    });

    test('should handle undefined xOriginatingApplCode', async () => {
      const mockResponseData = {
        data: {
          portfolio_balance: 500,
        },
      };

      mockFetch.mockResolvedValueOnce({ status: 200 });
      getResponse.mockResolvedValueOnce(mockResponseData);
      mockLaunchDarklyService.isFeatureEnabled.mockResolvedValue(false);

      await getRewardPoints(
        { fetch: mockFetch, logger: mockLogger, launchDarklyService: mockLaunchDarklyService },
        config,
        cardNumber,
        language,
        preferredEnv,
        channelId,
        traceId,
        spanId,
        undefined, // xOriginatingApplCode
      );

      expect(pickTruthy).toHaveBeenCalledWith(
        expect.objectContaining({
          'x-originating-appl-code': undefined,
        }),
      );
    });
  });

  describe('Error Handling Branch Coverage', () => {
    test('should handle fetch error and log error', async () => {
      const fetchError = new Error('Network error');

      mockFetch.mockRejectedValue(fetchError);
      mockLaunchDarklyService.isFeatureEnabled.mockResolvedValue(false);

      await expect(
        getRewardPoints(
          { fetch: mockFetch, logger: mockLogger, launchDarklyService: mockLaunchDarklyService },
          config,
          cardNumber,
          language,
          preferredEnv,
          channelId,
          traceId,
          spanId,
          xOriginatingApplCode,
        ),
      ).rejects.toThrow('Network error');

      expect(mockLogger.error).toHaveBeenCalledWith({
        message: 'Get Reward Points - failed to fetch reward points from Marvel',
        err: fetchError,
      });
    });

    test('should handle getResponse error and log error', async () => {
      const responseError = new Error('Response parsing error');

      mockFetch.mockResolvedValueOnce({ status: 200 });
      getResponse.mockRejectedValueOnce(responseError);
      mockLaunchDarklyService.isFeatureEnabled.mockResolvedValue(false);

      await expect(
        getRewardPoints(
          { fetch: mockFetch, logger: mockLogger, launchDarklyService: mockLaunchDarklyService },
          config,
          cardNumber,
          language,
          preferredEnv,
          channelId,
          traceId,
          spanId,
          xOriginatingApplCode,
        ),
      ).rejects.toThrow('Response parsing error');

      expect(mockLogger.error).toHaveBeenCalledWith({
        message: 'Get Reward Points - failed to fetch reward points from Marvel',
        err: responseError,
      });
    });
  });

  describe('Response Data Handling', () => {
    test('should return portfolio_balance from response data', async () => {
      const mockResponseData = {
        data: {
          reward_type: 'ScenePlus',
          portfolio_balance: 1500,
        },
      };

      mockFetch.mockResolvedValueOnce({ status: 200 });
      getResponse.mockResolvedValueOnce(mockResponseData);
      mockLaunchDarklyService.isFeatureEnabled.mockResolvedValue(false);

      const result = await getRewardPoints(
        { fetch: mockFetch, logger: mockLogger, launchDarklyService: mockLaunchDarklyService },
        config,
        cardNumber,
        language,
        preferredEnv,
        channelId,
        traceId,
        spanId,
        xOriginatingApplCode,
      );

      expect(result).toBe(1500);
      expect(mockLogger.info).toHaveBeenCalledWith({
        message: 'Get Reward Points - reward points successfully fetched from Marvel',
        response: mockResponseData,
      });
    });

    test('should handle missing portfolio_balance in response', async () => {
      const mockResponseData = {
        data: {
          reward_type: 'ScenePlus',
          // portfolio_balance missing
        },
      };

      mockFetch.mockResolvedValueOnce({ status: 200 });
      getResponse.mockResolvedValueOnce(mockResponseData);
      mockLaunchDarklyService.isFeatureEnabled.mockResolvedValue(false);

      const result = await getRewardPoints(
        { fetch: mockFetch, logger: mockLogger, launchDarklyService: mockLaunchDarklyService },
        config,
        cardNumber,
        language,
        preferredEnv,
        channelId,
        traceId,
        spanId,
        xOriginatingApplCode,
      );

      expect(result).toBeUndefined();
    });
  });

  describe('pickTruthy Integration', () => {
    test('should call pickTruthy with headers object', async () => {
      const mockResponseData = {
        data: {
          portfolio_balance: 500,
        },
      };

      mockFetch.mockResolvedValueOnce({ status: 200 });
      getResponse.mockResolvedValueOnce(mockResponseData);
      mockLaunchDarklyService.isFeatureEnabled.mockResolvedValue(false);

      await getRewardPoints(
        { fetch: mockFetch, logger: mockLogger, launchDarklyService: mockLaunchDarklyService },
        config,
        cardNumber,
        language,
        preferredEnv,
        channelId,
        traceId,
        spanId,
        xOriginatingApplCode,
      );

      expect(pickTruthy).toHaveBeenCalledWith({
        'Content-Type': 'application/json',
        Accept: 'application/json',
        'x-language': language,
        'x-customer-scotiacard': cardNumber,
        'x-b3-traceid': traceId,
        'x-b3-spanid': spanId,
        'x-channel-id': channelId,
        'x-originating-appl-code': xOriginatingApplCode,
        'x-country-code': 'CA',
        'Preferred-Environment': preferredEnv,
      });
    });
  });
});
