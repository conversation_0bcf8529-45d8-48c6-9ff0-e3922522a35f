const Config = require('./index');

const MOCK_SPACE = 'DEV';
const MOCK_NODE_ENV = 'development';
const MOCK_SERVER_PORT = 8000;
const MOCK_GRACEFUL_TIMOUT = 3000;
const MOCK_TTL = 30;
const MOCK_PASSPORT_PRIVATE_KEY = 'dGVzdA==';
const MOCK_LD_KEY = 'dGVzdC1zdHJpbmc=';
const MOCK_PERPPER = 'somesecret';
const MOCK_REDIS_AUTH_STRING = '285839g4-ab09-4c82-a29v-b824c7cdb109';
const MOCK_REDIS_HOST = '*************';
const MOCK_REDIS_LOCAL_URL = 'redis://localhost:6379';
const MOCK_REDIS_B64_ENCRYPT_KEY = 'MTIzNDU2Nzg5MDEyMzQ1Njc4OTAxMjM0NTY3ODkwMTI=';
const MOCK_REDIS_ENCRYPT_KEY = '12345678901234567890123456789012';

describe('Config', () => {
  beforeAll(() => {
    process.env.CDP_SECRET_PASSPORT_PRIVATE_KEY = MOCK_PASSPORT_PRIVATE_KEY;
    process.env.CDP_SECRET_LAUNCH_DARKLY_SDK_KEY = MOCK_LD_KEY;
    process.env.CDP_SECRET_CARD_PEPPER = MOCK_PERPPER;
    process.env.VCAP_APPLICATION = `{"space_name":"${MOCK_SPACE}"}`;
    process.env.NODE_ENV = MOCK_NODE_ENV;
    process.env.SERVER_PORT = MOCK_SERVER_PORT;
    process.env.SERVER_GRACEFUL_TIMEOUT = MOCK_GRACEFUL_TIMOUT;
    process.env.PASSPORT_API_S2S_TTL = MOCK_TTL;
  });

  test('should have basic config properties', () => {
    const config = Config();
    expect(config).toHaveProperty('env');
    expect(config).toHaveProperty('http');
    expect(config.http).toHaveProperty('port');
    expect(config).toHaveProperty('campaignAPI');
    expect(config.campaignAPI).toHaveProperty('uri');
    expect(config).toHaveProperty('alertAPI');
    expect(config.alertAPI).toHaveProperty('uri');
    expect(config).toHaveProperty('contentAPI');
    expect(config.contentAPI).toHaveProperty('uri');
    expect(config).toHaveProperty('serviceAuth');
    expect(config.targetedCampaignAPI).toHaveProperty('insightBasePath');
    expect(config.targetedCampaignAPI).toHaveProperty('insightsMockUri');
    expect(config.targetedCampaignAPI).toHaveProperty('timeout');
    expect(config.space).toEqual(MOCK_SPACE);
    expect(config.env).toEqual(MOCK_NODE_ENV);
    expect(config.http.port).toEqual(MOCK_SERVER_PORT);
    expect(config.http.gracefulTimeout).toEqual(MOCK_GRACEFUL_TIMOUT);
    expect(config.serviceAuth.privateKey).toEqual('test');
    expect(config.launchDarkly.secret).toEqual('test-string');
  });

  test('test configuration - default timeout time', () => {
    process.env.CAMPAIGN_CACHE_TTL = '5000';
    delete process.env.ALERT_CACHE_TTL;
    delete process.env.CAMPAIGN_API_TIMEOUT;
    delete process.env.ALERT_API_TIMEOUT;
    delete process.env.CONTENT_API_TIMEOUT;
    delete process.env.INVESTMENT_API_TIMEOUT;
    const config = Config();
    expect(config.campaignAPI.cacheTTL).toEqual('5000');
    expect(config.campaignAPI.timeout).toEqual(1000);
    expect(config.alertAPI.timeout).toEqual(1000);
    expect(config.alertAPI.cacheTTL).toEqual(5000);
    expect(config.contentAPI.timeout).toEqual(1000);
    expect(config.investmentAPI.timeout).toEqual(3000);
  });

  describe('Redis connection', () => {
    test('GCP Atlas redis configuration', () => {
      process.env.SECRET_REDIS_AUTH_STRING = MOCK_REDIS_AUTH_STRING;
      process.env.REDIS_HOST = MOCK_REDIS_HOST;
      process.env.CDP_SECRET_REDIS_CACHE = MOCK_REDIS_B64_ENCRYPT_KEY;
      const config = Config();
      expect(config.redisConfig).toEqual({
        connection: {
          port: 6378,
          host: MOCK_REDIS_HOST,
          password: MOCK_REDIS_AUTH_STRING,
          tls: {
            servername: MOCK_REDIS_HOST,
          },
        },
        encryptionKey: MOCK_REDIS_ENCRYPT_KEY,
        namespace: undefined,
        useKeyHash: true,
      });
    });

    test('Local redis configuration - Redis URL defined in local env variables', () => {
      delete process.env.SECRET_REDIS_AUTH_STRING;
      process.env.REDIS_URL = MOCK_REDIS_LOCAL_URL;
      const config = Config();
      expect(config.redisConfig).toEqual({
        connection: {
          url: MOCK_REDIS_LOCAL_URL,
        },
        encryptionKey: MOCK_REDIS_ENCRYPT_KEY,
        namespace: undefined,
        useKeyHash: true,
      });
    });

    test('Local redis configuration - Default redis URL if undefined', () => {
      delete process.env.REDIS_URL;
      const config = Config();
      expect(config.redisConfig).toEqual({
        connection: {
          url: MOCK_REDIS_LOCAL_URL,
        },
        encryptionKey: MOCK_REDIS_ENCRYPT_KEY,
        namespace: undefined,
        useKeyHash: true,
      });
      delete process.env.CDP_SECRET_REDIS_CACHE;
    });
  });

  describe('SQL database connection', () => {
    test('test mssql configuration ', () => {
      process.env.VCAP_SERVICES = '{"azure-sqldb-failover-group":[{"name":"pigeon-ist-failover-ist","credentials":{"hostname":"hostname", "port":"4333"}}]}';
      const config = Config();
      expect(config.mssql.port).toEqual(4333);
    });
  });

  describe('Logging', () => {
    it('Should return valid input when valid IGNORED_ROUTES_FOR_LOGGING is passed', () => {
      process.env.IGNORED_ROUTES_FOR_LOGGING = '["/health"]';
      const config = Config();
      expect(config.logging.ignoredLogRoutes).toEqual([ '/health' ]);
    });

    it('Should return [] when invalid IGNORED_ROUTES_FOR_LOGGING is passed', () => {
      process.env.IGNORED_ROUTES_FOR_LOGGING = 'someincorrectthing';
      const config = Config();
      expect(config.logging.ignoredLogRoutes).toEqual([]);
    });
  });

  describe('Invalid configurations', () => {
    test('should fail if Passport private key & S2S private key are missing', () => {
      delete process.env.CDP_SECRET_PASSPORT_PRIVATE_KEY;
      delete process.env.CDP_SECRET_S2S_PRIVATE_KEY;
      try {
        const config = Config();
        expect(config).toBeUndefined();
      } catch (err) {
        expect(err).toBeInstanceOf(Error);
      }
    });

    test('should fail if secret card pepper is missing', () => {
      delete process.env.CDP_SECRET_CARD_PEPPER;
      try {
        const config = Config();
        expect(config).toBeUndefined();
      } catch (err) {
        expect(err).toBeInstanceOf(Error);
      }
    });
  });
});
