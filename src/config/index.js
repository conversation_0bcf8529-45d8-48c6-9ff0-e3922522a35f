const R = require('ramda');
require('dotenv').config();

const jsonParse = (x) => JSON.parse(x);
const getEnvString = R.curry((env, defaults) => R.pathOr(defaults, [ env ], process.env));
const getEnvNumber = R.pipe(getEnvString, Number);
const getEnvBool = R.pipe(getEnvNumber, x => !!x);
const getEnvObject = R.pipe(getEnvString, R.ifElse(R.is(String), jsonParse, R.identity));
const fromBase64 = (s) => s ? Buffer.from(s, 'base64').toString() : s;
const getEnvJson = (env, defaults) => {
  try {
    return JSON.parse(env);
  } catch (err) {
    return defaults;
  }
};

const getSpace = (defaultSpace) => {
  try {
    const vcapa = process.env.VCAP_APPLICATION;
    const { space_name: space } = JSON.parse(vcapa);
    return space;
  } catch (err) {
    return defaultSpace;
  }
};

const getRedisConnectionConfig = () => {
  const redisConnectionConfig = {};
  if (process.env.SECRET_REDIS_AUTH_STRING) {
    redisConnectionConfig.port = 6378;
    redisConnectionConfig.host = process.env.REDIS_HOST;
    redisConnectionConfig.password = process.env.SECRET_REDIS_AUTH_STRING;
    redisConnectionConfig.tls = {
      servername: process.env.REDIS_HOST,
    };
  } else if (process.env.REDIS_URL) {
    redisConnectionConfig.url = process.env.REDIS_URL;
  } else {
    redisConnectionConfig.url = 'redis://localhost:6379';
  }
  return redisConnectionConfig;
};

const getSqlVcapsCredentials = (dbname) => R.pipe(
  R.path([ 'azure-sqldb-failover-group' ]),
  R.find(R.propEq(dbname, 'name')),
  R.path([ 'credentials' ]),
  (o) => ({
    server: o.hostname,
    port: Number(o.port),
    user: o.username,
    password: o.password,
    database: o.name,
    options: {
      encrypt: true,
      trustServerCertificate: true,
    },
  }));

const getSqlLocalCredentials = () => ({
  server: getEnvString('MSSQL_HOST', 'localhost'),
  port: getEnvNumber('MSSQL_PORT', 1433),
  user: getEnvString('MSSQL_USER', 'sa'),
  password: getEnvString('MSSQL_PASSWORD', 'Password1!'),
  database: getEnvString('MSSQL_DATABASE', 'master'),
  options: {
    encrypt: getEnvBool('MSSQL_ENCRYPT', false),
  },
});

const getSqlCredentials = (vcaps, dbname) => {
  return R.cond([
    // AzureSQL failover group
    [ R.hasPath([ 'azure-sqldb-failover-group' ]), getSqlVcapsCredentials(dbname) ],
    // local
    [ R.T, getSqlLocalCredentials ],
  ])(vcaps);
};

const getDispositionConfig = () => {
  return {
    purgeCheckInterval: Number(process.env.DISPOSITION_PURGE_CHECK_INTERVALE) * 60000 || 60 * 60000, // minutes to be converted to milliseconds
    purgeInterval: Number(process.env.DISPOSITION_PURGE_INTERVAL) || 1440, // minutes
    purgeTTL: Number(process.env.DISPOSITION_PURGE_TTL) || 60, // seconds
    deleteCheck: Number(process.env.DISPOSITION_PURGE_DELETE_CHECK) || 2000,
    maxDeleteCount: Number(process.env.DISPOSITION_PURGE_DELETE_COUNT) || 10000,
    secretCustomerPepper: process.env.CDP_SECRET_CUSTOMER_PEPPER || '',
  };
};

// eslint-disable-next-line sonarjs/cognitive-complexity
module.exports = () => { // NOSONAR
  // remove CDP_SECRET_PASSPORT_PRIVATE_KEY when migration is done
  const secretKey = process.env.CDP_SECRET_PASSPORT_PRIVATE_KEY || process.env.CDP_SECRET_S2S_PRIVATE_KEY;
  if (!secretKey) {
    throw new Error('service to service authentication private key is missing');
  }
  const pepperKey = process.env.CDP_SECRET_CARD_PEPPER;
  if (!pepperKey) {
    throw new Error('CDP Secret card pepper is missing');
  }
  return {
    epm: getEnvString('EPM', 'BFB6'),
    env: process.env.NODE_ENV || 'production',
    space: getSpace('IST'),
    clsNamespace: 'pigeon_cls_namespace',
    hostingEnv: getEnvString('HOSTING_ENV', undefined),
    pigeonAPIAtlas: {
      uri: process.env.PIGEON_BFF_ATLAS_URI,
      timeout: Number(process.env.PIGEON_BFF_ATLAS_TIMEOUT) || 1000,
    },
    pigeonAPIPCF: {
      uri: process.env.PIGEON_BFF_PCF_URI,
      timeout: Number(process.env.PIGEON_BFF_PCF_TIMEOUT) || 1000,
    },
    http: {
      port: Number(process.env.SERVER_PORT) || 8080,
      gracefulTimeout: Number(process.env.SERVER_GRACEFUL_TIMEOUT) || 10000,
      origin: (process.env.CORS_ORIGIN || '*').split(','),
    },
    saml: {
      exitUrl: process.env.SAML_EXIT_URL,
    },
    campaignAPI: {
      uri: process.env.CAMPAIGN_API_URI,
      timeout: Number(process.env.CAMPAIGN_API_TIMEOUT) || 1000,
      cacheTTL: process.env.CAMPAIGN_CACHE_TTL || 5000,
    },
    alertAPI: {
      uri: process.env.ALERT_API_URI,
      timeout: Number(process.env.ALERT_API_TIMEOUT) || 1000,
      supportedLocales: [ 'en-US', 'fr' ], // contentful locales
      cacheTTL: process.env.ALERT_CACHE_TTL || 5000,
    },
    contentAPI: {
      uri: process.env.CONTENT_API_URI,
      timeout: Number(process.env.CONTENT_API_TIMEOUT) || 1000,
    },
    targetedCampaignAPI: {
      insightBasePath: process.env.INSIGHTS_API_URI,
      insightsAtlasUri: process.env.INSIGHTS_ATLAS_API_URI,
      insightsMockUri: [ 'IST', 'UAT', 'NFT' ].includes(getSpace('IST')) ? process.env.INSIGHTS_API_URI_MOCK : undefined,
      dcCampaignsBasePath: process.env.DCCAMPAIGNS_API_URI,
      dcCampaignsMockUri: [ 'IST', 'UAT', 'NFT' ].includes(getSpace('IST')) ? process.env.DCCAMPAIGNS_API_URI_MOCK : undefined,
      timeout: Number(process.env.INSIGHTS_API_TIMEOUT) || 2000,
    },
    serviceAuth: {
      exchangeOpaqueTokenURI: process.env.AUTH_OPAQUE_EXCHANGE_URI,
      accessTokenURI: process.env.AUTH_ACCESS_TOKEN_URI,
      publicKeyJWKS: process.env.AUTH_S2S_JWKS_URI,
      privateKey: Buffer.from(secretKey, 'base64').toString(),
      privateKeyAlgorithm: process.env.AUTH_S2S_PRIVATEKEY_ALGO,
      scope: process.env.AUTH_S2S_CLAIM_SCOPE,
      clientId: process.env.AUTH_S2S_CLIENTID,
      expiresIn: process.env.AUTH_S2S_CLAIM_EXPIRESIN,
      notBefore: process.env.AUTH_S2S_CLAIM_NOTBEFORE,
      allowCustomerToken: getEnvBool('AUTH_S2S_ALLOW_CUSTOMER', false),
      allowOpaqueToken: getEnvBool('AUTH_S2S_ALLOW_OPAQUE', true),
      tokenPath: process.env.AUTH_S2S_TOKEN_PATH,
      tokenClaimsPath: process.env.AUTH_S2S_CLAIMS_PATH,
      anonymousClientIds: (process.env.ANONYMOUS_CLIENT_IDS || '').split(','),
    },
    investmentAPI: {
      uri: process.env.INVESTMENT_API_URI,
      timeout: Number(process.env.INVESTMENT_API_TIMEOUT) || 3000,
    },
    credentialsAPI: {
      uri: process.env.CREDENTIALS_API_URI,
      AtlasUri: process.env.CREDENTIALS_ATLAS_API_URI,
      timeout: Number(process.env.CREDENTIALS_API_TIMEOUT) || 3000,
    },
    launchDarkly: {
      proxyHost: process.env.PROXY_HOST,
      proxyPort: Number(process.env.PROXY_PORT),
      secret: Buffer.from(process.env.CDP_SECRET_LAUNCH_DARKLY_SDK_KEY, 'base64').toString(),
      userKey: process.env.LAUNCH_DARKLY_USER_ID,
    },
    marvelAPI: {
      rewardsUri: process.env.MARVEL_REWARDS_API,
      rewardsAtlasUri: process.env.MARVEL_REWARDS_ATLAS_API,
      cardProfileUri: process.env.MARVEL_CARD_PROFILE,
      AtlasUri: process.env.MARVEL_ACCOUNTS_ATLAS_API,
      uri: process.env.MARVEL_ACCOUNTS_API,
      productBook: {
        refreshIntervalMillis: 24 * 60 * 60 * 1000,
        productsUri: process.env.MARVEL_PRODUCT_API_URL,
      },
      circuitBreaker: {
        timeout: process.env.CIRCUIT_BREAKER_TIMEOUT,
        errorThreshold: process.env.CIRCUIT_BREAKER_ERROR_THRESHOLD,
        breakerResetTimeout: process.env.CIRCUIT_BREAKER_RESET_TIMEOUT,
      },
      ttl: getEnvNumber('MARVEL_ACCOUNT_API_REDIS_TTL', 600),
      timeout: process.env.MARVEL_API_TIMEOUT,
    },
    mssql: {
      ...getSqlCredentials(getEnvObject('VCAP_SERVICES', {}), getEnvString('MSSQL_DBNAME', 'pigeon-ist-failover-ist')),
      schema: getEnvString('MSSQL_SCHEMA', 'disposition'),
      min: getEnvNumber('MSSQL_POOL_MIN', 1),
      max: getEnvNumber('MSSQL_POOL_MAX', 5),
      timezone: 'UTC',
      timeout: getEnvNumber('MSSQL_TIMEOUT', 30000),
    },
    redisConfig: {
      connection: getRedisConnectionConfig(),
      namespace: getEnvString('REDIS_NAMESPACE', undefined),
      useKeyHash: getEnvBool('REDIS_USE_KEY_HASH', true),
      encryptionKey: fromBase64(getEnvString('CDP_SECRET_REDIS_CACHE', undefined)),
    },
    features: {
      application: getEnvBool('FEATURES_APPLICATION', false),
      disposition: getEnvBool('FEATURES_DISPOSITION', false),
      keepAlive: getEnvBool('FEATURES_KEEPALIVE', false),
    },
    dispositions: getDispositionConfig(),
    logging: {
      name: getEnvString('LOG_NAME', 'pigeon'),
      pepper: Buffer.from(pepperKey, 'base64').toString(),
      obfuscate: getEnvBool('LOG_OBFUSCATE', 1),
      colorize: getEnvBool('LOG_COLORIZE', 1),
      prettyPrint: getEnvBool('LOG_PRETTY_PRINT', 1),
      ignoreSiem: !getEnvBool('LOG_SIEM', 0),
      ignoredLogRoutes: getEnvJson(process.env.IGNORED_ROUTES_FOR_LOGGING, []),
    },
    rateLimiting: {
      window: 60000, // 1 minute
      cdpTrustedIp: process.env.RATE_LIMIT_CDP_TRUSTED_IP,
      client: {
        alerts: getEnvNumber('RATE_LIMIT_CLIENT_ALERTS_MAX', 10),
        campaigns: getEnvNumber('RATE_LIMIT_CLIENT_CAMPAIGNS_MAX', 10),
      },
      overall: {
        alerts: getEnvNumber('RATE_LIMIT_OVERALL_ALERTS_MAX', 20),
        campaigns: getEnvNumber('RATE_LIMIT_OVERALL_CAMPAIGNS_MAX', 20),
        contentsCatalog: getEnvNumber('RATE_LIMIT_OVERALL_CONTENTS_CATALOG_MAX', 20),
      },
    },
    prodigyWealthAPI: {
      uri: process.env.PRODIGY_WEALTH_API_URI,
      timeout: Number(process.env.PROGIDY_WEALTH_API_TIMEOUT) || 5000,
    },
  };
};
