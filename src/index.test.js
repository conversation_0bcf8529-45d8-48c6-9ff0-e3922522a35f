// process.env.CDP_SECRET_S2S_PRIVATE_KEY = '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
// process.env.PASSPORT_API_S2S_ALGORITHM = 'RS256';
// process.env.PASSPORT_API_S2S_CLIENTID = '107eb6a6-da19-41bd-9e7f-01598f83689d';
// process.env.PASSPORT_API_S2S_SCOPE = 'some:scope';
// process.env.CDP_SECRET_LAUNCH_DARKLY_SDK_KEY = 'dGVzdC1zdHJpbmc=';
// const http = require('./index');

describe('HTTP', () => {
  test('dummy', () => {});
  // afterAll(() => {
  //   http.close();
  // });
  // test('loaded', () => {
  //   expect(http).toBeDefined();
  // });
});
