const redis = require('redis');
const LaunchDarkly = require('launchdarkly-node-server-sdk');
const createLaunchDarkly = require('./components/launch-darkly');
const config = require('./config')();
const { createRedisClient } = require('./components/cache');
const Redlock = require('redlock');
const init = require('./init');
const { createLogger } = require('nrlw-express-scribe');

// initialize logger
const logger = createLogger({
  level: [ 'production', 'test' ].indexOf(config.env) !== -1 ? 'info' : 'debug',
  programId: `EPM${config.epm}-${config.logging.name}`,
  pepper: config.logging.pepper,
  obfuscate: config.logging.obfuscate,
  colorize: config.logging.colorize,
  prettyPrint: config.logging.prettyPrint,
  ignoreSiem: config.logging.ignoreSiem,
});

// catch unhandled promise rejection and exit
process.on('unhandledRejection', err => {
  logger.error({ message: 'unhandled promise rejection', err: { message: err.message, stack: err.stack } });
});
// catch uncaught exception, log it and exit
process.on('uncaughtException', (err) => {
  logger.error({ message: 'uncaught exception', err: { message: err.message, stack: err.stack } });
  // launchDarklyService.close();
  process.exit(1);
});

let launchDarklyService;
(async () => {
  launchDarklyService = createLaunchDarkly(
    LaunchDarkly,
    config.launchDarkly.secret,
    config.launchDarkly.userKey,
    {
      proxyHost: config.launchDarkly.proxyHost,
      proxyPort: config.launchDarkly.proxyPort,
      logger,
    },
  );

  try {
    await launchDarklyService.init();
    logger.info({ message: 'Launch Darkly initialized successfully' });
  } catch (err) {
    logger.error({ message: 'Could not initilize Launch Darkly', err });
  }

  // try to initialize redis cache here
  let redisClient;
  try {
    redisClient = await createRedisClient({ redis, Redlock, logger }, {
      connection: config.redisConfig.connection,
      namespace: config.redisConfig.namespace,
      useKeyHash: config.redisConfig.useKeyHash,
      encryptionKey: config.redisConfig.encryptionKey,
    });
    logger.info({ message: 'Redis initialized successfuly' });
  } catch (err) {
    logger.error({ message: 'Unable to initialize Redis client', err });
  }

  /* if Launch Darkly can't initialize, launchDarklyService.isFeatureEnabled
  will always return the default value for that flag */
  await init(config, logger, redisClient, launchDarklyService);
}
)();
